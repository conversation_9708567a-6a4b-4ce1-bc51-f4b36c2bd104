/**
 * Configuration et connexion à la base de données PostgreSQL
 * Utilise Prisma comme ORM pour une gestion type-safe
 */

import { PrismaClient } from '@prisma/client';
import { config } from './config';
import { logger } from '../utils/logger';

// Instance globale Prisma
let prisma: PrismaClient;

/**
 * Initialise la connexion à la base de données
 */
export const connectDatabase = async (): Promise<PrismaClient> => {
  try {
    if (!prisma) {
      prisma = new PrismaClient({
        datasources: {
          db: {
            url: config.database.url
          }
        },
        log: [
          {
            emit: 'event',
            level: 'query',
          },
          {
            emit: 'event',
            level: 'error',
          },
          {
            emit: 'event',
            level: 'info',
          },
          {
            emit: 'event',
            level: 'warn',
          },
        ],
      });

      // Logging des requêtes en développement
      if (config.app.environment === 'development') {
        prisma.$on('query', (e) => {
          logger.debug('Database Query', {
            query: e.query,
            params: e.params,
            duration: `${e.duration}ms`,
            target: e.target
          });
        });
      }

      // Logging des erreurs
      prisma.$on('error', (e) => {
        logger.error('Database Error', {
          message: e.message,
          target: e.target
        });
      });

      // Logging des informations
      prisma.$on('info', (e) => {
        logger.info('Database Info', {
          message: e.message,
          target: e.target
        });
      });

      // Logging des avertissements
      prisma.$on('warn', (e) => {
        logger.warn('Database Warning', {
          message: e.message,
          target: e.target
        });
      });

      // Test de connexion
      await prisma.$connect();
      
      // Vérification de la santé de la base
      await prisma.$queryRaw`SELECT 1`;
      
      logger.info('✅ Connexion à PostgreSQL établie avec succès', {
        database: config.database.name,
        host: config.database.host,
        port: config.database.port
      });
    }

    return prisma;
  } catch (error) {
    logger.error('❌ Erreur lors de la connexion à PostgreSQL:', error);
    throw error;
  }
};

/**
 * Ferme la connexion à la base de données
 */
export const disconnectDatabase = async (): Promise<void> => {
  try {
    if (prisma) {
      await prisma.$disconnect();
      logger.info('🔌 Connexion à PostgreSQL fermée');
    }
  } catch (error) {
    logger.error('Erreur lors de la fermeture de la connexion PostgreSQL:', error);
    throw error;
  }
};

/**
 * Vérifie la santé de la base de données
 */
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    if (!prisma) {
      return false;
    }

    // Test simple de requête
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('Vérification de santé de la base échouée:', error);
    return false;
  }
};

/**
 * Obtient les statistiques de la base de données
 */
export const getDatabaseStats = async () => {
  try {
    if (!prisma) {
      throw new Error('Base de données non connectée');
    }

    // Statistiques des tables principales
    const stats = await prisma.$transaction([
      prisma.user.count(),
      prisma.account.count(),
      prisma.transaction.count(),
      prisma.budget.count(),
      prisma.savingsGoal.count(),
    ]);

    return {
      users: stats[0],
      accounts: stats[1],
      transactions: stats[2],
      budgets: stats[3],
      savingsGoals: stats[4],
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques:', error);
    throw error;
  }
};

/**
 * Exécute les migrations de base de données
 */
export const runMigrations = async (): Promise<void> => {
  try {
    logger.info('🔄 Exécution des migrations de base de données...');
    
    // Les migrations Prisma sont gérées via CLI
    // Cette fonction peut être étendue pour des migrations personnalisées
    
    logger.info('✅ Migrations exécutées avec succès');
  } catch (error) {
    logger.error('❌ Erreur lors de l'exécution des migrations:', error);
    throw error;
  }
};

/**
 * Nettoie les données expirées (tâche de maintenance)
 */
export const cleanupExpiredData = async (): Promise<void> => {
  try {
    logger.info('🧹 Nettoyage des données expirées...');

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Supprimer les sessions expirées
    const deletedSessions = await prisma.userSession.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    // Supprimer les tokens de réinitialisation expirés
    const deletedTokens = await prisma.passwordResetToken.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    // Archiver les anciennes notifications (> 30 jours)
    const archivedNotifications = await prisma.notification.updateMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo
        },
        archived: false
      },
      data: {
        archived: true
      }
    });

    logger.info('✅ Nettoyage terminé', {
      deletedSessions: deletedSessions.count,
      deletedTokens: deletedTokens.count,
      archivedNotifications: archivedNotifications.count
    });
  } catch (error) {
    logger.error('❌ Erreur lors du nettoyage:', error);
    throw error;
  }
};

// Gestion de la fermeture propre
process.on('beforeExit', async () => {
  await disconnectDatabase();
});

process.on('SIGINT', async () => {
  await disconnectDatabase();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectDatabase();
  process.exit(0);
});

// Export de l'instance Prisma
export const getPrismaClient = (): PrismaClient => {
  if (!prisma) {
    throw new Error('Base de données non initialisée. Appelez connectDatabase() d\'abord.');
  }
  return prisma;
};

export { prisma };
