<!doctype html>
<html lang="fr" dir="ltr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- SEO Meta Tags -->
    <title>Nouri - Coach Financier IA | Gestion intelligente de vos finances</title>
    <meta name="description" content="Nouri est votre assistant financier intelligent qui vous aide à gérer vos finances, créer des budgets et atteindre vos objectifs d'épargne en Tunisie." />
    <meta name="keywords" content="finance, budget, épargne, tunisie, coach financier, IA, gestion financière" />
    <meta name="author" content="Équipe Nouri" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://nouri.tn/" />
    <meta property="og:title" content="Nouri - Coach Financier IA" />
    <meta property="og:description" content="Votre assistant financier intelligent pour une meilleure gestion de vos finances en Tunisie." />
    <meta property="og:image" content="https://nouri.tn/og-image.png" />
    <meta property="og:locale" content="fr_TN" />
    
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://nouri.tn/" />
    <meta property="twitter:title" content="Nouri - Coach Financier IA" />
    <meta property="twitter:description" content="Votre assistant financier intelligent pour une meilleure gestion de vos finances en Tunisie." />
    <meta property="twitter:image" content="https://nouri.tn/twitter-image.png" />
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#1976d2" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Nouri" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-Frame-Options" content="DENY" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />
    
    <!-- Critical CSS will be injected here by Vite -->
    <style>
      /* Critical CSS for initial render */
      * {
        box-sizing: border-box;
      }
      
      body {
        margin: 0;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #fafafa;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background-color: #fafafa;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e3f2fd;
        border-top: 4px solid #1976d2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* RTL Support for Arabic */
      [dir="rtl"] {
        text-align: right;
      }
      
      /* Hide scrollbar for Chrome, Safari and Opera */
      .hide-scrollbar::-webkit-scrollbar {
        display: none;
      }
      
      /* Hide scrollbar for IE, Edge and Firefox */
      .hide-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div class="loading-spinner">
        <div class="spinner"></div>
      </div>
    </div>
    
    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
    
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
