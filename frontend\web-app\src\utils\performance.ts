/**
 * Performance Monitoring Utilities
 * Measures and reports application performance metrics
 */

export interface PerformanceMetrics {
  fcp: number // First Contentful Paint
  lcp: number // Largest Contentful Paint
  fid: number // First Input Delay
  cls: number // Cumulative Layout Shift
  ttfb: number // Time to First Byte
  domContentLoaded: number
  loadComplete: number
}

class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {}
  private observers: PerformanceObserver[] = []

  constructor() {
    this.initializeObservers()
  }

  private initializeObservers() {
    // Observe paint metrics (FCP, LCP)
    if ('PerformanceObserver' in window) {
      try {
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.name === 'first-contentful-paint') {
              this.metrics.fcp = entry.startTime
            }
          }
        })
        paintObserver.observe({ entryTypes: ['paint'] })
        this.observers.push(paintObserver)

        // Observe LCP
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1] as any
          this.metrics.lcp = lastEntry.startTime
        })
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
        this.observers.push(lcpObserver)

        // Observe FID
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.metrics.fid = (entry as any).processingStart - entry.startTime
          }
        })
        fidObserver.observe({ entryTypes: ['first-input'] })
        this.observers.push(fidObserver)

        // Observe CLS
        const clsObserver = new PerformanceObserver((list) => {
          let clsValue = 0
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              clsValue += (entry as any).value
            }
          }
          this.metrics.cls = clsValue
        })
        clsObserver.observe({ entryTypes: ['layout-shift'] })
        this.observers.push(clsObserver)

      } catch (error) {
        console.warn('Performance Observer not supported:', error)
      }
    }

    // Measure navigation timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        if (navigation) {
          this.metrics.ttfb = navigation.responseStart - navigation.requestStart
          this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart
          this.metrics.loadComplete = navigation.loadEventEnd - navigation.loadEventStart
        }
        this.reportMetrics()
      }, 0)
    })
  }

  private reportMetrics() {
    if (import.meta.env.DEV) {
      console.group('🚀 Performance Metrics')
      console.log('First Contentful Paint (FCP):', this.formatTime(this.metrics.fcp))
      console.log('Largest Contentful Paint (LCP):', this.formatTime(this.metrics.lcp))
      console.log('First Input Delay (FID):', this.formatTime(this.metrics.fid))
      console.log('Cumulative Layout Shift (CLS):', this.metrics.cls?.toFixed(4))
      console.log('Time to First Byte (TTFB):', this.formatTime(this.metrics.ttfb))
      console.log('DOM Content Loaded:', this.formatTime(this.metrics.domContentLoaded))
      console.log('Load Complete:', this.formatTime(this.metrics.loadComplete))
      console.groupEnd()

      // Performance scoring
      this.scorePerformance()
    }
  }

  private formatTime(time?: number): string {
    if (!time) return 'N/A'
    return `${time.toFixed(2)}ms`
  }

  private scorePerformance() {
    const scores = {
      fcp: this.scoreFCP(this.metrics.fcp),
      lcp: this.scoreLCP(this.metrics.lcp),
      fid: this.scoreFID(this.metrics.fid),
      cls: this.scoreCLS(this.metrics.cls)
    }

    const averageScore = Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.values(scores).length

    console.group('📊 Performance Score')
    console.log('FCP Score:', this.getScoreLabel(scores.fcp))
    console.log('LCP Score:', this.getScoreLabel(scores.lcp))
    console.log('FID Score:', this.getScoreLabel(scores.fid))
    console.log('CLS Score:', this.getScoreLabel(scores.cls))
    console.log('Overall Score:', this.getScoreLabel(averageScore), `(${averageScore.toFixed(1)}/100)`)
    console.groupEnd()
  }

  private scoreFCP(fcp?: number): number {
    if (!fcp) return 0
    if (fcp <= 1800) return 100
    if (fcp <= 3000) return 50
    return 0
  }

  private scoreLCP(lcp?: number): number {
    if (!lcp) return 0
    if (lcp <= 2500) return 100
    if (lcp <= 4000) return 50
    return 0
  }

  private scoreFID(fid?: number): number {
    if (!fid) return 100
    if (fid <= 100) return 100
    if (fid <= 300) return 50
    return 0
  }

  private scoreCLS(cls?: number): number {
    if (!cls) return 100
    if (cls <= 0.1) return 100
    if (cls <= 0.25) return 50
    return 0
  }

  private getScoreLabel(score: number): string {
    if (score >= 90) return '🟢 Good'
    if (score >= 50) return '🟡 Needs Improvement'
    return '🔴 Poor'
  }

  public getMetrics(): Partial<PerformanceMetrics> {
    return { ...this.metrics }
  }

  public destroy() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Create global performance monitor instance
export const performanceMonitor = new PerformanceMonitor()

// Export utility functions
export const measureComponentRender = (componentName: string) => {
  const startTime = performance.now()
  
  return () => {
    const endTime = performance.now()
    const renderTime = endTime - startTime
    
    if (import.meta.env.DEV && renderTime > 16) { // Warn if render takes more than 16ms
      console.warn(`⚠️ Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`)
    }
    
    return renderTime
  }
}

export const measureAsyncOperation = async <T>(
  operation: () => Promise<T>,
  operationName: string
): Promise<T> => {
  const startTime = performance.now()
  
  try {
    const result = await operation()
    const endTime = performance.now()
    const duration = endTime - startTime
    
    if (import.meta.env.DEV) {
      console.log(`⏱️ ${operationName}: ${duration.toFixed(2)}ms`)
    }
    
    return result
  } catch (error) {
    const endTime = performance.now()
    const duration = endTime - startTime
    
    if (import.meta.env.DEV) {
      console.error(`❌ ${operationName} failed after ${duration.toFixed(2)}ms:`, error)
    }
    
    throw error
  }
}

export default performanceMonitor
