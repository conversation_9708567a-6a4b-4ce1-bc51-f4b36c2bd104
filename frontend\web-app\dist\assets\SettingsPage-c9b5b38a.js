import{j as e,B as s,o as i,p as r,b as a,I as n,H as l,q as t,d as c,U as o,G as d,g as h,h as x,ac as j,l as u,s as m,ad as p,ae as f,af as g,ag as v,ah as y,ai as b,aj as D,ak as Y,F as C,v as k,w as N,M,al as R,D as w,f as A,_ as F,am as S}from"./mui-vendor-b761306f.js";import{u as T,r as W}from"./react-vendor-2f216e43.js";import{A as I,R as B}from"./index-bd55ea72.js";const E=()=>{const E=T(),{logout:P}=W.useContext(I),[U,q]=W.useState({notifications:{email:!0,push:!0,budgetAlerts:!0,goalReminders:!0,weeklyReports:!1},preferences:{language:"fr",currency:"TND",theme:"light",dateFormat:"DD/MM/YYYY"},security:{twoFactor:!1,biometric:!0,sessionTimeout:30},profile:{firstName:"<PERSON>",lastName:"Ben <PERSON>",email:"<EMAIL>",phone:"+216 20 123 456"}}),[H,G]=W.useState(!1),O=(e,s,i)=>{q((r=>({...r,[e]:{...r[e],[s]:i}})))};return e.jsxs(s,{sx:{flexGrow:1},children:[e.jsx(i,{position:"static",elevation:1,children:e.jsxs(r,{children:[e.jsx(a,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Paramètres"}),e.jsx(n,{color:"inherit",onClick:()=>E(B.DASHBOARD),children:e.jsx(l,{})}),e.jsx(n,{color:"inherit",onClick:()=>{P(),E(B.HOME)},children:e.jsx(t,{})})]})}),e.jsxs(c,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(s,{sx:{mb:4},children:[e.jsx(a,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Paramètres"}),e.jsx(a,{variant:"body1",color:"text.secondary",children:"Personnalisez votre expérience Nouri"})]}),H&&e.jsx(o,{severity:"success",sx:{mb:3},children:"Paramètres sauvegardés avec succès !"}),e.jsxs(d,{container:!0,spacing:3,children:[e.jsx(d,{item:!0,xs:12,md:6,children:e.jsx(h,{children:e.jsxs(x,{children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(j,{}),"Profil"]}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:2,mb:3},children:[e.jsxs(u,{sx:{width:64,height:64,bgcolor:"primary.main"},children:[U.profile.firstName[0],U.profile.lastName[0]]}),e.jsxs(s,{children:[e.jsxs(a,{variant:"h6",children:[U.profile.firstName," ",U.profile.lastName]}),e.jsx(a,{variant:"body2",color:"text.secondary",children:U.profile.email})]})]}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsx(m,{label:"Prénom",value:U.profile.firstName,onChange:e=>O("profile","firstName",e.target.value),fullWidth:!0}),e.jsx(m,{label:"Nom",value:U.profile.lastName,onChange:e=>O("profile","lastName",e.target.value),fullWidth:!0}),e.jsx(m,{label:"Email",type:"email",value:U.profile.email,onChange:e=>O("profile","email",e.target.value),fullWidth:!0}),e.jsx(m,{label:"Téléphone",value:U.profile.phone,onChange:e=>O("profile","phone",e.target.value),fullWidth:!0})]})]})})}),e.jsx(d,{item:!0,xs:12,md:6,children:e.jsx(h,{children:e.jsxs(x,{children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(p,{}),"Notifications"]}),e.jsxs(f,{children:[e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(p,{})}),e.jsx(y,{primary:"Notifications email",secondary:"Recevoir des emails de notification"}),e.jsx(b,{children:e.jsx(D,{checked:U.notifications.email,onChange:e=>O("notifications","email",e.target.checked)})})]}),e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(p,{})}),e.jsx(y,{primary:"Notifications push",secondary:"Recevoir des notifications sur l'appareil"}),e.jsx(b,{children:e.jsx(D,{checked:U.notifications.push,onChange:e=>O("notifications","push",e.target.checked)})})]}),e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(p,{})}),e.jsx(y,{primary:"Alertes budget",secondary:"Être alerté en cas de dépassement"}),e.jsx(b,{children:e.jsx(D,{checked:U.notifications.budgetAlerts,onChange:e=>O("notifications","budgetAlerts",e.target.checked)})})]}),e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(p,{})}),e.jsx(y,{primary:"Rappels objectifs",secondary:"Rappels pour vos objectifs d'épargne"}),e.jsx(b,{children:e.jsx(D,{checked:U.notifications.goalReminders,onChange:e=>O("notifications","goalReminders",e.target.checked)})})]}),e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(p,{})}),e.jsx(y,{primary:"Rapports hebdomadaires",secondary:"Recevoir un résumé chaque semaine"}),e.jsx(b,{children:e.jsx(D,{checked:U.notifications.weeklyReports,onChange:e=>O("notifications","weeklyReports",e.target.checked)})})]})]})]})})}),e.jsx(d,{item:!0,xs:12,md:6,children:e.jsx(h,{children:e.jsxs(x,{children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(Y,{}),"Préférences"]}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsxs(C,{fullWidth:!0,children:[e.jsx(k,{children:"Langue"}),e.jsxs(N,{value:U.preferences.language,label:"Langue",onChange:e=>O("preferences","language",e.target.value),children:[e.jsx(M,{value:"fr",children:"Français"}),e.jsx(M,{value:"ar",children:"العربية"}),e.jsx(M,{value:"en",children:"English"})]})]}),e.jsxs(C,{fullWidth:!0,children:[e.jsx(k,{children:"Devise"}),e.jsxs(N,{value:U.preferences.currency,label:"Devise",onChange:e=>O("preferences","currency",e.target.value),children:[e.jsx(M,{value:"TND",children:"Dinar Tunisien (TND)"}),e.jsx(M,{value:"EUR",children:"Euro (EUR)"}),e.jsx(M,{value:"USD",children:"Dollar US (USD)"})]})]}),e.jsxs(C,{fullWidth:!0,children:[e.jsx(k,{children:"Thème"}),e.jsxs(N,{value:U.preferences.theme,label:"Thème",onChange:e=>O("preferences","theme",e.target.value),children:[e.jsx(M,{value:"light",children:"Clair"}),e.jsx(M,{value:"dark",children:"Sombre"}),e.jsx(M,{value:"auto",children:"Automatique"})]})]}),e.jsxs(C,{fullWidth:!0,children:[e.jsx(k,{children:"Format de date"}),e.jsxs(N,{value:U.preferences.dateFormat,label:"Format de date",onChange:e=>O("preferences","dateFormat",e.target.value),children:[e.jsx(M,{value:"DD/MM/YYYY",children:"DD/MM/YYYY"}),e.jsx(M,{value:"MM/DD/YYYY",children:"MM/DD/YYYY"}),e.jsx(M,{value:"YYYY-MM-DD",children:"YYYY-MM-DD"})]})]})]})]})})}),e.jsx(d,{item:!0,xs:12,md:6,children:e.jsx(h,{children:e.jsxs(x,{children:[e.jsxs(a,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(R,{}),"Sécurité"]}),e.jsxs(f,{children:[e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(R,{})}),e.jsx(y,{primary:"Authentification à deux facteurs",secondary:"Sécurité renforcée pour votre compte"}),e.jsx(b,{children:e.jsx(D,{checked:U.security.twoFactor,onChange:e=>O("security","twoFactor",e.target.checked)})})]}),e.jsxs(g,{children:[e.jsx(v,{children:e.jsx(R,{})}),e.jsx(y,{primary:"Authentification biométrique",secondary:"Utiliser empreinte/Face ID"}),e.jsx(b,{children:e.jsx(D,{checked:U.security.biometric,onChange:e=>O("security","biometric",e.target.checked)})})]})]}),e.jsx(w,{sx:{my:2}}),e.jsxs(C,{fullWidth:!0,children:[e.jsx(k,{children:"Délai d'expiration de session"}),e.jsxs(N,{value:U.security.sessionTimeout,label:"Délai d'expiration de session",onChange:e=>O("security","sessionTimeout",e.target.value),children:[e.jsx(M,{value:15,children:"15 minutes"}),e.jsx(M,{value:30,children:"30 minutes"}),e.jsx(M,{value:60,children:"1 heure"}),e.jsx(M,{value:120,children:"2 heures"})]})]})]})})})]}),e.jsxs(s,{sx:{mt:4,display:"flex",gap:2,justifyContent:"space-between"},children:[e.jsx(A,{variant:"outlined",color:"error",startIcon:e.jsx(F,{}),onClick:()=>{window.confirm("Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.")&&(alert("Compte supprimé (simulation)"),P(),E(B.HOME))},children:"Supprimer le compte"}),e.jsx(A,{variant:"contained",startIcon:e.jsx(S,{}),onClick:()=>{G(!0),setTimeout((()=>G(!1)),3e3)},size:"large",children:"Sauvegarder les paramètres"})]})]})]})};export{E as SettingsPage,E as default};
