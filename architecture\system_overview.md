# Nouri – Coach Financier IA Tunisie
## Architecture Système & Spécifications Techniques

### **Résumé Exécutif**
Nouri est une plateforme de coaching financier de nouvelle génération alimentée par l'IA, conçue spécifiquement pour le marché tunisien. L'application fournit une gestion intelligente des finances personnelles grâce à l'analyse automatisée des transactions, des recommandations personnalisées et un support multilingue (arabe/français).

### **Architecture Principale : Microservices Cloud-Native**

#### **Applications Frontend**
- **Application Web** : React 18 + TypeScript + Vite
- **Application Mobile** : Flutter (iOS/Android)
- **Tableau de Bord Admin** : React + Material-UI

#### **Architecture Backend en Microservices**

1. **Service Passerelle API** (Node.js + Express)
   - Point d'entrée unique pour toutes les requêtes clients
   - Authentification et autorisation (intégration Auth0)
   - Limitation de débit et routage des requêtes
   - Versioning API et documentation

2. **Service Gestion Utilisateurs** (Node.js + TypeScript)
   - Profils utilisateurs et préférences
   - Conformité KYC (Know Your Customer)
   - Authentification multi-facteurs
   - Paramètres de confidentialité et gestion du consentement

3. **Service Intégration Bancaire** (Node.js + TypeScript)
   - Connexions sécurisées aux APIs bancaires
   - Synchronisation des transactions en temps réel
   - Surveillance des soldes de comptes
   - Initiation de paiements (fonctionnalité future)

4. **AI Analytics Engine** (Python + FastAPI)
   - Transaction categorization using NLP
   - Spending pattern analysis
   - Predictive modeling for budgets
   - Personalized recommendation generation

5. **Chatbot Service** (Python + FastAPI)
   - Natural Language Processing (Arabic/French)
   - Conversational AI using OpenAI GPT-4
   - Intent recognition and response generation
   - Voice-to-text integration

6. **Budget & Goals Service** (Node.js + TypeScript)
   - Smart budget creation and management
   - Savings goal tracking
   - Financial milestone notifications
   - Automated savings recommendations

7. **Notification Service** (Node.js + TypeScript)
   - Push notifications (Firebase)
   - Email notifications
   - SMS alerts (critical notifications)
   - In-app notification center

8. **Education Service** (Node.js + TypeScript)
   - Gamified learning modules
   - Progress tracking and badges
   - Financial literacy content
   - Interactive quizzes and simulations

9. **Reporting Service** (Python + FastAPI)
   - Financial report generation
   - Data visualization and charts
   - PDF export functionality
   - Analytics dashboard data

#### **Data Layer**
- **Primary Database**: PostgreSQL (user data, transactions, budgets)
- **Cache Layer**: Redis (session management, real-time data)
- **Document Store**: MongoDB (educational content, chat logs)
- **Time Series DB**: InfluxDB (financial metrics, analytics)

#### **Infrastructure & DevOps**
- **Cloud Provider**: AWS (with multi-AZ deployment)
- **Container Orchestration**: Kubernetes
- **CI/CD Pipeline**: GitHub Actions
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Security**: AWS WAF, VPC, IAM roles