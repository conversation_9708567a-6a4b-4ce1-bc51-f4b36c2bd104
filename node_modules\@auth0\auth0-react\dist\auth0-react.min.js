!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).reactAuth0={},e.<PERSON>act)}(this,(function(e,t){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},n(e,t)};var i=function(){return i=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},i.apply(this,arguments)};function o(e,t,n,i){return new(n||(n=Promise))((function(o,r){function a(e){try{c(i.next(e))}catch(e){r(e)}}function s(e){try{c(i.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((i=i.apply(e,t||[])).next())}))}function r(e,t){var n,i,o,r={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(c){return function(s){if(n)throw new TypeError("Generator is already executing.");for(;a&&(a=0,s[0]&&(r=0)),r;)try{if(n=1,i&&(o=2&s[0]?i.return:s[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,s[1])).done)return o;switch(i=0,o&&(s=[2&s[0],o.value]),s[0]){case 0:case 1:o=s;break;case 4:return r.label++,{value:s[1],done:!1};case 5:r.label++,i=s[1],s=[0];continue;case 7:s=r.ops.pop(),r.trys.pop();continue;default:if(!(o=r.trys,(o=o.length>0&&o[o.length-1])||6!==s[0]&&2!==s[0])){r=0;continue}if(3===s[0]&&(!o||s[1]>o[0]&&s[1]<o[3])){r.label=s[1];break}if(6===s[0]&&r.label<o[1]){r.label=o[1],o=s;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(s);break}o[2]&&r.ops.pop(),r.trys.pop();continue}s=t.call(e,r)}catch(e){s=[6,e],i=0}finally{n=o=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,c])}}}function a(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n}"function"==typeof SuppressedError&&SuppressedError,"function"==typeof SuppressedError&&SuppressedError;var s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function u(e,t){return e(t={exports:{}},t.exports),t.exports}var l=u((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(){var e=this;this.locked=new Map,this.addToLocked=function(t,n){var i=e.locked.get(t);void 0===i?void 0===n?e.locked.set(t,[]):e.locked.set(t,[n]):void 0!==n&&(i.unshift(n),e.locked.set(t,i))},this.isLocked=function(t){return e.locked.has(t)},this.lock=function(t){return new Promise((function(n,i){e.isLocked(t)?e.addToLocked(t,n):(e.addToLocked(t),n())}))},this.unlock=function(t){var n=e.locked.get(t);if(void 0!==n&&0!==n.length){var i=n.pop();e.locked.set(t,n),void 0!==i&&setTimeout(i,0)}else e.locked.delete(t)}}return e.getInstance=function(){return void 0===e.instance&&(e.instance=new e),e.instance},e}();t.default=function(){return n.getInstance()}}));c(l);var d=c(u((function(e,t){var n=s&&s.__awaiter||function(e,t,n,i){return new(n||(n=Promise))((function(o,r){function a(e){try{c(i.next(e))}catch(e){r(e)}}function s(e){try{c(i.throw(e))}catch(e){r(e)}}function c(e){e.done?o(e.value):new n((function(t){t(e.value)})).then(a,s)}c((i=i.apply(e,t||[])).next())}))},i=s&&s.__generator||function(e,t){var n,i,o,r,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(o=2&r[0]?i.return:r[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,r[1])).done)return o;switch(i=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,i=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!((o=(o=a.trys).length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){a.label=r[1];break}if(6===r[0]&&a.label<o[1]){a.label=o[1],o=r;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(r);break}o[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(e){r=[6,e],i=0}finally{n=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}},o=s;Object.defineProperty(t,"__esModule",{value:!0});var r="browser-tabs-lock-key",a={key:function(e){return n(o,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},getItem:function(e){return n(o,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},clear:function(){return n(o,void 0,void 0,(function(){return i(this,(function(e){return[2,window.localStorage.clear()]}))}))},removeItem:function(e){return n(o,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},setItem:function(e,t){return n(o,void 0,void 0,(function(){return i(this,(function(e){throw new Error("Unsupported")}))}))},keySync:function(e){return window.localStorage.key(e)},getItemSync:function(e){return window.localStorage.getItem(e)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(e){return window.localStorage.removeItem(e)},setItemSync:function(e,t){return window.localStorage.setItem(e,t)}};function c(e){return new Promise((function(t){return setTimeout(t,e)}))}function u(e){for(var t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",n="",i=0;i<e;i++)n+=t[Math.floor(61*Math.random())];return n}var d=function(){function e(t){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+u(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=t,void 0===e.waiters&&(e.waiters=[])}return e.prototype.acquireLock=function(t,o){return void 0===o&&(o=5e3),n(this,void 0,void 0,(function(){var n,s,l,d,h,p,m;return i(this,(function(i){switch(i.label){case 0:n=Date.now()+u(4),s=Date.now()+o,l=r+"-"+t,d=void 0===this.storageHandler?a:this.storageHandler,i.label=1;case 1:return Date.now()<s?[4,c(30)]:[3,8];case 2:return i.sent(),null!==d.getItemSync(l)?[3,5]:(h=this.id+"-"+t+"-"+n,[4,c(Math.floor(25*Math.random()))]);case 3:return i.sent(),d.setItemSync(l,JSON.stringify({id:this.id,iat:n,timeoutKey:h,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,c(30)];case 4:return i.sent(),null!==(p=d.getItemSync(l))&&(m=JSON.parse(p)).id===this.id&&m.iat===n?(this.acquiredIatSet.add(n),this.refreshLockWhileAcquired(l,n),[2,!0]):[3,7];case 5:return e.lockCorrector(void 0===this.storageHandler?a:this.storageHandler),[4,this.waitForSomethingToChange(s)];case 6:i.sent(),i.label=7;case 7:return n=Date.now()+u(4),[3,1];case 8:return[2,!1]}}))}))},e.prototype.refreshLockWhileAcquired=function(e,t){return n(this,void 0,void 0,(function(){var o=this;return i(this,(function(r){return setTimeout((function(){return n(o,void 0,void 0,(function(){var n,o,r;return i(this,(function(i){switch(i.label){case 0:return[4,l.default().lock(t)];case 1:return i.sent(),this.acquiredIatSet.has(t)?(n=void 0===this.storageHandler?a:this.storageHandler,null===(o=n.getItemSync(e))?(l.default().unlock(t),[2]):((r=JSON.parse(o)).timeRefreshed=Date.now(),n.setItemSync(e,JSON.stringify(r)),l.default().unlock(t),this.refreshLockWhileAcquired(e,t),[2])):(l.default().unlock(t),[2])}}))}))}),1e3),[2]}))}))},e.prototype.waitForSomethingToChange=function(t){return n(this,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return[4,new Promise((function(n){var i=!1,o=Date.now(),r=!1;function a(){if(r||(window.removeEventListener("storage",a),e.removeFromWaiting(a),clearTimeout(s),r=!0),!i){i=!0;var t=50-(Date.now()-o);t>0?setTimeout(n,t):n(null)}}window.addEventListener("storage",a),e.addToWaiting(a);var s=setTimeout(a,Math.max(0,t-Date.now()))}))];case 1:return n.sent(),[2]}}))}))},e.addToWaiting=function(t){this.removeFromWaiting(t),void 0!==e.waiters&&e.waiters.push(t)},e.removeFromWaiting=function(t){void 0!==e.waiters&&(e.waiters=e.waiters.filter((function(e){return e!==t})))},e.notifyWaiters=function(){void 0!==e.waiters&&e.waiters.slice().forEach((function(e){return e()}))},e.prototype.releaseLock=function(e){return n(this,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:return[4,this.releaseLock__private__(e)];case 1:return[2,t.sent()]}}))}))},e.prototype.releaseLock__private__=function(t){return n(this,void 0,void 0,(function(){var n,o,s,c;return i(this,(function(i){switch(i.label){case 0:return n=void 0===this.storageHandler?a:this.storageHandler,o=r+"-"+t,null===(s=n.getItemSync(o))?[2]:(c=JSON.parse(s)).id!==this.id?[3,2]:[4,l.default().lock(c.iat)];case 1:i.sent(),this.acquiredIatSet.delete(c.iat),n.removeItemSync(o),l.default().unlock(c.iat),e.notifyWaiters(),i.label=2;case 2:return[2]}}))}))},e.lockCorrector=function(t){for(var n=Date.now()-5e3,i=t,o=[],a=0;;){var s=i.keySync(a);if(null===s)break;o.push(s),a++}for(var c=!1,u=0;u<o.length;u++){var l=o[u];if(l.includes(r)){var d=i.getItemSync(l);if(null!==d){var h=JSON.parse(d);(void 0===h.timeRefreshed&&h.timeAcquired<n||void 0!==h.timeRefreshed&&h.timeRefreshed<n)&&(i.removeItemSync(l),c=!0)}}}c&&e.notifyWaiters()},e.waiters=void 0,e}();t.default=d})));const h={timeoutInSeconds:60},p={name:"auth0-spa-js",version:"2.1.3"},m=()=>Date.now();class f extends Error{constructor(e,t){super(t),this.error=e,this.error_description=t,Object.setPrototypeOf(this,f.prototype)}static fromPayload({error:e,error_description:t}){return new f(e,t)}}class g extends f{constructor(e,t,n,i=null){super(e,t),this.state=n,this.appState=i,Object.setPrototypeOf(this,g.prototype)}}class w extends f{constructor(){super("timeout","Timeout"),Object.setPrototypeOf(this,w.prototype)}}class y extends w{constructor(e){super(),this.popup=e,Object.setPrototypeOf(this,y.prototype)}}class v extends f{constructor(e){super("cancelled","Popup closed"),this.popup=e,Object.setPrototypeOf(this,v.prototype)}}class b extends f{constructor(e,t,n){super(e,t),this.mfa_token=n,Object.setPrototypeOf(this,b.prototype)}}class k extends f{constructor(e,t){super("missing_refresh_token",`Missing Refresh Token (audience: '${_(e,["default"])}', scope: '${_(t)}')`),this.audience=e,this.scope=t,Object.setPrototypeOf(this,k.prototype)}}function _(e,t=[]){return e&&!t.includes(e)?e:""}const I=()=>window.crypto,S=()=>{const e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.";let t="";return Array.from(I().getRandomValues(new Uint8Array(43))).forEach((n=>t+=e[n%66])),t},O=e=>btoa(e),T=e=>{var{clientId:t}=e,n=a(e,["clientId"]);return new URLSearchParams((e=>Object.keys(e).filter((t=>void 0!==e[t])).reduce(((t,n)=>Object.assign(Object.assign({},t),{[n]:e[n]})),{}))(Object.assign({client_id:t},n))).toString()},P=e=>(e=>decodeURIComponent(atob(e).split("").map((e=>"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2))).join("")))(e.replace(/_/g,"/").replace(/-/g,"+")),C=async(e,t)=>{const n=await fetch(e,t);return{ok:n.ok,json:await n.json()}},E=async(e,t,n,i,o,r,a=1e4)=>o?(async(e,t,n,i,o,r,a)=>{return s={auth:{audience:t,scope:n},timeout:o,fetchUrl:e,fetchOptions:i,useFormData:a},c=r,new Promise((function(e,t){const n=new MessageChannel;n.port1.onmessage=function(i){i.data.error?t(new Error(i.data.error)):e(i.data),n.port1.close()},c.postMessage(s,[n.port2])}));var s,c})(e,t,n,i,a,o,r):(async(e,t,n)=>{const i=new AbortController;let o;return t.signal=i.signal,Promise.race([C(e,t),new Promise(((e,t)=>{o=setTimeout((()=>{i.abort(),t(new Error("Timeout when executing 'fetch'"))}),n)}))]).finally((()=>{clearTimeout(o)}))})(e,i,a);async function j(e,t){var{baseUrl:n,timeout:i,audience:o,scope:r,auth0Client:s,useFormData:c}=e,u=a(e,["baseUrl","timeout","audience","scope","auth0Client","useFormData"]);const l=c?T(u):JSON.stringify(u);return await async function(e,t,n,i,o,r,s){let c,u=null;for(let a=0;a<3;a++)try{c=await E(e,n,i,o,r,s,t),u=null;break}catch(e){u=e}if(u)throw u;const l=c.json,{error:d,error_description:h}=l,p=a(l,["error","error_description"]),{ok:m}=c;if(!m){const t=h||`HTTP error. Unable to fetch ${e}`;if("mfa_required"===d)throw new b(d,t,p.mfa_token);if("missing_refresh_token"===d)throw new k(n,i);throw new f(d||"request_error",t)}return p}(`${n}/oauth/token`,i,o||"default",r,{method:"POST",body:l,headers:{"Content-Type":c?"application/x-www-form-urlencoded":"application/json","Auth0-Client":btoa(JSON.stringify(s||p))}},t,c)}const z=(...e)=>{return(t=e.filter(Boolean).join(" ").trim().split(/\s+/),Array.from(new Set(t))).join(" ");var t};class x{constructor(e,t="@@auth0spajs@@",n){this.prefix=t,this.suffix=n,this.clientId=e.clientId,this.scope=e.scope,this.audience=e.audience}toKey(){return[this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join("::")}static fromKey(e){const[t,n,i,o]=e.split("::");return new x({clientId:n,scope:o,audience:i},t)}static fromCacheEntry(e){const{scope:t,audience:n,client_id:i}=e;return new x({scope:t,audience:n,clientId:i})}}class R{set(e,t){localStorage.setItem(e,JSON.stringify(t))}get(e){const t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return}}remove(e){localStorage.removeItem(e)}allKeys(){return Object.keys(window.localStorage).filter((e=>e.startsWith("@@auth0spajs@@")))}}class U{constructor(){this.enclosedCache=function(){let e={};return{set(t,n){e[t]=n},get(t){const n=e[t];if(n)return n},remove(t){delete e[t]},allKeys:()=>Object.keys(e)}}()}}class Z{constructor(e,t,n){this.cache=e,this.keyManifest=t,this.nowProvider=n||m}async setIdToken(e,t,n){var i;const o=this.getIdTokenCacheKey(e);await this.cache.set(o,{id_token:t,decodedToken:n}),await(null===(i=this.keyManifest)||void 0===i?void 0:i.add(o))}async getIdToken(e){const t=await this.cache.get(this.getIdTokenCacheKey(e.clientId));if(!t&&e.scope&&e.audience){const t=await this.get(e);if(!t)return;if(!t.id_token||!t.decodedToken)return;return{id_token:t.id_token,decodedToken:t.decodedToken}}if(t)return{id_token:t.id_token,decodedToken:t.decodedToken}}async get(e,t=0){var n;let i=await this.cache.get(e.toKey());if(!i){const t=await this.getCacheKeys();if(!t)return;const n=this.matchExistingCacheKey(e,t);n&&(i=await this.cache.get(n))}if(!i)return;const o=await this.nowProvider(),r=Math.floor(o/1e3);return i.expiresAt-t<r?i.body.refresh_token?(i.body={refresh_token:i.body.refresh_token},await this.cache.set(e.toKey(),i),i.body):(await this.cache.remove(e.toKey()),void await(null===(n=this.keyManifest)||void 0===n?void 0:n.remove(e.toKey()))):i.body}async set(e){var t;const n=new x({clientId:e.client_id,scope:e.scope,audience:e.audience}),i=await this.wrapCacheEntry(e);await this.cache.set(n.toKey(),i),await(null===(t=this.keyManifest)||void 0===t?void 0:t.add(n.toKey()))}async clear(e){var t;const n=await this.getCacheKeys();n&&(await n.filter((t=>!e||t.includes(e))).reduce((async(e,t)=>{await e,await this.cache.remove(t)}),Promise.resolve()),await(null===(t=this.keyManifest)||void 0===t?void 0:t.clear()))}async wrapCacheEntry(e){const t=await this.nowProvider();return{body:e,expiresAt:Math.floor(t/1e3)+e.expires_in}}async getCacheKeys(){var e;return this.keyManifest?null===(e=await this.keyManifest.get())||void 0===e?void 0:e.keys:this.cache.allKeys?this.cache.allKeys():void 0}getIdTokenCacheKey(e){return new x({clientId:e},"@@auth0spajs@@","@@user@@").toKey()}matchExistingCacheKey(e,t){return t.filter((t=>{var n;const i=x.fromKey(t),o=new Set(i.scope&&i.scope.split(" ")),r=(null===(n=e.scope)||void 0===n?void 0:n.split(" "))||[],a=i.scope&&r.reduce(((e,t)=>e&&o.has(t)),!0);return"@@auth0spajs@@"===i.prefix&&i.clientId===e.clientId&&i.audience===e.audience&&a}))[0]}}class L{constructor(e,t,n){this.storage=e,this.clientId=t,this.cookieDomain=n,this.storageKey=`a0.spajs.txs.${this.clientId}`}create(e){this.storage.save(this.storageKey,e,{daysUntilExpire:1,cookieDomain:this.cookieDomain})}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain})}}const W=e=>"number"==typeof e,K=["iss","aud","exp","nbf","iat","jti","azp","nonce","auth_time","at_hash","c_hash","acr","amr","sub_jwk","cnf","sip_from_tag","sip_date","sip_callid","sip_cseq_num","sip_via_branch","orig","dest","mky","events","toe","txn","rph","sid","vot","vtm"];var D=u((function(e,t){var n=s&&s.__assign||function(){return n=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};function i(e,t){if(!t)return"";var n="; "+e;return!0===t?n:n+"="+t}function o(e,t,n){return encodeURIComponent(e).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\(/g,"%28").replace(/\)/g,"%29")+"="+encodeURIComponent(t).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(e){if("number"==typeof e.expires){var t=new Date;t.setMilliseconds(t.getMilliseconds()+864e5*e.expires),e.expires=t}return i("Expires",e.expires?e.expires.toUTCString():"")+i("Domain",e.domain)+i("Path",e.path)+i("Secure",e.secure)+i("SameSite",e.sameSite)}(n)}function r(e){for(var t={},n=e?e.split("; "):[],i=/(%[\dA-F]{2})+/gi,o=0;o<n.length;o++){var r=n[o].split("="),a=r.slice(1).join("=");'"'===a.charAt(0)&&(a=a.slice(1,-1));try{t[r[0].replace(i,decodeURIComponent)]=a.replace(i,decodeURIComponent)}catch(e){}}return t}function a(){return r(document.cookie)}function c(e,t,i){document.cookie=o(e,t,n({path:"/"},i))}t.__esModule=!0,t.encode=o,t.parse=r,t.getAll=a,t.get=function(e){return a()[e]},t.set=c,t.remove=function(e,t){c(e,"",n(n({},t),{expires:-1}))}}));c(D),D.encode,D.parse,D.getAll;var N=D.get,A=D.set,X=D.remove;const G={get(e){const t=N(e);if(void 0!==t)return JSON.parse(t)},save(e,t,n){let i={};"https:"===window.location.protocol&&(i={secure:!0,sameSite:"none"}),(null==n?void 0:n.daysUntilExpire)&&(i.expires=n.daysUntilExpire),(null==n?void 0:n.cookieDomain)&&(i.domain=n.cookieDomain),A(e,JSON.stringify(t),i)},remove(e,t){let n={};(null==t?void 0:t.cookieDomain)&&(n.domain=t.cookieDomain),X(e,n)}},J={get:e=>G.get(e)||G.get(`_legacy_${e}`),save(e,t,n){let i={};"https:"===window.location.protocol&&(i={secure:!0}),(null==n?void 0:n.daysUntilExpire)&&(i.expires=n.daysUntilExpire),(null==n?void 0:n.cookieDomain)&&(i.domain=n.cookieDomain),A(`_legacy_${e}`,JSON.stringify(t),i),G.save(e,t,n)},remove(e,t){let n={};(null==t?void 0:t.cookieDomain)&&(n.domain=t.cookieDomain),X(e,n),G.remove(e,t),G.remove(`_legacy_${e}`,t)}},M={get(e){if("undefined"==typeof sessionStorage)return;const t=sessionStorage.getItem(e);return null!=t?JSON.parse(t):void 0},save(e,t){sessionStorage.setItem(e,JSON.stringify(t))},remove(e){sessionStorage.removeItem(e)}};var F,H=function(e){return F=F||function(e,t,n){var i=void 0===t?null:t,o=function(e,t){var n=atob(e);if(t){for(var i=new Uint8Array(n.length),o=0,r=n.length;o<r;++o)i[o]=n.charCodeAt(o);return String.fromCharCode.apply(null,new Uint16Array(i.buffer))}return n}(e,void 0!==n&&n),r=o.indexOf("\n",10)+1,a=o.substring(r)+(i?"//# sourceMappingURL="+i:""),s=new Blob([a],{type:"application/javascript"});return URL.createObjectURL(s)}("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",null,false),new Worker(F,e)};const Y={};class V{constructor(e,t){this.cache=e,this.clientId=t,this.manifestKey=this.createManifestKeyFrom(this.clientId)}async add(e){var t;const n=new Set((null===(t=await this.cache.get(this.manifestKey))||void 0===t?void 0:t.keys)||[]);n.add(e),await this.cache.set(this.manifestKey,{keys:[...n]})}async remove(e){const t=await this.cache.get(this.manifestKey);if(t){const n=new Set(t.keys);return n.delete(e),n.size>0?await this.cache.set(this.manifestKey,{keys:[...n]}):await this.cache.remove(this.manifestKey)}}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(e){return`@@auth0spajs@@::${e}`}}const B={memory:()=>(new U).enclosedCache,localstorage:()=>new R},$=e=>B[e],q=e=>{const{openUrl:t,onRedirect:n}=e,i=a(e,["openUrl","onRedirect"]);return Object.assign(Object.assign({},i),{openUrl:!1===t||t?t:n})},Q=new d;class ee{constructor(e){let t,n;if(this.userCache=(new U).enclosedCache,this.defaultOptions={authorizationParams:{scope:"openid profile email"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=async()=>{await Q.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)},this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),e),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),e.authorizationParams)}),"undefined"!=typeof window&&(()=>{if(!I())throw new Error("For security reasons, `window.crypto` is required to run `auth0-spa-js`.");if(void 0===I().subtle)throw new Error("\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    ")})(),e.cache&&e.cacheLocation&&console.warn("Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`."),e.cache)n=e.cache;else{if(t=e.cacheLocation||"memory",!$(t))throw new Error(`Invalid cache location "${t}"`);n=$(t)()}this.httpTimeoutMs=e.httpTimeoutInSeconds?1e3*e.httpTimeoutInSeconds:1e4,this.cookieStorage=!1===e.legacySameSiteCookie?G:J,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(()=>`auth0.${this.options.clientId}.is.authenticated`)(),this.sessionCheckExpiryDays=e.sessionCheckExpiryDays||1;const i=e.useCookiesForTransactions?this.cookieStorage:M;var o;this.scope=z("openid",this.options.authorizationParams.scope,this.options.useRefreshTokens?"offline_access":""),this.transactionManager=new L(i,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||m,this.cacheManager=new Z(n,n.allKeys?void 0:new V(n,this.options.clientId),this.nowProvider),this.domainUrl=(o=this.options.domain,/^https?:\/\//.test(o)?o:`https://${o}`),this.tokenIssuer=((e,t)=>e?e.startsWith("https://")?e:`https://${e}/`:`${t}/`)(this.options.issuer,this.domainUrl),"undefined"!=typeof window&&window.Worker&&this.options.useRefreshTokens&&"memory"===t&&(this.options.workerUrl?this.worker=new Worker(this.options.workerUrl):this.worker=new H)}_url(e){const t=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||p)));return`${this.domainUrl}${e}&auth0Client=${t}`}_authorizeUrl(e){return this._url(`/authorize?${T(e)}`)}async _verifyIdToken(e,t,n){const i=await this.nowProvider();return(e=>{if(!e.id_token)throw new Error("ID token is required but missing");const t=(e=>{const t=e.split("."),[n,i,o]=t;if(3!==t.length||!n||!i||!o)throw new Error("ID token could not be decoded");const r=JSON.parse(P(i)),a={__raw:e},s={};return Object.keys(r).forEach((e=>{a[e]=r[e],K.includes(e)||(s[e]=r[e])})),{encoded:{header:n,payload:i,signature:o},header:JSON.parse(P(n)),claims:a,user:s}})(e.id_token);if(!t.claims.iss)throw new Error("Issuer (iss) claim must be a string present in the ID token");if(t.claims.iss!==e.iss)throw new Error(`Issuer (iss) claim mismatch in the ID token; expected "${e.iss}", found "${t.claims.iss}"`);if(!t.user.sub)throw new Error("Subject (sub) claim must be a string present in the ID token");if("RS256"!==t.header.alg)throw new Error(`Signature algorithm of "${t.header.alg}" is not supported. Expected the ID token to be signed with "RS256".`);if(!t.claims.aud||"string"!=typeof t.claims.aud&&!Array.isArray(t.claims.aud))throw new Error("Audience (aud) claim must be a string or array of strings present in the ID token");if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e.aud))throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${e.aud}" but was not one of "${t.claims.aud.join(", ")}"`);if(t.claims.aud.length>1){if(!t.claims.azp)throw new Error("Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values");if(t.claims.azp!==e.aud)throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected "${e.aud}", found "${t.claims.azp}"`)}}else if(t.claims.aud!==e.aud)throw new Error(`Audience (aud) claim mismatch in the ID token; expected "${e.aud}" but found "${t.claims.aud}"`);if(e.nonce){if(!t.claims.nonce)throw new Error("Nonce (nonce) claim must be a string present in the ID token");if(t.claims.nonce!==e.nonce)throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected "${e.nonce}", found "${t.claims.nonce}"`)}if(e.max_age&&!W(t.claims.auth_time))throw new Error("Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified");if(null==t.claims.exp||!W(t.claims.exp))throw new Error("Expiration Time (exp) claim must be a number present in the ID token");if(!W(t.claims.iat))throw new Error("Issued At (iat) claim must be a number present in the ID token");const n=e.leeway||60,i=new Date(e.now||Date.now()),o=new Date(0);if(o.setUTCSeconds(t.claims.exp+n),i>o)throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${i}) is after expiration time (${o})`);if(null!=t.claims.nbf&&W(t.claims.nbf)){const e=new Date(0);if(e.setUTCSeconds(t.claims.nbf-n),i<e)throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${i}) is before ${e}`)}if(null!=t.claims.auth_time&&W(t.claims.auth_time)){const o=new Date(0);if(o.setUTCSeconds(parseInt(t.claims.auth_time)+e.max_age+n),i>o)throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${i}) is after last auth at ${o}`)}if(e.organization){const n=e.organization.trim();if(n.startsWith("org_")){const e=n;if(!t.claims.org_id)throw new Error("Organization ID (org_id) claim must be a string present in the ID token");if(e!==t.claims.org_id)throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected "${e}", found "${t.claims.org_id}"`)}else{const e=n.toLowerCase();if(!t.claims.org_name)throw new Error("Organization Name (org_name) claim must be a string present in the ID token");if(e!==t.claims.org_name)throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected "${e}", found "${t.claims.org_name}"`)}}return t})({iss:this.tokenIssuer,aud:this.options.clientId,id_token:e,nonce:t,organization:n,leeway:this.options.leeway,max_age:(o=this.options.authorizationParams.max_age,"string"!=typeof o?o:parseInt(o,10)||void 0),now:i});var o}_processOrgHint(e){e?this.cookieStorage.save(this.orgHintCookieName,e,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain})}async _prepareAuthorizeUrl(e,t,n){const i=O(S()),o=O(S()),r=S(),a=(e=>{const t=new Uint8Array(e);return(e=>{const t={"+":"-","/":"_","=":""};return e.replace(/[+/=]/g,(e=>t[e]))})(window.btoa(String.fromCharCode(...Array.from(t))))})(await(async e=>{const t=I().subtle.digest({name:"SHA-256"},(new TextEncoder).encode(e));return await t})(r)),s=((e,t,n,i,o,r,a,s)=>Object.assign(Object.assign(Object.assign({client_id:e.clientId},e.authorizationParams),n),{scope:z(t,n.scope),response_type:"code",response_mode:s||"query",state:i,nonce:o,redirect_uri:a||e.authorizationParams.redirect_uri,code_challenge:r,code_challenge_method:"S256"}))(this.options,this.scope,e,i,o,a,e.redirect_uri||this.options.authorizationParams.redirect_uri||n,null==t?void 0:t.response_mode),c=this._authorizeUrl(s);return{nonce:o,code_verifier:r,scope:s.scope,audience:s.audience||"default",redirect_uri:s.redirect_uri,state:i,url:c}}async loginWithPopup(e,t){var n;if(e=e||{},!(t=t||{}).popup&&(t.popup=(()=>{const e=window.screenX+(window.innerWidth-400)/2,t=window.screenY+(window.innerHeight-600)/2;return window.open("","auth0:authorize:popup",`left=${e},top=${t},width=400,height=600,resizable,scrollbars=yes,status=1`)})(),!t.popup))throw new Error("Unable to open a popup for loginWithPopup - window.open returned `null`");const i=await this._prepareAuthorizeUrl(e.authorizationParams||{},{response_mode:"web_message"},window.location.origin);t.popup.location.href=i.url;const o=await(e=>new Promise(((t,n)=>{let i;const o=setInterval((()=>{e.popup&&e.popup.closed&&(clearInterval(o),clearTimeout(r),window.removeEventListener("message",i,!1),n(new v(e.popup)))}),1e3),r=setTimeout((()=>{clearInterval(o),n(new y(e.popup)),window.removeEventListener("message",i,!1)}),1e3*(e.timeoutInSeconds||60));i=function(a){if(a.data&&"authorization_response"===a.data.type){if(clearTimeout(r),clearInterval(o),window.removeEventListener("message",i,!1),e.popup.close(),a.data.response.error)return n(f.fromPayload(a.data.response));t(a.data.response)}},window.addEventListener("message",i)})))(Object.assign(Object.assign({},t),{timeoutInSeconds:t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}));if(i.state!==o.state)throw new f("state_mismatch","Invalid state");const r=(null===(n=e.authorizationParams)||void 0===n?void 0:n.organization)||this.options.authorizationParams.organization;await this._requestToken({audience:i.audience,scope:i.scope,code_verifier:i.code_verifier,grant_type:"authorization_code",code:o.code,redirect_uri:i.redirect_uri},{nonceIn:i.nonce,organization:r})}async getUser(){var e;const t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.user}async getIdTokenClaims(){var e;const t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.claims}async loginWithRedirect(e={}){var t;const n=q(e),{openUrl:i,fragment:o,appState:r}=n,s=a(n,["openUrl","fragment","appState"]),c=(null===(t=s.authorizationParams)||void 0===t?void 0:t.organization)||this.options.authorizationParams.organization,u=await this._prepareAuthorizeUrl(s.authorizationParams||{}),{url:l}=u,d=a(u,["url"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},d),{appState:r}),c&&{organization:c}));const h=o?`${l}#${o}`:l;i?await i(h):window.location.assign(h)}async handleRedirectCallback(e=window.location.href){const t=e.split("?").slice(1);if(0===t.length)throw new Error("There are no query params available for parsing.");const{state:n,code:i,error:o,error_description:r}=(e=>{e.indexOf("#")>-1&&(e=e.substring(0,e.indexOf("#")));const t=new URLSearchParams(e);return{state:t.get("state"),code:t.get("code")||void 0,error:t.get("error")||void 0,error_description:t.get("error_description")||void 0}})(t.join("")),a=this.transactionManager.get();if(!a)throw new f("missing_transaction","Invalid state");if(this.transactionManager.remove(),o)throw new g(o,r||o,n,a.appState);if(!a.code_verifier||a.state&&a.state!==n)throw new f("state_mismatch","Invalid state");const s=a.organization,c=a.nonce,u=a.redirect_uri;return await this._requestToken(Object.assign({audience:a.audience,scope:a.scope,code_verifier:a.code_verifier,grant_type:"authorization_code",code:i},u?{redirect_uri:u}:{}),{nonceIn:c,organization:s}),{appState:a.appState}}async checkSession(e){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get("auth0.is.authenticated"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove("auth0.is.authenticated")}try{await this.getTokenSilently(e)}catch(e){}}async getTokenSilently(e={}){var t;const n=Object.assign(Object.assign({cacheMode:"on"},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:z(this.scope,null===(t=e.authorizationParams)||void 0===t?void 0:t.scope)})}),i=await((e,t)=>{let n=Y[t];return n||(n=e().finally((()=>{delete Y[t],n=null})),Y[t]=n),n})((()=>this._getTokenSilently(n)),`${this.options.clientId}::${n.authorizationParams.audience}::${n.authorizationParams.scope}`);return e.detailedResponse?i:null==i?void 0:i.access_token}async _getTokenSilently(e){const{cacheMode:t}=e,n=a(e,["cacheMode"]);if("off"!==t){const e=await this._getEntryFromCache({scope:n.authorizationParams.scope,audience:n.authorizationParams.audience||"default",clientId:this.options.clientId});if(e)return e}if("cache-only"!==t){if(!await(async(e,t=3)=>{for(let n=0;n<t;n++)if(await e())return!0;return!1})((()=>Q.acquireLock("auth0.lock.getTokenSilently",5e3)),10))throw new w;try{if(window.addEventListener("pagehide",this._releaseLockOnPageHide),"off"!==t){const e=await this._getEntryFromCache({scope:n.authorizationParams.scope,audience:n.authorizationParams.audience||"default",clientId:this.options.clientId});if(e)return e}const e=this.options.useRefreshTokens?await this._getTokenUsingRefreshToken(n):await this._getTokenFromIFrame(n),{id_token:i,access_token:o,oauthTokenScope:r,expires_in:a}=e;return Object.assign(Object.assign({id_token:i,access_token:o},r?{scope:r}:null),{expires_in:a})}finally{await Q.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)}}}async getTokenWithPopup(e={},t={}){var n;const i=Object.assign(Object.assign({},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:z(this.scope,null===(n=e.authorizationParams)||void 0===n?void 0:n.scope)})});return t=Object.assign(Object.assign({},h),t),await this.loginWithPopup(i,t),(await this.cacheManager.get(new x({scope:i.authorizationParams.scope,audience:i.authorizationParams.audience||"default",clientId:this.options.clientId}))).access_token}async isAuthenticated(){return!!await this.getUser()}_buildLogoutUrl(e){null!==e.clientId?e.clientId=e.clientId||this.options.clientId:delete e.clientId;const t=e.logoutParams||{},{federated:n}=t,i=a(t,["federated"]),o=n?"&federated":"";return this._url(`/v2/logout?${T(Object.assign({clientId:e.clientId},i))}`)+o}async logout(e={}){const t=q(e),{openUrl:n}=t,i=a(t,["openUrl"]);null===e.clientId?await this.cacheManager.clear():await this.cacheManager.clear(e.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove("@@user@@");const o=this._buildLogoutUrl(i);n?await n(o):!1!==n&&window.location.assign(o)}async _getTokenFromIFrame(e){const t=Object.assign(Object.assign({},e.authorizationParams),{prompt:"none"}),n=this.cookieStorage.get(this.orgHintCookieName);n&&!t.organization&&(t.organization=n);const{url:i,state:o,nonce:r,code_verifier:a,redirect_uri:s,scope:c,audience:u}=await this._prepareAuthorizeUrl(t,{response_mode:"web_message"},window.location.origin);try{if(window.crossOriginIsolated)throw new f("login_required","The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.");const n=e.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,l=await((e,t,n=60)=>new Promise(((i,o)=>{const r=window.document.createElement("iframe");r.setAttribute("width","0"),r.setAttribute("height","0"),r.style.display="none";const a=()=>{window.document.body.contains(r)&&(window.document.body.removeChild(r),window.removeEventListener("message",s,!1))};let s;const c=setTimeout((()=>{o(new w),a()}),1e3*n);s=function(e){if(e.origin!=t)return;if(!e.data||"authorization_response"!==e.data.type)return;const n=e.source;n&&n.close(),e.data.response.error?o(f.fromPayload(e.data.response)):i(e.data.response),clearTimeout(c),window.removeEventListener("message",s,!1),setTimeout(a,2e3)},window.addEventListener("message",s,!1),window.document.body.appendChild(r),r.setAttribute("src",e)})))(i,this.domainUrl,n);if(o!==l.state)throw new f("state_mismatch","Invalid state");const d=await this._requestToken(Object.assign(Object.assign({},e.authorizationParams),{code_verifier:a,code:l.code,grant_type:"authorization_code",redirect_uri:s,timeout:e.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:r,organization:t.organization});return Object.assign(Object.assign({},d),{scope:c,oauthTokenScope:d.scope,audience:u})}catch(e){throw"login_required"===e.error&&this.logout({openUrl:!1}),e}}async _getTokenUsingRefreshToken(e){const t=await this.cacheManager.get(new x({scope:e.authorizationParams.scope,audience:e.authorizationParams.audience||"default",clientId:this.options.clientId}));if(!(t&&t.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw new k(e.authorizationParams.audience||"default",e.authorizationParams.scope)}const n=e.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,i="number"==typeof e.timeoutInSeconds?1e3*e.timeoutInSeconds:null;try{const o=await this._requestToken(Object.assign(Object.assign(Object.assign({},e.authorizationParams),{grant_type:"refresh_token",refresh_token:t&&t.refresh_token,redirect_uri:n}),i&&{timeout:i}));return Object.assign(Object.assign({},o),{scope:e.authorizationParams.scope,oauthTokenScope:o.scope,audience:e.authorizationParams.audience||"default"})}catch(t){if((t.message.indexOf("Missing Refresh Token")>-1||t.message&&t.message.indexOf("invalid refresh token")>-1)&&this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw t}}async _saveEntryInCache(e){const{id_token:t,decodedToken:n}=e,i=a(e,["id_token","decodedToken"]);this.userCache.set("@@user@@",{id_token:t,decodedToken:n}),await this.cacheManager.setIdToken(this.options.clientId,e.id_token,e.decodedToken),await this.cacheManager.set(i)}async _getIdTokenFromCache(){const e=this.options.authorizationParams.audience||"default",t=await this.cacheManager.getIdToken(new x({clientId:this.options.clientId,audience:e,scope:this.scope})),n=this.userCache.get("@@user@@");return t&&t.id_token===(null==n?void 0:n.id_token)?n:(this.userCache.set("@@user@@",t),t)}async _getEntryFromCache({scope:e,audience:t,clientId:n}){const i=await this.cacheManager.get(new x({scope:e,audience:t,clientId:n}),60);if(i&&i.access_token){const{access_token:e,oauthTokenScope:t,expires_in:n}=i,o=await this._getIdTokenFromCache();return o&&Object.assign(Object.assign({id_token:o.id_token,access_token:e},t?{scope:t}:null),{expires_in:n})}}async _requestToken(e,t){const{nonceIn:n,organization:i}=t||{},o=await j(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},e),this.worker),r=await this._verifyIdToken(o.id_token,n,i);return await this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},o),{decodedToken:r,scope:e.scope,audience:e.audience||"default"}),o.scope?{oauthTokenScope:o.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(i||r.claims.org_id),Object.assign(Object.assign({},o),{decodedToken:r})}}var te={isAuthenticated:!1,isLoading:!0},ne=function(){throw new Error("You forgot to wrap your component in <Auth0Provider>.")},ie=i(i({},te),{buildAuthorizeUrl:ne,buildLogoutUrl:ne,getAccessTokenSilently:ne,getAccessTokenWithPopup:ne,getIdTokenClaims:ne,loginWithRedirect:ne,loginWithPopup:ne,logout:ne,handleRedirectCallback:ne}),oe=t.createContext(ie),re=function(e){function t(n,i){var o=e.call(this,i||n)||this;return o.error=n,o.error_description=i,Object.setPrototypeOf(o,t.prototype),o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}(t,e),t}(Error),ae=/[?&]code=[^&]+/,se=/[?&]state=[^&]+/,ce=/[?&]error=[^&]+/,ue=function(e){return function(t){return t instanceof Error?t:null!==t&&"object"==typeof t&&"error"in t&&"string"==typeof t.error?"error_description"in t&&"string"==typeof t.error_description?new re(t.error,t.error_description):new re(t.error):new Error(e)}},le=ue("Login failed"),de=ue("Get access token failed"),he=function(e){var t;(null==e?void 0:e.redirectUri)&&(console.warn("Using `redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version"),e.authorizationParams=e.authorizationParams||{},e.authorizationParams.redirect_uri=e.redirectUri,delete e.redirectUri),(null===(t=null==e?void 0:e.authorizationParams)||void 0===t?void 0:t.redirectUri)&&(console.warn("Using `authorizationParams.redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `authorizationParams.redirectUri` will be removed in a future version"),e.authorizationParams.redirect_uri=e.authorizationParams.redirectUri,delete e.authorizationParams.redirectUri)},pe=function(e,t){switch(t.type){case"LOGIN_POPUP_STARTED":return i(i({},e),{isLoading:!0});case"LOGIN_POPUP_COMPLETE":case"INITIALISED":return i(i({},e),{isAuthenticated:!!t.user,user:t.user,isLoading:!1,error:void 0});case"HANDLE_REDIRECT_COMPLETE":case"GET_ACCESS_TOKEN_COMPLETE":return e.user===t.user?e:i(i({},e),{isAuthenticated:!!t.user,user:t.user});case"LOGOUT":return i(i({},e),{isAuthenticated:!1,user:void 0});case"ERROR":return i(i({},e),{isLoading:!1,error:t.error})}},me=function(e){window.history.replaceState({},document.title,(null==e?void 0:e.returnTo)||window.location.pathname)},fe=function(e){return void 0===e&&(e=oe),t.useContext(e)},ge=function(){return t.createElement(t.Fragment,null)},we=function(){return o(void 0,void 0,void 0,(function(){return r(this,(function(e){return[2]}))}))},ye=function(){return"".concat(window.location.pathname).concat(window.location.search)};e.Auth0Context=oe,e.Auth0Provider=function(e){var n=e.children,a=e.skipRedirectCallback,s=e.onRedirectCallback,c=void 0===s?me:s,u=e.context,l=void 0===u?oe:u,d=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(i=Object.getOwnPropertySymbols(e);o<i.length;o++)t.indexOf(i[o])<0&&Object.prototype.propertyIsEnumerable.call(e,i[o])&&(n[i[o]]=e[i[o]])}return n}(e,["children","skipRedirectCallback","onRedirectCallback","context"]),h=t.useState((function(){return new ee(function(e){return he(e),i(i({},e),{auth0Client:{name:"auth0-react",version:"2.3.0"}})}(d))}))[0],p=t.useReducer(pe,te),m=p[0],f=p[1],g=t.useRef(!1),w=t.useCallback((function(e){return f({type:"ERROR",error:e}),e}),[]);t.useEffect((function(){g.current||(g.current=!0,o(void 0,void 0,void 0,(function(){var e,t,n;return r(this,(function(i){switch(i.label){case 0:return i.trys.push([0,7,,8]),e=void 0,void 0===o&&(o=window.location.search),!ae.test(o)&&!ce.test(o)||!se.test(o)||a?[3,3]:[4,h.handleRedirectCallback()];case 1:return t=i.sent().appState,[4,h.getUser()];case 2:return e=i.sent(),c(t,e),[3,6];case 3:return[4,h.checkSession()];case 4:return i.sent(),[4,h.getUser()];case 5:e=i.sent(),i.label=6;case 6:return f({type:"INITIALISED",user:e}),[3,8];case 7:return n=i.sent(),w(le(n)),[3,8];case 8:return[2]}var o}))})))}),[h,c,a,w]);var y=t.useCallback((function(e){return he(e),h.loginWithRedirect(e)}),[h]),v=t.useCallback((function(e,t){return o(void 0,void 0,void 0,(function(){var n,i;return r(this,(function(o){switch(o.label){case 0:f({type:"LOGIN_POPUP_STARTED"}),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,h.loginWithPopup(e,t)];case 2:return o.sent(),[3,4];case 3:return n=o.sent(),w(le(n)),[2];case 4:return[4,h.getUser()];case 5:return i=o.sent(),f({type:"LOGIN_POPUP_COMPLETE",user:i}),[2]}}))}))}),[h]),b=t.useCallback((function(e){return void 0===e&&(e={}),o(void 0,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return[4,h.logout(e)];case 1:return t.sent(),(e.openUrl||!1===e.openUrl)&&f({type:"LOGOUT"}),[2]}}))}))}),[h]),k=t.useCallback((function(e){return o(void 0,void 0,void 0,(function(){var t,n,i,o;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,3,5]),[4,h.getTokenSilently(e)];case 1:return t=r.sent(),[3,5];case 2:throw n=r.sent(),de(n);case 3:return i=f,o={type:"GET_ACCESS_TOKEN_COMPLETE"},[4,h.getUser()];case 4:return i.apply(void 0,[(o.user=r.sent(),o)]),[7];case 5:return[2,t]}}))}))}),[h]),_=t.useCallback((function(e,t){return o(void 0,void 0,void 0,(function(){var n,i,o,a;return r(this,(function(r){switch(r.label){case 0:return r.trys.push([0,2,3,5]),[4,h.getTokenWithPopup(e,t)];case 1:return n=r.sent(),[3,5];case 2:throw i=r.sent(),de(i);case 3:return o=f,a={type:"GET_ACCESS_TOKEN_COMPLETE"},[4,h.getUser()];case 4:return o.apply(void 0,[(a.user=r.sent(),a)]),[7];case 5:return[2,n]}}))}))}),[h]),I=t.useCallback((function(){return h.getIdTokenClaims()}),[h]),S=t.useCallback((function(e){return o(void 0,void 0,void 0,(function(){var t,n,i;return r(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,3,5]),[4,h.handleRedirectCallback(e)];case 1:return[2,o.sent()];case 2:throw t=o.sent(),de(t);case 3:return n=f,i={type:"HANDLE_REDIRECT_COMPLETE"},[4,h.getUser()];case 4:return n.apply(void 0,[(i.user=o.sent(),i)]),[7];case 5:return[2]}}))}))}),[h]),O=t.useMemo((function(){return i(i({},m),{getAccessTokenSilently:k,getAccessTokenWithPopup:_,getIdTokenClaims:I,loginWithRedirect:y,loginWithPopup:v,logout:b,handleRedirectCallback:S})}),[m,k,_,I,y,v,b,S]);return t.createElement(l.Provider,{value:O},n)},e.AuthenticationError=g,e.GenericError=f,e.InMemoryCache=U,e.LocalStorageCache=R,e.MfaRequiredError=b,e.MissingRefreshTokenError=k,e.OAuthError=re,e.PopupCancelledError=v,e.PopupTimeoutError=y,e.TimeoutError=w,e.User=class{},e.initialContext=ie,e.useAuth0=fe,e.withAuth0=function(e,n){return void 0===n&&(n=oe),function(o){return t.createElement(n.Consumer,null,(function(n){return t.createElement(e,i({},o,{auth0:n}))}))}},e.withAuthenticationRequired=function(e,n){return void 0===n&&(n={}),function(a){var s=this,c=n.returnTo,u=void 0===c?ye:c,l=n.onRedirecting,d=void 0===l?ge:l,h=n.onBeforeAuthentication,p=void 0===h?we:h,m=n.loginOptions,f=n.context,g=fe(void 0===f?oe:f),w=g.isAuthenticated,y=g.isLoading,v=g.loginWithRedirect;return t.useEffect((function(){if(!y&&!w){var e=i(i({},m),{appState:i(i({},m&&m.appState),{returnTo:"function"==typeof u?u():u})});o(s,void 0,void 0,(function(){return r(this,(function(t){switch(t.label){case 0:return[4,p()];case 1:return t.sent(),[4,v(e)];case 2:return t.sent(),[2]}}))}))}}),[y,w,v,p,m,u]),w?t.createElement(e,i({},a)):d()}}}));
//# sourceMappingURL=auth0-react.min.js.map
