/**
 * Sidebar de navigation pour Nouri
 * Menu principal avec navigation et raccourcis
 */

import React from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Toolbar,
  Typography,
  Divider,
  Avatar,
  Chip,
  Tooltip,
  IconButton
} from '@mui/material'
import {
  Dashboard as DashboardIcon,
  AccountBalance as BankIcon,
  Receipt as TransactionsIcon,
  AccountBalanceWallet as BudgetIcon,
  Savings as SavingsIcon,
  Chat as ChatIcon,
  School as EducationIcon,
  Assessment as ReportsIcon,
  Settings as SettingsIcon,
  Help as HelpIcon,
  ChevronLeft as CollapseIcon,
  ChevronRight as ExpandIcon
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'

// Hooks et stores
import { useUser } from '@/store/useAppStore'
import { useThemeStore } from '@/store/useThemeStore'
import { ROUTES, config } from '@/config'

// Types
interface NavigationItem {
  id: string
  label: string
  icon: React.ReactNode
  path: string
  badge?: string | number
  disabled?: boolean
  divider?: boolean
}

export const Sidebar: React.FC = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const location = useLocation()
  
  // Store
  const user = useUser()
  const { sidebarCollapsed, toggleSidebarCollapsed } = useThemeStore()

  // Navigation items
  const navigationItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: t('navigation.dashboard'),
      icon: <DashboardIcon />,
      path: ROUTES.DASHBOARD
    },
    {
      id: 'transactions',
      label: t('navigation.transactions'),
      icon: <TransactionsIcon />,
      path: ROUTES.TRANSACTIONS
    },
    {
      id: 'budgets',
      label: t('navigation.budgets'),
      icon: <BudgetIcon />,
      path: ROUTES.BUDGETS
    },
    {
      id: 'savings',
      label: t('navigation.savings'),
      icon: <SavingsIcon />,
      path: ROUTES.SAVINGS
    },
    {
      id: 'divider1',
      label: '',
      icon: null,
      path: '',
      divider: true
    },
    {
      id: 'chatbot',
      label: t('navigation.chatbot'),
      icon: <ChatIcon />,
      path: ROUTES.CHATBOT,
      badge: 'IA'
    },
    {
      id: 'education',
      label: t('navigation.education'),
      icon: <EducationIcon />,
      path: ROUTES.EDUCATION,
      badge: config.features.enableEducation ? undefined : 'Bientôt'
    },
    {
      id: 'reports',
      label: t('navigation.reports'),
      icon: <ReportsIcon />,
      path: ROUTES.REPORTS
    },
    {
      id: 'divider2',
      label: '',
      icon: null,
      path: '',
      divider: true
    },
    {
      id: 'settings',
      label: t('navigation.settings'),
      icon: <SettingsIcon />,
      path: ROUTES.SETTINGS
    },
    {
      id: 'help',
      label: t('navigation.help'),
      icon: <HelpIcon />,
      path: ROUTES.HELP
    }
  ]

  // Vérifier si un item est actif
  const isActive = (path: string): boolean => {
    return location.pathname === path
  }

  // Gérer la navigation
  const handleNavigation = (path: string) => {
    if (path) {
      navigate(path)
    }
  }

  // Rendu d'un item de navigation
  const renderNavigationItem = (item: NavigationItem) => {
    if (item.divider) {
      return <Divider key={item.id} sx={{ my: 1 }} />
    }

    const active = isActive(item.path)
    
    const listItem = (
      <ListItem key={item.id} disablePadding>
        <ListItemButton
          selected={active}
          onClick={() => handleNavigation(item.path)}
          disabled={item.disabled}
          sx={{
            minHeight: 48,
            justifyContent: sidebarCollapsed ? 'center' : 'initial',
            px: 2.5,
            borderRadius: 1,
            mx: 1,
            mb: 0.5,
            '&.Mui-selected': {
              backgroundColor: 'primary.main',
              color: 'primary.contrastText',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
              '& .MuiListItemIcon-root': {
                color: 'primary.contrastText',
              }
            }
          }}
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              mr: sidebarCollapsed ? 'auto' : 3,
              justifyContent: 'center',
            }}
          >
            {item.icon}
          </ListItemIcon>
          
          {!sidebarCollapsed && (
            <>
              <ListItemText 
                primary={item.label}
                sx={{ opacity: 1 }}
              />
              {item.badge && (
                <Chip
                  label={item.badge}
                  size="small"
                  color={typeof item.badge === 'string' ? 'secondary' : 'primary'}
                  sx={{ ml: 1, height: 20, fontSize: '0.75rem' }}
                />
              )}
            </>
          )}
        </ListItemButton>
      </ListItem>
    )

    // Ajouter un tooltip si la sidebar est réduite
    if (sidebarCollapsed) {
      return (
        <Tooltip key={item.id} title={item.label} placement="right">
          {listItem}
        </Tooltip>
      )
    }

    return listItem
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header de la sidebar */}
      <Toolbar
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: sidebarCollapsed ? 'center' : 'space-between',
          px: 2,
          minHeight: config.ui.headerHeight
        }}
      >
        {!sidebarCollapsed && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar
              sx={{
                width: 32,
                height: 32,
                bgcolor: 'primary.main',
                fontSize: '1rem',
                fontWeight: 'bold'
              }}
            >
              N
            </Avatar>
            <Typography variant="h6" component="div" sx={{ fontWeight: 'bold' }}>
              Nouri
            </Typography>
          </Box>
        )}
        
        <IconButton
          onClick={toggleSidebarCollapsed}
          size="small"
          sx={{
            color: 'text.secondary',
            '&:hover': {
              backgroundColor: 'action.hover'
            }
          }}
        >
          {sidebarCollapsed ? <ExpandIcon /> : <CollapseIcon />}
        </IconButton>
      </Toolbar>

      <Divider />

      {/* Profil utilisateur */}
      {!sidebarCollapsed && user && (
        <Box sx={{ p: 2 }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              p: 2,
              borderRadius: 2,
              backgroundColor: 'background.paper',
              border: '1px solid',
              borderColor: 'divider'
            }}
          >
            <Avatar
              src={user.profilePicture}
              sx={{ width: 40, height: 40 }}
            >
              {user.firstName?.[0]}{user.lastName?.[0]}
            </Avatar>
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Typography variant="subtitle2" noWrap>
                {user.firstName} {user.lastName}
              </Typography>
              <Typography variant="caption" color="text.secondary" noWrap>
                {user.email}
              </Typography>
            </Box>
          </Box>
        </Box>
      )}

      {/* Navigation */}
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <List sx={{ px: 1 }}>
          {navigationItems.map(renderNavigationItem)}
        </List>
      </Box>

      {/* Footer de la sidebar */}
      {!sidebarCollapsed && (
        <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
          <Typography variant="caption" color="text.secondary" align="center" display="block">
            Nouri v{config.app.version}
          </Typography>
          <Typography variant="caption" color="text.secondary" align="center" display="block">
            © 2024 Banque Tunisienne
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default Sidebar
