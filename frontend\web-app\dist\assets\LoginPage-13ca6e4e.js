import{j as e,d as n,B as o,b as r,f as t}from"./mui-vendor-9c785b1c.js";import{W as i}from"./index.module-a126d235.js";import{R as c}from"./index-69cba9ec.js";import{u as s}from"./react-vendor-ef8b78f8.js";const a=()=>{const a=s();return e.jsxs(e.Fragment,{children:[e.jsxs(i,{children:[e.jsx("title",{children:"Connexion - Nouri"}),e.jsx("meta",{name:"description",content:"Connectez-vous à votre compte Nouri"})]}),e.jsx(n,{maxWidth:"sm",children:e.jsxs(o,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",gap:3},children:[e.jsx(r,{variant:"h3",component:"h1",sx:{color:"primary.main",fontWeight:"bold"},children:"🇹🇳 Nouri"}),e.jsx(r,{variant:"h4",gutterBottom:!0,children:"Connexion"}),e.jsx(r,{variant:"body1",color:"text.secondary",sx:{mb:4},children:"Connectez-vous à votre compte pour accéder à votre coach financier personnel."}),e.jsx(t,{variant:"contained",size:"large",sx:{px:4,py:1.5,mb:2},onClick:()=>a(c.DASHBOARD),children:"Se connecter (Demo)"}),e.jsx(t,{variant:"text",onClick:()=>a(c.REGISTER),children:"Pas encore de compte ? S'inscrire"}),e.jsx(t,{variant:"text",onClick:()=>a(c.HOME),children:"← Retour à l'accueil"})]})})]})};export{a as LoginPage,a as default};
