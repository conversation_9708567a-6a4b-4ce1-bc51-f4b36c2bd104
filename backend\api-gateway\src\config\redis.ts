/**
 * Configuration et connexion à Redis
 * Utilisé pour le cache, les sessions et les données temporaires
 */

import { createClient, RedisClientType } from 'redis';
import { config } from './config';
import { logger } from '../utils/logger';

// Instance globale Redis
let redisClient: RedisClientType;

/**
 * Initialise la connexion à Redis
 */
export const connectRedis = async (): Promise<RedisClientType> => {
  try {
    if (!redisClient) {
      redisClient = createClient({
        url: config.redis.url,
        socket: {
          connectTimeout: 10000,
          lazyConnect: true,
          reconnectStrategy: (retries) => {
            if (retries > 10) {
              logger.error('❌ Trop de tentatives de reconnexion à Redis');
              return new Error('Trop de tentatives de reconnexion');
            }
            return Math.min(retries * 100, 3000);
          }
        }
      });

      // Gestion des événements Redis
      redisClient.on('connect', () => {
        logger.info('🔄 Connexion à Redis en cours...');
      });

      redisClient.on('ready', () => {
        logger.info('✅ Redis prêt et connecté', {
          host: config.redis.host,
          port: config.redis.port
        });
      });

      redisClient.on('error', (error) => {
        logger.error('❌ Erreur Redis:', error);
      });

      redisClient.on('end', () => {
        logger.warn('🔌 Connexion Redis fermée');
      });

      redisClient.on('reconnecting', () => {
        logger.info('🔄 Reconnexion à Redis...');
      });

      // Connexion
      await redisClient.connect();
      
      // Test de connexion
      await redisClient.ping();
      
      logger.info('✅ Connexion à Redis établie avec succès');
    }

    return redisClient;
  } catch (error) {
    logger.error('❌ Erreur lors de la connexion à Redis:', error);
    throw error;
  }
};

/**
 * Ferme la connexion à Redis
 */
export const disconnectRedis = async (): Promise<void> => {
  try {
    if (redisClient && redisClient.isOpen) {
      await redisClient.quit();
      logger.info('🔌 Connexion à Redis fermée');
    }
  } catch (error) {
    logger.error('Erreur lors de la fermeture de la connexion Redis:', error);
    throw error;
  }
};

/**
 * Vérifie la santé de Redis
 */
export const checkRedisHealth = async (): Promise<boolean> => {
  try {
    if (!redisClient || !redisClient.isOpen) {
      return false;
    }

    const response = await redisClient.ping();
    return response === 'PONG';
  } catch (error) {
    logger.error('Vérification de santé Redis échouée:', error);
    return false;
  }
};

/**
 * Obtient les statistiques de Redis
 */
export const getRedisStats = async () => {
  try {
    if (!redisClient || !redisClient.isOpen) {
      throw new Error('Redis non connecté');
    }

    const info = await redisClient.info();
    const dbSize = await redisClient.dbSize();
    const memory = await redisClient.info('memory');
    
    // Parser les informations
    const parseInfo = (infoString: string) => {
      const lines = infoString.split('\r\n');
      const result: any = {};
      
      lines.forEach(line => {
        if (line && !line.startsWith('#')) {
          const [key, value] = line.split(':');
          if (key && value) {
            result[key] = isNaN(Number(value)) ? value : Number(value);
          }
        }
      });
      
      return result;
    };

    const memoryInfo = parseInfo(memory);
    
    return {
      connected: true,
      dbSize,
      usedMemory: memoryInfo.used_memory_human,
      usedMemoryPeak: memoryInfo.used_memory_peak_human,
      connectedClients: memoryInfo.connected_clients,
      totalCommandsProcessed: memoryInfo.total_commands_processed,
      uptime: memoryInfo.uptime_in_seconds,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Erreur lors de la récupération des statistiques Redis:', error);
    throw error;
  }
};

/**
 * Classe utilitaire pour les opérations de cache
 */
export class CacheManager {
  private static instance: CacheManager;
  
  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * Stocke une valeur dans le cache avec TTL
   */
  async set(key: string, value: any, ttlSeconds: number = 3600): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await redisClient.setEx(key, ttlSeconds, serializedValue);
    } catch (error) {
      logger.error('Erreur lors de la mise en cache:', error);
      throw error;
    }
  }

  /**
   * Récupère une valeur du cache
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await redisClient.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('Erreur lors de la récupération du cache:', error);
      return null;
    }
  }

  /**
   * Supprime une clé du cache
   */
  async delete(key: string): Promise<void> {
    try {
      await redisClient.del(key);
    } catch (error) {
      logger.error('Erreur lors de la suppression du cache:', error);
      throw error;
    }
  }

  /**
   * Supprime toutes les clés correspondant à un pattern
   */
  async deletePattern(pattern: string): Promise<void> {
    try {
      const keys = await redisClient.keys(pattern);
      if (keys.length > 0) {
        await redisClient.del(keys);
      }
    } catch (error) {
      logger.error('Erreur lors de la suppression par pattern:', error);
      throw error;
    }
  }

  /**
   * Vérifie si une clé existe
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await redisClient.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('Erreur lors de la vérification d\'existence:', error);
      return false;
    }
  }

  /**
   * Définit un TTL sur une clé existante
   */
  async expire(key: string, ttlSeconds: number): Promise<void> {
    try {
      await redisClient.expire(key, ttlSeconds);
    } catch (error) {
      logger.error('Erreur lors de la définition du TTL:', error);
      throw error;
    }
  }

  /**
   * Incrémente une valeur numérique
   */
  async increment(key: string, by: number = 1): Promise<number> {
    try {
      return await redisClient.incrBy(key, by);
    } catch (error) {
      logger.error('Erreur lors de l\'incrémentation:', error);
      throw error;
    }
  }

  /**
   * Ajoute un élément à une liste
   */
  async listPush(key: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      await redisClient.lPush(key, serializedValue);
    } catch (error) {
      logger.error('Erreur lors de l\'ajout à la liste:', error);
      throw error;
    }
  }

  /**
   * Récupère les éléments d'une liste
   */
  async listRange<T>(key: string, start: number = 0, end: number = -1): Promise<T[]> {
    try {
      const values = await redisClient.lRange(key, start, end);
      return values.map(value => JSON.parse(value));
    } catch (error) {
      logger.error('Erreur lors de la récupération de la liste:', error);
      return [];
    }
  }
}

/**
 * Nettoie les données expirées et optimise Redis
 */
export const cleanupRedis = async (): Promise<void> => {
  try {
    logger.info('🧹 Nettoyage Redis en cours...');

    // Supprimer les clés expirées manuellement (Redis le fait automatiquement mais on peut forcer)
    const patterns = [
      'session:*',
      'cache:*',
      'temp:*',
      'blacklist:*'
    ];

    for (const pattern of patterns) {
      const keys = await redisClient.keys(pattern);
      for (const key of keys) {
        const ttl = await redisClient.ttl(key);
        if (ttl === -1) { // Clé sans expiration
          await redisClient.expire(key, 86400); // 24h par défaut
        }
      }
    }

    logger.info('✅ Nettoyage Redis terminé');
  } catch (error) {
    logger.error('❌ Erreur lors du nettoyage Redis:', error);
    throw error;
  }
};

// Gestion de la fermeture propre
process.on('beforeExit', async () => {
  await disconnectRedis();
});

process.on('SIGINT', async () => {
  await disconnectRedis();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectRedis();
  process.exit(0);
});

// Export de l'instance Redis et du cache manager
export { redisClient };
export const cache = CacheManager.getInstance();
