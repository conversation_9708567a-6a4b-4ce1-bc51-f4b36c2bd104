/**
 * Complete Profile Page
 * User profile and account information
 */

import React, { useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  Avatar,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material'
import {
  Home as HomeIcon,
  Logout as LogoutIcon,
  Edit as EditIcon,
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  AccountBalance as AccountBalanceIcon,
  School as SchoolIcon,
  EmojiEvents as TrophyIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock user data
const userData = {
  firstName: 'Ahmed',
  lastName: 'Ben Ali',
  email: '<EMAIL>',
  phone: '+216 20 123 456',
  joinDate: '2023-06-15',
  location: '<PERSON><PERSON>, <PERSON><PERSON><PERSON>',
  financialScore: 85,
  level: 'Expert',
  achievements: [
    { id: 1, title: 'Premier budget créé', icon: '🎯', earned: true, date: '2023-06-20' },
    { id: 2, title: 'Objectif d\'épargne atteint', icon: '💰', earned: true, date: '2023-08-15' },
    { id: 3, title: '3 mois de budget respecté', icon: '📊', earned: true, date: '2023-09-30' },
    { id: 4, title: 'Formation terminée', icon: '🎓', earned: true, date: '2023-10-10' },
    { id: 5, title: 'Épargne de 10,000 TND', icon: '🏆', earned: false, progress: 65 },
    { id: 6, title: 'Maître de l\'investissement', icon: '📈', earned: false, progress: 20 }
  ],
  stats: {
    totalSavings: 30250,
    budgetsCreated: 12,
    goalsAchieved: 3,
    coursesCompleted: 4,
    daysActive: 195
  }
}

export const ProfilePage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'success'
    if (score >= 60) return 'warning'
    return 'error'
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'Expert': return 'success'
      case 'Avancé': return 'info'
      case 'Intermédiaire': return 'warning'
      default: return 'default'
    }
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Mon Profil
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Profile Header */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 3, mb: 3 }}>
              <Avatar sx={{ width: 100, height: 100, bgcolor: 'primary.main', fontSize: '2rem' }}>
                {userData.firstName[0]}{userData.lastName[0]}
              </Avatar>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="h4" fontWeight="bold">
                  {userData.firstName} {userData.lastName}
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 1 }}>
                  {userData.email}
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  📍 {userData.location} • Membre depuis {new Date(userData.joinDate).toLocaleDateString('fr-FR')}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Chip
                    label={`Niveau ${userData.level}`}
                    color={getLevelColor(userData.level) as any}
                    icon={<StarIcon />}
                  />
                  <Chip
                    label={`Score: ${userData.financialScore}/100`}
                    color={getScoreColor(userData.financialScore) as any}
                    icon={<TrendingUpIcon />}
                  />
                </Box>
              </Box>
              <Button
                variant="outlined"
                startIcon={<EditIcon />}
                onClick={() => navigate(ROUTES.SETTINGS)}
              >
                Modifier
              </Button>
            </Box>

            {/* Financial Score */}
            <Box>
              <Typography variant="h6" gutterBottom>
                Score Financier
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={userData.financialScore}
                  sx={{ flexGrow: 1, height: 10, borderRadius: 5 }}
                  color={getScoreColor(userData.financialScore) as any}
                />
                <Typography variant="h6" fontWeight="bold">
                  {userData.financialScore}/100
                </Typography>
              </Box>
              <Typography variant="caption" color="text.secondary">
                Votre score s'améliore avec une meilleure gestion financière
              </Typography>
            </Box>
          </CardContent>
        </Card>

        <Grid container spacing={3}>
          {/* Statistics */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 Mes Statistiques
                </Typography>
                <Grid container spacing={3}>
                  <Grid item xs={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="primary.main" fontWeight="bold">
                        {userData.stats.totalSavings.toLocaleString()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        TND Épargnés
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="info.main" fontWeight="bold">
                        {userData.stats.budgetsCreated}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Budgets Créés
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="success.main" fontWeight="bold">
                        {userData.stats.goalsAchieved}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Objectifs Atteints
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" color="warning.main" fontWeight="bold">
                        {userData.stats.daysActive}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Jours Actifs
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" gutterBottom>
                  🎯 Activité Récente
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <AccountBalanceIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Nouveau budget créé"
                      secondary="Budget Loisirs - Il y a 2 jours"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <TrophyIcon color="success" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Objectif d'épargne atteint"
                      secondary="Vacances d'été - Il y a 1 semaine"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <SchoolIcon color="info" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Formation terminée"
                      secondary="Gestion des budgets - Il y a 2 semaines"
                    />
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Achievements */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  🏆 Mes Réussites
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {userData.achievements.map((achievement) => (
                    <Box
                      key={achievement.id}
                      sx={{
                        p: 2,
                        border: 1,
                        borderColor: achievement.earned ? 'success.main' : 'grey.300',
                        borderRadius: 2,
                        bgcolor: achievement.earned ? 'success.50' : 'grey.50',
                        opacity: achievement.earned ? 1 : 0.7
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Typography variant="h6">{achievement.icon}</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {achievement.title}
                        </Typography>
                      </Box>
                      {achievement.earned ? (
                        <Typography variant="caption" color="success.dark">
                          ✅ Obtenu le {new Date(achievement.date!).toLocaleDateString('fr-FR')}
                        </Typography>
                      ) : (
                        <Box>
                          <LinearProgress
                            variant="determinate"
                            value={achievement.progress || 0}
                            sx={{ height: 6, borderRadius: 3, mb: 1 }}
                          />
                          <Typography variant="caption" color="text.secondary">
                            Progression: {achievement.progress}%
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Quick Actions */}
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🚀 Actions Rapides
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                onClick={() => navigate(ROUTES.BUDGETS)}
              >
                Créer un Budget
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate(ROUTES.SAVINGS)}
              >
                Nouvel Objectif
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate(ROUTES.EDUCATION)}
              >
                Suivre une Formation
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate(ROUTES.REPORTS)}
              >
                Voir mes Rapports
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  )
}

export default ProfilePage
