var e=Object.defineProperty,t=(t,r,n)=>(((t,r,n)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n})(t,"symbol"!=typeof r?r+"":r,n),n);import{c as r,j as n,T as o,C as s,B as i,a as E,b as a}from"./mui-vendor-9c785b1c.js";import{c as _,b as T,B as I,r as c,d as l,e as d,N as f}from"./react-vendor-ef8b78f8.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var p={},h=_;p.createRoot=h.createRoot,p.hydrateRoot=h.hydrateRoot;const A={},m=function(e,t,r){if(!t||0===t.length)return e();const n=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if((e=function(e){return"/"+e}(e))in A)return;A[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(!!r)for(let r=n.length-1;r>=0;r--){const o=n[r];if(o.href===e&&(!t||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${e}"]${o}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script",s.crossOrigin=""),s.href=e,document.head.appendChild(s),t?new Promise(((t,r)=>{s.addEventListener("load",t),s.addEventListener("error",(()=>r(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))},u=r({palette:{primary:{main:"#1976d2",light:"#42a5f5",dark:"#1565c0",contrastText:"#ffffff"},secondary:{main:"#dc004e",light:"#ff5983",dark:"#9a0036",contrastText:"#ffffff"},success:{main:"#2e7d32",light:"#4caf50",dark:"#1b5e20",contrastText:"#ffffff"},warning:{main:"#ed6c02",light:"#ff9800",dark:"#e65100",contrastText:"#ffffff"},error:{main:"#d32f2f",light:"#ef5350",dark:"#c62828",contrastText:"#ffffff"},background:{default:"#fafafa",paper:"#ffffff"},text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"}},typography:{fontFamily:["Inter","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial","sans-serif"].join(","),h1:{fontWeight:700,fontSize:"2.5rem",lineHeight:1.2},h2:{fontWeight:700,fontSize:"2rem",lineHeight:1.3},h3:{fontWeight:600,fontSize:"1.75rem",lineHeight:1.3},h4:{fontWeight:600,fontSize:"1.5rem",lineHeight:1.4},h5:{fontWeight:500,fontSize:"1.25rem",lineHeight:1.4},h6:{fontWeight:500,fontSize:"1.125rem",lineHeight:1.4},body1:{fontSize:"1rem",lineHeight:1.5},body2:{fontSize:"0.875rem",lineHeight:1.5},button:{fontWeight:500,textTransform:"none"}},shape:{borderRadius:8},spacing:8,components:{MuiButton:{styleOverrides:{root:{borderRadius:8,textTransform:"none",fontWeight:500,boxShadow:"none","&:hover":{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"}},contained:{"&:hover":{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}}},MuiCard:{styleOverrides:{root:{borderRadius:12,boxShadow:"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)","&:hover":{boxShadow:"0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08)"}}}},MuiChip:{styleOverrides:{root:{borderRadius:16}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}}}}),N=(e,t="")=>({VITE_API_URL:"http://localhost:3000",VITE_AUTH0_DOMAIN:"dev-nouri.eu.auth0.com",VITE_AUTH0_CLIENT_ID:"test-client-id",VITE_APP_VERSION:"1.0.0",VITE_NODE_ENV:"development",VITE_AUTH0_AUDIENCE:"https://api.nouri.tn",VITE_DEFAULT_LANGUAGE:"fr",VITE_DEFAULT_CURRENCY:"TND",VITE_ENABLE_CHATBOT:"true",BASE_URL:"/",MODE:"development",DEV:!1,PROD:!0,SSR:!1}[e]||t),O=(e,t=!1)=>{const r={VITE_API_URL:"http://localhost:3000",VITE_AUTH0_DOMAIN:"dev-nouri.eu.auth0.com",VITE_AUTH0_CLIENT_ID:"test-client-id",VITE_APP_VERSION:"1.0.0",VITE_NODE_ENV:"development",VITE_AUTH0_AUDIENCE:"https://api.nouri.tn",VITE_DEFAULT_LANGUAGE:"fr",VITE_DEFAULT_CURRENCY:"TND",VITE_ENABLE_CHATBOT:"true",BASE_URL:"/",MODE:"development",DEV:!1,PROD:!0,SSR:!1}[e];return void 0===r?t:"true"===r||"1"===r},V={name:"Nouri - Coach Financier IA",version:N("VITE_APP_VERSION","1.0.0"),environment:N("VITE_NODE_ENV","development"),baseUrl:N("VITE_BASE_URL",window.location.origin),supportEmail:N("VITE_SUPPORT_EMAIL","<EMAIL>"),isDevelopment:"development"===N("VITE_NODE_ENV","development"),isProduction:"production"===N("VITE_NODE_ENV","development")};N("VITE_API_URL","http://localhost:3000"),parseInt(N("VITE_API_TIMEOUT","30000"),10),parseInt(N("VITE_API_RETRY_ATTEMPTS","3"),10),parseInt(N("VITE_API_RETRY_DELAY","1000"),10),N("VITE_AUTH0_DOMAIN",""),N("VITE_AUTH0_CLIENT_ID",""),N("VITE_AUTH0_AUDIENCE","https://api.nouri.tn"),N("VITE_AUTH0_REDIRECT_URI",window.location.origin),N("VITE_AUTH0_LOGOUT_URI",window.location.origin),N("VITE_DEFAULT_LANGUAGE","fr"),N("VITE_SUPPORTED_LANGUAGES","fr,ar,en").split(","),N("VITE_FALLBACK_LANGUAGE","fr"),O("VITE_DETECT_BROWSER_LANGUAGE",!0),N("VITE_DEFAULT_CURRENCY","TND"),N("VITE_SUPPORTED_CURRENCIES","TND,EUR,USD").split(","),parseInt(N("VITE_DECIMAL_PLACES","3"),10),O("VITE_ENABLE_PWA",!0),O("VITE_ENABLE_NOTIFICATIONS",!0),O("VITE_ENABLE_ANALYTICS",!1),O("VITE_ENABLE_CHATBOT",!0),O("VITE_ENABLE_EDUCATION",!0),O("VITE_ENABLE_REPORTING",!0),O("VITE_ENABLE_DARK_MODE",!0),O("VITE_ENABLE_OFFLINE_MODE",!0),parseInt(N("VITE_SIDEBAR_WIDTH","280"),10),parseInt(N("VITE_HEADER_HEIGHT","64"),10),parseInt(N("VITE_MOBILE_BREAKPOINT","768"),10),parseInt(N("VITE_ANIMATION_DURATION","300"),10),parseInt(N("VITE_DEBOUNCE_DELAY","300"),10),parseInt(N("VITE_USER_DATA_TTL","300000"),10),parseInt(N("VITE_TRANSACTIONS_TTL","60000"),10),parseInt(N("VITE_BUDGETS_TTL","300000"),10),parseInt(N("VITE_STATIC_DATA_TTL","3600000"),10),O("VITE_ENABLE_CSP",!0),parseInt(N("VITE_SESSION_TIMEOUT","1800000"),10),parseInt(N("VITE_MAX_LOGIN_ATTEMPTS","5"),10),parseInt(N("VITE_PASSWORD_MIN_LENGTH","8"),10),O("VITE_ENABLE_ERROR_REPORTING",!1),O("VITE_ENABLE_PERFORMANCE_MONITORING",!1),N("VITE_SENTRY_DSN",""),N("VITE_LOG_LEVEL","info"),N("VITE_GOOGLE_ANALYTICS_ID",""),N("VITE_HOTJAR_ID",""),N("VITE_INTERCOM_APP_ID",""),N("VITE_CRISP_WEBSITE_ID",""),O("VITE_CRISP_ENABLED",!1),parseInt(N("VITE_MAX_FILE_UPLOAD_SIZE","5242880"),10),parseInt(N("VITE_MAX_TRANSACTIONS_PER_PAGE","50"),10),parseInt(N("VITE_MAX_BUDGET_CATEGORIES","20"),10),parseInt(N("VITE_MAX_SAVINGS_GOALS","10"),10),N("VITE_PRIVACY_POLICY_URL","/privacy"),N("VITE_TERMS_OF_SERVICE_URL","/terms"),N("VITE_HELP_URL","/help"),N("VITE_CONTACT_URL","/contact"),N("VITE_BLOG_URL","https://blog.nouri.tn"),N("VITE_DOCUMENTATION_URL","https://docs.nouri.tn");if(V.isDevelopment){["VITE_API_URL","VITE_AUTH0_DOMAIN","VITE_AUTH0_CLIENT_ID"].filter((e=>!N(e))).length}const R={AUTH_TOKEN:"nouri_auth_token",USER_PREFERENCES:"nouri_user_preferences",LANGUAGE:"nouri_language",THEME:"nouri_theme",ONBOARDING_COMPLETED:"nouri_onboarding_completed",LAST_SYNC:"nouri_last_sync"},L={HOME:"/",LOGIN:"/login",REGISTER:"/register",DASHBOARD:"/dashboard",TRANSACTIONS:"/transactions",BUDGETS:"/budgets",SAVINGS:"/savings",CHATBOT:"/chat",EDUCATION:"/education",REPORTS:"/reports",SETTINGS:"/settings",PROFILE:"/profile",HELP:"/help"},S=T.lazy((()=>m((()=>import("./HomePage-80d6e500.js")),["assets/HomePage-80d6e500.js","assets/mui-vendor-9c785b1c.js","assets/react-vendor-ef8b78f8.js","assets/index.module-a126d235.js"]))),g=T.lazy((()=>m((()=>import("./LoginPage-13ca6e4e.js")),["assets/LoginPage-13ca6e4e.js","assets/mui-vendor-9c785b1c.js","assets/react-vendor-ef8b78f8.js","assets/index.module-a126d235.js"]))),D=T.lazy((()=>m((()=>import("./DashboardPage-b118c283.js")),["assets/DashboardPage-b118c283.js","assets/mui-vendor-9c785b1c.js","assets/react-vendor-ef8b78f8.js","assets/index.module-a126d235.js","assets/utils-vendor-be215184.js"]))),v=()=>n.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",gap:2},children:[n.jsx(E,{size:40}),n.jsxs(i,{sx:{textAlign:"center"},children:[n.jsx(a,{variant:"h3",sx:{mb:1},children:"🇹🇳"}),n.jsx(a,{variant:"h6",sx:{color:"primary.main",fontWeight:"bold"},children:"Nouri"}),n.jsx(a,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Chargement de votre coach financier..."})]})]}),P=()=>n.jsxs(o,{theme:u,children:[n.jsx(s,{}),n.jsx(I,{children:n.jsx(c.Suspense,{fallback:n.jsx(v,{}),children:n.jsxs(l,{children:[n.jsx(d,{path:L.HOME,element:n.jsx(S,{})}),n.jsx(d,{path:L.LOGIN,element:n.jsx(g,{})}),n.jsx(d,{path:L.DASHBOARD,element:n.jsx(D,{})}),n.jsx(d,{path:"*",element:n.jsx(f,{to:L.HOME,replace:!0})})]})})})]});new class{constructor(){t(this,"metrics",{}),t(this,"observers",[]),this.initializeObservers()}initializeObservers(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver((e=>{for(const t of e.getEntries())"first-contentful-paint"===t.name&&(this.metrics.fcp=t.startTime)}));e.observe({entryTypes:["paint"]}),this.observers.push(e);const t=new PerformanceObserver((e=>{const t=e.getEntries(),r=t[t.length-1];this.metrics.lcp=r.startTime}));t.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(t);const r=new PerformanceObserver((e=>{for(const t of e.getEntries())this.metrics.fid=t.processingStart-t.startTime}));r.observe({entryTypes:["first-input"]}),this.observers.push(r);const n=new PerformanceObserver((e=>{let t=0;for(const r of e.getEntries())r.hadRecentInput||(t+=r.value);this.metrics.cls=t}));n.observe({entryTypes:["layout-shift"]}),this.observers.push(n)}catch(e){}window.addEventListener("load",(()=>{setTimeout((()=>{const e=performance.getEntriesByType("navigation")[0];e&&(this.metrics.ttfb=e.responseStart-e.requestStart,this.metrics.domContentLoaded=e.domContentLoadedEventEnd-e.domContentLoadedEventStart,this.metrics.loadComplete=e.loadEventEnd-e.loadEventStart),this.reportMetrics()}),0)}))}reportMetrics(){}formatTime(e){return e?`${e.toFixed(2)}ms`:"N/A"}scorePerformance(){const e={fcp:this.scoreFCP(this.metrics.fcp),lcp:this.scoreLCP(this.metrics.lcp),fid:this.scoreFID(this.metrics.fid),cls:this.scoreCLS(this.metrics.cls)};Object.values(e).reduce(((e,t)=>e+t),0),Object.values(e).length}scoreFCP(e){return e?e<=1800?100:e<=3e3?50:0:0}scoreLCP(e){return e?e<=2500?100:e<=4e3?50:0:0}scoreFID(e){return e?e<=100?100:e<=300?50:0:100}scoreCLS(e){return e?e<=.1?100:e<=.25?50:0:100}getScoreLabel(e){return e>=90?"🟢 Good":e>=50?"🟡 Needs Improvement":"🔴 Poor"}getMetrics(){return{...this.metrics}}destroy(){this.observers.forEach((e=>e.disconnect())),this.observers=[]}};p.createRoot(document.getElementById("root")).render(n.jsx(T.StrictMode,{children:n.jsx(P,{})}));export{L as R,R as S};
