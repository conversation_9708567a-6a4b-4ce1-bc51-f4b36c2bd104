/**
 * Store pour la gestion du thème et des préférences UI
 */

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { STORAGE_KEYS } from '@/config'

// Types
type ThemeMode = 'light' | 'dark' | 'system'
type Language = 'fr' | 'ar' | 'en'
type Currency = 'TND' | 'EUR' | 'USD'

interface ThemeState {
  // Thème
  mode: ThemeMode
  isDarkMode: boolean
  
  // Localisation
  language: Language
  currency: Currency
  isRTL: boolean
  
  // Préférences UI
  sidebarCollapsed: boolean
  compactMode: boolean
  animationsEnabled: boolean
  soundEnabled: boolean
  
  // Accessibilité
  highContrast: boolean
  fontSize: 'small' | 'medium' | 'large'
  reducedMotion: boolean
}

interface ThemeActions {
  // Actions thème
  setThemeMode: (mode: ThemeMode) => void
  toggleTheme: () => void
  
  // Actions localisation
  setLanguage: (language: Language) => void
  setCurrency: (currency: Currency) => void
  
  // Actions UI
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleSidebarCollapsed: () => void
  setCompactMode: (compact: boolean) => void
  setAnimationsEnabled: (enabled: boolean) => void
  setSoundEnabled: (enabled: boolean) => void
  
  // Actions accessibilité
  setHighContrast: (enabled: boolean) => void
  setFontSize: (size: 'small' | 'medium' | 'large') => void
  setReducedMotion: (enabled: boolean) => void
  
  // Actions utilitaires
  resetToDefaults: () => void
  applySystemPreferences: () => void
}

type ThemeStore = ThemeState & ThemeActions

// Détection des préférences système
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }
  return 'light'
}

const getSystemLanguage = (): Language => {
  if (typeof window !== 'undefined') {
    const lang = navigator.language.split('-')[0]
    if (['fr', 'ar', 'en'].includes(lang)) {
      return lang as Language
    }
  }
  return 'fr'
}

const getSystemReducedMotion = (): boolean => {
  if (typeof window !== 'undefined') {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }
  return false
}

// État initial
const initialState: ThemeState = {
  mode: 'system',
  isDarkMode: getSystemTheme() === 'dark',
  language: getSystemLanguage(),
  currency: 'TND',
  isRTL: false,
  sidebarCollapsed: false,
  compactMode: false,
  animationsEnabled: true,
  soundEnabled: true,
  highContrast: false,
  fontSize: 'medium',
  reducedMotion: getSystemReducedMotion()
}

// Store du thème
export const useThemeStore = create<ThemeStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Actions thème
        setThemeMode: (mode) => {
          set((state) => {
            state.mode = mode
            
            if (mode === 'system') {
              state.isDarkMode = getSystemTheme() === 'dark'
            } else {
              state.isDarkMode = mode === 'dark'
            }
          })
        },

        toggleTheme: () => {
          const { mode, isDarkMode } = get()
          
          if (mode === 'system') {
            set((state) => {
              state.mode = isDarkMode ? 'light' : 'dark'
              state.isDarkMode = !isDarkMode
            })
          } else {
            set((state) => {
              state.mode = isDarkMode ? 'light' : 'dark'
              state.isDarkMode = !isDarkMode
            })
          }
        },

        // Actions localisation
        setLanguage: (language) => {
          set((state) => {
            state.language = language
            state.isRTL = language === 'ar'
          })
          
          // Mettre à jour la direction du document
          if (typeof document !== 'undefined') {
            document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
            document.documentElement.lang = language
          }
        },

        setCurrency: (currency) => {
          set((state) => {
            state.currency = currency
          })
        },

        // Actions UI
        setSidebarCollapsed: (collapsed) => {
          set((state) => {
            state.sidebarCollapsed = collapsed
          })
        },

        toggleSidebarCollapsed: () => {
          set((state) => {
            state.sidebarCollapsed = !state.sidebarCollapsed
          })
        },

        setCompactMode: (compact) => {
          set((state) => {
            state.compactMode = compact
          })
        },

        setAnimationsEnabled: (enabled) => {
          set((state) => {
            state.animationsEnabled = enabled
          })
        },

        setSoundEnabled: (enabled) => {
          set((state) => {
            state.soundEnabled = enabled
          })
        },

        // Actions accessibilité
        setHighContrast: (enabled) => {
          set((state) => {
            state.highContrast = enabled
          })
        },

        setFontSize: (size) => {
          set((state) => {
            state.fontSize = size
          })
          
          // Appliquer la taille de police au document
          if (typeof document !== 'undefined') {
            const root = document.documentElement
            switch (size) {
              case 'small':
                root.style.fontSize = '14px'
                break
              case 'large':
                root.style.fontSize = '18px'
                break
              default:
                root.style.fontSize = '16px'
            }
          }
        },

        setReducedMotion: (enabled) => {
          set((state) => {
            state.reducedMotion = enabled
          })
        },

        // Actions utilitaires
        resetToDefaults: () => {
          set(() => ({ ...initialState }))
        },

        applySystemPreferences: () => {
          set((state) => {
            state.isDarkMode = getSystemTheme() === 'dark'
            state.reducedMotion = getSystemReducedMotion()
            
            if (state.mode === 'system') {
              state.isDarkMode = getSystemTheme() === 'dark'
            }
          })
        }
      })),
      {
        name: STORAGE_KEYS.THEME,
        partialize: (state) => ({
          mode: state.mode,
          language: state.language,
          currency: state.currency,
          sidebarCollapsed: state.sidebarCollapsed,
          compactMode: state.compactMode,
          animationsEnabled: state.animationsEnabled,
          soundEnabled: state.soundEnabled,
          highContrast: state.highContrast,
          fontSize: state.fontSize,
          reducedMotion: state.reducedMotion
        })
      }
    ),
    {
      name: 'theme-store'
    }
  )
)

// Écouter les changements de préférences système
if (typeof window !== 'undefined') {
  // Écouter les changements de thème système
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.addEventListener('change', () => {
    const { mode, applySystemPreferences } = useThemeStore.getState()
    if (mode === 'system') {
      applySystemPreferences()
    }
  })

  // Écouter les changements de préférence de mouvement réduit
  const motionQuery = window.matchMedia('(prefers-reduced-motion: reduce)')
  motionQuery.addEventListener('change', (e) => {
    const { setReducedMotion } = useThemeStore.getState()
    setReducedMotion(e.matches)
  })
}

// Sélecteurs utiles
export const useTheme = () => useThemeStore((state) => ({
  mode: state.mode,
  isDarkMode: state.isDarkMode,
  setThemeMode: state.setThemeMode,
  toggleTheme: state.toggleTheme
}))

export const useLanguage = () => useThemeStore((state) => ({
  language: state.language,
  isRTL: state.isRTL,
  setLanguage: state.setLanguage
}))

export const useCurrency = () => useThemeStore((state) => ({
  currency: state.currency,
  setCurrency: state.setCurrency
}))

export const useAccessibility = () => useThemeStore((state) => ({
  highContrast: state.highContrast,
  fontSize: state.fontSize,
  reducedMotion: state.reducedMotion,
  setHighContrast: state.setHighContrast,
  setFontSize: state.setFontSize,
  setReducedMotion: state.setReducedMotion
}))

export const useUIPreferences = () => useThemeStore((state) => ({
  sidebarCollapsed: state.sidebarCollapsed,
  compactMode: state.compactMode,
  animationsEnabled: state.animationsEnabled,
  soundEnabled: state.soundEnabled,
  setSidebarCollapsed: state.setSidebarCollapsed,
  toggleSidebarCollapsed: state.toggleSidebarCollapsed,
  setCompactMode: state.setCompactMode,
  setAnimationsEnabled: state.setAnimationsEnabled,
  setSoundEnabled: state.setSoundEnabled
}))

export default useThemeStore
