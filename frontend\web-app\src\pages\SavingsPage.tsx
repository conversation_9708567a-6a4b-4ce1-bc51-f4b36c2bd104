/**
 * Complete Savings Page
 * Manage savings goals and track progress
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Logout as LogoutIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock savings goals data
const mockSavingsGoals = [
  {
    id: '1',
    title: 'Vacances d\'été en Europe',
    description: 'Voyage de 2 semaines en Europe',
    target: 5000,
    current: 3250,
    deadline: '2024-06-01',
    category: 'Voyage',
    icon: '✈️',
    color: '#2196f3',
    priority: 'high'
  },
  {
    id: '2',
    title: 'Fonds d\'urgence',
    description: '6 mois de dépenses courantes',
    target: 10000,
    current: 6500,
    deadline: '2024-12-31',
    category: 'Urgence',
    icon: '🛡️',
    color: '#4caf50',
    priority: 'high'
  },
  {
    id: '3',
    title: 'Nouvelle voiture',
    description: 'Achat d\'une voiture neuve',
    target: 25000,
    current: 8500,
    deadline: '2025-03-01',
    category: 'Transport',
    icon: '🚗',
    color: '#ff9800',
    priority: 'medium'
  },
  {
    id: '4',
    title: 'Appartement',
    description: 'Apport pour achat immobilier',
    target: 50000,
    current: 12000,
    deadline: '2026-01-01',
    category: 'Immobilier',
    icon: '🏠',
    color: '#9c27b0',
    priority: 'low'
  }
]

const categories = ['Voyage', 'Urgence', 'Transport', 'Immobilier', 'Éducation', 'Loisirs', 'Santé', 'Autre']

interface SavingsGoalCardProps {
  goal: typeof mockSavingsGoals[0]
  onEdit: (goal: typeof mockSavingsGoals[0]) => void
  onDelete: (id: string) => void
  onAddMoney: (id: string) => void
}

const SavingsGoalCard: React.FC<SavingsGoalCardProps> = ({ goal, onEdit, onDelete, onAddMoney }) => {
  const progress = (goal.current / goal.target) * 100
  const remaining = goal.target - goal.current
  const daysLeft = Math.ceil((new Date(goal.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error'
      case 'medium': return 'warning'
      case 'low': return 'success'
      default: return 'default'
    }
  }

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high': return 'Priorité haute'
      case 'medium': return 'Priorité moyenne'
      case 'low': return 'Priorité basse'
      default: return 'Normal'
    }
  }

  return (
    <Card sx={{ height: '100%', position: 'relative' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Avatar sx={{ bgcolor: goal.color, width: 48, height: 48 }}>
              <Typography variant="h5">{goal.icon}</Typography>
            </Avatar>
            <Box>
              <Typography variant="h6" fontWeight="bold">
                {goal.title}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {goal.description}
              </Typography>
            </Box>
          </Box>
          <Box>
            <IconButton size="small" onClick={() => onEdit(goal)}>
              <EditIcon />
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(goal.id)} color="error">
              <DeleteIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Chip
            label={getPriorityLabel(goal.priority)}
            size="small"
            color={getPriorityColor(goal.priority) as any}
            sx={{ mb: 1 }}
          />
          <Chip
            label={goal.category}
            size="small"
            variant="outlined"
            sx={{ mb: 1, ml: 1 }}
          />
        </Box>

        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" fontWeight="bold">
              {goal.current.toLocaleString()} TND
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {goal.target.toLocaleString()} TND
            </Typography>
          </Box>

          <LinearProgress
            variant="determinate"
            value={Math.min(progress, 100)}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                backgroundColor: goal.color
              }
            }}
          />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              {progress.toFixed(1)}% atteint
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {remaining.toLocaleString()} TND restants
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box>
            <Typography variant="caption" color="text.secondary">
              Échéance
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {new Date(goal.deadline).toLocaleDateString('fr-FR')}
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="caption" color="text.secondary">
              Temps restant
            </Typography>
            <Typography variant="body2" fontWeight="medium" color={daysLeft < 30 ? 'error.main' : 'text.primary'}>
              {daysLeft > 0 ? `${daysLeft} jours` : 'Échéance dépassée'}
            </Typography>
          </Box>
        </Box>

        <Button
          variant="contained"
          fullWidth
          onClick={() => onAddMoney(goal.id)}
          sx={{ backgroundColor: goal.color }}
        >
          Ajouter de l'argent
        </Button>
      </CardContent>
    </Card>
  )
}

export const SavingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [savingsGoals, setSavingsGoals] = useState(mockSavingsGoals)
  const [openDialog, setOpenDialog] = useState(false)
  const [openAddMoneyDialog, setOpenAddMoneyDialog] = useState(false)
  const [editingGoal, setEditingGoal] = useState<typeof mockSavingsGoals[0] | null>(null)
  const [selectedGoalId, setSelectedGoalId] = useState<string>('')
  const [addMoneyAmount, setAddMoneyAmount] = useState('')
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    target: '',
    deadline: '',
    category: '',
    priority: 'medium',
    icon: '💰'
  })

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const handleAddGoal = () => {
    setEditingGoal(null)
    setFormData({
      title: '',
      description: '',
      target: '',
      deadline: '',
      category: '',
      priority: 'medium',
      icon: '💰'
    })
    setOpenDialog(true)
  }

  const handleEditGoal = (goal: typeof mockSavingsGoals[0]) => {
    setEditingGoal(goal)
    setFormData({
      title: goal.title,
      description: goal.description,
      target: goal.target.toString(),
      deadline: goal.deadline,
      category: goal.category,
      priority: goal.priority,
      icon: goal.icon
    })
    setOpenDialog(true)
  }

  const handleDeleteGoal = (id: string) => {
    setSavingsGoals(savingsGoals.filter(g => g.id !== id))
  }

  const handleAddMoney = (id: string) => {
    setSelectedGoalId(id)
    setAddMoneyAmount('')
    setOpenAddMoneyDialog(true)
  }

  const handleSaveGoal = () => {
    if (!formData.title || !formData.target || !formData.deadline) return

    const goalData = {
      id: editingGoal?.id || Date.now().toString(),
      title: formData.title,
      description: formData.description,
      target: parseFloat(formData.target),
      current: editingGoal?.current || 0,
      deadline: formData.deadline,
      category: formData.category,
      priority: formData.priority,
      icon: formData.icon,
      color: editingGoal?.color || '#4caf50'
    }

    if (editingGoal) {
      setSavingsGoals(savingsGoals.map(g => g.id === editingGoal.id ? goalData : g))
    } else {
      setSavingsGoals([...savingsGoals, goalData])
    }

    setOpenDialog(false)
  }

  const handleConfirmAddMoney = () => {
    const amount = parseFloat(addMoneyAmount)
    if (!amount || amount <= 0) return

    setSavingsGoals(savingsGoals.map(goal =>
      goal.id === selectedGoalId
        ? { ...goal, current: goal.current + amount }
        : goal
    ))

    setOpenAddMoneyDialog(false)
  }

  const totalTarget = savingsGoals.reduce((sum, g) => sum + g.target, 0)
  const totalCurrent = savingsGoals.reduce((sum, g) => sum + g.current, 0)
  const completedGoals = savingsGoals.filter(g => g.current >= g.target).length

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Épargne
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                Mes Objectifs d'Épargne
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Définissez et suivez vos objectifs financiers
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddGoal}
              size="large"
            >
              Nouvel Objectif
            </Button>
          </Box>
        </Box>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" fontWeight="bold">
                  {totalCurrent.toLocaleString()} TND
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Épargné
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main" fontWeight="bold">
                  {totalTarget.toLocaleString()} TND
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Objectif Total
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {completedGoals}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Objectifs Atteints
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {((totalCurrent / totalTarget) * 100).toFixed(1)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Progression Globale
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Savings Goals */}
        <Grid container spacing={3}>
          {savingsGoals.map((goal) => (
            <Grid item xs={12} sm={6} md={4} key={goal.id}>
              <SavingsGoalCard
                goal={goal}
                onEdit={handleEditGoal}
                onDelete={handleDeleteGoal}
                onAddMoney={handleAddMoney}
              />
            </Grid>
          ))}
        </Grid>

        {/* Add/Edit Goal Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingGoal ? 'Modifier l\'objectif' : 'Nouvel objectif d\'épargne'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <TextField
                label="Titre de l'objectif"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                fullWidth
              />

              <TextField
                label="Description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                fullWidth
                multiline
                rows={2}
              />

              <TextField
                label="Montant cible (TND)"
                type="number"
                value={formData.target}
                onChange={(e) => setFormData({ ...formData, target: e.target.value })}
                fullWidth
              />

              <TextField
                label="Date d'échéance"
                type="date"
                value={formData.deadline}
                onChange={(e) => setFormData({ ...formData, deadline: e.target.value })}
                fullWidth
                InputLabelProps={{ shrink: true }}
              />

              <FormControl fullWidth>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={formData.category}
                  label="Catégorie"
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                >
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>{category}</MenuItem>
                  ))}
                </Select>
              </FormControl>

              <FormControl fullWidth>
                <InputLabel>Priorité</InputLabel>
                <Select
                  value={formData.priority}
                  label="Priorité"
                  onChange={(e) => setFormData({ ...formData, priority: e.target.value })}
                >
                  <MenuItem value="low">Basse</MenuItem>
                  <MenuItem value="medium">Moyenne</MenuItem>
                  <MenuItem value="high">Haute</MenuItem>
                </Select>
              </FormControl>

              <TextField
                label="Icône"
                value={formData.icon}
                onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                fullWidth
                helperText="Choisissez un emoji pour représenter cet objectif"
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
            <Button onClick={handleSaveGoal} variant="contained">
              {editingGoal ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>

        {/* Add Money Dialog */}
        <Dialog open={openAddMoneyDialog} onClose={() => setOpenAddMoneyDialog(false)} maxWidth="xs" fullWidth>
          <DialogTitle>Ajouter de l'argent</DialogTitle>
          <DialogContent>
            <TextField
              label="Montant à ajouter (TND)"
              type="number"
              value={addMoneyAmount}
              onChange={(e) => setAddMoneyAmount(e.target.value)}
              fullWidth
              sx={{ mt: 1 }}
              autoFocus
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenAddMoneyDialog(false)}>Annuler</Button>
            <Button onClick={handleConfirmAddMoney} variant="contained">
              Ajouter
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  )
}

export default SavingsPage
