/**
 * Complete Budgets Page
 * Manage budgets and spending categories
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  LinearProgress,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Home as HomeIcon,
  Logout as LogoutIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock budget data
const mockBudgets = [
  {
    id: '1',
    category: 'Alimentation',
    allocated: 400,
    spent: 285,
    period: 'monthly',
    color: '#4caf50',
    icon: '🍽️'
  },
  {
    id: '2',
    category: 'Transport',
    allocated: 200,
    spent: 150,
    period: 'monthly',
    color: '#2196f3',
    icon: '🚗'
  },
  {
    id: '3',
    category: 'Loisirs',
    allocated: 150,
    spent: 180,
    period: 'monthly',
    color: '#ff9800',
    icon: '🎮'
  },
  {
    id: '4',
    category: 'Santé',
    allocated: 100,
    spent: 45,
    period: 'monthly',
    color: '#e91e63',
    icon: '🏥'
  },
  {
    id: '5',
    category: 'Éducation',
    allocated: 300,
    spent: 120,
    period: 'monthly',
    color: '#9c27b0',
    icon: '📚'
  },
  {
    id: '6',
    category: 'Vêtements',
    allocated: 120,
    spent: 95,
    period: 'monthly',
    color: '#607d8b',
    icon: '👕'
  }
]

const categories = [
  'Alimentation', 'Transport', 'Loisirs', 'Santé', 'Éducation', 'Vêtements',
  'Utilities', 'Restaurants', 'Shopping', 'Voyage', 'Assurance', 'Autre'
]

interface BudgetCardProps {
  budget: typeof mockBudgets[0]
  onEdit: (budget: typeof mockBudgets[0]) => void
  onDelete: (id: string) => void
}

const BudgetCard: React.FC<BudgetCardProps> = ({ budget, onEdit, onDelete }) => {
  const percentage = (budget.spent / budget.allocated) * 100
  const remaining = budget.allocated - budget.spent
  const isOverBudget = percentage > 100

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="h4">{budget.icon}</Typography>
            <Box>
              <Typography variant="h6" fontWeight="bold">
                {budget.category}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Budget mensuel
              </Typography>
            </Box>
          </Box>
          <Box>
            <IconButton size="small" onClick={() => onEdit(budget)}>
              <EditIcon />
            </IconButton>
            <IconButton size="small" onClick={() => onDelete(budget.id)} color="error">
              <DeleteIcon />
            </IconButton>
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Dépensé: {budget.spent.toFixed(2)} TND
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Budget: {budget.allocated.toFixed(2)} TND
            </Typography>
          </Box>

          <LinearProgress
            variant="determinate"
            value={Math.min(percentage, 100)}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                backgroundColor: isOverBudget ? 'error.main' : budget.color
              }
            }}
          />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 1 }}>
            <Typography variant="caption" color="text.secondary">
              {percentage.toFixed(1)}% utilisé
            </Typography>
            <Chip
              size="small"
              label={remaining >= 0 ? `${remaining.toFixed(2)} TND restants` : `Dépassé de ${Math.abs(remaining).toFixed(2)} TND`}
              color={remaining >= 0 ? 'success' : 'error'}
              variant="outlined"
            />
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {isOverBudget ? (
            <TrendingUpIcon color="error" fontSize="small" />
          ) : (
            <TrendingDownIcon color="success" fontSize="small" />
          )}
          <Typography
            variant="caption"
            color={isOverBudget ? 'error.main' : 'success.main'}
          >
            {isOverBudget ? 'Budget dépassé' : 'Dans les limites'}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}

export const BudgetsPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [budgets, setBudgets] = useState(mockBudgets)
  const [openDialog, setOpenDialog] = useState(false)
  const [editingBudget, setEditingBudget] = useState<typeof mockBudgets[0] | null>(null)
  const [formData, setFormData] = useState({
    category: '',
    allocated: '',
    icon: '💰'
  })

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const handleAddBudget = () => {
    setEditingBudget(null)
    setFormData({ category: '', allocated: '', icon: '💰' })
    setOpenDialog(true)
  }

  const handleEditBudget = (budget: typeof mockBudgets[0]) => {
    setEditingBudget(budget)
    setFormData({
      category: budget.category,
      allocated: budget.allocated.toString(),
      icon: budget.icon
    })
    setOpenDialog(true)
  }

  const handleDeleteBudget = (id: string) => {
    setBudgets(budgets.filter(b => b.id !== id))
  }

  const handleSaveBudget = () => {
    if (!formData.category || !formData.allocated) return

    const budgetData = {
      id: editingBudget?.id || Date.now().toString(),
      category: formData.category,
      allocated: parseFloat(formData.allocated),
      spent: editingBudget?.spent || 0,
      period: 'monthly' as const,
      color: editingBudget?.color || '#4caf50',
      icon: formData.icon
    }

    if (editingBudget) {
      setBudgets(budgets.map(b => b.id === editingBudget.id ? budgetData : b))
    } else {
      setBudgets([...budgets, budgetData])
    }

    setOpenDialog(false)
  }

  const totalAllocated = budgets.reduce((sum, b) => sum + b.allocated, 0)
  const totalSpent = budgets.reduce((sum, b) => sum + b.spent, 0)
  const overBudgetCount = budgets.filter(b => b.spent > b.allocated).length

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Budgets
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                Mes Budgets
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Gérez vos budgets mensuels et suivez vos dépenses par catégorie
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddBudget}
              size="large"
            >
              Nouveau Budget
            </Button>
          </Box>
        </Box>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" fontWeight="bold">
                  {totalAllocated.toFixed(2)} TND
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Budget Total Mensuel
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" fontWeight="bold">
                  {totalSpent.toFixed(2)} TND
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Dépensé
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color={overBudgetCount > 0 ? 'error.main' : 'success.main'} fontWeight="bold">
                  {overBudgetCount}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Budgets Dépassés
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Budget Overview */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Vue d'ensemble du budget mensuel
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2">
                  {totalSpent.toFixed(2)} TND / {totalAllocated.toFixed(2)} TND
                </Typography>
                <Typography variant="body2">
                  {((totalSpent / totalAllocated) * 100).toFixed(1)}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={Math.min((totalSpent / totalAllocated) * 100, 100)}
                sx={{ height: 10, borderRadius: 5 }}
                color={totalSpent > totalAllocated ? 'error' : 'primary'}
              />
            </Box>
            {overBudgetCount > 0 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                Attention: {overBudgetCount} budget(s) dépassé(s) ce mois-ci
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Budget Cards */}
        <Grid container spacing={3}>
          {budgets.map((budget) => (
            <Grid item xs={12} sm={6} md={4} key={budget.id}>
              <BudgetCard
                budget={budget}
                onEdit={handleEditBudget}
                onDelete={handleDeleteBudget}
              />
            </Grid>
          ))}
        </Grid>

        {/* Add/Edit Budget Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            {editingBudget ? 'Modifier le budget' : 'Nouveau budget'}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
              <FormControl fullWidth>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={formData.category}
                  label="Catégorie"
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                >
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>{category}</MenuItem>
                  ))}
                </Select>
              </FormControl>

              <TextField
                label="Montant alloué (TND)"
                type="number"
                value={formData.allocated}
                onChange={(e) => setFormData({ ...formData, allocated: e.target.value })}
                fullWidth
              />

              <TextField
                label="Icône"
                value={formData.icon}
                onChange={(e) => setFormData({ ...formData, icon: e.target.value })}
                fullWidth
                helperText="Choisissez un emoji pour représenter cette catégorie"
              />
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenDialog(false)}>Annuler</Button>
            <Button onClick={handleSaveBudget} variant="contained">
              {editingBudget ? 'Modifier' : 'Créer'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Box>
  )
}

export default BudgetsPage
