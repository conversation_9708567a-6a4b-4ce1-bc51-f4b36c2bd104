import{j as e,B as t,o as i,p as r,b as n,I as s,H as a,q as o,d as l,f as c,k as d,G as x,g as h,h as u,V as j,W as g,X as m,s as p,F as y,v as b,w as f,M as v,Y as C,l as W,Z as w,_ as A,e as D,L as S}from"./mui-vendor-b761306f.js";import{u as T,r as k}from"./react-vendor-2f216e43.js";import{A as M,R as I}from"./index-bd55ea72.js";const N=[{id:"1",title:"Vacances d'été en Europe",description:"Voyage de 2 semaines en Europe",target:5e3,current:3250,deadline:"2024-06-01",category:"Voyage",icon:"✈️",color:"#2196f3",priority:"high"},{id:"2",title:"Fonds d'urgence",description:"6 mois de dépenses courantes",target:1e4,current:6500,deadline:"2024-12-31",category:"Urgence",icon:"🛡️",color:"#4caf50",priority:"high"},{id:"3",title:"Nouvelle voiture",description:"Achat d'une voiture neuve",target:25e3,current:8500,deadline:"2025-03-01",category:"Transport",icon:"🚗",color:"#ff9800",priority:"medium"},{id:"4",title:"Appartement",description:"Apport pour achat immobilier",target:5e4,current:12e3,deadline:"2026-01-01",category:"Immobilier",icon:"🏠",color:"#9c27b0",priority:"low"}],L=["Voyage","Urgence","Transport","Immobilier","Éducation","Loisirs","Santé","Autre"],z=({goal:i,onEdit:r,onDelete:a,onAddMoney:o})=>{const l=i.current/i.target*100,d=i.target-i.current,x=Math.ceil((new Date(i.deadline).getTime()-(new Date).getTime())/864e5);return e.jsx(h,{sx:{height:"100%",position:"relative"},children:e.jsxs(u,{children:[e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(W,{sx:{bgcolor:i.color,width:48,height:48},children:e.jsx(n,{variant:"h5",children:i.icon})}),e.jsxs(t,{children:[e.jsx(n,{variant:"h6",fontWeight:"bold",children:i.title}),e.jsx(n,{variant:"body2",color:"text.secondary",children:i.description})]})]}),e.jsxs(t,{children:[e.jsx(s,{size:"small",onClick:()=>r(i),children:e.jsx(w,{})}),e.jsx(s,{size:"small",onClick:()=>a(i.id),color:"error",children:e.jsx(A,{})})]})]}),e.jsxs(t,{sx:{mb:2},children:[e.jsx(D,{label:(e=>{switch(e){case"high":return"Priorité haute";case"medium":return"Priorité moyenne";case"low":return"Priorité basse";default:return"Normal"}})(i.priority),size:"small",color:(e=>{switch(e){case"high":return"error";case"medium":return"warning";case"low":return"success";default:return"default"}})(i.priority),sx:{mb:1}}),e.jsx(D,{label:i.category,size:"small",variant:"outlined",sx:{mb:1,ml:1}})]}),e.jsxs(t,{sx:{mb:3},children:[e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsxs(n,{variant:"body2",fontWeight:"bold",children:[i.current.toLocaleString()," TND"]}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:[i.target.toLocaleString()," TND"]})]}),e.jsx(S,{variant:"determinate",value:Math.min(l,100),sx:{height:8,borderRadius:4,backgroundColor:"grey.200","& .MuiLinearProgress-bar":{borderRadius:4,backgroundColor:i.color}}}),e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:1},children:[e.jsxs(n,{variant:"caption",color:"text.secondary",children:[l.toFixed(1),"% atteint"]}),e.jsxs(n,{variant:"caption",color:"text.secondary",children:[d.toLocaleString()," TND restants"]})]})]}),e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(t,{children:[e.jsx(n,{variant:"caption",color:"text.secondary",children:"Échéance"}),e.jsx(n,{variant:"body2",fontWeight:"medium",children:new Date(i.deadline).toLocaleDateString("fr-FR")})]}),e.jsxs(t,{sx:{textAlign:"right"},children:[e.jsx(n,{variant:"caption",color:"text.secondary",children:"Temps restant"}),e.jsx(n,{variant:"body2",fontWeight:"medium",color:x<30?"error.main":"text.primary",children:x>0?`${x} jours`:"Échéance dépassée"})]})]}),e.jsx(c,{variant:"contained",fullWidth:!0,onClick:()=>o(i.id),sx:{backgroundColor:i.color},children:"Ajouter de l'argent"})]})})},F=()=>{const W=T(),{logout:w}=k.useContext(M),[A,D]=k.useState(N),[S,F]=k.useState(!1),[P,O]=k.useState(!1),[E,R]=k.useState(null),[V,B]=k.useState(""),[G,H]=k.useState(""),[U,q]=k.useState({title:"",description:"",target:"",deadline:"",category:"",priority:"medium",icon:"💰"}),X=e=>{R(e),q({title:e.title,description:e.description,target:e.target.toString(),deadline:e.deadline,category:e.category,priority:e.priority,icon:e.icon}),F(!0)},Y=e=>{D(A.filter((t=>t.id!==e)))},Z=e=>{B(e),H(""),O(!0)},$=A.reduce(((e,t)=>e+t.target),0),_=A.reduce(((e,t)=>e+t.current),0),J=A.filter((e=>e.current>=e.target)).length;return e.jsxs(t,{sx:{flexGrow:1},children:[e.jsx(i,{position:"static",elevation:1,children:e.jsxs(r,{children:[e.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Épargne"}),e.jsx(s,{color:"inherit",onClick:()=>W(I.DASHBOARD),children:e.jsx(a,{})}),e.jsx(s,{color:"inherit",onClick:()=>{w(),W(I.HOME)},children:e.jsx(o,{})})]})}),e.jsxs(l,{maxWidth:"xl",sx:{py:4},children:[e.jsx(t,{sx:{mb:4},children:e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(t,{children:[e.jsx(n,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Mes Objectifs d'Épargne"}),e.jsx(n,{variant:"body1",color:"text.secondary",children:"Définissez et suivez vos objectifs financiers"})]}),e.jsx(c,{variant:"contained",startIcon:e.jsx(d,{}),onClick:()=>{R(null),q({title:"",description:"",target:"",deadline:"",category:"",priority:"medium",icon:"💰"}),F(!0)},size:"large",children:"Nouvel Objectif"})]})}),e.jsxs(x,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(h,{children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsxs(n,{variant:"h4",color:"primary.main",fontWeight:"bold",children:[_.toLocaleString()," TND"]}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Total Épargné"})]})})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(h,{children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsxs(n,{variant:"h4",color:"info.main",fontWeight:"bold",children:[$.toLocaleString()," TND"]}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Objectif Total"})]})})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(h,{children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsx(n,{variant:"h4",color:"success.main",fontWeight:"bold",children:J}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Objectifs Atteints"})]})})}),e.jsx(x,{item:!0,xs:12,md:3,children:e.jsx(h,{children:e.jsxs(u,{sx:{textAlign:"center"},children:[e.jsxs(n,{variant:"h4",color:"warning.main",fontWeight:"bold",children:[(_/$*100).toFixed(1),"%"]}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Progression Globale"})]})})})]}),e.jsx(x,{container:!0,spacing:3,children:A.map((t=>e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(z,{goal:t,onEdit:X,onDelete:Y,onAddMoney:Z})},t.id)))}),e.jsxs(j,{open:S,onClose:()=>F(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(g,{children:E?"Modifier l'objectif":"Nouvel objectif d'épargne"}),e.jsx(m,{children:e.jsxs(t,{sx:{display:"flex",flexDirection:"column",gap:2,mt:1},children:[e.jsx(p,{label:"Titre de l'objectif",value:U.title,onChange:e=>q({...U,title:e.target.value}),fullWidth:!0}),e.jsx(p,{label:"Description",value:U.description,onChange:e=>q({...U,description:e.target.value}),fullWidth:!0,multiline:!0,rows:2}),e.jsx(p,{label:"Montant cible (TND)",type:"number",value:U.target,onChange:e=>q({...U,target:e.target.value}),fullWidth:!0}),e.jsx(p,{label:"Date d'échéance",type:"date",value:U.deadline,onChange:e=>q({...U,deadline:e.target.value}),fullWidth:!0,InputLabelProps:{shrink:!0}}),e.jsxs(y,{fullWidth:!0,children:[e.jsx(b,{children:"Catégorie"}),e.jsx(f,{value:U.category,label:"Catégorie",onChange:e=>q({...U,category:e.target.value}),children:L.map((t=>e.jsx(v,{value:t,children:t},t)))})]}),e.jsxs(y,{fullWidth:!0,children:[e.jsx(b,{children:"Priorité"}),e.jsxs(f,{value:U.priority,label:"Priorité",onChange:e=>q({...U,priority:e.target.value}),children:[e.jsx(v,{value:"low",children:"Basse"}),e.jsx(v,{value:"medium",children:"Moyenne"}),e.jsx(v,{value:"high",children:"Haute"})]})]}),e.jsx(p,{label:"Icône",value:U.icon,onChange:e=>q({...U,icon:e.target.value}),fullWidth:!0,helperText:"Choisissez un emoji pour représenter cet objectif"})]})}),e.jsxs(C,{children:[e.jsx(c,{onClick:()=>F(!1),children:"Annuler"}),e.jsx(c,{onClick:()=>{if(!U.title||!U.target||!U.deadline)return;const e={id:(null==E?void 0:E.id)||Date.now().toString(),title:U.title,description:U.description,target:parseFloat(U.target),current:(null==E?void 0:E.current)||0,deadline:U.deadline,category:U.category,priority:U.priority,icon:U.icon,color:(null==E?void 0:E.color)||"#4caf50"};D(E?A.map((t=>t.id===E.id?e:t)):[...A,e]),F(!1)},variant:"contained",children:E?"Modifier":"Créer"})]})]}),e.jsxs(j,{open:P,onClose:()=>O(!1),maxWidth:"xs",fullWidth:!0,children:[e.jsx(g,{children:"Ajouter de l'argent"}),e.jsx(m,{children:e.jsx(p,{label:"Montant à ajouter (TND)",type:"number",value:G,onChange:e=>H(e.target.value),fullWidth:!0,sx:{mt:1},autoFocus:!0})}),e.jsxs(C,{children:[e.jsx(c,{onClick:()=>O(!1),children:"Annuler"}),e.jsx(c,{onClick:()=>{const e=parseFloat(G);!e||e<=0||(D(A.map((t=>t.id===V?{...t,current:t.current+e}:t))),O(!1))},variant:"contained",children:"Ajouter"})]})]})]})]})};export{F as SavingsPage,F as default};
