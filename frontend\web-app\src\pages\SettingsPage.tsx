/**
 * Complete Settings Page
 * User preferences and application settings
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material'
import {
  Home as HomeIcon,
  Logout as LogoutIcon,
  Notifications as NotificationsIcon,
  Security as SecurityIcon,
  Language as LanguageIcon,
  Palette as PaletteIcon,
  AccountCircle as AccountIcon,
  Save as SaveIcon,
  Delete as DeleteIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

export const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)

  // Settings state
  const [settings, setSettings] = useState({
    notifications: {
      email: true,
      push: true,
      budgetAlerts: true,
      goalReminders: true,
      weeklyReports: false
    },
    preferences: {
      language: 'fr',
      currency: 'TND',
      theme: 'light',
      dateFormat: 'DD/MM/YYYY'
    },
    security: {
      twoFactor: false,
      biometric: true,
      sessionTimeout: 30
    },
    profile: {
      firstName: 'Ahmed',
      lastName: 'Ben Ali',
      email: '<EMAIL>',
      phone: '+216 20 123 456'
    }
  })

  const [showSaveAlert, setShowSaveAlert] = useState(false)

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const handleSettingChange = (category: string, setting: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category as keyof typeof prev],
        [setting]: value
      }
    }))
  }

  const handleSaveSettings = () => {
    // Mock save functionality
    setShowSaveAlert(true)
    setTimeout(() => setShowSaveAlert(false), 3000)
  }

  const handleDeleteAccount = () => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer votre compte ? Cette action est irréversible.')) {
      alert('Compte supprimé (simulation)')
      logout()
      navigate(ROUTES.HOME)
    }
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Paramètres
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Paramètres
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Personnalisez votre expérience Nouri
          </Typography>
        </Box>

        {showSaveAlert && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Paramètres sauvegardés avec succès !
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Profile Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AccountIcon />
                  Profil
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                  <Avatar sx={{ width: 64, height: 64, bgcolor: 'primary.main' }}>
                    {settings.profile.firstName[0]}{settings.profile.lastName[0]}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {settings.profile.firstName} {settings.profile.lastName}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {settings.profile.email}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <TextField
                    label="Prénom"
                    value={settings.profile.firstName}
                    onChange={(e) => handleSettingChange('profile', 'firstName', e.target.value)}
                    fullWidth
                  />
                  <TextField
                    label="Nom"
                    value={settings.profile.lastName}
                    onChange={(e) => handleSettingChange('profile', 'lastName', e.target.value)}
                    fullWidth
                  />
                  <TextField
                    label="Email"
                    type="email"
                    value={settings.profile.email}
                    onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
                    fullWidth
                  />
                  <TextField
                    label="Téléphone"
                    value={settings.profile.phone}
                    onChange={(e) => handleSettingChange('profile', 'phone', e.target.value)}
                    fullWidth
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Notification Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <NotificationsIcon />
                  Notifications
                </Typography>

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Notifications email"
                      secondary="Recevoir des emails de notification"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.email}
                        onChange={(e) => handleSettingChange('notifications', 'email', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Notifications push"
                      secondary="Recevoir des notifications sur l'appareil"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.push}
                        onChange={(e) => handleSettingChange('notifications', 'push', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Alertes budget"
                      secondary="Être alerté en cas de dépassement"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.budgetAlerts}
                        onChange={(e) => handleSettingChange('notifications', 'budgetAlerts', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Rappels objectifs"
                      secondary="Rappels pour vos objectifs d'épargne"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.goalReminders}
                        onChange={(e) => handleSettingChange('notifications', 'goalReminders', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <NotificationsIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Rapports hebdomadaires"
                      secondary="Recevoir un résumé chaque semaine"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.notifications.weeklyReports}
                        onChange={(e) => handleSettingChange('notifications', 'weeklyReports', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Preferences */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PaletteIcon />
                  Préférences
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControl fullWidth>
                    <InputLabel>Langue</InputLabel>
                    <Select
                      value={settings.preferences.language}
                      label="Langue"
                      onChange={(e) => handleSettingChange('preferences', 'language', e.target.value)}
                    >
                      <MenuItem value="fr">Français</MenuItem>
                      <MenuItem value="ar">العربية</MenuItem>
                      <MenuItem value="en">English</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Devise</InputLabel>
                    <Select
                      value={settings.preferences.currency}
                      label="Devise"
                      onChange={(e) => handleSettingChange('preferences', 'currency', e.target.value)}
                    >
                      <MenuItem value="TND">Dinar Tunisien (TND)</MenuItem>
                      <MenuItem value="EUR">Euro (EUR)</MenuItem>
                      <MenuItem value="USD">Dollar US (USD)</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Thème</InputLabel>
                    <Select
                      value={settings.preferences.theme}
                      label="Thème"
                      onChange={(e) => handleSettingChange('preferences', 'theme', e.target.value)}
                    >
                      <MenuItem value="light">Clair</MenuItem>
                      <MenuItem value="dark">Sombre</MenuItem>
                      <MenuItem value="auto">Automatique</MenuItem>
                    </Select>
                  </FormControl>

                  <FormControl fullWidth>
                    <InputLabel>Format de date</InputLabel>
                    <Select
                      value={settings.preferences.dateFormat}
                      label="Format de date"
                      onChange={(e) => handleSettingChange('preferences', 'dateFormat', e.target.value)}
                    >
                      <MenuItem value="DD/MM/YYYY">DD/MM/YYYY</MenuItem>
                      <MenuItem value="MM/DD/YYYY">MM/DD/YYYY</MenuItem>
                      <MenuItem value="YYYY-MM-DD">YYYY-MM-DD</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Security Settings */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SecurityIcon />
                  Sécurité
                </Typography>

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <SecurityIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Authentification à deux facteurs"
                      secondary="Sécurité renforcée pour votre compte"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.security.twoFactor}
                        onChange={(e) => handleSettingChange('security', 'twoFactor', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <SecurityIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Authentification biométrique"
                      secondary="Utiliser empreinte/Face ID"
                    />
                    <ListItemSecondaryAction>
                      <Switch
                        checked={settings.security.biometric}
                        onChange={(e) => handleSettingChange('security', 'biometric', e.target.checked)}
                      />
                    </ListItemSecondaryAction>
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <FormControl fullWidth>
                  <InputLabel>Délai d'expiration de session</InputLabel>
                  <Select
                    value={settings.security.sessionTimeout}
                    label="Délai d'expiration de session"
                    onChange={(e) => handleSettingChange('security', 'sessionTimeout', e.target.value)}
                  >
                    <MenuItem value={15}>15 minutes</MenuItem>
                    <MenuItem value={30}>30 minutes</MenuItem>
                    <MenuItem value={60}>1 heure</MenuItem>
                    <MenuItem value={120}>2 heures</MenuItem>
                  </Select>
                </FormControl>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Action Buttons */}
        <Box sx={{ mt: 4, display: 'flex', gap: 2, justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={handleDeleteAccount}
          >
            Supprimer le compte
          </Button>

          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSaveSettings}
            size="large"
          >
            Sauvegarder les paramètres
          </Button>
        </Box>
      </Container>
    </Box>
  )
}

export default SettingsPage
