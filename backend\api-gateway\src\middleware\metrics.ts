/**
 * Middleware de métriques Prometheus pour Nouri
 * Collecte des métriques de performance et d'utilisation
 */

import { Request, Response, NextFunction } from 'express';
import promClient from 'prom-client';
import { config } from '../config/config';
import { logger } from '../utils/logger';

// Configuration du registre Prometheus
const register = new promClient.Registry();

// Métriques par défaut de Node.js
promClient.collectDefaultMetrics({
  register,
  prefix: 'nouri_api_gateway_',
});

// Métriques personnalisées
const httpRequestDuration = new promClient.Histogram({
  name: 'nouri_http_request_duration_seconds',
  help: 'Durée des requêtes HTTP en secondes',
  labelNames: ['method', 'route', 'status_code', 'user_id'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestTotal = new promClient.Counter({
  name: 'nouri_http_requests_total',
  help: 'Nombre total de requêtes HTTP',
  labelNames: ['method', 'route', 'status_code', 'user_id']
});

const httpRequestSize = new promClient.Histogram({
  name: 'nouri_http_request_size_bytes',
  help: 'Taille des requêtes HTTP en bytes',
  labelNames: ['method', 'route'],
  buckets: [100, 1000, 10000, 100000, 1000000]
});

const httpResponseSize = new promClient.Histogram({
  name: 'nouri_http_response_size_bytes',
  help: 'Taille des réponses HTTP en bytes',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [100, 1000, 10000, 100000, 1000000]
});

const activeConnections = new promClient.Gauge({
  name: 'nouri_active_connections',
  help: 'Nombre de connexions actives'
});

const databaseConnections = new promClient.Gauge({
  name: 'nouri_database_connections',
  help: 'Nombre de connexions à la base de données'
});

const redisConnections = new promClient.Gauge({
  name: 'nouri_redis_connections',
  help: 'Nombre de connexions Redis'
});

const authenticationAttempts = new promClient.Counter({
  name: 'nouri_authentication_attempts_total',
  help: 'Nombre total de tentatives d\'authentification',
  labelNames: ['status', 'method']
});

const businessEvents = new promClient.Counter({
  name: 'nouri_business_events_total',
  help: 'Nombre total d\'événements business',
  labelNames: ['event_type', 'user_id']
});

const errorRate = new promClient.Counter({
  name: 'nouri_errors_total',
  help: 'Nombre total d\'erreurs',
  labelNames: ['error_type', 'status_code', 'route']
});

// Enregistrer les métriques
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestTotal);
register.registerMetric(httpRequestSize);
register.registerMetric(httpResponseSize);
register.registerMetric(activeConnections);
register.registerMetric(databaseConnections);
register.registerMetric(redisConnections);
register.registerMetric(authenticationAttempts);
register.registerMetric(businessEvents);
register.registerMetric(errorRate);

/**
 * Middleware principal de collecte de métriques
 */
export const metricsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  if (!config.monitoring.enableMetrics) {
    return next();
  }

  const startTime = Date.now();
  const startHrTime = process.hrtime();

  // Incrémenter les connexions actives
  activeConnections.inc();

  // Mesurer la taille de la requête
  const requestSize = parseInt(req.get('content-length') || '0', 10);
  if (requestSize > 0) {
    httpRequestSize
      .labels(req.method, getRoutePattern(req))
      .observe(requestSize);
  }

  // Hook sur la fin de la réponse
  res.on('finish', () => {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    const hrDuration = process.hrtime(startHrTime);
    const durationInSeconds = hrDuration[0] + hrDuration[1] / 1e9;

    const route = getRoutePattern(req);
    const userId = req.user?.id || 'anonymous';
    const statusCode = res.statusCode.toString();

    // Métriques de durée
    httpRequestDuration
      .labels(req.method, route, statusCode, userId)
      .observe(durationInSeconds);

    // Métriques de comptage
    httpRequestTotal
      .labels(req.method, route, statusCode, userId)
      .inc();

    // Métriques de taille de réponse
    const responseSize = parseInt(res.get('content-length') || '0', 10);
    if (responseSize > 0) {
      httpResponseSize
        .labels(req.method, route, statusCode)
        .observe(responseSize);
    }

    // Métriques d'erreur
    if (res.statusCode >= 400) {
      const errorType = res.statusCode >= 500 ? 'server_error' : 'client_error';
      errorRate
        .labels(errorType, statusCode, route)
        .inc();
    }

    // Décrémenter les connexions actives
    activeConnections.dec();

    // Log des requêtes lentes
    if (duration > 1) {
      logger.warn('Requête lente détectée', {
        method: req.method,
        url: req.originalUrl,
        duration: `${duration}s`,
        statusCode: res.statusCode,
        userId
      });
    }
  });

  next();
};

/**
 * Fonction utilitaire pour extraire le pattern de route
 */
const getRoutePattern = (req: Request): string => {
  // Extraire le pattern de route pour éviter la cardinalité élevée
  const path = req.route?.path || req.path;
  
  // Remplacer les IDs par des placeholders
  return path
    .replace(/\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '/:id')
    .replace(/\/\d+/g, '/:id')
    .replace(/\/[a-zA-Z0-9_-]{20,}/g, '/:token');
};

/**
 * Fonction pour enregistrer les événements d'authentification
 */
export const recordAuthenticationAttempt = (status: 'success' | 'failure', method: string): void => {
  authenticationAttempts
    .labels(status, method)
    .inc();
};

/**
 * Fonction pour enregistrer les événements business
 */
export const recordBusinessEvent = (eventType: string, userId: string): void => {
  businessEvents
    .labels(eventType, userId)
    .inc();
};

/**
 * Fonction pour mettre à jour les métriques de connexions DB
 */
export const updateDatabaseConnections = (count: number): void => {
  databaseConnections.set(count);
};

/**
 * Fonction pour mettre à jour les métriques de connexions Redis
 */
export const updateRedisConnections = (count: number): void => {
  redisConnections.set(count);
};

/**
 * Route pour exposer les métriques Prometheus
 */
export const getMetrics = async (): Promise<string> => {
  return register.metrics();
};

/**
 * Fonction pour réinitialiser les métriques (utile pour les tests)
 */
export const resetMetrics = (): void => {
  register.resetMetrics();
};

/**
 * Métriques personnalisées pour les services spécifiques
 */
export const serviceMetrics = {
  // Métriques bancaires
  bankingSync: new promClient.Counter({
    name: 'nouri_banking_sync_total',
    help: 'Nombre total de synchronisations bancaires',
    labelNames: ['status', 'bank', 'user_id']
  }),

  // Métriques de transactions
  transactionProcessed: new promClient.Counter({
    name: 'nouri_transactions_processed_total',
    help: 'Nombre total de transactions traitées',
    labelNames: ['category', 'type', 'user_id']
  }),

  // Métriques de budget
  budgetCreated: new promClient.Counter({
    name: 'nouri_budgets_created_total',
    help: 'Nombre total de budgets créés',
    labelNames: ['type', 'user_id']
  }),

  // Métriques de chatbot
  chatbotInteractions: new promClient.Counter({
    name: 'nouri_chatbot_interactions_total',
    help: 'Nombre total d\'interactions avec le chatbot',
    labelNames: ['language', 'intent', 'user_id']
  }),

  // Métriques d'éducation
  educationModuleCompleted: new promClient.Counter({
    name: 'nouri_education_modules_completed_total',
    help: 'Nombre total de modules éducatifs terminés',
    labelNames: ['module_id', 'difficulty', 'user_id']
  })
};

// Enregistrer les métriques de service
Object.values(serviceMetrics).forEach(metric => {
  register.registerMetric(metric);
});

/**
 * Middleware pour collecter les métriques de santé système
 */
export const collectSystemMetrics = (): void => {
  setInterval(() => {
    try {
      // Métriques de mémoire
      const memUsage = process.memoryUsage();
      
      const memoryUsage = new promClient.Gauge({
        name: 'nouri_memory_usage_bytes',
        help: 'Utilisation mémoire en bytes',
        labelNames: ['type']
      });

      memoryUsage.labels('heap_used').set(memUsage.heapUsed);
      memoryUsage.labels('heap_total').set(memUsage.heapTotal);
      memoryUsage.labels('external').set(memUsage.external);
      memoryUsage.labels('rss').set(memUsage.rss);

      register.registerMetric(memoryUsage);

      // Métriques CPU
      const cpuUsage = process.cpuUsage();
      
      const cpuMetric = new promClient.Gauge({
        name: 'nouri_cpu_usage_microseconds',
        help: 'Utilisation CPU en microsecondes',
        labelNames: ['type']
      });

      cpuMetric.labels('user').set(cpuUsage.user);
      cpuMetric.labels('system').set(cpuUsage.system);

      register.registerMetric(cpuMetric);

    } catch (error) {
      logger.error('Erreur lors de la collecte des métriques système:', error);
    }
  }, 30000); // Toutes les 30 secondes
};

// Démarrer la collecte des métriques système si activée
if (config.monitoring.enableMetrics) {
  collectSystemMetrics();
}

export { register };
