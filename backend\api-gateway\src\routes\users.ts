/**
 * Routes de gestion des utilisateurs pour Nouri
 * Profil, préférences, et paramètres utilisateur
 */

import { Router, Request, Response } from 'express';
import { body, param, validationResult } from 'express-validator';
import { getPrismaClient } from '../config/database';
import { logger, logBusinessEvent } from '../utils/logger';
import { requirePermission } from '../middleware/auth';

const router = Router();
const prisma = getPrismaClient();

/**
 * GET /api/users/profile
 * Récupération du profil utilisateur complet
 */
router.get('/profile', async (req: Request, res: Response) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      include: {
        accounts: {
          select: {
            id: true,
            accountName: true,
            accountType: true,
            balance: true,
            currency: true,
            bankName: true,
            isActive: true,
            lastSyncAt: true
          }
        },
        budgets: {
          where: { isActive: true },
          select: {
            id: true,
            name: true,
            totalAmount: true,
            spentAmount: true,
            currency: true,
            startDate: true,
            endDate: true
          }
        },
        savingsGoals: {
          where: { isCompleted: false },
          select: {
            id: true,
            name: true,
            targetAmount: true,
            currentAmount: true,
            currency: true,
            targetDate: true,
            category: true,
            imageUrl: true
          }
        },
        _count: {
          select: {
            transactions: true,
            notifications: {
              where: { isRead: false }
            }
          }
        }
      }
    });

    if (!user) {
      return res.status(404).json({
        error: 'Utilisateur non trouvé',
        code: 'USER_NOT_FOUND'
      });
    }

    // Calculer quelques statistiques
    const totalBalance = user.accounts.reduce((sum, account) => 
      sum + parseFloat(account.balance.toString()), 0
    );

    const totalSavingsTarget = user.savingsGoals.reduce((sum, goal) => 
      sum + parseFloat(goal.targetAmount.toString()), 0
    );

    const totalSavingsCurrent = user.savingsGoals.reduce((sum, goal) => 
      sum + parseFloat(goal.currentAmount.toString()), 0
    );

    res.json({
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        phoneNumber: user.phoneNumber,
        dateOfBirth: user.dateOfBirth,
        profilePicture: user.profilePicture,
        language: user.language,
        currency: user.currency,
        timezone: user.timezone,
        emailNotifications: user.emailNotifications,
        pushNotifications: user.pushNotifications,
        smsNotifications: user.smsNotifications,
        onboardingCompleted: user.onboardingCompleted,
        kycStatus: user.kycStatus,
        riskProfile: user.riskProfile,
        createdAt: user.createdAt,
        lastLoginAt: user.lastLoginAt
      },
      accounts: user.accounts,
      budgets: user.budgets,
      savingsGoals: user.savingsGoals,
      statistics: {
        totalBalance,
        totalSavingsTarget,
        totalSavingsCurrent,
        savingsProgress: totalSavingsTarget > 0 ? (totalSavingsCurrent / totalSavingsTarget) * 100 : 0,
        totalTransactions: user._count.transactions,
        unreadNotifications: user._count.notifications
      }
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du profil:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * PUT /api/users/profile
 * Mise à jour du profil utilisateur
 */
router.put('/profile', [
  body('firstName').optional().trim().isLength({ min: 2 }).withMessage('Le prénom doit contenir au moins 2 caractères'),
  body('lastName').optional().trim().isLength({ min: 2 }).withMessage('Le nom doit contenir au moins 2 caractères'),
  body('phoneNumber').optional().isMobilePhone('any').withMessage('Numéro de téléphone invalide'),
  body('dateOfBirth').optional().isISO8601().withMessage('Date de naissance invalide'),
  body('language').optional().isIn(['ar', 'fr', 'en']).withMessage('Langue non supportée'),
  body('currency').optional().isIn(['TND', 'EUR', 'USD']).withMessage('Devise non supportée'),
  body('timezone').optional().isString().withMessage('Fuseau horaire invalide'),
], async (req: Request, res: Response) => {
  try {
    // Validation des données
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array(),
        code: 'VALIDATION_ERROR'
      });
    }

    const {
      firstName,
      lastName,
      phoneNumber,
      dateOfBirth,
      language,
      currency,
      timezone
    } = req.body;

    // Préparer les données à mettre à jour
    const updateData: any = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (phoneNumber !== undefined) updateData.phoneNumber = phoneNumber;
    if (dateOfBirth !== undefined) updateData.dateOfBirth = new Date(dateOfBirth);
    if (language !== undefined) updateData.language = language;
    if (currency !== undefined) updateData.currency = currency;
    if (timezone !== undefined) updateData.timezone = timezone;

    // Mettre à jour l'utilisateur
    const updatedUser = await prisma.user.update({
      where: { id: req.user!.id },
      data: updateData,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        dateOfBirth: true,
        language: true,
        currency: true,
        timezone: true,
        updatedAt: true
      }
    });

    // Log de l'événement business
    logBusinessEvent('user_profile_updated', req.user!.id, {
      updatedFields: Object.keys(updateData)
    });

    res.json({
      message: 'Profil mis à jour avec succès',
      user: updatedUser
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour du profil:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * PUT /api/users/notifications
 * Mise à jour des préférences de notification
 */
router.put('/notifications', [
  body('emailNotifications').optional().isBoolean().withMessage('emailNotifications doit être un booléen'),
  body('pushNotifications').optional().isBoolean().withMessage('pushNotifications doit être un booléen'),
  body('smsNotifications').optional().isBoolean().withMessage('smsNotifications doit être un booléen'),
], async (req: Request, res: Response) => {
  try {
    // Validation des données
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array(),
        code: 'VALIDATION_ERROR'
      });
    }

    const {
      emailNotifications,
      pushNotifications,
      smsNotifications
    } = req.body;

    // Préparer les données à mettre à jour
    const updateData: any = {};
    if (emailNotifications !== undefined) updateData.emailNotifications = emailNotifications;
    if (pushNotifications !== undefined) updateData.pushNotifications = pushNotifications;
    if (smsNotifications !== undefined) updateData.smsNotifications = smsNotifications;

    // Mettre à jour les préférences
    const updatedUser = await prisma.user.update({
      where: { id: req.user!.id },
      data: updateData,
      select: {
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: true,
        updatedAt: true
      }
    });

    // Log de l'événement business
    logBusinessEvent('user_notification_preferences_updated', req.user!.id, {
      preferences: updateData
    });

    res.json({
      message: 'Préférences de notification mises à jour avec succès',
      preferences: updatedUser
    });

  } catch (error) {
    logger.error('Erreur lors de la mise à jour des préférences:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * POST /api/users/complete-onboarding
 * Marquer l'onboarding comme terminé
 */
router.post('/complete-onboarding', async (req: Request, res: Response) => {
  try {
    const updatedUser = await prisma.user.update({
      where: { id: req.user!.id },
      data: { onboardingCompleted: true },
      select: {
        id: true,
        onboardingCompleted: true,
        updatedAt: true
      }
    });

    // Log de l'événement business
    logBusinessEvent('user_onboarding_completed', req.user!.id, {
      completedAt: new Date().toISOString()
    });

    res.json({
      message: 'Onboarding terminé avec succès',
      user: updatedUser
    });

  } catch (error) {
    logger.error('Erreur lors de la finalisation de l\'onboarding:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * GET /api/users/dashboard
 * Données du tableau de bord utilisateur
 */
router.get('/dashboard', async (req: Request, res: Response) => {
  try {
    const userId = req.user!.id;

    // Récupérer les données en parallèle
    const [
      accounts,
      recentTransactions,
      activeBudgets,
      savingsGoals,
      unreadNotifications
    ] = await Promise.all([
      // Comptes
      prisma.account.findMany({
        where: { userId, isActive: true },
        select: {
          id: true,
          accountName: true,
          accountType: true,
          balance: true,
          currency: true,
          bankName: true
        }
      }),
      
      // Transactions récentes (7 derniers jours)
      prisma.transaction.findMany({
        where: {
          userId,
          transactionDate: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        },
        orderBy: { transactionDate: 'desc' },
        take: 10,
        select: {
          id: true,
          amount: true,
          currency: true,
          description: true,
          category: true,
          transactionDate: true,
          transactionType: true
        }
      }),
      
      // Budgets actifs
      prisma.budget.findMany({
        where: { userId, isActive: true },
        select: {
          id: true,
          name: true,
          totalAmount: true,
          spentAmount: true,
          currency: true,
          endDate: true
        }
      }),
      
      // Objectifs d'épargne
      prisma.savingsGoal.findMany({
        where: { userId, isCompleted: false },
        select: {
          id: true,
          name: true,
          targetAmount: true,
          currentAmount: true,
          currency: true,
          targetDate: true,
          category: true
        }
      }),
      
      // Notifications non lues
      prisma.notification.count({
        where: { userId, isRead: false }
      })
    ]);

    // Calculer les statistiques
    const totalBalance = accounts.reduce((sum, account) => 
      sum + parseFloat(account.balance.toString()), 0
    );

    const weeklySpending = recentTransactions
      .filter(t => t.transactionType === 'debit')
      .reduce((sum, t) => sum + parseFloat(t.amount.toString()), 0);

    const budgetUtilization = activeBudgets.map(budget => ({
      ...budget,
      utilizationPercent: parseFloat(budget.totalAmount.toString()) > 0 
        ? (parseFloat(budget.spentAmount.toString()) / parseFloat(budget.totalAmount.toString())) * 100 
        : 0
    }));

    res.json({
      summary: {
        totalBalance,
        weeklySpending,
        unreadNotifications,
        activeAccounts: accounts.length,
        activeBudgets: activeBudgets.length,
        activeSavingsGoals: savingsGoals.length
      },
      accounts,
      recentTransactions,
      budgets: budgetUtilization,
      savingsGoals
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du tableau de bord:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * DELETE /api/users/account
 * Suppression du compte utilisateur (GDPR)
 */
router.delete('/account', async (req: Request, res: Response) => {
  try {
    // Cette opération nécessite une confirmation supplémentaire
    // En production, on pourrait demander une confirmation par email
    
    await prisma.user.delete({
      where: { id: req.user!.id }
    });

    // Log de l'événement business
    logBusinessEvent('user_account_deleted', req.user!.id, {
      deletedAt: new Date().toISOString(),
      reason: 'user_request'
    });

    res.json({
      message: 'Compte supprimé avec succès'
    });

  } catch (error) {
    logger.error('Erreur lors de la suppression du compte:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

export default router;
