/**
 * Optimized Entry Point for Nouri Application
 * Implements performance optimizations and lazy loading
 */

import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

// Initialize performance monitoring
import './utils/performance'

// Performance monitoring (optional)
if (import.meta.env.DEV) {
  // Log performance metrics in development
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'navigation') {
        const navEntry = entry as PerformanceNavigationTiming
        console.log('🚀 Navigation Performance:', {
          domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
          loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
          firstContentfulPaint: navEntry.responseEnd - navEntry.requestStart
        })
      }
    }
  })
  observer.observe({ entryTypes: ['navigation'] })
}

// Render the application
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
)

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
)
