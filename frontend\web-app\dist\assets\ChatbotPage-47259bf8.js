import{j as e,B as s,o as i,p as t,b as n,I as r,R as a,H as o,q as l,d,G as x,g as c,l as u,$ as m,a0 as h,a1 as j,D as p,s as f,a2 as g,h as v,f as y,e as b}from"./mui-vendor-b761306f.js";import{u as D,r as w}from"./react-vendor-2f216e43.js";import{A as C,R as z}from"./index-bd55ea72.js";const A=["Bonjour ! Je suis Nouri, votre assistant financier IA. Comment puis-je vous aider aujourd'hui ?","D'après vos données, vous avez bien géré votre budget ce mois-ci. Félicitations !","Je recommande d'augmenter votre épargne de 50 TND ce mois pour atteindre votre objectif vacances.","Votre dépense en restaurants a augmenté de 15% ce mois. Voulez-vous que je vous aide à optimiser ce budget ?","Excellente question ! Basé sur vos habitudes, je suggère de créer un budget de 200 TND pour les loisirs.","Votre fonds d'urgence représente actuellement 3 mois de dépenses. L'objectif recommandé est de 6 mois.","Je peux vous aider à analyser vos dépenses par catégorie. Quelle période vous intéresse ?","Votre score financier s'améliore ! Continuez sur cette voie pour atteindre vos objectifs."],S=["Analyser mes dépenses","Conseils d'épargne","Optimiser mon budget","Objectifs financiers","Rapport mensuel","Conseils d'investissement"],B=()=>{const B=D(),{logout:R}=w.useContext(C),[I,N]=w.useState([{id:"1",text:"Bonjour ! Je suis Nouri, votre assistant financier IA. Comment puis-je vous aider aujourd'hui ?",sender:"bot",timestamp:new Date,type:"text"}]),[T,k]=w.useState(""),[M,G]=w.useState(!1),V=w.useRef(null);w.useEffect((()=>{var e;null==(e=V.current)||e.scrollIntoView({behavior:"smooth"})}),[I]);const W=e=>{const s=e||T.trim();if(!s)return;const i={id:Date.now().toString(),text:s,sender:"user",timestamp:new Date,type:"text"};N((e=>[...e,i])),k(""),G(!0),setTimeout((()=>{const e=A[Math.floor(Math.random()*A.length)],s={id:(Date.now()+1).toString(),text:e,sender:"bot",timestamp:new Date,type:"text"};N((e=>[...e,s])),G(!1)}),1e3+2e3*Math.random())};return e.jsxs(s,{sx:{flexGrow:1,height:"100vh",display:"flex",flexDirection:"column"},children:[e.jsx(i,{position:"static",elevation:1,children:e.jsxs(t,{children:[e.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Assistant IA"}),e.jsx(r,{color:"inherit",onClick:()=>{N([{id:"1",text:"Bonjour ! Je suis Nouri, votre assistant financier IA. Comment puis-je vous aider aujourd'hui ?",sender:"bot",timestamp:new Date,type:"text"}])},children:e.jsx(a,{})}),e.jsx(r,{color:"inherit",onClick:()=>B(z.DASHBOARD),children:e.jsx(o,{})}),e.jsx(r,{color:"inherit",onClick:()=>{R(),B(z.HOME)},children:e.jsx(l,{})})]})}),e.jsx(d,{maxWidth:"lg",sx:{flexGrow:1,display:"flex",flexDirection:"column",py:2},children:e.jsxs(x,{container:!0,spacing:2,sx:{flexGrow:1},children:[e.jsx(x,{item:!0,xs:12,md:8,children:e.jsxs(c,{sx:{height:"70vh",display:"flex",flexDirection:"column"},children:[e.jsxs(s,{sx:{flexGrow:1,overflow:"auto",p:2},children:[I.map((i=>{return e.jsx(s,{sx:{display:"flex",justifyContent:"user"===i.sender?"flex-end":"flex-start",mb:2},children:e.jsxs(s,{sx:{display:"flex",alignItems:"flex-start",gap:1,maxWidth:"70%",flexDirection:"user"===i.sender?"row-reverse":"row"},children:[e.jsx(u,{sx:{bgcolor:"user"===i.sender?"primary.main":"secondary.main",width:32,height:32},children:"user"===i.sender?e.jsx(m,{}):e.jsx(h,{})}),e.jsxs(j,{sx:{p:2,bgcolor:"user"===i.sender?"primary.main":"grey.100",color:"user"===i.sender?"white":"text.primary",borderRadius:2},children:[e.jsx(n,{variant:"body2",children:i.text}),e.jsx(n,{variant:"caption",sx:{opacity:.7,display:"block",mt:.5,textAlign:"user"===i.sender?"right":"left"},children:(t=i.timestamp,t.toLocaleTimeString("fr-FR",{hour:"2-digit",minute:"2-digit"}))})]})]})},i.id);var t})),M&&e.jsx(s,{sx:{display:"flex",justifyContent:"flex-start",mb:2},children:e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(u,{sx:{bgcolor:"secondary.main",width:32,height:32},children:e.jsx(h,{})}),e.jsx(j,{sx:{p:2,bgcolor:"grey.100",borderRadius:2},children:e.jsx(n,{variant:"body2",color:"text.secondary",children:"Nouri est en train d'écrire..."})})]})}),e.jsx("div",{ref:V})]}),e.jsx(p,{}),e.jsx(s,{sx:{p:2},children:e.jsxs(s,{sx:{display:"flex",gap:1},children:[e.jsx(f,{fullWidth:!0,multiline:!0,maxRows:3,placeholder:"Tapez votre message...",value:T,onChange:e=>k(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),W())},disabled:M}),e.jsx(r,{color:"primary",onClick:()=>W(),disabled:!T.trim()||M,sx:{alignSelf:"flex-end"},children:e.jsx(g,{})})]})})]})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsx(c,{children:e.jsxs(v,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Suggestions rapides"}),e.jsx(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:S.map(((s,i)=>e.jsx(y,{variant:"outlined",size:"small",onClick:()=>W(s),disabled:M,sx:{justifyContent:"flex-start"},children:s},i)))})]})}),e.jsx(c,{children:e.jsxs(v,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Mes capacités"}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:[e.jsx(b,{label:"📊 Analyse des dépenses",variant:"outlined",size:"small"}),e.jsx(b,{label:"💰 Conseils d'épargne",variant:"outlined",size:"small"}),e.jsx(b,{label:"📈 Planification budgétaire",variant:"outlined",size:"small"}),e.jsx(b,{label:"🎯 Objectifs financiers",variant:"outlined",size:"small"}),e.jsx(b,{label:"📋 Rapports personnalisés",variant:"outlined",size:"small"}),e.jsx(b,{label:"⚠️ Alertes intelligentes",variant:"outlined",size:"small"})]})]})}),e.jsx(c,{children:e.jsxs(v,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"💡 Conseil du jour"}),e.jsx(n,{variant:"body2",color:"text.secondary",children:"Saviez-vous que mettre de côté seulement 10 TND par jour peut vous faire économiser plus de 3600 TND par an ? Commencez petit et augmentez progressivement !"})]})}),e.jsx(c,{children:e.jsxs(v,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"📈 Vos statistiques"}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(n,{variant:"body2",children:"Messages échangés"}),e.jsx(n,{variant:"body2",fontWeight:"bold",children:I.length})]}),e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(n,{variant:"body2",children:"Conseils reçus"}),e.jsx(n,{variant:"body2",fontWeight:"bold",children:I.filter((e=>"bot"===e.sender)).length})]}),e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between"},children:[e.jsx(n,{variant:"body2",children:"Session active"}),e.jsxs(n,{variant:"body2",fontWeight:"bold",children:[Math.floor((Date.now()-I[0].timestamp.getTime())/6e4)," min"]})]})]})]})})]})})]})})]})};export{B as ChatbotPage,B as default};
