import{j as e,B as t,o as n,p as s,b as r,I as a,H as i,q as o,d as c,g as l,h as d,s as x,t as h,u,F as p,v as j,w as m,M as g,f as y,x as f,y as v,z as C,E as b,J as W,K as w,N as T,l as D,e as S,P as A,O as F,Q as R}from"./mui-vendor-b761306f.js";import{u as I,r as N}from"./react-vendor-2f216e43.js";import{A as E,R as L}from"./index-bd55ea72.js";const M=[{id:"1",date:"2024-01-15",description:"Salaire Janvier",amount:1800,type:"income",category:"Salaire",account:"Compte Courant"},{id:"2",date:"2024-01-14",description:"Courses Carrefour",amount:-85.5,type:"expense",category:"Alimentation",account:"Compte Courant"},{id:"3",date:"2024-01-13",description:"Facture STEG",amount:-120,type:"expense",category:"Utilities",account:"Compte Courant"},{id:"4",date:"2024-01-12",description:"Freelance Web",amount:450,type:"income",category:"Freelance",account:"Compte Courant"},{id:"5",date:"2024-01-11",description:"Restaurant",amount:-45,type:"expense",category:"Restaurants",account:"Carte de Crédit"},{id:"6",date:"2024-01-10",description:"Essence",amount:-60,type:"expense",category:"Transport",account:"Compte Courant"},{id:"7",date:"2024-01-09",description:"Virement Épargne",amount:-200,type:"transfer",category:"Épargne",account:"Compte Courant"},{id:"8",date:"2024-01-08",description:"Pharmacie",amount:-25.5,type:"expense",category:"Santé",account:"Compte Courant"},{id:"9",date:"2024-01-07",description:"Abonnement Netflix",amount:-15.99,type:"expense",category:"Divertissement",account:"Carte de Crédit"},{id:"10",date:"2024-01-06",description:"Vente en ligne",amount:120,type:"income",category:"Vente",account:"Compte Courant"}],z=()=>{const z=I(),{logout:V}=N.useContext(E),[B,G]=N.useState(""),[H,O]=N.useState("all"),[P,k]=N.useState("all"),[J,$]=N.useState(1),q=M.filter((e=>{const t=e.description.toLowerCase().includes(B.toLowerCase())||e.category.toLowerCase().includes(B.toLowerCase()),n="all"===H||e.type===H,s="all"===P||e.category===P;return t&&n&&s})),K=q.slice(10*(J-1),10*J),Q=Math.ceil(q.length/10),U=t=>{switch(t){case"income":return e.jsx(F,{sx:{color:"success.main"}});case"expense":return e.jsx(R,{sx:{color:"error.main"}});default:return e.jsx(F,{sx:{color:"info.main"}})}},X=e=>{switch(e){case"income":return"success.main";case"expense":return"error.main";default:return"info.main"}},Y=[...new Set(M.map((e=>e.category)))];return e.jsxs(t,{sx:{flexGrow:1},children:[e.jsx(n,{position:"static",elevation:1,children:e.jsxs(s,{children:[e.jsx(r,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Transactions"}),e.jsx(a,{color:"inherit",onClick:()=>z(L.DASHBOARD),children:e.jsx(i,{})}),e.jsx(a,{color:"inherit",onClick:()=>{V(),z(L.HOME)},children:e.jsx(o,{})})]})}),e.jsxs(c,{maxWidth:"xl",sx:{py:4},children:[e.jsxs(t,{sx:{mb:4},children:[e.jsx(r,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Mes Transactions"}),e.jsx(r,{variant:"body1",color:"text.secondary",children:"Consultez et gérez toutes vos transactions financières"})]}),e.jsx(l,{sx:{mb:3},children:e.jsx(d,{children:e.jsxs(t,{sx:{display:"flex",gap:2,flexWrap:"wrap",alignItems:"center"},children:[e.jsx(x,{placeholder:"Rechercher une transaction...",value:B,onChange:e=>G(e.target.value),InputProps:{startAdornment:e.jsx(h,{position:"start",children:e.jsx(u,{})})},sx:{minWidth:300}}),e.jsxs(p,{sx:{minWidth:150},children:[e.jsx(j,{children:"Type"}),e.jsxs(m,{value:H,label:"Type",onChange:e=>O(e.target.value),children:[e.jsx(g,{value:"all",children:"Tous"}),e.jsx(g,{value:"income",children:"Revenus"}),e.jsx(g,{value:"expense",children:"Dépenses"}),e.jsx(g,{value:"transfer",children:"Virements"})]})]}),e.jsxs(p,{sx:{minWidth:150},children:[e.jsx(j,{children:"Catégorie"}),e.jsxs(m,{value:P,label:"Catégorie",onChange:e=>k(e.target.value),children:[e.jsx(g,{value:"all",children:"Toutes"}),Y.map((t=>e.jsx(g,{value:t,children:t},t)))]})]}),e.jsx(y,{variant:"outlined",startIcon:e.jsx(f,{}),sx:{ml:"auto"},children:"Exporter"})]})})}),e.jsxs(l,{children:[e.jsx(v,{children:e.jsxs(C,{children:[e.jsx(b,{children:e.jsxs(W,{children:[e.jsx(w,{children:"Date"}),e.jsx(w,{children:"Description"}),e.jsx(w,{children:"Catégorie"}),e.jsx(w,{children:"Compte"}),e.jsx(w,{align:"right",children:"Montant"})]})}),e.jsx(T,{children:K.map((n=>{return e.jsxs(W,{hover:!0,children:[e.jsx(w,{children:e.jsx(r,{variant:"body2",children:new Date(n.date).toLocaleDateString("fr-FR")})}),e.jsx(w,{children:e.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(D,{sx:{width:32,height:32,bgcolor:"grey.100"},children:U(n.type)}),e.jsx(r,{variant:"body2",fontWeight:"medium",children:n.description})]})}),e.jsx(w,{children:e.jsx(S,{label:n.category,size:"small",variant:"outlined",color:"income"===n.type?"success":"expense"===n.type?"error":"info"})}),e.jsx(w,{children:e.jsx(r,{variant:"body2",color:"text.secondary",children:n.account})}),e.jsx(w,{align:"right",children:e.jsx(r,{variant:"body2",fontWeight:"bold",color:X(n.type),children:(s=n.amount,`${s>=0?"+":""}${s.toFixed(2)} TND`)})})]},n.id);var s}))})]})}),Q>1&&e.jsx(t,{sx:{display:"flex",justifyContent:"center",p:2},children:e.jsx(A,{count:Q,page:J,onChange:(e,t)=>$(t),color:"primary"})})]}),e.jsxs(t,{sx:{mt:3,display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(l,{sx:{flex:1,minWidth:200},children:e.jsxs(d,{sx:{textAlign:"center"},children:[e.jsxs(r,{variant:"h6",color:"success.main",fontWeight:"bold",children:["+",M.filter((e=>"income"===e.type)).reduce(((e,t)=>e+t.amount),0).toFixed(2)," TND"]}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Total Revenus"})]})}),e.jsx(l,{sx:{flex:1,minWidth:200},children:e.jsxs(d,{sx:{textAlign:"center"},children:[e.jsxs(r,{variant:"h6",color:"error.main",fontWeight:"bold",children:[M.filter((e=>"expense"===e.type)).reduce(((e,t)=>e+t.amount),0).toFixed(2)," TND"]}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Total Dépenses"})]})}),e.jsx(l,{sx:{flex:1,minWidth:200},children:e.jsxs(d,{sx:{textAlign:"center"},children:[e.jsx(r,{variant:"h6",color:"primary.main",fontWeight:"bold",children:q.length}),e.jsx(r,{variant:"body2",color:"text.secondary",children:"Transactions Affichées"})]})})]})]})]})};export{z as TransactionsPage,z as default};
