import{j as e,B as r,d as n,g as s,h as i,b as t,f as o,D as a}from"./mui-vendor-b761306f.js";import{u as l,r as c}from"./react-vendor-2f216e43.js";import{A as d,R as x}from"./index-bd55ea72.js";const h=()=>{const h=l(),{login:m}=c.useContext(d);return e.jsx(r,{sx:{minHeight:"100vh",background:"linear-gradient(135deg, #1976d2 0%, #1565c0 100%)",display:"flex",alignItems:"center",justifyContent:"center",p:2},children:e.jsx(n,{maxWidth:"sm",children:e.jsx(s,{sx:{borderRadius:3,boxShadow:3},children:e.jsxs(i,{sx:{p:4},children:[e.jsxs(r,{sx:{textAlign:"center",mb:4},children:[e.jsx(t,{variant:"h3",component:"h1",sx:{color:"primary.main",fontWeight:"bold",mb:1},children:"🇹🇳 Nouri"}),e.jsx(t,{variant:"h4",gutterBottom:!0,children:"Inscription"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Rejoignez des milliers d'utilisateurs qui gèrent leurs finances avec Nouri"})]}),e.jsxs(r,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsx(o,{variant:"contained",size:"large",fullWidth:!0,sx:{py:1.5,fontSize:"1.1rem"},onClick:()=>{m(),h(x.DASHBOARD)},children:"🚀 Créer un compte (Demo)"}),e.jsx(a,{sx:{my:2},children:e.jsx(t,{variant:"body2",color:"text.secondary",children:"ou"})}),e.jsx(o,{variant:"outlined",size:"large",fullWidth:!0,sx:{py:1.5},onClick:()=>h(x.LOGIN),children:"J'ai déjà un compte"})]}),e.jsx(r,{sx:{textAlign:"center",mt:4},children:e.jsx(o,{variant:"text",onClick:()=>h(x.HOME),sx:{color:"text.secondary"},children:"← Retour à l'accueil"})}),e.jsx(r,{sx:{mt:4,p:2,bgcolor:"grey.50",borderRadius:2},children:e.jsxs(t,{variant:"body2",color:"text.secondary",align:"center",children:[e.jsx("strong",{children:"Mode Démo:"})," Explorez toutes les fonctionnalités sans inscription réelle"]})})]})})})})};export{h as RegisterPage,h as default};
