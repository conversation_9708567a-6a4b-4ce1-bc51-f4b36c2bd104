/**
 * Streamlined Dashboard Preview
 * Comprehensive financial overview with mock data
 */

import React, { useContext } from 'react'
import {
  Box,
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Avatar,
  Chip,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Divider,
  AppBar,
  Toolbar,
  IconButton
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  AccountBalance as AccountBalanceIcon,
  Receipt as ReceiptIcon,
  Savings as SavingsIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Logout as LogoutIcon,
  Home as HomeIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock data for preview
const mockData = {
  user: { firstName: 'Ahmed', lastName: 'Ben <PERSON>' },
  accounts: [
    { id: '1', name: '<PERSON><PERSON><PERSON>', balance: 2450.75, bank: '<PERSON>que de Tunisie' },
    { id: '2', name: '<PERSON><PERSON><PERSON>', balance: 8920.00, bank: 'BIAT' },
    { id: '3', name: '<PERSON><PERSON>ré<PERSON>', balance: -1250.30, bank: 'Attijari Bank' }
  ],
  transactions: [
    { id: '1', description: 'Salaire', amount: 1800.00, date: '15 Jan', type: 'income' },
    { id: '2', description: 'Courses Carrefour', amount: -85.50, date: '14 Jan', type: 'expense' },
    { id: '3', description: 'Facture Électricité', amount: -120.00, date: '13 Jan', type: 'expense' },
    { id: '4', description: 'Freelance', amount: 450.00, date: '12 Jan', type: 'income' }
  ],
  budgets: [
    { category: 'Alimentation', spent: 285, allocated: 400, percentage: 71 },
    { category: 'Transport', spent: 150, allocated: 200, percentage: 75 },
    { category: 'Loisirs', spent: 180, allocated: 150, percentage: 120 }
  ],
  goals: [
    { title: 'Vacances d\'été', current: 1850, target: 3000, status: 'on-track' },
    { title: 'Fonds d\'urgence', current: 2100, target: 5000, status: 'behind' }
  ]
}

export const DashboardPageSimple: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const totalBalance = mockData.accounts.reduce((sum, account) => sum + account.balance, 0)
  const monthlyIncome = mockData.transactions
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0)
  const monthlyExpenses = mockData.transactions
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + Math.abs(t.amount), 0)

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri Dashboard
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.HOME)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Welcome Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            Bonjour, {mockData.user.firstName} ! 👋
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Voici un aperçu de vos finances aujourd'hui
          </Typography>
        </Box>

        <Grid container spacing={3}>
          {/* Summary Cards */}
          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                    <AccountBalanceIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Solde Total</Typography>
                    <Typography variant="h5" color="primary.main" fontWeight="bold">
                      {totalBalance.toFixed(2)} TND
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'success.main', mr: 2 }}>
                    <ArrowUpwardIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Revenus ce mois</Typography>
                    <Typography variant="h5" color="success.main" fontWeight="bold">
                      +{monthlyIncome.toFixed(2)} TND
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'error.main', mr: 2 }}>
                    <ArrowDownwardIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Dépenses ce mois</Typography>
                    <Typography variant="h5" color="error.main" fontWeight="bold">
                      -{monthlyExpenses.toFixed(2)} TND
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Avatar sx={{ bgcolor: 'info.main', mr: 2 }}>
                    <SavingsIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="body2" color="text.secondary">Épargne</Typography>
                    <Typography variant="h5" color="info.main" fontWeight="bold">
                      {(monthlyIncome - monthlyExpenses).toFixed(2)} TND
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Accounts */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <AccountBalanceIcon sx={{ mr: 1 }} />
                  Mes Comptes
                </Typography>
                <List dense>
                  {mockData.accounts.map((account, index) => (
                    <React.Fragment key={account.id}>
                      <ListItem>
                        <ListItemText
                          primary={account.name}
                          secondary={account.bank}
                        />
                        <Typography
                          variant="h6"
                          color={account.balance >= 0 ? 'success.main' : 'error.main'}
                          fontWeight="bold"
                        >
                          {account.balance.toFixed(2)} TND
                        </Typography>
                      </ListItem>
                      {index < mockData.accounts.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Transactions */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <ReceiptIcon sx={{ mr: 1 }} />
                  Transactions Récentes
                </Typography>
                <List dense>
                  {mockData.transactions.map((transaction, index) => (
                    <React.Fragment key={transaction.id}>
                      <ListItem>
                        <ListItemText
                          primary={transaction.description}
                          secondary={transaction.date}
                        />
                        <Typography
                          variant="body1"
                          color={transaction.type === 'income' ? 'success.main' : 'error.main'}
                          fontWeight="bold"
                        >
                          {transaction.type === 'income' ? '+' : ''}{transaction.amount.toFixed(2)} TND
                        </Typography>
                      </ListItem>
                      {index < mockData.transactions.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
                <Button variant="outlined" fullWidth sx={{ mt: 2 }}>
                  Voir toutes les transactions
                </Button>
              </CardContent>
            </Card>
          </Grid>

          {/* Budgets */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  📊 Budgets du Mois
                </Typography>
                {mockData.budgets.map((budget, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">{budget.category}</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {budget.spent}/{budget.allocated} TND
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={Math.min(budget.percentage, 100)}
                      color={budget.percentage > 100 ? 'error' : budget.percentage > 80 ? 'warning' : 'primary'}
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {budget.allocated - budget.spent >= 0 
                        ? `${budget.allocated - budget.spent} TND restants` 
                        : `Dépassé de ${budget.spent - budget.allocated} TND`}
                    </Typography>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Savings Goals */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  🎯 Objectifs d'Épargne
                </Typography>
                {mockData.goals.map((goal, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="body1">{goal.title}</Typography>
                      <Chip
                        size="small"
                        label={goal.status === 'on-track' ? 'En cours' : 'En retard'}
                        color={goal.status === 'on-track' ? 'success' : 'warning'}
                        icon={<TrendingUpIcon />}
                      />
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(goal.current / goal.target) * 100}
                      color={goal.status === 'on-track' ? 'success' : 'warning'}
                      sx={{ height: 8, borderRadius: 4, mb: 1 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {goal.current} / {goal.target} TND ({((goal.current / goal.target) * 100).toFixed(1)}%)
                    </Typography>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* AI Insights */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  🤖 Conseils IA Personnalisés
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 2, border: '1px solid', borderColor: 'success.200' }}>
                      <Typography variant="body2" color="success.dark" fontWeight="bold">
                        ✅ Bonne nouvelle !
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        Vous êtes en avance sur votre objectif d'épargne vacances. Continuez ainsi !
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 2, border: '1px solid', borderColor: 'warning.200' }}>
                      <Typography variant="body2" color="warning.dark" fontWeight="bold">
                        ⚠️ Attention
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        Votre budget loisirs est dépassé de 30 TND ce mois-ci.
                      </Typography>
                    </Box>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <Box sx={{ p: 2, bgcolor: 'info.50', borderRadius: 2, border: '1px solid', borderColor: 'info.200' }}>
                      <Typography variant="body2" color="info.dark" fontWeight="bold">
                        💡 Conseil
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        Transférez 200 TND vers votre épargne pour optimiser vos intérêts.
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default DashboardPageSimple
