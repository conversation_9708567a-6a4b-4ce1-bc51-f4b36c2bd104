{"version": 3, "file": "auth0-spa-js.worker.production.js", "sources": ["../src/errors.ts", "../node_modules/tslib/tslib.es6.js", "../src/utils.ts", "../src/worker/token.worker.ts"], "sourcesContent": ["/**\n * Thrown when network requests to the Auth server fail.\n */\nexport class GenericError extends Error {\n  constructor(public error: string, public error_description: string) {\n    super(error_description);\n    Object.setPrototypeOf(this, GenericError.prototype);\n  }\n\n  static fromPayload({\n    error,\n    error_description\n  }: {\n    error: string;\n    error_description: string;\n  }) {\n    return new GenericError(error, error_description);\n  }\n}\n\n/**\n * Thrown when handling the redirect callback fails, will be one of Auth0's\n * Authentication API's Standard Error Responses: https://auth0.com/docs/api/authentication?javascript#standard-error-responses\n */\nexport class AuthenticationError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public state: string,\n    public appState: any = null\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, AuthenticationError.prototype);\n  }\n}\n\n/**\n * Thrown when silent auth times out (usually due to a configuration issue) or\n * when network requests to the Auth server timeout.\n */\nexport class TimeoutError extends GenericError {\n  constructor() {\n    super('timeout', 'Timeout');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Error thrown when the login popup times out (if the user does not complete auth)\n */\nexport class PopupTimeoutError extends TimeoutError {\n  constructor(public popup: Window) {\n    super();\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupTimeoutError.prototype);\n  }\n}\n\nexport class PopupCancelledError extends GenericError {\n  constructor(public popup: Window) {\n    super('cancelled', 'Popup closed');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupCancelledError.prototype);\n  }\n}\n\n/**\n * Error thrown when the token exchange results in a `mfa_required` error\n */\nexport class MfaRequiredError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public mfa_token: string\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, MfaRequiredError.prototype);\n  }\n}\n\n/**\n * Error thrown when there is no refresh token to use\n */\nexport class MissingRefreshTokenError extends GenericError {\n  constructor(public audience: string, public scope: string) {\n    super(\n      'missing_refresh_token',\n      `Missing Refresh Token (audience: '${valueOrEmptyString(audience, [\n        'default'\n      ])}', scope: '${valueOrEmptyString(scope)}')`\n    );\n    Object.setPrototypeOf(this, MissingRefreshTokenError.prototype);\n  }\n}\n\n/**\n * Returns an empty string when value is falsy, or when it's value is included in the exclude argument.\n * @param value The value to check\n * @param exclude An array of values that should result in an empty string.\n * @returns The value, or an empty string when falsy or included in the exclude argument.\n */\nfunction valueOrEmptyString(value: string, exclude: string[] = []) {\n  return value && !exclude.includes(value) ? value : '';\n}\n", "/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "import { AuthenticationResult, PopupConfigOptions } from './global';\n\nimport {\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  CLEANUP_IFRAME_TIMEOUT_IN_SECONDS\n} from './constants';\n\nimport {\n  PopupTimeoutError,\n  TimeoutError,\n  GenericError,\n  PopupCancelledError\n} from './errors';\n\nexport const parseAuthenticationResult = (\n  queryString: string\n): AuthenticationResult => {\n  if (queryString.indexOf('#') > -1) {\n    queryString = queryString.substring(0, queryString.indexOf('#'));\n  }\n\n  const searchParams = new URLSearchParams(queryString);\n\n  return {\n    state: searchParams.get('state')!,\n    code: searchParams.get('code') || undefined,\n    error: searchParams.get('error') || undefined,\n    error_description: searchParams.get('error_description') || undefined\n  };\n};\n\nexport const runIframe = (\n  authorizeUrl: string,\n  eventOrigin: string,\n  timeoutInSeconds: number = DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n) => {\n  return new Promise<AuthenticationResult>((res, rej) => {\n    const iframe = window.document.createElement('iframe');\n\n    iframe.setAttribute('width', '0');\n    iframe.setAttribute('height', '0');\n    iframe.style.display = 'none';\n\n    const removeIframe = () => {\n      if (window.document.body.contains(iframe)) {\n        window.document.body.removeChild(iframe);\n        window.removeEventListener('message', iframeEventHandler, false);\n      }\n    };\n\n    let iframeEventHandler: (e: MessageEvent) => void;\n\n    const timeoutSetTimeoutId = setTimeout(() => {\n      rej(new TimeoutError());\n      removeIframe();\n    }, timeoutInSeconds * 1000);\n\n    iframeEventHandler = function (e: MessageEvent) {\n      if (e.origin != eventOrigin) return;\n      if (!e.data || e.data.type !== 'authorization_response') return;\n\n      const eventSource = e.source;\n\n      if (eventSource) {\n        (eventSource as any).close();\n      }\n\n      e.data.response.error\n        ? rej(GenericError.fromPayload(e.data.response))\n        : res(e.data.response);\n\n      clearTimeout(timeoutSetTimeoutId);\n      window.removeEventListener('message', iframeEventHandler, false);\n\n      // Delay the removal of the iframe to prevent hanging loading status\n      // in Chrome: https://github.com/auth0/auth0-spa-js/issues/240\n      setTimeout(removeIframe, CLEANUP_IFRAME_TIMEOUT_IN_SECONDS * 1000);\n    };\n\n    window.addEventListener('message', iframeEventHandler, false);\n    window.document.body.appendChild(iframe);\n    iframe.setAttribute('src', authorizeUrl);\n  });\n};\n\nexport const openPopup = (url: string) => {\n  const width = 400;\n  const height = 600;\n  const left = window.screenX + (window.innerWidth - width) / 2;\n  const top = window.screenY + (window.innerHeight - height) / 2;\n\n  return window.open(\n    url,\n    'auth0:authorize:popup',\n    `left=${left},top=${top},width=${width},height=${height},resizable,scrollbars=yes,status=1`\n  );\n};\n\nexport const runPopup = (config: PopupConfigOptions) => {\n  return new Promise<AuthenticationResult>((resolve, reject) => {\n    let popupEventListener: (e: MessageEvent) => void;\n\n    // Check each second if the popup is closed triggering a PopupCancelledError\n    const popupTimer = setInterval(() => {\n      if (config.popup && config.popup.closed) {\n        clearInterval(popupTimer);\n        clearTimeout(timeoutId);\n        window.removeEventListener('message', popupEventListener, false);\n        reject(new PopupCancelledError(config.popup));\n      }\n    }, 1000);\n\n    const timeoutId = setTimeout(() => {\n      clearInterval(popupTimer);\n      reject(new PopupTimeoutError(config.popup));\n      window.removeEventListener('message', popupEventListener, false);\n    }, (config.timeoutInSeconds || DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS) * 1000);\n\n    popupEventListener = function (e: MessageEvent) {\n      if (!e.data || e.data.type !== 'authorization_response') {\n        return;\n      }\n\n      clearTimeout(timeoutId);\n      clearInterval(popupTimer);\n      window.removeEventListener('message', popupEventListener, false);\n      config.popup.close();\n\n      if (e.data.response.error) {\n        return reject(GenericError.fromPayload(e.data.response));\n      }\n\n      resolve(e.data.response);\n    };\n\n    window.addEventListener('message', popupEventListener);\n  });\n};\n\nexport const getCrypto = () => {\n  return window.crypto;\n};\n\nexport const createRandomString = () => {\n  const charset =\n    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.';\n  let random = '';\n  const randomValues = Array.from(\n    getCrypto().getRandomValues(new Uint8Array(43))\n  );\n  randomValues.forEach(v => (random += charset[v % charset.length]));\n  return random;\n};\n\nexport const encode = (value: string) => btoa(value);\nexport const decode = (value: string) => atob(value);\n\nconst stripUndefined = (params: any) => {\n  return Object.keys(params)\n    .filter(k => typeof params[k] !== 'undefined')\n    .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {});\n};\n\nexport const createQueryParams = ({ clientId: client_id, ...params }: any) => {\n  return new URLSearchParams(\n    stripUndefined({ client_id, ...params })\n  ).toString();\n};\n\nexport const sha256 = async (s: string) => {\n  const digestOp: any = getCrypto().subtle.digest(\n    { name: 'SHA-256' },\n    new TextEncoder().encode(s)\n  );\n\n  return await digestOp;\n};\n\nconst urlEncodeB64 = (input: string) => {\n  const b64Chars: { [index: string]: string } = { '+': '-', '/': '_', '=': '' };\n  return input.replace(/[+/=]/g, (m: string) => b64Chars[m]);\n};\n\n// https://stackoverflow.com/questions/30106476/\nconst decodeB64 = (input: string) =>\n  decodeURIComponent(\n    atob(input)\n      .split('')\n      .map(c => {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n      })\n      .join('')\n  );\n\nexport const urlDecodeB64 = (input: string) =>\n  decodeB64(input.replace(/_/g, '/').replace(/-/g, '+'));\n\nexport const bufferToBase64UrlEncoded = (input: number[] | Uint8Array) => {\n  const ie11SafeInput = new Uint8Array(input);\n  return urlEncodeB64(\n    window.btoa(String.fromCharCode(...Array.from(ie11SafeInput)))\n  );\n};\n\nexport const validateCrypto = () => {\n  if (!getCrypto()) {\n    throw new Error(\n      'For security reasons, `window.crypto` is required to run `auth0-spa-js`.'\n    );\n  }\n  if (typeof getCrypto().subtle === 'undefined') {\n    throw new Error(`\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    `);\n  }\n};\n\n/**\n * @ignore\n */\nexport const getDomain = (domainUrl: string) => {\n  if (!/^https?:\\/\\//.test(domainUrl)) {\n    return `https://${domainUrl}`;\n  }\n\n  return domainUrl;\n};\n\n/**\n * @ignore\n */\nexport const getTokenIssuer = (\n  issuer: string | undefined,\n  domainUrl: string\n) => {\n  if (issuer) {\n    return issuer.startsWith('https://') ? issuer : `https://${issuer}/`;\n  }\n\n  return `${domainUrl}/`;\n};\n\nexport const parseNumber = (value: any): number | undefined => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  return parseInt(value, 10) || undefined;\n};\n", "import { MissingRefreshTokenError } from '../errors';\nimport { createQueryParams } from '../utils';\nimport { WorkerRefreshTokenMessage } from './worker.types';\n\nlet refreshTokens: Record<string, string> = {};\n\nconst cacheKey = (audience: string, scope: string) => `${audience}|${scope}`;\n\nconst getRefreshToken = (audience: string, scope: string) =>\n  refreshTokens[cacheKey(audience, scope)];\n\nconst setRefreshToken = (\n  refreshToken: string,\n  audience: string,\n  scope: string\n) => (refreshTokens[cacheKey(audience, scope)] = refreshToken);\n\nconst deleteRefreshToken = (audience: string, scope: string) =>\n  delete refreshTokens[cacheKey(audience, scope)];\n\nconst wait = (time: number) =>\n  new Promise(resolve => setTimeout(resolve, time));\n\nconst formDataToObject = (formData: string): Record<string, any> => {\n  const queryParams = new URLSearchParams(formData);\n  const parsedQuery: any = {};\n\n  queryParams.forEach((val, key) => {\n    parsedQuery[key] = val;\n  });\n\n  return parsedQuery;\n};\n\nconst messageHandler = async ({\n  data: { timeout, auth, fetchUrl, fetchOptions, useFormData },\n  ports: [port]\n}: MessageEvent<WorkerRefreshTokenMessage>) => {\n  let json: {\n    refresh_token?: string;\n  };\n\n  const { audience, scope } = auth || {};\n\n  try {\n    const body = useFormData\n      ? formDataToObject(fetchOptions.body as string)\n      : JSON.parse(fetchOptions.body as string);\n\n    if (!body.refresh_token && body.grant_type === 'refresh_token') {\n      const refreshToken = getRefreshToken(audience, scope);\n\n      if (!refreshToken) {\n        throw new MissingRefreshTokenError(audience, scope);\n      }\n\n      fetchOptions.body = useFormData\n        ? createQueryParams({\n            ...body,\n            refresh_token: refreshToken\n          })\n        : JSON.stringify({\n            ...body,\n            refresh_token: refreshToken\n          });\n    }\n\n    let abortController: AbortController | undefined;\n\n    if (typeof AbortController === 'function') {\n      abortController = new AbortController();\n      fetchOptions.signal = abortController.signal;\n    }\n\n    let response: any;\n\n    try {\n      response = await Promise.race([\n        wait(timeout),\n        fetch(fetchUrl, { ...fetchOptions })\n      ]);\n    } catch (error) {\n      // fetch error, reject `sendMessage` using `error` key so that we retry.\n      port.postMessage({\n        error: error.message\n      });\n\n      return;\n    }\n\n    if (!response) {\n      // If the request times out, abort it and let `switchFetch` raise the error.\n      if (abortController) abortController.abort();\n\n      port.postMessage({\n        error: \"Timeout when executing 'fetch'\"\n      });\n\n      return;\n    }\n\n    json = await response.json();\n\n    if (json.refresh_token) {\n      setRefreshToken(json.refresh_token, audience, scope);\n      delete json.refresh_token;\n    } else {\n      deleteRefreshToken(audience, scope);\n    }\n\n    port.postMessage({\n      ok: response.ok,\n      json\n    });\n  } catch (error) {\n    port.postMessage({\n      ok: false,\n      json: {\n        error: error.error,\n        error_description: error.message\n      }\n    });\n  }\n};\n\n// Don't run `addEventListener` in our tests (this is replaced in rollup)\nif (process.env.NODE_ENV === 'test') {\n  module.exports = { messageHandler };\n  /* c8 ignore next 4  */\n} else {\n  // @ts-ignore\n  addEventListener('message', messageHandler);\n}\n"], "names": ["GenericError", "Error", "constructor", "error", "error_description", "super", "this", "Object", "setPrototypeOf", "prototype", "static", "MissingRefreshTokenError", "audience", "scope", "valueOrEmptyString", "value", "exclude", "includes", "SuppressedError", "createQueryParams", "_a", "clientId", "client_id", "params", "s", "e", "t", "p", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "__rest", "URLSearchParams", "keys", "filter", "k", "reduce", "acc", "key", "assign", "stripUndefined", "toString", "refreshTokens", "cache<PERSON>ey", "addEventListener", "async", "data", "timeout", "auth", "fetchUrl", "fetchOptions", "useFormData", "ports", "port", "json", "body", "formData", "queryParams", "parsed<PERSON><PERSON><PERSON>", "for<PERSON>ach", "val", "formDataToObject", "JSON", "parse", "refresh_token", "grant_type", "refreshToken", "getRefreshToken", "stringify", "abortController", "response", "AbortController", "signal", "Promise", "race", "time", "resolve", "setTimeout", "fetch", "postMessage", "message", "abort", "setRefreshToken", "deleteRefreshToken", "ok"], "mappings": "2FAGM,MAAOA,UAAqBC,MAChCC,YAAmBC,EAAsBC,GACvCC,MAAMD,GADWE,KAAKH,MAALA,EAAsBG,KAAiBF,kBAAjBA,EAEvCG,OAAOC,eAAeF,KAAMN,EAAaS,UAC1C,CAEDC,oBAAmBP,MACjBA,EAAKC,kBACLA,IAKA,OAAO,IAAIJ,EAAaG,EAAOC,EAChC,EAqEG,MAAOO,UAAiCX,EAC5CE,YAAmBU,EAAyBC,GAC1CR,MACE,wBACA,qCAAqCS,EAAmBF,EAAU,CAChE,yBACcE,EAAmBD,QALpBP,KAAQM,SAARA,EAAyBN,KAAKO,MAALA,EAO1CN,OAAOC,eAAeF,KAAMK,EAAyBF,UACtD,EASH,SAASK,EAAmBC,EAAeC,EAAoB,IAC7D,OAAOD,IAAUC,EAAQC,SAASF,GAASA,EAAQ,EACrD,CCiNkD,mBAApBG,iBAAiCA,gBC9J/D,MAMaC,EAAqBC,QAAEC,SAAUC,GAASF,EAAKG,EDzHrD,SAAgBC,EAAGC,GACtB,IAAIC,EAAI,CAAA,EACR,IAAK,IAAIC,KAAKH,EAAOjB,OAAOE,UAAUmB,eAAeC,KAAKL,EAAGG,IAAMF,EAAEK,QAAQH,GAAK,IAC9ED,EAAEC,GAAKH,EAAEG,IACb,GAAS,MAALH,GAAqD,mBAAjCjB,OAAOwB,sBACtB,KAAIC,EAAI,EAAb,IAAgBL,EAAIpB,OAAOwB,sBAAsBP,GAAIQ,EAAIL,EAAEM,OAAQD,IAC3DP,EAAEK,QAAQH,EAAEK,IAAM,GAAKzB,OAAOE,UAAUyB,qBAAqBL,KAAKL,EAAGG,EAAEK,MACvEN,EAAEC,EAAEK,IAAMR,EAAEG,EAAEK,IAF4B,CAItD,OAAON,CACX,CC+GkES,CAAAf,EAAhC,cAChC,OAAO,IAAIgB,gBAPU,CAACb,GACfhB,OAAO8B,KAAKd,GAChBe,QAAOC,QAA0B,IAAdhB,EAAOgB,KAC1BC,QAAO,CAACC,EAAKC,IAAQnC,OAAAoC,OAAApC,OAAAoC,OAAA,CAAA,EAAMF,GAAG,CAAEC,CAACA,GAAMnB,EAAOmB,MAAS,CAAA,GAKxDE,CAAiBrC,OAAAoC,OAAA,CAAArB,aAAcC,KAC/BsB,UAAU,EClKd,IAAIC,EAAwC,CAAA,EAE5C,MAAMC,EAAW,CAACnC,EAAkBC,IAAkB,GAAGD,KAAYC,IA6HnEmC,iBAAiB,WAjGIC,OACrBC,MAAQC,UAASC,OAAMC,WAAUC,eAAcC,eAC/CC,OAAQC,OAER,IAAIC,EAIJ,MAAM9C,SAAEA,EAAQC,MAAEA,GAAUuC,GAAQ,CAAA,EAEpC,IACE,MAAMO,EAAOJ,EAtBQ,CAACK,IACxB,MAAMC,EAAc,IAAIzB,gBAAgBwB,GAClCE,EAAmB,CAAA,EAMzB,OAJAD,EAAYE,SAAQ,CAACC,EAAKtB,KACxBoB,EAAYpB,GAAOsB,CAAG,IAGjBF,CAAW,EAeZG,CAAiBX,EAAaK,MAC9BO,KAAKC,MAAMb,EAAaK,MAE5B,IAAKA,EAAKS,eAAqC,kBAApBT,EAAKU,WAAgC,CAC9D,MAAMC,EA1CY,EAAC1D,EAAkBC,IACzCiC,EAAcC,EAASnC,EAAUC,IAyCR0D,CAAgB3D,EAAUC,GAE/C,IAAKyD,EACH,MAAM,IAAI3D,EAAyBC,EAAUC,GAG/CyC,EAAaK,KAAOJ,EAChBpC,EACKZ,OAAAoC,OAAApC,OAAAoC,OAAA,GAAAgB,IACHS,cAAeE,KAEjBJ,KAAKM,UAASjE,OAAAoC,OAAApC,OAAAoC,OAAA,GACTgB,GAAI,CACPS,cAAeE,IAEtB,CAED,IAAIG,EAOAC,EAL2B,mBAApBC,kBACTF,EAAkB,IAAIE,gBACtBrB,EAAasB,OAASH,EAAgBG,QAKxC,IACEF,QAAiBG,QAAQC,KAAK,EAzDtBC,EA0DD5B,EAzDX,IAAI0B,SAAQG,GAAWC,WAAWD,EAASD,MA0DrCG,MAAM7B,EAAe9C,OAAAoC,OAAA,CAAA,EAAAW,KASxB,CAPC,MAAOnD,GAMP,YAJAsD,EAAK0B,YAAY,CACfhF,MAAOA,EAAMiF,SAIhB,CAED,IAAKV,EAQH,OANID,GAAiBA,EAAgBY,aAErC5B,EAAK0B,YAAY,CACfhF,MAAO,mCAMXuD,QAAagB,EAAShB,OAElBA,EAAKU,eA5FW,EACtBE,EACA1D,EACAC,KACIiC,EAAcC,EAASnC,EAAUC,IAAUyD,CAAa,EAyFxDgB,CAAgB5B,EAAKU,cAAexD,EAAUC,UACvC6C,EAAKU,eAxFS,EAACxD,EAAkBC,YACrCiC,EAAcC,EAASnC,EAAUC,GAAO,EAyF3C0E,CAAmB3E,EAAUC,GAG/B4C,EAAK0B,YAAY,CACfK,GAAId,EAASc,GACb9B,QAUH,CARC,MAAOvD,GACPsD,EAAK0B,YAAY,CACfK,IAAI,EACJ9B,KAAM,CACJvD,MAAOA,EAAMA,MACbC,kBAAmBD,EAAMiF,UAG9B,CAtGU,IAACL,CAsGX"}