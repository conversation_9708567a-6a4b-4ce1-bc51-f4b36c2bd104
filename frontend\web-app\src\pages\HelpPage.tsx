/**
 * Complete Help Page
 * Help center with FAQ and support
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material'
import {
  Home as HomeIcon,
  Logout as LogoutIcon,
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,

  Phone as PhoneIcon,
  Email as EmailIcon,
  Chat as ChatIcon,
  Book as BookIcon,
  VideoLibrary as VideoIcon,
  QuestionAnswer as QAIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock FAQ data
const faqData = [
  {
    category: 'Général',
    questions: [
      {
        question: 'Comment créer mon premier budget ?',
        answer: 'Pour créer votre premier budget, allez dans la section "Budgets" et cliquez sur "Nouveau Budget". Choisissez une catégorie, définissez un montant mensuel et suivez vos dépenses.'
      },
      {
        question: 'Comment connecter mes comptes bancaires ?',
        answer: 'Actuellement, Nouri fonctionne en mode démo. Dans la version complète, vous pourrez connecter vos comptes via notre partenariat sécurisé avec les banques tunisiennes.'
      },
      {
        question: 'Mes données sont-elles sécurisées ?',
        answer: 'Absolument ! Nouri utilise un chiffrement de niveau bancaire pour protéger toutes vos données financières. Nous ne partageons jamais vos informations avec des tiers.'
      }
    ]
  },
  {
    category: 'Budgets',
    questions: [
      {
        question: 'Comment modifier un budget existant ?',
        answer: 'Dans la page Budgets, cliquez sur l\'icône "Modifier" sur la carte du budget que vous souhaitez changer. Vous pouvez ajuster le montant, la catégorie ou supprimer le budget.'
      },
      {
        question: 'Que faire si je dépasse mon budget ?',
        answer: 'Si vous dépassez un budget, Nouri vous enverra une alerte. Analysez vos dépenses, ajustez le budget si nécessaire, ou réduisez les dépenses dans cette catégorie.'
      },
      {
        question: 'Puis-je créer des budgets personnalisés ?',
        answer: 'Oui ! Vous pouvez créer des budgets pour n\'importe quelle catégorie. Utilisez "Autre" comme catégorie et personnalisez le nom selon vos besoins.'
      }
    ]
  },
  {
    category: 'Épargne',
    questions: [
      {
        question: 'Comment définir un objectif d\'épargne ?',
        answer: 'Allez dans "Épargne", cliquez sur "Nouvel Objectif", définissez le montant cible, la date d\'échéance et la priorité. Nouri vous aidera à suivre vos progrès.'
      },
      {
        question: 'Comment ajouter de l\'argent à mes objectifs ?',
        answer: 'Sur chaque carte d\'objectif, cliquez sur "Ajouter de l\'argent" et saisissez le montant. Vos progrès seront automatiquement mis à jour.'
      },
      {
        question: 'Puis-je modifier mes objectifs d\'épargne ?',
        answer: 'Oui, vous pouvez modifier le montant cible, la date d\'échéance et la priorité de vos objectifs à tout moment via le bouton "Modifier".'
      }
    ]
  },
  {
    category: 'Assistant IA',
    questions: [
      {
        question: 'Comment utiliser l\'assistant IA Nouri ?',
        answer: 'L\'assistant IA est disponible dans la section "Assistant IA". Posez vos questions en français ou en arabe, et il vous donnera des conseils personnalisés basés sur vos données financières.'
      },
      {
        question: 'Quels types de conseils peut donner l\'IA ?',
        answer: 'L\'IA peut analyser vos dépenses, suggérer des optimisations de budget, recommander des stratégies d\'épargne et vous aider à atteindre vos objectifs financiers.'
      },
      {
        question: 'L\'IA a-t-elle accès à mes données bancaires ?',
        answer: 'L\'IA analyse uniquement les données que vous saisissez dans Nouri. Elle ne peut pas accéder directement à vos comptes bancaires.'
      }
    ]
  }
]

const supportOptions = [
  {
    title: 'Chat en direct',
    description: 'Discutez avec notre équipe support',
    icon: <ChatIcon />,
    action: 'Démarrer le chat',
    available: true
  },
  {
    title: 'Email',
    description: '<EMAIL>',
    icon: <EmailIcon />,
    action: 'Envoyer un email',
    available: true
  },
  {
    title: 'Téléphone',
    description: '+216 70 123 456',
    icon: <PhoneIcon />,
    action: 'Appeler',
    available: true
  }
]

const resources = [
  {
    title: 'Guide de démarrage',
    description: 'Apprenez les bases de Nouri',
    icon: <BookIcon />,
    type: 'guide'
  },
  {
    title: 'Tutoriels vidéo',
    description: 'Regardez nos tutoriels',
    icon: <VideoIcon />,
    type: 'video'
  },
  {
    title: 'Formation financière',
    description: 'Modules d\'apprentissage',
    icon: <BookIcon />,
    type: 'education'
  }
]

export const HelpPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedCategory, setExpandedCategory] = useState<string | false>('Général')

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const filteredFAQ = faqData.map(category => ({
    ...category,
    questions: category.questions.filter(
      q => q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
           q.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.questions.length > 0)

  const handleAccordionChange = (panel: string) => (_event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedCategory(isExpanded ? panel : false)
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Centre d'Aide
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Comment pouvons-nous vous aider ?
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
            Trouvez des réponses à vos questions ou contactez notre équipe support
          </Typography>

          {/* Search */}
          <TextField
            placeholder="Rechercher dans l'aide..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ maxWidth: 500, width: '100%' }}
          />
        </Box>

        <Grid container spacing={3}>
          {/* FAQ Section */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <QAIcon />
                  Questions Fréquentes
                </Typography>

                {filteredFAQ.length === 0 ? (
                  <Typography color="text.secondary">
                    Aucune question trouvée pour "{searchTerm}"
                  </Typography>
                ) : (
                  filteredFAQ.map((category) => (
                    <Box key={category.category} sx={{ mb: 2 }}>
                      <Chip
                        label={category.category}
                        color="primary"
                        sx={{ mb: 2 }}
                      />
                      {category.questions.map((faq, index) => (
                        <Accordion
                          key={index}
                          expanded={expandedCategory === `${category.category}-${index}`}
                          onChange={handleAccordionChange(`${category.category}-${index}`)}
                        >
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography fontWeight="medium">
                              {faq.question}
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Typography color="text.secondary">
                              {faq.answer}
                            </Typography>
                          </AccordionDetails>
                        </Accordion>
                      ))}
                    </Box>
                  ))
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Support & Resources */}
          <Grid item xs={12} md={4}>
            {/* Contact Support */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💬 Contacter le Support
                </Typography>
                <List>
                  {supportOptions.map((option, index) => (
                    <ListItem key={index} sx={{ px: 0 }}>
                      <ListItemIcon>
                        {option.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={option.title}
                        secondary={option.description}
                      />
                      <Button
                        size="small"
                        variant="outlined"
                        disabled={!option.available}
                      >
                        {option.action}
                      </Button>
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>

            {/* Resources */}
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📚 Ressources Utiles
                </Typography>
                <List>
                  {resources.map((resource, index) => (
                    <ListItem
                      key={index}
                      sx={{ px: 0, cursor: 'pointer' }}
                      onClick={() => {
                        if (resource.type === 'education') {
                          navigate(ROUTES.EDUCATION)
                        }
                      }}
                    >
                      <ListItemIcon>
                        {resource.icon}
                      </ListItemIcon>
                      <ListItemText
                        primary={resource.title}
                        secondary={resource.description}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>

            {/* Quick Tips */}
            <Card sx={{ mt: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💡 Conseils Rapides
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ p: 2, bgcolor: 'info.50', borderRadius: 2 }}>
                    <Typography variant="body2" fontWeight="bold" color="info.dark">
                      Astuce du jour
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Utilisez l'assistant IA pour obtenir des conseils personnalisés sur vos finances !
                    </Typography>
                  </Box>

                  <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 2 }}>
                    <Typography variant="body2" fontWeight="bold" color="success.dark">
                      Saviez-vous ?
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1 }}>
                      Vous pouvez exporter vos rapports financiers en PDF depuis la page Rapports.
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Still Need Help */}
        <Card sx={{ mt: 4, textAlign: 'center' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Vous ne trouvez pas ce que vous cherchez ?
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Notre équipe est là pour vous aider 24h/24 et 7j/7
            </Typography>
            <Button
              variant="contained"
              startIcon={<ChatIcon />}
              size="large"
              onClick={() => navigate(ROUTES.CHATBOT)}
            >
              Parler à l'Assistant IA
            </Button>
          </CardContent>
        </Card>
      </Container>
    </Box>
  )
}

export default HelpPage
