/**
 * Configuration centralisée pour l'application Nouri
 */

// Variables d'environnement avec valeurs par défaut
const getEnvVar = (name: string, defaultValue: string = ''): string => {
  return import.meta.env[name] || defaultValue
}

const getBooleanEnvVar = (name: string, defaultValue: boolean = false): boolean => {
  const value = import.meta.env[name]
  if (value === undefined) return defaultValue
  return value === 'true' || value === '1'
}

export const config = {
  // Configuration de l'application
  app: {
    name: 'Nouri - Coach Financier IA',
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
    environment: getEnvVar('VITE_NODE_ENV', 'development'),
    baseUrl: getEnvVar('VITE_BASE_URL', window.location.origin),
    supportEmail: getEnvVar('VITE_SUPPORT_EMAIL', '<EMAIL>'),
    isDevelopment: getEnvVar('VITE_NODE_ENV', 'development') === 'development',
    isProduction: getEnvVar('VITE_NODE_ENV', 'development') === 'production'
  },

  // Configuration API
  api: {
    baseUrl: getEnvVar('VITE_API_URL', 'http://localhost:3000'),
    timeout: parseInt(getEnvVar('VITE_API_TIMEOUT', '30000'), 10),
    retryAttempts: parseInt(getEnvVar('VITE_API_RETRY_ATTEMPTS', '3'), 10),
    retryDelay: parseInt(getEnvVar('VITE_API_RETRY_DELAY', '1000'), 10)
  },

  // Configuration Auth0
  auth0: {
    domain: getEnvVar('VITE_AUTH0_DOMAIN', ''),
    clientId: getEnvVar('VITE_AUTH0_CLIENT_ID', ''),
    audience: getEnvVar('VITE_AUTH0_AUDIENCE', 'https://api.nouri.tn'),
    redirectUri: getEnvVar('VITE_AUTH0_REDIRECT_URI', window.location.origin),
    logoutUri: getEnvVar('VITE_AUTH0_LOGOUT_URI', window.location.origin)
  },

  // Configuration de localisation
  i18n: {
    defaultLanguage: getEnvVar('VITE_DEFAULT_LANGUAGE', 'fr'),
    supportedLanguages: getEnvVar('VITE_SUPPORTED_LANGUAGES', 'fr,ar,en').split(','),
    fallbackLanguage: getEnvVar('VITE_FALLBACK_LANGUAGE', 'fr'),
    detectBrowserLanguage: getBooleanEnvVar('VITE_DETECT_BROWSER_LANGUAGE', true)
  },

  // Configuration financière
  finance: {
    defaultCurrency: getEnvVar('VITE_DEFAULT_CURRENCY', 'TND'),
    supportedCurrencies: getEnvVar('VITE_SUPPORTED_CURRENCIES', 'TND,EUR,USD').split(','),
    currencySymbols: {
      TND: 'د.ت',
      EUR: '€',
      USD: '$'
    },
    decimalPlaces: parseInt(getEnvVar('VITE_DECIMAL_PLACES', '3'), 10)
  },

  // Configuration des fonctionnalités
  features: {
    enablePWA: getBooleanEnvVar('VITE_ENABLE_PWA', true),
    enableNotifications: getBooleanEnvVar('VITE_ENABLE_NOTIFICATIONS', true),
    enableAnalytics: getBooleanEnvVar('VITE_ENABLE_ANALYTICS', false),
    enableChatbot: getBooleanEnvVar('VITE_ENABLE_CHATBOT', true),
    enableEducation: getBooleanEnvVar('VITE_ENABLE_EDUCATION', true),
    enableReporting: getBooleanEnvVar('VITE_ENABLE_REPORTING', true),
    enableDarkMode: getBooleanEnvVar('VITE_ENABLE_DARK_MODE', true),
    enableOfflineMode: getBooleanEnvVar('VITE_ENABLE_OFFLINE_MODE', true)
  },

  // Configuration UI
  ui: {
    sidebarWidth: parseInt(getEnvVar('VITE_SIDEBAR_WIDTH', '280'), 10),
    headerHeight: parseInt(getEnvVar('VITE_HEADER_HEIGHT', '64'), 10),
    mobileBreakpoint: parseInt(getEnvVar('VITE_MOBILE_BREAKPOINT', '768'), 10),
    animationDuration: parseInt(getEnvVar('VITE_ANIMATION_DURATION', '300'), 10),
    debounceDelay: parseInt(getEnvVar('VITE_DEBOUNCE_DELAY', '300'), 10)
  },

  // Configuration de cache
  cache: {
    userDataTTL: parseInt(getEnvVar('VITE_USER_DATA_TTL', '300000'), 10), // 5 minutes
    transactionsTTL: parseInt(getEnvVar('VITE_TRANSACTIONS_TTL', '60000'), 10), // 1 minute
    budgetsTTL: parseInt(getEnvVar('VITE_BUDGETS_TTL', '300000'), 10), // 5 minutes
    staticDataTTL: parseInt(getEnvVar('VITE_STATIC_DATA_TTL', '3600000'), 10) // 1 heure
  },

  // Configuration de sécurité
  security: {
    enableCSP: getBooleanEnvVar('VITE_ENABLE_CSP', true),
    sessionTimeout: parseInt(getEnvVar('VITE_SESSION_TIMEOUT', '1800000'), 10), // 30 minutes
    maxLoginAttempts: parseInt(getEnvVar('VITE_MAX_LOGIN_ATTEMPTS', '5'), 10),
    passwordMinLength: parseInt(getEnvVar('VITE_PASSWORD_MIN_LENGTH', '8'), 10)
  },

  // Configuration de monitoring
  monitoring: {
    enableErrorReporting: getBooleanEnvVar('VITE_ENABLE_ERROR_REPORTING', false),
    enablePerformanceMonitoring: getBooleanEnvVar('VITE_ENABLE_PERFORMANCE_MONITORING', false),
    sentryDsn: getEnvVar('VITE_SENTRY_DSN', ''),
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'info')
  },

  // Configuration des services externes
  external: {
    googleAnalyticsId: getEnvVar('VITE_GOOGLE_ANALYTICS_ID', ''),
    hotjarId: getEnvVar('VITE_HOTJAR_ID', ''),
    intercomAppId: getEnvVar('VITE_INTERCOM_APP_ID', ''),
    crisp: {
      websiteId: getEnvVar('VITE_CRISP_WEBSITE_ID', ''),
      enabled: getBooleanEnvVar('VITE_CRISP_ENABLED', false)
    }
  },

  // Configuration des limites
  limits: {
    maxFileUploadSize: parseInt(getEnvVar('VITE_MAX_FILE_UPLOAD_SIZE', '5242880'), 10), // 5MB
    maxTransactionsPerPage: parseInt(getEnvVar('VITE_MAX_TRANSACTIONS_PER_PAGE', '50'), 10),
    maxBudgetCategories: parseInt(getEnvVar('VITE_MAX_BUDGET_CATEGORIES', '20'), 10),
    maxSavingsGoals: parseInt(getEnvVar('VITE_MAX_SAVINGS_GOALS', '10'), 10)
  },

  // URLs importantes
  urls: {
    privacyPolicy: getEnvVar('VITE_PRIVACY_POLICY_URL', '/privacy'),
    termsOfService: getEnvVar('VITE_TERMS_OF_SERVICE_URL', '/terms'),
    help: getEnvVar('VITE_HELP_URL', '/help'),
    contact: getEnvVar('VITE_CONTACT_URL', '/contact'),
    blog: getEnvVar('VITE_BLOG_URL', 'https://blog.nouri.tn'),
    documentation: getEnvVar('VITE_DOCUMENTATION_URL', 'https://docs.nouri.tn')
  }
}

// Validation de la configuration en développement
if (config.app.isDevelopment) {
  const requiredEnvVars = [
    'VITE_API_URL',
    'VITE_AUTH0_DOMAIN',
    'VITE_AUTH0_CLIENT_ID'
  ]

  const missingVars = requiredEnvVars.filter(varName => !getEnvVar(varName))
  
  if (missingVars.length > 0) {
    console.warn('⚠️ Variables d\'environnement manquantes:', missingVars)
    console.warn('Créez un fichier .env.local avec ces variables pour un fonctionnement optimal')
  }
}

// Export des constantes utiles
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'nouri_auth_token',
  USER_PREFERENCES: 'nouri_user_preferences',
  LANGUAGE: 'nouri_language',
  THEME: 'nouri_theme',
  ONBOARDING_COMPLETED: 'nouri_onboarding_completed',
  LAST_SYNC: 'nouri_last_sync'
} as const

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    ME: '/api/auth/me'
  },
  USERS: {
    PROFILE: '/api/users/profile',
    DASHBOARD: '/api/users/dashboard',
    NOTIFICATIONS: '/api/users/notifications'
  },
  BANKING: {
    ACCOUNTS: '/api/banking/accounts',
    TRANSACTIONS: '/api/banking/transactions',
    SYNC: '/api/banking/accounts/sync'
  },
  BUDGETS: {
    LIST: '/api/budgets',
    SAVINGS_GOALS: '/api/budgets/savings-goals'
  },
  CHATBOT: {
    CHAT: '/api/chatbot/chat',
    SESSIONS: '/api/chatbot/sessions'
  },
  EDUCATION: {
    MODULES: '/api/education/modules',
    PROGRESS: '/api/education/progress'
  },
  REPORTING: {
    MONTHLY: '/api/reporting/monthly',
    YEARLY: '/api/reporting/yearly'
  }
} as const

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  TRANSACTIONS: '/transactions',
  BUDGETS: '/budgets',
  SAVINGS: '/savings',
  CHATBOT: '/chat',
  EDUCATION: '/education',
  REPORTS: '/reports',
  SETTINGS: '/settings',
  PROFILE: '/profile',
  HELP: '/help'
} as const

export default config
