import{j as e,B as s,o as i,p as r,b as t,I as n,H as o,q as a,d as l,G as c,g as d,h as x,e as p,V as h,W as m,X as u,L as j,Y as g,f,a3 as y,a4 as b,a5 as v,a6 as C,a7 as z,a8 as I,a9 as W,aa as w}from"./mui-vendor-b761306f.js";import{u as k,r as D}from"./react-vendor-2f216e43.js";import{A,R as S}from"./index-bd55ea72.js";const B=[{id:"1",title:"Les bases de la finance personnelle",description:"Apprenez les fondamentaux de la gestion financière personnelle",duration:"30 min",difficulty:"Débutant",progress:100,completed:!0,type:"video",category:"Bases",lessons:5,image:"💰",color:"#4caf50"},{id:"2",title:"<PERSON><PERSON>er et gérer un budget",description:"Maîtrisez l'art de la budgétisation pour contrôler vos finances",duration:"45 min",difficulty:"Débutant",progress:75,completed:!1,type:"interactive",category:"Budget",lessons:7,image:"📊",color:"#2196f3"},{id:"3",title:"Stratégies d'épargne efficaces",description:"Découvrez comment épargner intelligemment pour vos objectifs",duration:"35 min",difficulty:"Intermédiaire",progress:40,completed:!1,type:"article",category:"Épargne",lessons:6,image:"🏦",color:"#ff9800"},{id:"4",title:"Introduction aux investissements",description:"Les bases pour commencer à investir en toute sécurité",duration:"60 min",difficulty:"Intermédiaire",progress:0,completed:!1,type:"video",category:"Investissement",lessons:8,image:"📈",color:"#9c27b0"},{id:"5",title:"Gestion des dettes",description:"Apprenez à gérer et rembourser vos dettes efficacement",duration:"40 min",difficulty:"Intermédiaire",progress:0,completed:!1,type:"quiz",category:"Dettes",lessons:5,image:"💳",color:"#f44336"},{id:"6",title:"Planification de la retraite",description:"Préparez votre avenir financier dès maintenant",duration:"50 min",difficulty:"Avancé",progress:0,completed:!1,type:"video",category:"Retraite",lessons:9,image:"🏖️",color:"#607d8b",locked:!0}],R=["Tous","Bases","Budget","Épargne","Investissement","Dettes","Retraite"],T=["Tous","Débutant","Intermédiaire","Avancé"],q=({module:i,onStart:r})=>e.jsxs(d,{sx:{height:"100%",display:"flex",flexDirection:"column"},children:[e.jsxs(s,{sx:{position:"relative"},children:[e.jsx(s,{sx:{height:120,background:`linear-gradient(135deg, ${i.color}20 0%, ${i.color}40 100%)`,display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(t,{variant:"h2",children:i.image})}),i.locked&&e.jsx(s,{sx:{position:"absolute",top:8,right:8,bgcolor:"rgba(0,0,0,0.7)",borderRadius:1,p:.5},children:e.jsx(y,{sx:{color:"white",fontSize:20}})}),i.completed&&e.jsx(s,{sx:{position:"absolute",top:8,left:8,bgcolor:"success.main",borderRadius:1,p:.5},children:e.jsx(b,{sx:{color:"white",fontSize:20}})})]}),e.jsxs(x,{sx:{flexGrow:1},children:[e.jsxs(s,{sx:{display:"flex",gap:1,mb:2},children:[e.jsx(p,{label:i.difficulty,size:"small",color:(e=>{switch(e){case"Débutant":return"success";case"Intermédiaire":return"warning";case"Avancé":return"error";default:return"default"}})(i.difficulty)}),e.jsx(p,{label:i.category,size:"small",variant:"outlined"})]}),e.jsx(t,{variant:"h6",gutterBottom:!0,fontWeight:"bold",children:i.title}),e.jsx(t,{variant:"body2",color:"text.secondary",sx:{mb:2},children:i.description}),e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[(s=>{switch(s){case"video":return e.jsx(w,{});case"article":return e.jsx(W,{});case"quiz":return e.jsx(I,{});default:return e.jsx(z,{})}})(i.type),e.jsxs(t,{variant:"caption",color:"text.secondary",children:[i.lessons," leçons"]})]}),e.jsx(t,{variant:"caption",color:"text.secondary",children:i.duration})]}),i.progress>0&&e.jsxs(s,{sx:{mb:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsx(t,{variant:"caption",children:"Progression"}),e.jsxs(t,{variant:"caption",children:[i.progress,"%"]})]}),e.jsx(j,{variant:"determinate",value:i.progress,sx:{height:6,borderRadius:3}})]})]}),e.jsx(v,{children:e.jsx(f,{fullWidth:!0,variant:i.completed?"outlined":"contained",onClick:()=>r(i),disabled:!!i.locked,startIcon:i.completed?e.jsx(b,{}):e.jsx(C,{}),children:i.locked?"Verrouillé":i.completed?"Revoir":i.progress>0?"Continuer":"Commencer"})})]}),F=()=>{const y=k(),{logout:b}=D.useContext(A),[v,C]=D.useState("Tous"),[z,I]=D.useState("Tous"),[W,w]=D.useState(null),[F,G]=D.useState(!1),L=e=>{w(e),G(!0)},M=B.filter((e=>{const s="Tous"===v||e.category===v,i="Tous"===z||e.difficulty===z;return s&&i})),P=B.filter((e=>e.completed)).length,H=B.reduce(((e,s)=>e+s.progress),0)/B.length;return e.jsxs(s,{sx:{flexGrow:1},children:[e.jsx(i,{position:"static",elevation:1,children:e.jsxs(r,{children:[e.jsx(t,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Éducation Financière"}),e.jsx(n,{color:"inherit",onClick:()=>y(S.DASHBOARD),children:e.jsx(o,{})}),e.jsx(n,{color:"inherit",onClick:()=>{b(),y(S.HOME)},children:e.jsx(a,{})})]})}),e.jsxs(l,{maxWidth:"xl",sx:{py:4},children:[e.jsxs(s,{sx:{mb:4},children:[e.jsx(t,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Éducation Financière"}),e.jsx(t,{variant:"body1",color:"text.secondary",children:"Développez vos connaissances financières avec nos modules d'apprentissage interactifs"})]}),e.jsxs(c,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(c,{item:!0,xs:12,md:4,children:e.jsx(d,{children:e.jsxs(x,{sx:{textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"primary.main",fontWeight:"bold",children:P}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Modules Terminés"})]})})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsx(d,{children:e.jsxs(x,{sx:{textAlign:"center"},children:[e.jsxs(t,{variant:"h4",color:"success.main",fontWeight:"bold",children:[H.toFixed(0),"%"]}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Progression Globale"})]})})}),e.jsx(c,{item:!0,xs:12,md:4,children:e.jsx(d,{children:e.jsxs(x,{sx:{textAlign:"center"},children:[e.jsx(t,{variant:"h4",color:"info.main",fontWeight:"bold",children:B.length}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"Modules Disponibles"})]})})})]}),e.jsx(d,{sx:{mb:3},children:e.jsx(x,{children:e.jsxs(s,{sx:{display:"flex",gap:2,flexWrap:"wrap",alignItems:"center"},children:[e.jsx(t,{variant:"body2",fontWeight:"bold",children:"Filtres:"}),e.jsxs(s,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Catégorie:"}),R.map((s=>e.jsx(p,{label:s,onClick:()=>C(s),color:v===s?"primary":"default",variant:v===s?"filled":"outlined",size:"small"},s)))]}),e.jsxs(s,{sx:{display:"flex",gap:1,flexWrap:"wrap"},children:[e.jsx(t,{variant:"body2",color:"text.secondary",children:"Difficulté:"}),T.map((s=>e.jsx(p,{label:s,onClick:()=>I(s),color:z===s?"secondary":"default",variant:z===s?"filled":"outlined",size:"small"},s)))]})]})})}),e.jsx(c,{container:!0,spacing:3,children:M.map((s=>e.jsx(c,{item:!0,xs:12,sm:6,md:4,children:e.jsx(q,{module:s,onStart:L})},s.id)))}),e.jsx(h,{open:F,onClose:()=>G(!1),maxWidth:"sm",fullWidth:!0,children:W&&e.jsxs(e.Fragment,{children:[e.jsx(m,{children:e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(t,{variant:"h2",children:W.image}),e.jsxs(s,{children:[e.jsx(t,{variant:"h6",children:W.title}),e.jsxs(t,{variant:"body2",color:"text.secondary",children:[W.duration," • ",W.lessons," leçons"]})]})]})}),e.jsxs(u,{children:[e.jsx(t,{variant:"body1",sx:{mb:2},children:W.description}),e.jsxs(s,{sx:{mb:2},children:[e.jsx(t,{variant:"body2",fontWeight:"bold",gutterBottom:!0,children:"Ce que vous allez apprendre:"}),e.jsxs(t,{variant:"body2",color:"text.secondary",children:["• Concepts fondamentaux de ",W.category.toLowerCase()]}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"• Stratégies pratiques et applicables"}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"• Exercices interactifs et quiz"}),e.jsx(t,{variant:"body2",color:"text.secondary",children:"• Certificat de completion"})]}),W.progress>0&&e.jsxs(s,{children:[e.jsx(t,{variant:"body2",fontWeight:"bold",gutterBottom:!0,children:"Votre progression:"}),e.jsx(j,{variant:"determinate",value:W.progress,sx:{height:8,borderRadius:4,mb:1}}),e.jsxs(t,{variant:"caption",color:"text.secondary",children:[W.progress,"% terminé"]})]})]}),e.jsxs(g,{children:[e.jsx(f,{onClick:()=>G(!1),children:"Fermer"}),e.jsx(f,{variant:"contained",onClick:()=>{G(!1)},disabled:!!W.locked,children:W.completed?"Revoir":W.progress>0?"Continuer":"Commencer"})]})]})})]})]})};export{F as EducationPage,F as default};
