/**
 * Configuration et instance Axios pour les appels API
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { config, API_ENDPOINTS } from '@/config'
import toast from 'react-hot-toast'

// Types pour les réponses API
export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  error: string
  message: string
  code: string
  details?: any[]
  timestamp: string
  path: string
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Configuration de base d'Axios
const createApiInstance = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: config.api.baseUrl,
    timeout: config.api.timeout,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  })

  // Intercepteur de requête
  instance.interceptors.request.use(
    (config) => {
      // Ajouter le token d'authentification si disponible
      const token = getAuthToken()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }

      // Ajouter des headers personnalisés
      config.headers['X-Client-Version'] = import.meta.env.VITE_APP_VERSION || '1.0.0'
      config.headers['X-Client-Platform'] = 'web'
      
      // Log des requêtes en développement
      if (config.app.isDevelopment) {
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          data: config.data
        })
      }

      return config
    },
    (error) => {
      console.error('❌ Request Error:', error)
      return Promise.reject(error)
    }
  )

  // Intercepteur de réponse
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      // Log des réponses en développement
      if (config.app.isDevelopment) {
        console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          data: response.data
        })
      }

      return response
    },
    async (error: AxiosError<ApiError>) => {
      const { response, request, config: requestConfig } = error

      // Log des erreurs
      console.error('❌ API Error:', {
        url: requestConfig?.url,
        method: requestConfig?.method,
        status: response?.status,
        data: response?.data
      })

      // Gestion des erreurs spécifiques
      if (response) {
        const { status, data } = response

        switch (status) {
          case 401:
            // Token expiré ou invalide
            handleAuthError()
            toast.error('Session expirée. Veuillez vous reconnecter.')
            break

          case 403:
            // Permissions insuffisantes
            toast.error('Vous n\'avez pas les permissions nécessaires.')
            break

          case 404:
            // Ressource non trouvée
            toast.error('Ressource non trouvée.')
            break

          case 422:
            // Erreur de validation
            if (data?.details) {
              const validationErrors = data.details.map((detail: any) => detail.message).join(', ')
              toast.error(`Erreur de validation: ${validationErrors}`)
            } else {
              toast.error('Données invalides.')
            }
            break

          case 429:
            // Trop de requêtes
            toast.error('Trop de requêtes. Veuillez patienter.')
            break

          case 500:
          case 502:
          case 503:
          case 504:
            // Erreurs serveur
            toast.error('Erreur du serveur. Veuillez réessayer plus tard.')
            break

          default:
            // Autres erreurs
            toast.error(data?.message || 'Une erreur est survenue.')
        }
      } else if (request) {
        // Erreur réseau
        toast.error('Erreur de connexion. Vérifiez votre connexion internet.')
      } else {
        // Erreur de configuration
        toast.error('Erreur de configuration de la requête.')
      }

      return Promise.reject(error)
    }
  )

  return instance
}

// Instance API principale
export const api = createApiInstance()

// Fonctions utilitaires
const getAuthToken = (): string | null => {
  // En production, récupérer le token depuis Auth0 ou le localStorage
  return localStorage.getItem('auth_token')
}

const handleAuthError = (): void => {
  // Supprimer le token invalide
  localStorage.removeItem('auth_token')
  
  // Rediriger vers la page de connexion
  if (typeof window !== 'undefined') {
    window.location.href = '/login'
  }
}

// Fonctions d'API génériques avec retry
const withRetry = async <T>(
  apiCall: () => Promise<AxiosResponse<T>>,
  maxRetries: number = config.api.retryAttempts
): Promise<AxiosResponse<T>> => {
  let lastError: any

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall()
    } catch (error) {
      lastError = error
      
      // Ne pas retry sur les erreurs 4xx (erreurs client)
      if (axios.isAxiosError(error) && error.response?.status && error.response.status < 500) {
        throw error
      }

      // Attendre avant de retry
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, config.api.retryDelay * attempt))
      }
    }
  }

  throw lastError
}

// Méthodes HTTP avec retry
export const apiGet = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  const response = await withRetry(() => api.get<T>(url, config))
  return response.data
}

export const apiPost = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  const response = await withRetry(() => api.post<T>(url, data, config))
  return response.data
}

export const apiPut = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  const response = await withRetry(() => api.put<T>(url, data, config))
  return response.data
}

export const apiPatch = async <T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {
  const response = await withRetry(() => api.patch<T>(url, data, config))
  return response.data
}

export const apiDelete = async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
  const response = await withRetry(() => api.delete<T>(url, config))
  return response.data
}

// Fonction pour uploader des fichiers
export const apiUpload = async <T>(
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<T> => {
  const formData = new FormData()
  formData.append('file', file)

  const response = await api.post<T>(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })

  return response.data
}

// Fonction pour télécharger des fichiers
export const apiDownload = async (url: string, filename?: string): Promise<void> => {
  const response = await api.get(url, {
    responseType: 'blob'
  })

  // Créer un lien de téléchargement
  const blob = new Blob([response.data])
  const downloadUrl = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = downloadUrl
  link.download = filename || 'download'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(downloadUrl)
}

// Fonction pour annuler les requêtes
export const createCancelToken = () => {
  return axios.CancelToken.source()
}

// Fonction pour vérifier si une erreur est due à l'annulation
export const isCancel = (error: any): boolean => {
  return axios.isCancel(error)
}

// Fonction pour construire des URLs avec des paramètres de requête
export const buildUrl = (baseUrl: string, params?: Record<string, any>): string => {
  if (!params) return baseUrl

  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value))
    }
  })

  const queryString = searchParams.toString()
  return queryString ? `${baseUrl}?${queryString}` : baseUrl
}

// Export par défaut
export default api
