{"name": "@nouri/api-gateway", "version": "1.0.0", "description": "Passerelle API principale pour Nouri Coach Financier", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:seed": "npx prisma db seed", "db:studio": "npx prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "winston": "^3.10.0", "morgan": "^1.10.0", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "@prisma/client": "^5.3.1", "redis": "^4.6.8", "axios": "^1.5.0", "express-async-errors": "^3.1.1", "joi": "^17.10.1", "multer": "^1.4.5-lts.1", "uuid": "^9.0.0", "moment": "^2.29.4", "express-prometheus-middleware": "^1.2.0", "prom-client": "^14.2.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.14", "@types/compression": "^1.7.3", "@types/morgan": "^1.9.5", "@types/bcryptjs": "^2.4.4", "@types/jsonwebtoken": "^9.0.3", "@types/swagger-ui-express": "^4.1.4", "@types/swagger-jsdoc": "^6.0.1", "@types/multer": "^1.4.7", "@types/uuid": "^9.0.4", "@types/jest": "^29.5.5", "@types/supertest": "^2.0.13", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "prisma": "^5.3.1"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": ["api-gateway", "microservices", "fintech", "tunisia", "nouri"], "author": "<PERSON><PERSON><PERSON>", "license": "PROPRIETARY"}