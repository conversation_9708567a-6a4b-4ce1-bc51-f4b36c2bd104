# Dockerfile de développement pour l'API Gateway Nouri
FROM node:18-alpine

# Métadonnées
LABEL maintainer="Équipe Nouri <<EMAIL>>"
LABEL description="API Gateway Nouri - Environnement de développement"
LABEL version="1.0.0"

# Variables d'environnement
ENV NODE_ENV=development
ENV PORT=3000

# Installer les dépendances système
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Créer le répertoire de travail
WORKDIR /app

# Copier les fichiers de configuration
COPY package*.json ./
COPY tsconfig.json ./
COPY prisma/ ./prisma/

# Installer les dépendances
RUN npm ci --only=development

# Générer le client Prisma
RUN npx prisma generate

# Copier le code source
COPY src/ ./src/

# Créer les répertoires nécessaires
RUN mkdir -p logs

# Exposer le port
EXPOSE 3000

# Commande de développement avec hot reload
CMD ["npm", "run", "dev"]
