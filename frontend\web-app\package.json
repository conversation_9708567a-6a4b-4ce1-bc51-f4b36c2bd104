{"name": "@nouri/web-app", "version": "1.0.0", "description": "Application web Nouri - Coach Financier IA", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "@auth0/auth0-react": "^2.2.1", "@tanstack/react-query": "^4.35.0", "axios": "^1.5.0", "zustand": "^4.4.1", "@mui/material": "^5.14.5", "@mui/icons-material": "^5.14.3", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-charts": "^6.0.0-alpha.18", "@mui/x-date-pickers": "^6.12.0", "dayjs": "^1.11.9", "react-hook-form": "^7.45.4", "@hookform/resolvers": "^3.3.1", "yup": "^1.3.2", "react-i18next": "^13.2.2", "i18next": "^23.4.6", "i18next-browser-languagedetector": "^7.1.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.4", "recharts": "^2.8.0", "react-helmet-async": "^1.3.0", "react-intersection-observer": "^9.5.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.3", "@testing-library/user-event": "^14.4.3", "jsdom": "^22.1.0", "vite-plugin-pwa": "^0.16.4", "workbox-window": "^7.0.0"}, "keywords": ["react", "typescript", "vite", "fintech", "tunisia", "financial-coach"], "author": "<PERSON><PERSON><PERSON>", "license": "PROPRIETARY"}