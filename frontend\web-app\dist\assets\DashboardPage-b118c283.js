import{j as e,B as t,b as n,I as r,R as i,G as s,A as o,i as a,S as c,f as l,k as d,g as u,h as f,l as p,m,n as h,e as y,L as g}from"./mui-vendor-9c785b1c.js";import{W as v}from"./index.module-a126d235.js";import{c as b}from"./utils-vendor-be215184.js";import{S as x}from"./index-69cba9ec.js";import"./react-vendor-ef8b78f8.js";const _=new Map,S=e=>{const t=_.get(e);return t?Object.fromEntries(Object.entries(t.stores).map((([e,t])=>[e,t.getState()]))):{}},j=(e,t={})=>(n,r,i)=>{const{enabled:s,anonymousActionType:o,store:a,...c}=t;let l;try{l=(null==s||s)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(h){}if(!l)return e(n,r,i);const{connection:d,...u}=((e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};const r=_.get(n.name);if(r)return{type:"tracked",store:e,...r};const i={connection:t.connect(n),stores:{}};return _.set(n.name,i),{type:"tracked",store:e,...i}})(a,l,c);let f=!0;i.setState=(e,t,s)=>{const l=n(e,t);if(!f)return l;const u=void 0===s?{type:o||"anonymous"}:"string"==typeof s?{type:s}:s;return void 0===a?(null==d||d.send(u,r()),l):(null==d||d.send({...u,type:`${a}/${u.type}`},{...S(c.name),[a]:i.getState()}),l)};const p=(...e)=>{const t=f;f=!1,n(...e),f=t},m=e(i.setState,r,i);if("untracked"===u.type?null==d||d.init(m):(u.stores[u.store]=i,null==d||d.init(Object.fromEntries(Object.entries(u.stores).map((([e,t])=>[e,e===u.store?m:t.getState()]))))),i.dispatchFromDevtools&&"function"==typeof i.dispatch){let e=!1;const t=i.dispatch;i.dispatch=(...n)=>{"__setState"!==n[0].type||e||(e=!0),t(...n)}}return d.subscribe((e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return;return w(e.payload,(e=>{if("__setState"!==e.type)i.dispatchFromDevtools&&"function"==typeof i.dispatch&&i.dispatch(e);else{if(void 0===a)return void p(e.state);Object.keys(e.state).length;const t=e.state[a];if(null==t)return;JSON.stringify(i.getState())!==JSON.stringify(t)&&p(t)}}));case"DISPATCH":switch(e.payload.type){case"RESET":return p(m),void 0===a?null==d?void 0:d.init(i.getState()):null==d?void 0:d.init(S(c.name));case"COMMIT":return void 0===a?void(null==d||d.init(i.getState())):null==d?void 0:d.init(S(c.name));case"ROLLBACK":return w(e.state,(e=>{if(void 0===a)return p(e),void(null==d||d.init(i.getState()));p(e[a]),null==d||d.init(S(c.name))}));case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return w(e.state,(e=>{void 0!==a?JSON.stringify(i.getState())!==JSON.stringify(e[a])&&p(e[a]):p(e)}));case"IMPORT_STATE":{const{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;return p(void 0===a?r:r[a]),void(null==d||d.send(null,n))}case"PAUSE_RECORDING":return f=!f}return}})),m},w=(e,t)=>{let n;try{n=JSON.parse(e)}catch(r){}void 0!==n&&t(n)};function O(e,t){let n;try{n=e()}catch(r){return}return{getItem:e=>{var r;const i=e=>null===e?null:JSON.parse(e,null==t?void 0:t.reviver),s=null!=(r=n.getItem(e))?r:null;return s instanceof Promise?s.then(i):i(s)},setItem:(e,r)=>n.setItem(e,JSON.stringify(r,null==t?void 0:t.replacer)),removeItem:e=>n.removeItem(e)}}const z=e=>t=>{try{const n=e(t);return n instanceof Promise?n:{then:e=>z(e)(n),catch(e){return this}}}catch(n){return{then(e){return this},catch:e=>z(e)(n)}}},E=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((e,t)=>(n,r,i)=>{let s={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1;const a=new Set,c=new Set;let l;try{l=s.getStorage()}catch(y){}if(!l)return e(((...e)=>{n(...e)}),r,i);const d=z(s.serialize),u=()=>{const e=s.partialize({...r()});let t;const n=d({state:e,version:s.version}).then((e=>l.setItem(s.name,e))).catch((e=>{t=e}));if(t)throw t;return n},f=i.setState;i.setState=(e,t)=>{f(e,t),u()};const p=e(((...e)=>{n(...e),u()}),r,i);let m;const h=()=>{var e;if(!l)return;o=!1,a.forEach((e=>e(r())));const t=(null==(e=s.onRehydrateStorage)?void 0:e.call(s,r()))||void 0;return z(l.getItem.bind(l))(s.name).then((e=>{if(e)return s.deserialize(e)})).then((e=>{if(e){if("number"!=typeof e.version||e.version===s.version)return e.state;if(s.migrate)return s.migrate(e.state,e.version)}})).then((e=>{var t;return m=s.merge(e,null!=(t=r())?t:p),n(m,!0),u()})).then((()=>{null==t||t(m,void 0),o=!0,c.forEach((e=>e(m)))})).catch((e=>{null==t||t(void 0,e)}))};return i.persist={setOptions:e=>{s={...s,...e},e.getStorage&&(l=e.getStorage())},clearStorage:()=>{null==l||l.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>h(),hasHydrated:()=>o,onHydrate:e=>(a.add(e),()=>{a.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},h(),m||p})(e,t):((e,t)=>(n,r,i)=>{let s={storage:O((()=>localStorage)),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1;const a=new Set,c=new Set;let l=s.storage;if(!l)return e(((...e)=>{n(...e)}),r,i);const d=()=>{const e=s.partialize({...r()});return l.setItem(s.name,{state:e,version:s.version})},u=i.setState;i.setState=(e,t)=>{u(e,t),d()};const f=e(((...e)=>{n(...e),d()}),r,i);let p;i.getInitialState=()=>f;const m=()=>{var e,t;if(!l)return;o=!1,a.forEach((e=>{var t;return e(null!=(t=r())?t:f)}));const i=(null==(t=s.onRehydrateStorage)?void 0:t.call(s,null!=(e=r())?e:f))||void 0;return z(l.getItem.bind(l))(s.name).then((e=>{if(e){if("number"!=typeof e.version||e.version===s.version)return[!1,e.state];if(s.migrate)return[!0,s.migrate(e.state,e.version)]}return[!1,void 0]})).then((e=>{var t;const[i,o]=e;if(p=s.merge(o,null!=(t=r())?t:f),n(p,!0),i)return d()})).then((()=>{null==i||i(p,void 0),p=r(),o=!0,c.forEach((e=>e(p)))})).catch((e=>{null==i||i(void 0,e)}))};return i.persist={setOptions:e=>{s={...s,...e},e.storage&&(l=e.storage)},clearStorage:()=>{null==l||l.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>m(),hasHydrated:()=>o,onHydrate:e=>(a.add(e),()=>{a.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},s.skipHydration||m(),p||f})(e,t);var P=Symbol.for("immer-nothing"),C=Symbol.for("immer-draftable"),A=Symbol.for("immer-state");function I(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var M=Object.getPrototypeOf;function k(e){return!!e&&!!e[A]}function N(e){var t;return!!e&&(D(e)||Array.isArray(e)||!!e[C]||!!(null==(t=e.constructor)?void 0:t[C])||J(e)||H(e))}var R=Object.prototype.constructor.toString();function D(e){if(!e||"object"!=typeof e)return!1;const t=M(e);if(null===t)return!0;const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===R}function T(e,t){0===F(e)?Reflect.ownKeys(e).forEach((n=>{t(n,e[n],e)})):e.forEach(((n,r)=>t(r,n,e)))}function F(e){const t=e[A];return t?t.type_:Array.isArray(e)?1:J(e)?2:H(e)?3:0}function L(e,t){return 2===F(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function U(e,t,n){const r=F(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function J(e){return e instanceof Map}function H(e){return e instanceof Set}function V(e){return e.copy_||e.base_}function W(e,t){if(J(e))return new Map(e);if(H(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const n=D(e);if(!0===t||"class_only"===t&&!n){const t=Object.getOwnPropertyDescriptors(e);delete t[A];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){const i=n[r],s=t[i];!1===s.writable&&(s.writable=!0,s.configurable=!0),(s.get||s.set)&&(t[i]={configurable:!0,writable:!0,enumerable:s.enumerable,value:e[i]})}return Object.create(M(e),t)}{const t=M(e);if(null!==t&&n)return{...e};const r=Object.create(t);return Object.assign(r,e)}}function $(e,t=!1){return K(e)||k(e)||!N(e)||(F(e)>1&&(e.set=e.add=e.clear=e.delete=B),Object.freeze(e),t&&Object.entries(e).forEach((([e,t])=>$(t,!0)))),e}function B(){I(2)}function K(e){return Object.isFrozen(e)}var G,X={};function q(e){const t=X[e];return t||I(0),t}function Q(){return G}function Y(e,t){t&&(q("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function Z(e){ee(e),e.drafts_.forEach(ne),e.drafts_=null}function ee(e){e===G&&(G=e.parent_)}function te(e){return G={drafts_:[],parent_:G,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ne(e){const t=e[A];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function re(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];return void 0!==e&&e!==n?(n[A].modified_&&(Z(t),I(4)),N(e)&&(e=ie(t,e),t.parent_||oe(t,e)),t.patches_&&q("Patches").generateReplacementPatches_(n[A].base_,e,t.patches_,t.inversePatches_)):e=ie(t,n,[]),Z(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==P?e:void 0}function ie(e,t,n){if(K(t))return t;const r=t[A];if(!r)return T(t,((i,s)=>se(e,r,t,i,s,n))),t;if(r.scope_!==e)return t;if(!r.modified_)return oe(e,r.base_,!0),r.base_;if(!r.finalized_){r.finalized_=!0,r.scope_.unfinalizedDrafts_--;const t=r.copy_;let i=t,s=!1;3===r.type_&&(i=new Set(t),t.clear(),s=!0),T(i,((i,o)=>se(e,r,t,i,o,n,s))),oe(e,t,!1),n&&e.patches_&&q("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}return r.copy_}function se(e,t,n,r,i,s,o){if(k(i)){const o=ie(e,i,s&&t&&3!==t.type_&&!L(t.assigned_,r)?s.concat(r):void 0);if(U(n,r,o),!k(o))return;e.canAutoFreeze_=!1}else o&&n.add(i);if(N(i)&&!K(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;ie(e,i),t&&t.scope_.parent_||"symbol"==typeof r||!Object.prototype.propertyIsEnumerable.call(n,r)||oe(e,i)}}function oe(e,t,n=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&$(t,n)}var ae={get(e,t){if(t===A)return e;const n=V(e);if(!L(n,t))return function(e,t,n){var r;const i=de(t,n);return i?"value"in i?i.value:null==(r=i.get)?void 0:r.call(e.draft_):void 0}(e,n,t);const r=n[t];return e.finalized_||!N(r)?r:r===le(e.base_,t)?(fe(e),e.copy_[t]=pe(r,e)):r},has:(e,t)=>t in V(e),ownKeys:e=>Reflect.ownKeys(V(e)),set(e,t,n){const r=de(V(e),t);if(null==r?void 0:r.set)return r.set.call(e.draft_,n),!0;if(!e.modified_){const r=le(V(e),t),o=null==r?void 0:r[A];if(o&&o.base_===n)return e.copy_[t]=n,e.assigned_[t]=!1,!0;if(((i=n)===(s=r)?0!==i||1/i==1/s:i!=i&&s!=s)&&(void 0!==n||L(e.base_,t)))return!0;fe(e),ue(e)}var i,s;return e.copy_[t]===n&&(void 0!==n||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=n,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==le(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,fe(e),ue(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const n=V(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty(){I(11)},getPrototypeOf:e=>M(e.base_),setPrototypeOf(){I(12)}},ce={};function le(e,t){const n=e[A];return(n?V(n):e)[t]}function de(e,t){if(!(t in e))return;let n=M(e);for(;n;){const e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=M(n)}}function ue(e){e.modified_||(e.modified_=!0,e.parent_&&ue(e.parent_))}function fe(e){e.copy_||(e.copy_=W(e.base_,e.scope_.immer_.useStrictShallowCopy_))}T(ae,((e,t)=>{ce[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),ce.deleteProperty=function(e,t){return ce.set.call(this,e,t,void 0)},ce.set=function(e,t,n){return ae.set.call(this,e[0],t,n,e[0])};function pe(e,t){const n=J(e)?q("MapSet").proxyMap_(e,t):H(e)?q("MapSet").proxySet_(e,t):function(e,t){const n=Array.isArray(e),r={type_:n?1:0,scope_:t?t.scope_:Q(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let i=r,s=ae;n&&(i=[r],s=ce);const{revoke:o,proxy:a}=Proxy.revocable(i,s);return r.draft_=a,r.revoke_=o,a}(e,t);return(t?t.scope_:Q()).drafts_.push(n),n}function me(e){if(!N(e)||K(e))return e;const t=e[A];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,n=W(e,t.scope_.immer_.useStrictShallowCopy_)}else n=W(e,!0);return T(n,((e,t)=>{U(n,e,me(t))})),t&&(t.finalized_=!1),n}var he=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,n)=>{if("function"==typeof e&&"function"!=typeof t){const n=t;t=e;const r=this;return function(e=n,...i){return r.produce(e,(e=>t.call(this,e,...i)))}}let r;if("function"!=typeof t&&I(6),void 0!==n&&"function"!=typeof n&&I(7),N(e)){const i=te(this),s=pe(e,void 0);let o=!0;try{r=t(s),o=!1}finally{o?Z(i):ee(i)}return Y(i,n),re(r,i)}if(!e||"object"!=typeof e){if(r=t(e),void 0===r&&(r=e),r===P&&(r=void 0),this.autoFreeze_&&$(r,!0),n){const t=[],i=[];q("Patches").generateReplacementPatches_(e,r,t,i),n(t,i)}return r}I(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...n)=>this.produceWithPatches(t,(t=>e(t,...n)));let n,r;return[this.produce(e,t,((e,t)=>{n=e,r=t})),n,r]},"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof(null==e?void 0:e.useStrictShallowCopy)&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){N(e)||I(8),k(e)&&(e=function(e){k(e)||I(10);return me(e)}(e));const t=te(this),n=pe(e,void 0);return n[A].isManual_=!0,ee(t),n}finishDraft(e,t){const n=e&&e[A];n&&n.isManual_||I(9);const{scope_:r}=n;return Y(r,t),re(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));const r=q("Patches").applyPatches_;return k(e)?r(e,t):this.produce(e,(e=>r(e,t)))}},ye=he.produce;he.produceWithPatches.bind(he),he.setAutoFreeze.bind(he),he.setUseStrictShallowCopy.bind(he),he.applyPatches.bind(he),he.createDraft.bind(he),he.finishDraft.bind(he);const ge=e=>(t,n,r)=>(r.setState=(e,n,...r)=>{const i="function"==typeof e?ye(e):e;return t(i,n,...r)},e(r.setState,n,r)),ve={isInitialized:!1,isLoading:!1,error:null,user:null,accounts:[],dashboardStats:null,sidebarOpen:!0,currentPage:"dashboard",notifications:[],unreadCount:0,lastSyncAt:null,isSyncing:!1},be=b()(j(E(ge((e=>({...ve,initializeApp:()=>{e((e=>{e.isLoading=!0,e.error=null})),setTimeout((()=>{e((e=>{e.isInitialized=!0,e.isLoading=!1}))}),1e3)},setInitialized:t=>{e((e=>{e.isInitialized=t}))},setUser:t=>{e((e=>{e.user=t}))},updateUser:t=>{e((e=>{e.user&&Object.assign(e.user,t)}))},setAccounts:t=>{e((e=>{e.accounts=t}))},addAccount:t=>{e((e=>{e.accounts.push(t)}))},updateAccount:(t,n)=>{e((e=>{const r=e.accounts.findIndex((e=>e.id===t));-1!==r&&Object.assign(e.accounts[r],n)}))},removeAccount:t=>{e((e=>{e.accounts=e.accounts.filter((e=>e.id!==t))}))},setDashboardStats:t=>{e((e=>{e.dashboardStats=t}))},setSidebarOpen:t=>{e((e=>{e.sidebarOpen=t}))},toggleSidebar:()=>{e((e=>{e.sidebarOpen=!e.sidebarOpen}))},setCurrentPage:t=>{e((e=>{e.currentPage=t}))},setNotifications:t=>{e((e=>{e.notifications=t,e.unreadCount=t.filter((e=>!e.isRead)).length}))},addNotification:t=>{e((e=>{e.notifications.unshift(t),t.isRead||(e.unreadCount+=1)}))},markNotificationAsRead:t=>{e((e=>{const n=e.notifications.find((e=>e.id===t));n&&!n.isRead&&(n.isRead=!0,e.unreadCount-=1)}))},markAllNotificationsAsRead:()=>{e((e=>{e.notifications.forEach((e=>{e.isRead=!0})),e.unreadCount=0}))},removeNotification:t=>{e((e=>{const n=e.notifications.findIndex((e=>e.id===t));if(-1!==n){e.notifications[n].isRead||(e.unreadCount-=1),e.notifications.splice(n,1)}}))},setSyncing:t=>{e((e=>{e.isSyncing=t}))},setLastSyncAt:t=>{e((e=>{e.lastSyncAt=t}))},setError:t=>{e((e=>{e.error=t}))},clearError:()=>{e((e=>{e.error=null}))},setLoading:t=>{e((e=>{e.isLoading=t}))},resetStore:()=>{e((()=>({...ve})))}}))),{name:x.USER_PREFERENCES,partialize:e=>({sidebarOpen:e.sidebarOpen,currentPage:e.currentPage,lastSyncAt:e.lastSyncAt})}),{name:"app-store"})),xe=()=>"undefined"!=typeof window&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light",_e=()=>"undefined"!=typeof window&&window.matchMedia("(prefers-reduced-motion: reduce)").matches,Se={mode:"system",isDarkMode:"dark"===xe(),language:(()=>{if("undefined"!=typeof window){const e=navigator.language.split("-")[0];if(["fr","ar","en"].includes(e))return e}return"fr"})(),currency:"TND",isRTL:!1,sidebarCollapsed:!1,compactMode:!1,animationsEnabled:!0,soundEnabled:!0,highContrast:!1,fontSize:"medium",reducedMotion:_e()},je=b()(j(E(ge(((e,t)=>({...Se,setThemeMode:t=>{e((e=>{e.mode=t,e.isDarkMode="system"===t?"dark"===xe():"dark"===t}))},toggleTheme:()=>{const{mode:n,isDarkMode:r}=t();e((e=>{e.mode=r?"light":"dark",e.isDarkMode=!r}))},setLanguage:t=>{e((e=>{e.language=t,e.isRTL="ar"===t})),"undefined"!=typeof document&&(document.documentElement.dir="ar"===t?"rtl":"ltr",document.documentElement.lang=t)},setCurrency:t=>{e((e=>{e.currency=t}))},setSidebarCollapsed:t=>{e((e=>{e.sidebarCollapsed=t}))},toggleSidebarCollapsed:()=>{e((e=>{e.sidebarCollapsed=!e.sidebarCollapsed}))},setCompactMode:t=>{e((e=>{e.compactMode=t}))},setAnimationsEnabled:t=>{e((e=>{e.animationsEnabled=t}))},setSoundEnabled:t=>{e((e=>{e.soundEnabled=t}))},setHighContrast:t=>{e((e=>{e.highContrast=t}))},setFontSize:t=>{if(e((e=>{e.fontSize=t})),"undefined"!=typeof document){const e=document.documentElement;switch(t){case"small":e.style.fontSize="14px";break;case"large":e.style.fontSize="18px";break;default:e.style.fontSize="16px"}}},setReducedMotion:t=>{e((e=>{e.reducedMotion=t}))},resetToDefaults:()=>{e((()=>({...Se})))},applySystemPreferences:()=>{e((e=>{e.isDarkMode="dark"===xe(),e.reducedMotion=_e(),"system"===e.mode&&(e.isDarkMode="dark"===xe())}))}}))),{name:x.THEME,partialize:e=>({mode:e.mode,language:e.language,currency:e.currency,sidebarCollapsed:e.sidebarCollapsed,compactMode:e.compactMode,animationsEnabled:e.animationsEnabled,soundEnabled:e.soundEnabled,highContrast:e.highContrast,fontSize:e.fontSize,reducedMotion:e.reducedMotion})}),{name:"theme-store"}));if("undefined"!=typeof window){window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",(()=>{const{mode:e,applySystemPreferences:t}=je.getState();"system"===e&&t()}));window.matchMedia("(prefers-reduced-motion: reduce)").addEventListener("change",(e=>{const{setReducedMotion:t}=je.getState();t(e.matches)}))}const we=({title:r,value:i,subtitle:s,icon:o,color:a="primary",trend:c,trendValue:l})=>e.jsx(u,{sx:{height:"100%"},children:e.jsxs(f,{children:[e.jsxs(t,{sx:{display:"flex",alignItems:"center",mb:2},children:[e.jsx(p,{sx:{bgcolor:`${a}.main`,color:`${a}.contrastText`,mr:2},children:o}),e.jsxs(t,{sx:{flex:1},children:[e.jsx(n,{variant:"h4",component:"div",sx:{fontWeight:"bold"},children:i}),e.jsx(n,{variant:"body2",color:"text.secondary",children:r})]})]}),s&&e.jsx(n,{variant:"body2",color:"text.secondary",sx:{mb:1},children:s}),c&&l&&e.jsxs(t,{sx:{display:"flex",alignItems:"center",gap:1},children:["up"===c?e.jsx(m,{color:"success",fontSize:"small"}):"down"===c?e.jsx(h,{color:"error",fontSize:"small"}):null,e.jsx(n,{variant:"caption",color:"up"===c?"success.main":"down"===c?"error.main":"text.secondary",children:l})]})]})}),Oe=({name:r,current:i,target:s,currency:o,category:a})=>{const c=i/s*100;return e.jsx(u,{children:e.jsxs(f,{children:[e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsx(n,{variant:"h6",component:"div",children:r}),a&&e.jsx(y,{label:a,size:"small",color:"primary",variant:"outlined"})]}),e.jsxs(t,{sx:{mb:2},children:[e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsxs(n,{variant:"body2",color:"text.secondary",children:[i.toLocaleString()," ",o]}),e.jsxs(n,{variant:"body2",color:"text.secondary",children:[s.toLocaleString()," ",o]})]}),e.jsx(g,{variant:"determinate",value:Math.min(c,100),sx:{height:8,borderRadius:4,backgroundColor:"grey.200","& .MuiLinearProgress-bar":{borderRadius:4}}}),e.jsxs(n,{variant:"caption",color:"text.secondary",sx:{mt:.5,display:"block"},children:[c.toFixed(1),"% atteint"]})]})]})})},ze=()=>{const p=be((e=>e.user)),m=be((e=>e.dashboardStats)),{currency:h}=je(),y=m||{totalBalance:15420.5,weeklySpending:1250.3,unreadNotifications:3,activeAccounts:2,activeBudgets:4,activeSavingsGoals:2};return e.jsxs(e.Fragment,{children:[e.jsxs(v,{children:[e.jsx("title",{children:"Tableau de bord - Nouri"}),e.jsx("meta",{name:"description",content:"Vue d'ensemble de vos finances avec Nouri"})]}),e.jsxs(t,{children:[e.jsx(t,{sx:{mb:4},children:e.jsxs(t,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(t,{children:[e.jsxs(n,{variant:"h4",component:"h1",sx:{fontWeight:"bold",mb:1},children:["Bonjour ",(null==p?void 0:p.firstName)||"Utilisateur"," 👋"]}),e.jsx(n,{variant:"body1",color:"text.secondary",children:"Voici un aperçu de vos finances aujourd'hui"})]}),e.jsx(r,{color:"primary","aria-label":"actualiser",children:e.jsx(i,{})})]})}),e.jsxs(s,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(we,{title:"Solde total",value:`${y.totalBalance.toLocaleString()} ${h}`,icon:e.jsx(o,{}),color:"primary",trend:"up",trendValue:"+2.5% ce mois"})}),e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(we,{title:"Dépenses cette semaine",value:`${y.weeklySpending.toLocaleString()} ${h}`,icon:e.jsx(a,{}),color:"warning",trend:"down",trendValue:"-15% vs semaine dernière"})}),e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(we,{title:"Objectifs d'épargne",value:y.activeSavingsGoals,subtitle:"objectifs actifs",icon:e.jsx(c,{}),color:"success"})}),e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(we,{title:"Budgets actifs",value:y.activeBudgets,subtitle:"budgets en cours",icon:e.jsx(o,{}),color:"secondary"})})]}),e.jsxs(s,{container:!0,spacing:3,children:[e.jsxs(s,{item:!0,xs:12,md:6,children:[e.jsxs(t,{sx:{mb:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(n,{variant:"h6",component:"h2",children:"Objectifs d'épargne"}),e.jsx(l,{startIcon:e.jsx(d,{}),variant:"outlined",size:"small",children:"Nouvel objectif"})]}),e.jsx(t,{sx:{display:"flex",flexDirection:"column",gap:2},children:[{name:"Voyage en Europe",current:2500,target:5e3,currency:"TND",category:"Voyage"},{name:"Fonds d'urgence",current:8e3,target:1e4,currency:"TND",category:"Urgence"}].map(((t,n)=>e.jsx(Oe,{...t},n)))})]}),e.jsxs(s,{item:!0,xs:12,md:6,children:[e.jsxs(t,{sx:{mb:2,display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(n,{variant:"h6",component:"h2",children:"Transactions récentes"}),e.jsx(l,{variant:"text",size:"small",children:"Voir tout"})]}),e.jsx(u,{children:e.jsx(f,{children:e.jsx(n,{variant:"body2",color:"text.secondary",align:"center",sx:{py:4},children:"Aucune transaction récente"})})})]})]}),e.jsxs(t,{sx:{mt:4},children:[e.jsx(n,{variant:"h6",component:"h2",sx:{mb:2},children:"Actions rapides"}),e.jsxs(s,{container:!0,spacing:2,children:[e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(l,{variant:"outlined",fullWidth:!0,sx:{py:2},startIcon:e.jsx(d,{}),children:"Nouveau budget"})}),e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(l,{variant:"outlined",fullWidth:!0,sx:{py:2},startIcon:e.jsx(c,{}),children:"Objectif d'épargne"})}),e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(l,{variant:"outlined",fullWidth:!0,sx:{py:2},startIcon:e.jsx(i,{}),children:"Synchroniser"})}),e.jsx(s,{item:!0,xs:12,sm:6,md:3,children:e.jsx(l,{variant:"outlined",fullWidth:!0,sx:{py:2},startIcon:e.jsx(a,{}),children:"Voir rapports"})})]})]})]})]})};export{ze as DashboardPage,ze as default};
