{"version": 3, "file": "auth0-spa-js.cjs.js", "sources": ["../../node_modules/tslib/tslib.es6.js", "../../node_modules/browser-tabs-lock/processLock.js", "../../node_modules/browser-tabs-lock/index.js", "../../src/version.ts", "../../src/constants.ts", "../../src/errors.ts", "../../src/utils.ts", "../../src/worker/worker.utils.ts", "../../src/http.ts", "../../src/api.ts", "../../src/scope.ts", "../../src/cache/shared.ts", "../../src/cache/cache-localstorage.ts", "../../src/cache/cache-memory.ts", "../../src/cache/cache-manager.ts", "../../src/transaction-manager.ts", "../../src/jwt.ts", "../../node_modules/es-cookie/src/es-cookie.js", "../../src/storage.ts", "../../src/promise-utils.ts", "../../src/cache/key-manifest.ts", "../../src/Auth0Client.utils.ts", "../../src/Auth0Client.ts", "../../src/global.ts", "../../src/index.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ProcessLocking = /** @class */ (function () {\n    function ProcessLocking() {\n        var _this = this;\n        this.locked = new Map();\n        this.addToLocked = function (key, toAdd) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined) {\n                if (toAdd === undefined) {\n                    _this.locked.set(key, []);\n                }\n                else {\n                    _this.locked.set(key, [toAdd]);\n                }\n            }\n            else {\n                if (toAdd !== undefined) {\n                    callbacks.unshift(toAdd);\n                    _this.locked.set(key, callbacks);\n                }\n            }\n        };\n        this.isLocked = function (key) {\n            return _this.locked.has(key);\n        };\n        this.lock = function (key) {\n            return new Promise(function (resolve, reject) {\n                if (_this.isLocked(key)) {\n                    _this.addToLocked(key, resolve);\n                }\n                else {\n                    _this.addToLocked(key);\n                    resolve();\n                }\n            });\n        };\n        this.unlock = function (key) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined || callbacks.length === 0) {\n                _this.locked.delete(key);\n                return;\n            }\n            var toCall = callbacks.pop();\n            _this.locked.set(key, callbacks);\n            if (toCall !== undefined) {\n                setTimeout(toCall, 0);\n            }\n        };\n    }\n    ProcessLocking.getInstance = function () {\n        if (ProcessLocking.instance === undefined) {\n            ProcessLocking.instance = new ProcessLocking();\n        }\n        return ProcessLocking.instance;\n    };\n    return ProcessLocking;\n}());\nfunction getLock() {\n    return ProcessLocking.getInstance();\n}\nexports.default = getLock;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar _this = this;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar processLock_1 = require(\"./processLock\");\n/**\n * @author: SuperTokens (https://github.com/supertokens)\n * This library was created as a part of a larger project, SuperTokens(https://supertokens.io) - the best session management solution.\n * You can also check out our other projects on https://github.com/supertokens\n *\n * To contribute to this package visit https://github.com/supertokens/browser-tabs-lock\n * If you face any problems you can file an issue on https://github.com/supertokens/browser-tabs-lock/issues\n *\n * If you have any questions or if you just want to say hi visit https://supertokens.io/discord\n */\n/**\n * @constant\n * @type {string}\n * @default\n * @description All the locks taken by this package will have this as prefix\n*/\nvar LOCK_STORAGE_KEY = 'browser-tabs-lock-key';\nvar DEFAULT_STORAGE_HANDLER = {\n    key: function (index) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    getItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    clear: function () { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, window.localStorage.clear()];\n        });\n    }); },\n    removeItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    setItem: function (key, value) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    keySync: function (index) {\n        return window.localStorage.key(index);\n    },\n    getItemSync: function (key) {\n        return window.localStorage.getItem(key);\n    },\n    clearSync: function () {\n        return window.localStorage.clear();\n    },\n    removeItemSync: function (key) {\n        return window.localStorage.removeItem(key);\n    },\n    setItemSync: function (key, value) {\n        return window.localStorage.setItem(key, value);\n    },\n};\n/**\n * @function delay\n * @param {number} milliseconds - How long the delay should be in terms of milliseconds\n * @returns {Promise<void>}\n */\nfunction delay(milliseconds) {\n    return new Promise(function (resolve) { return setTimeout(resolve, milliseconds); });\n}\n/**\n * @function generateRandomString\n * @params {number} length - How long the random string should be\n * @returns {string}\n * @description returns random string whose length is equal to the length passed as parameter\n */\nfunction generateRandomString(length) {\n    var CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz';\n    var randomstring = '';\n    for (var i = 0; i < length; i++) {\n        var INDEX = Math.floor(Math.random() * CHARS.length);\n        randomstring += CHARS[INDEX];\n    }\n    return randomstring;\n}\n/**\n * @function getLockId\n * @returns {string}\n * @description Generates an id which will be unique for the browser tab\n */\nfunction getLockId() {\n    return Date.now().toString() + generateRandomString(15);\n}\nvar SuperTokensLock = /** @class */ (function () {\n    function SuperTokensLock(storageHandler) {\n        this.acquiredIatSet = new Set();\n        this.storageHandler = undefined;\n        this.id = getLockId();\n        this.acquireLock = this.acquireLock.bind(this);\n        this.releaseLock = this.releaseLock.bind(this);\n        this.releaseLock__private__ = this.releaseLock__private__.bind(this);\n        this.waitForSomethingToChange = this.waitForSomethingToChange.bind(this);\n        this.refreshLockWhileAcquired = this.refreshLockWhileAcquired.bind(this);\n        this.storageHandler = storageHandler;\n        if (SuperTokensLock.waiters === undefined) {\n            SuperTokensLock.waiters = [];\n        }\n    }\n    /**\n     * @async\n     * @memberOf Lock\n     * @function acquireLock\n     * @param {string} lockKey - Key for which the lock is being acquired\n     * @param {number} [timeout=5000] - Maximum time for which the function will wait to acquire the lock\n     * @returns {Promise<boolean>}\n     * @description Will return true if lock is being acquired, else false.\n     *              Also the lock can be acquired for maximum 10 secs\n     */\n    SuperTokensLock.prototype.acquireLock = function (lockKey, timeout) {\n        if (timeout === void 0) { timeout = 5000; }\n        return __awaiter(this, void 0, void 0, function () {\n            var iat, MAX_TIME, STORAGE_KEY, STORAGE, lockObj, TIMEOUT_KEY, lockObjPostDelay, parsedLockObjPostDelay;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        iat = Date.now() + generateRandomString(4);\n                        MAX_TIME = Date.now() + timeout;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        _a.label = 1;\n                    case 1:\n                        if (!(Date.now() < MAX_TIME)) return [3 /*break*/, 8];\n                        return [4 /*yield*/, delay(30)];\n                    case 2:\n                        _a.sent();\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (!(lockObj === null)) return [3 /*break*/, 5];\n                        TIMEOUT_KEY = this.id + \"-\" + lockKey + \"-\" + iat;\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        return [4 /*yield*/, delay(Math.floor(Math.random() * 25))];\n                    case 3:\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        _a.sent();\n                        STORAGE.setItemSync(STORAGE_KEY, JSON.stringify({\n                            id: this.id,\n                            iat: iat,\n                            timeoutKey: TIMEOUT_KEY,\n                            timeAcquired: Date.now(),\n                            timeRefreshed: Date.now()\n                        }));\n                        return [4 /*yield*/, delay(30)];\n                    case 4:\n                        _a.sent(); // this is to prevent race conditions. This time must be more than the time it takes for storage.setItem\n                        lockObjPostDelay = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObjPostDelay !== null) {\n                            parsedLockObjPostDelay = JSON.parse(lockObjPostDelay);\n                            if (parsedLockObjPostDelay.id === this.id && parsedLockObjPostDelay.iat === iat) {\n                                this.acquiredIatSet.add(iat);\n                                this.refreshLockWhileAcquired(STORAGE_KEY, iat);\n                                return [2 /*return*/, true];\n                            }\n                        }\n                        return [3 /*break*/, 7];\n                    case 5:\n                        SuperTokensLock.lockCorrector(this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler);\n                        return [4 /*yield*/, this.waitForSomethingToChange(MAX_TIME)];\n                    case 6:\n                        _a.sent();\n                        _a.label = 7;\n                    case 7:\n                        iat = Date.now() + generateRandomString(4);\n                        return [3 /*break*/, 1];\n                    case 8: return [2 /*return*/, false];\n                }\n            });\n        });\n    };\n    SuperTokensLock.prototype.refreshLockWhileAcquired = function (storageKey, iat) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {\n                    var STORAGE, lockObj, parsedLockObj;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0: return [4 /*yield*/, processLock_1.default().lock(iat)];\n                            case 1:\n                                _a.sent();\n                                if (!this.acquiredIatSet.has(iat)) {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                                lockObj = STORAGE.getItemSync(storageKey);\n                                if (lockObj !== null) {\n                                    parsedLockObj = JSON.parse(lockObj);\n                                    parsedLockObj.timeRefreshed = Date.now();\n                                    STORAGE.setItemSync(storageKey, JSON.stringify(parsedLockObj));\n                                    processLock_1.default().unlock(iat);\n                                }\n                                else {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                this.refreshLockWhileAcquired(storageKey, iat);\n                                return [2 /*return*/];\n                        }\n                    });\n                }); }, 1000);\n                return [2 /*return*/];\n            });\n        });\n    };\n    SuperTokensLock.prototype.waitForSomethingToChange = function (MAX_TIME) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, new Promise(function (resolve) {\n                            var resolvedCalled = false;\n                            var startedAt = Date.now();\n                            var MIN_TIME_TO_WAIT = 50; // ms\n                            var removedListeners = false;\n                            function stopWaiting() {\n                                if (!removedListeners) {\n                                    window.removeEventListener('storage', stopWaiting);\n                                    SuperTokensLock.removeFromWaiting(stopWaiting);\n                                    clearTimeout(timeOutId);\n                                    removedListeners = true;\n                                }\n                                if (!resolvedCalled) {\n                                    resolvedCalled = true;\n                                    var timeToWait = MIN_TIME_TO_WAIT - (Date.now() - startedAt);\n                                    if (timeToWait > 0) {\n                                        setTimeout(resolve, timeToWait);\n                                    }\n                                    else {\n                                        resolve(null);\n                                    }\n                                }\n                            }\n                            window.addEventListener('storage', stopWaiting);\n                            SuperTokensLock.addToWaiting(stopWaiting);\n                            var timeOutId = setTimeout(stopWaiting, Math.max(0, MAX_TIME - Date.now()));\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    SuperTokensLock.addToWaiting = function (func) {\n        this.removeFromWaiting(func);\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters.push(func);\n    };\n    SuperTokensLock.removeFromWaiting = function (func) {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters = SuperTokensLock.waiters.filter(function (i) { return i !== func; });\n    };\n    SuperTokensLock.notifyWaiters = function () {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        var waiters = SuperTokensLock.waiters.slice(); // so that if Lock.waiters is changed it's ok.\n        waiters.forEach(function (i) { return i(); });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.releaseLock__private__(lockKey)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock__private__ = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            var STORAGE, STORAGE_KEY, lockObj, parsedlockObj;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObj === null) {\n                            return [2 /*return*/];\n                        }\n                        parsedlockObj = JSON.parse(lockObj);\n                        if (!(parsedlockObj.id === this.id)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, processLock_1.default().lock(parsedlockObj.iat)];\n                    case 1:\n                        _a.sent();\n                        this.acquiredIatSet.delete(parsedlockObj.iat);\n                        STORAGE.removeItemSync(STORAGE_KEY);\n                        processLock_1.default().unlock(parsedlockObj.iat);\n                        SuperTokensLock.notifyWaiters();\n                        _a.label = 2;\n                    case 2: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * @function lockCorrector\n     * @returns {void}\n     * @description If a lock is acquired by a tab and the tab is closed before the lock is\n     *              released, this function will release those locks\n     */\n    SuperTokensLock.lockCorrector = function (storageHandler) {\n        var MIN_ALLOWED_TIME = Date.now() - 5000;\n        var STORAGE = storageHandler;\n        var KEYS = [];\n        var currIndex = 0;\n        while (true) {\n            var key = STORAGE.keySync(currIndex);\n            if (key === null) {\n                break;\n            }\n            KEYS.push(key);\n            currIndex++;\n        }\n        var notifyWaiters = false;\n        for (var i = 0; i < KEYS.length; i++) {\n            var LOCK_KEY = KEYS[i];\n            if (LOCK_KEY.includes(LOCK_STORAGE_KEY)) {\n                var lockObj = STORAGE.getItemSync(LOCK_KEY);\n                if (lockObj !== null) {\n                    var parsedlockObj = JSON.parse(lockObj);\n                    if ((parsedlockObj.timeRefreshed === undefined && parsedlockObj.timeAcquired < MIN_ALLOWED_TIME) ||\n                        (parsedlockObj.timeRefreshed !== undefined && parsedlockObj.timeRefreshed < MIN_ALLOWED_TIME)) {\n                        STORAGE.removeItemSync(LOCK_KEY);\n                        notifyWaiters = true;\n                    }\n                }\n            }\n        }\n        if (notifyWaiters) {\n            SuperTokensLock.notifyWaiters();\n        }\n    };\n    SuperTokensLock.waiters = undefined;\n    return SuperTokensLock;\n}());\nexports.default = SuperTokensLock;\n", "export default '2.2.0';\n", "import { PopupConfigOptions } from './global';\nimport version from './version';\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS = 60;\n\n/**\n * @ignore\n */\nexport const DEFAULT_POPUP_CONFIG_OPTIONS: PopupConfigOptions = {\n  timeoutInSeconds: DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n};\n\n/**\n * @ignore\n */\nexport const DEFAULT_SILENT_TOKEN_RETRY_COUNT = 3;\n\n/**\n * @ignore\n */\nexport const CLEANUP_IFRAME_TIMEOUT_IN_SECONDS = 2;\n\n/**\n * @ignore\n */\nexport const DEFAULT_FETCH_TIMEOUT_MS = 10000;\n\nexport const CACHE_LOCATION_MEMORY = 'memory';\nexport const CACHE_LOCATION_LOCAL_STORAGE = 'localstorage';\n\n/**\n * @ignore\n */\nexport const MISSING_REFRESH_TOKEN_ERROR_MESSAGE = 'Missing Refresh Token';\n\n/**\n * @ignore\n */\nexport const INVALID_REFRESH_TOKEN_ERROR_MESSAGE = 'invalid refresh token';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SCOPE = 'openid profile email';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SESSION_CHECK_EXPIRY_DAYS = 1;\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTH0_CLIENT = {\n  name: 'auth0-spa-js',\n  version: version\n};\n\nexport const DEFAULT_NOW_PROVIDER = () => Date.now();\n", "/**\n * Thrown when network requests to the Auth server fail.\n */\nexport class GenericError extends Error {\n  constructor(public error: string, public error_description: string) {\n    super(error_description);\n    Object.setPrototypeOf(this, GenericError.prototype);\n  }\n\n  static fromPayload({\n    error,\n    error_description\n  }: {\n    error: string;\n    error_description: string;\n  }) {\n    return new GenericError(error, error_description);\n  }\n}\n\n/**\n * Thrown when handling the redirect callback fails, will be one of Auth0's\n * Authentication API's Standard Error Responses: https://auth0.com/docs/api/authentication?javascript#standard-error-responses\n */\nexport class AuthenticationError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public state: string,\n    public appState: any = null\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, AuthenticationError.prototype);\n  }\n}\n\n/**\n * Thrown when silent auth times out (usually due to a configuration issue) or\n * when network requests to the Auth server timeout.\n */\nexport class TimeoutError extends GenericError {\n  constructor() {\n    super('timeout', 'Timeout');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Error thrown when the login popup times out (if the user does not complete auth)\n */\nexport class PopupTimeoutError extends TimeoutError {\n  constructor(public popup: Window) {\n    super();\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupTimeoutError.prototype);\n  }\n}\n\nexport class PopupCancelledError extends GenericError {\n  constructor(public popup: Window) {\n    super('cancelled', 'Popup closed');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupCancelledError.prototype);\n  }\n}\n\n/**\n * Error thrown when the token exchange results in a `mfa_required` error\n */\nexport class MfaRequiredError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public mfa_token: string\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, MfaRequiredError.prototype);\n  }\n}\n\n/**\n * Error thrown when there is no refresh token to use\n */\nexport class MissingRefreshTokenError extends GenericError {\n  constructor(public audience: string, public scope: string) {\n    super(\n      'missing_refresh_token',\n      `Missing Refresh Token (audience: '${valueOrEmptyString(audience, [\n        'default'\n      ])}', scope: '${valueOrEmptyString(scope)}')`\n    );\n    Object.setPrototypeOf(this, MissingRefreshTokenError.prototype);\n  }\n}\n\n/**\n * Returns an empty string when value is falsy, or when it's value is included in the exclude argument.\n * @param value The value to check\n * @param exclude An array of values that should result in an empty string.\n * @returns The value, or an empty string when falsy or included in the exclude argument.\n */\nfunction valueOrEmptyString(value: string, exclude: string[] = []) {\n  return value && !exclude.includes(value) ? value : '';\n}\n", "import { AuthenticationResult, PopupConfigOptions } from './global';\n\nimport {\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  CLEANUP_IFRAME_TIMEOUT_IN_SECONDS\n} from './constants';\n\nimport {\n  PopupTimeoutError,\n  TimeoutError,\n  GenericError,\n  PopupCancelledError\n} from './errors';\n\nexport const parseAuthenticationResult = (\n  queryString: string\n): AuthenticationResult => {\n  if (queryString.indexOf('#') > -1) {\n    queryString = queryString.substring(0, queryString.indexOf('#'));\n  }\n\n  const searchParams = new URLSearchParams(queryString);\n\n  return {\n    state: searchParams.get('state')!,\n    code: searchParams.get('code') || undefined,\n    error: searchParams.get('error') || undefined,\n    error_description: searchParams.get('error_description') || undefined\n  };\n};\n\nexport const runIframe = (\n  authorizeUrl: string,\n  eventOrigin: string,\n  timeoutInSeconds: number = DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n) => {\n  return new Promise<AuthenticationResult>((res, rej) => {\n    const iframe = window.document.createElement('iframe');\n\n    iframe.setAttribute('width', '0');\n    iframe.setAttribute('height', '0');\n    iframe.style.display = 'none';\n\n    const removeIframe = () => {\n      if (window.document.body.contains(iframe)) {\n        window.document.body.removeChild(iframe);\n        window.removeEventListener('message', iframeEventHandler, false);\n      }\n    };\n\n    let iframeEventHandler: (e: MessageEvent) => void;\n\n    const timeoutSetTimeoutId = setTimeout(() => {\n      rej(new TimeoutError());\n      removeIframe();\n    }, timeoutInSeconds * 1000);\n\n    iframeEventHandler = function (e: MessageEvent) {\n      if (e.origin != eventOrigin) return;\n      if (!e.data || e.data.type !== 'authorization_response') return;\n\n      const eventSource = e.source;\n\n      if (eventSource) {\n        (eventSource as any).close();\n      }\n\n      e.data.response.error\n        ? rej(GenericError.fromPayload(e.data.response))\n        : res(e.data.response);\n\n      clearTimeout(timeoutSetTimeoutId);\n      window.removeEventListener('message', iframeEventHandler, false);\n\n      // Delay the removal of the iframe to prevent hanging loading status\n      // in Chrome: https://github.com/auth0/auth0-spa-js/issues/240\n      setTimeout(removeIframe, CLEANUP_IFRAME_TIMEOUT_IN_SECONDS * 1000);\n    };\n\n    window.addEventListener('message', iframeEventHandler, false);\n    window.document.body.appendChild(iframe);\n    iframe.setAttribute('src', authorizeUrl);\n  });\n};\n\nexport const openPopup = (url: string) => {\n  const width = 400;\n  const height = 600;\n  const left = window.screenX + (window.innerWidth - width) / 2;\n  const top = window.screenY + (window.innerHeight - height) / 2;\n\n  return window.open(\n    url,\n    'auth0:authorize:popup',\n    `left=${left},top=${top},width=${width},height=${height},resizable,scrollbars=yes,status=1`\n  );\n};\n\nexport const runPopup = (config: PopupConfigOptions) => {\n  return new Promise<AuthenticationResult>((resolve, reject) => {\n    let popupEventListener: (e: MessageEvent) => void;\n\n    // Check each second if the popup is closed triggering a PopupCancelledError\n    const popupTimer = setInterval(() => {\n      if (config.popup && config.popup.closed) {\n        clearInterval(popupTimer);\n        clearTimeout(timeoutId);\n        window.removeEventListener('message', popupEventListener, false);\n        reject(new PopupCancelledError(config.popup));\n      }\n    }, 1000);\n\n    const timeoutId = setTimeout(() => {\n      clearInterval(popupTimer);\n      reject(new PopupTimeoutError(config.popup));\n      window.removeEventListener('message', popupEventListener, false);\n    }, (config.timeoutInSeconds || DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS) * 1000);\n\n    popupEventListener = function (e: MessageEvent) {\n      if (!e.data || e.data.type !== 'authorization_response') {\n        return;\n      }\n\n      clearTimeout(timeoutId);\n      clearInterval(popupTimer);\n      window.removeEventListener('message', popupEventListener, false);\n      config.popup.close();\n\n      if (e.data.response.error) {\n        return reject(GenericError.fromPayload(e.data.response));\n      }\n\n      resolve(e.data.response);\n    };\n\n    window.addEventListener('message', popupEventListener);\n  });\n};\n\nexport const getCrypto = () => {\n  return window.crypto;\n};\n\nexport const createRandomString = () => {\n  const charset =\n    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.';\n  let random = '';\n  const randomValues = Array.from(\n    getCrypto().getRandomValues(new Uint8Array(43))\n  );\n  randomValues.forEach(v => (random += charset[v % charset.length]));\n  return random;\n};\n\nexport const encode = (value: string) => btoa(value);\nexport const decode = (value: string) => atob(value);\n\nconst stripUndefined = (params: any) => {\n  return Object.keys(params)\n    .filter(k => typeof params[k] !== 'undefined')\n    .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {});\n};\n\nexport const createQueryParams = ({ clientId: client_id, ...params }: any) => {\n  return new URLSearchParams(\n    stripUndefined({ client_id, ...params })\n  ).toString();\n};\n\nexport const sha256 = async (s: string) => {\n  const digestOp: any = getCrypto().subtle.digest(\n    { name: 'SHA-256' },\n    new TextEncoder().encode(s)\n  );\n\n  return await digestOp;\n};\n\nconst urlEncodeB64 = (input: string) => {\n  const b64Chars: { [index: string]: string } = { '+': '-', '/': '_', '=': '' };\n  return input.replace(/[+/=]/g, (m: string) => b64Chars[m]);\n};\n\n// https://stackoverflow.com/questions/30106476/\nconst decodeB64 = (input: string) =>\n  decodeURIComponent(\n    atob(input)\n      .split('')\n      .map(c => {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n      })\n      .join('')\n  );\n\nexport const urlDecodeB64 = (input: string) =>\n  decodeB64(input.replace(/_/g, '/').replace(/-/g, '+'));\n\nexport const bufferToBase64UrlEncoded = (input: number[] | Uint8Array) => {\n  const ie11SafeInput = new Uint8Array(input);\n  return urlEncodeB64(\n    window.btoa(String.fromCharCode(...Array.from(ie11SafeInput)))\n  );\n};\n\nexport const validateCrypto = () => {\n  if (!getCrypto()) {\n    throw new Error(\n      'For security reasons, `window.crypto` is required to run `auth0-spa-js`.'\n    );\n  }\n  if (typeof getCrypto().subtle === 'undefined') {\n    throw new Error(`\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    `);\n  }\n};\n\n/**\n * @ignore\n */\nexport const getDomain = (domainUrl: string) => {\n  if (!/^https?:\\/\\//.test(domainUrl)) {\n    return `https://${domainUrl}`;\n  }\n\n  return domainUrl;\n};\n\n/**\n * @ignore\n */\nexport const getTokenIssuer = (\n  issuer: string | undefined,\n  domainUrl: string\n) => {\n  if (issuer) {\n    return issuer.startsWith('https://') ? issuer : `https://${issuer}/`;\n  }\n\n  return `${domainUrl}/`;\n};\n\nexport const parseNumber = (value: any): number | undefined => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  return parseInt(value, 10) || undefined;\n};\n", "import { WorkerRefreshTokenMessage } from './worker.types';\n\n/**\n * Sends the specified message to the web worker\n * @param message The message to send\n * @param to The worker to send the message to\n */\nexport const sendMessage = (message: WorkerRefreshTokenMessage, to: Worker) =>\n  new Promise(function (resolve, reject) {\n    const messageChannel = new MessageChannel();\n\n    messageChannel.port1.onmessage = function (event) {\n      // Only for fetch errors, as these get retried\n      if (event.data.error) {\n        reject(new Error(event.data.error));\n      } else {\n        resolve(event.data);\n      }\n      messageChannel.port1.close();\n    };\n\n    to.postMessage(message, [messageChannel.port2]);\n  });\n", "import {\n  DEFAULT_FETCH_TIMEOUT_MS,\n  DEFAULT_SILENT_TOKEN_RETRY_COUNT\n} from './constants';\n\nimport { sendMessage } from './worker/worker.utils';\nimport { FetchOptions } from './global';\nimport {\n  GenericError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport const createAbortController = () => new AbortController();\n\nconst dofetch = async (fetchUrl: string, fetchOptions: FetchOptions) => {\n  const response = await fetch(fetchUrl, fetchOptions);\n\n  return {\n    ok: response.ok,\n    json: await response.json()\n  };\n};\n\nconst fetchWithoutWorker = async (\n  fetchUrl: string,\n  fetchOptions: FetchOptions,\n  timeout: number\n) => {\n  const controller = createAbortController();\n  fetchOptions.signal = controller.signal;\n\n  let timeoutId: NodeJS.Timeout;\n\n  // The promise will resolve with one of these two promises (the fetch or the timeout), whichever completes first.\n  return Promise.race([\n    dofetch(fetchUrl, fetchOptions),\n\n    new Promise((_, reject) => {\n      timeoutId = setTimeout(() => {\n        controller.abort();\n        reject(new Error(\"Timeout when executing 'fetch'\"));\n      }, timeout);\n    })\n  ]).finally(() => {\n    clearTimeout(timeoutId);\n  });\n};\n\nconst fetchWithWorker = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  timeout: number,\n  worker: Worker,\n  useFormData?: boolean\n) => {\n  return sendMessage(\n    {\n      auth: {\n        audience,\n        scope\n      },\n      timeout,\n      fetchUrl,\n      fetchOptions,\n      useFormData\n    },\n    worker\n  );\n};\n\nexport const switchFetch = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean,\n  timeout = DEFAULT_FETCH_TIMEOUT_MS\n): Promise<any> => {\n  if (worker) {\n    return fetchWithWorker(\n      fetchUrl,\n      audience,\n      scope,\n      fetchOptions,\n      timeout,\n      worker,\n      useFormData\n    );\n  } else {\n    return fetchWithoutWorker(fetchUrl, fetchOptions, timeout);\n  }\n};\n\nexport async function getJSON<T>(\n  url: string,\n  timeout: number | undefined,\n  audience: string,\n  scope: string,\n  options: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean\n): Promise<T> {\n  let fetchError: null | Error = null;\n  let response: any;\n\n  for (let i = 0; i < DEFAULT_SILENT_TOKEN_RETRY_COUNT; i++) {\n    try {\n      response = await switchFetch(\n        url,\n        audience,\n        scope,\n        options,\n        worker,\n        useFormData,\n        timeout\n      );\n      fetchError = null;\n      break;\n    } catch (e) {\n      // Fetch only fails in the case of a network issue, so should be\n      // retried here. Failure status (4xx, 5xx, etc) return a resolved Promise\n      // with the failure in the body.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\n      fetchError = e;\n    }\n  }\n\n  if (fetchError) {\n    throw fetchError;\n  }\n\n  const {\n    json: { error, error_description, ...data },\n    ok\n  } = response;\n\n  if (!ok) {\n    const errorMessage =\n      error_description || `HTTP error. Unable to fetch ${url}`;\n\n    if (error === 'mfa_required') {\n      throw new MfaRequiredError(error, errorMessage, data.mfa_token);\n    }\n\n    if (error === 'missing_refresh_token') {\n      throw new MissingRefreshTokenError(audience, scope);\n    }\n\n    throw new GenericError(error || 'request_error', errorMessage);\n  }\n\n  return data;\n}\n", "import { TokenEndpointOptions, TokenEndpointResponse } from './global';\nimport { DEFAULT_AUTH0_CLIENT } from './constants';\nimport { getJSON } from './http';\nimport { createQueryParams } from './utils';\n\nexport async function oauthToken(\n  {\n    baseUrl,\n    timeout,\n    audience,\n    scope,\n    auth0Client,\n    useFormData,\n    ...options\n  }: TokenEndpointOptions,\n  worker?: Worker\n) {\n  const body = useFormData\n    ? createQueryParams(options)\n    : JSON.stringify(options);\n\n  return await getJSON<TokenEndpointResponse>(\n    `${baseUrl}/oauth/token`,\n    timeout,\n    audience || 'default',\n    scope,\n    {\n      method: 'POST',\n      body,\n      headers: {\n        'Content-Type': useFormData\n          ? 'application/x-www-form-urlencoded'\n          : 'application/json',\n        'Auth0-Client': btoa(\n          JSON.stringify(auth0Client || DEFAULT_AUTH0_CLIENT)\n        )\n      }\n    },\n    worker,\n    useFormData\n  );\n}\n", "/**\n * @ignore\n */\nconst dedupe = (arr: string[]) => Array.from(new Set(arr));\n\n/**\n * @ignore\n */\n/**\n * Returns a string of unique scopes by removing duplicates and unnecessary whitespace.\n *\n * @param {...(string | undefined)[]} scopes - A list of scope strings or undefined values.\n * @returns {string} A string containing unique scopes separated by a single space.\n */\nexport const getUniqueScopes = (...scopes: (string | undefined)[]) => {\n  return dedupe(scopes.filter(Boolean).join(' ').trim().split(/\\s+/)).join(' ');\n};\n", "import { IdToken, User } from '../global';\n\nexport const CACHE_KEY_PREFIX = '@@auth0spajs@@';\nexport const CACHE_KEY_ID_TOKEN_SUFFIX = '@@user@@';\n\nexport type CacheKeyData = {\n  audience?: string;\n  scope?: string;\n  clientId: string;\n};\n\nexport class C<PERSON><PERSON>ey {\n  public clientId: string;\n  public scope?: string;\n  public audience?: string;\n\n  constructor(\n    data: CacheKeyData,\n    public prefix: string = CACHE_KEY_PREFIX,\n    public suffix?: string\n  ) {\n    this.clientId = data.clientId;\n    this.scope = data.scope;\n    this.audience = data.audience;\n  }\n\n  /**\n   * Converts this `<PERSON><PERSON><PERSON><PERSON>` instance into a string for use in a cache\n   * @returns A string representation of the key\n   */\n  toKey(): string {\n    return [this.prefix, this.clientId, this.audience, this.scope, this.suffix]\n      .filter(Boolean)\n      .join('::');\n  }\n\n  /**\n   * Converts a cache key string into a `CacheKey` instance.\n   * @param key The key to convert\n   * @returns An instance of `<PERSON><PERSON><PERSON><PERSON>`\n   */\n  static from<PERSON><PERSON>(key: string): <PERSON>ache<PERSON><PERSON> {\n    const [prefix, clientId, audience, scope] = key.split('::');\n\n    return new CacheKey({ clientId, scope, audience }, prefix);\n  }\n\n  /**\n   * Utility function to build a `CacheKey` instance from a cache entry\n   * @param entry The entry\n   * @returns An instance of `CacheKey`\n   */\n  static fromCacheEntry(entry: CacheEntry): CacheKey {\n    const { scope, audience, client_id: clientId } = entry;\n\n    return new CacheKey({\n      scope,\n      audience,\n      clientId\n    });\n  }\n}\n\nexport interface DecodedToken {\n  claims: IdToken;\n  user: User;\n}\n\nexport interface IdTokenEntry {\n  id_token: string;\n  decodedToken: DecodedToken;\n}\n\nexport type CacheEntry = {\n  id_token?: string;\n  access_token: string;\n  expires_in: number;\n  decodedToken?: DecodedToken;\n  audience: string;\n  scope: string;\n  client_id: string;\n  refresh_token?: string;\n  oauthTokenScope?: string;\n};\n\nexport type WrappedCacheEntry = {\n  body: Partial<CacheEntry>;\n  expiresAt: number;\n};\n\nexport type KeyManifestEntry = {\n  keys: string[];\n};\n\nexport type Cacheable = WrappedCacheEntry | KeyManifestEntry;\n\nexport type MaybePromise<T> = Promise<T> | T;\n\nexport interface ICache {\n  set<T = Cacheable>(key: string, entry: T): MaybePromise<void>;\n  get<T = Cacheable>(key: string): MaybePromise<T | undefined>;\n  remove(key: string): MaybePromise<void>;\n  allKeys?(): MaybePromise<string[]>;\n}\n", "import { ICache, Cacheable, CACHE_KEY_PREFIX, Maybe<PERSON>rom<PERSON> } from './shared';\n\nexport class LocalStorageCache implements ICache {\n  public set<T = Cacheable>(key: string, entry: T) {\n    localStorage.setItem(key, JSON.stringify(entry));\n  }\n\n  public get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n    const json = window.localStorage.getItem(key);\n\n    if (!json) return;\n\n    try {\n      const payload = JSON.parse(json) as T;\n      return payload;\n      /* c8 ignore next 3 */\n    } catch (e) {\n      return;\n    }\n  }\n\n  public remove(key: string) {\n    localStorage.removeItem(key);\n  }\n\n  public allKeys() {\n    return Object.keys(window.localStorage).filter(key =>\n      key.startsWith(CACHE_KEY_PREFIX)\n    );\n  }\n}\n", "import { Cacheable, ICache, MaybePromise } from './shared';\n\nexport class InMemoryCache {\n  public enclosedCache: ICache = (function () {\n    let cache: Record<string, unknown> = {};\n\n    return {\n      set<T = Cacheable>(key: string, entry: T) {\n        cache[key] = entry;\n      },\n\n      get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n        const cacheEntry = cache[key] as T;\n\n        if (!cacheEntry) {\n          return;\n        }\n\n        return cacheEntry;\n      },\n\n      remove(key: string) {\n        delete cache[key];\n      },\n\n      allKeys(): string[] {\n        return Object.keys(cache);\n      }\n    };\n  })();\n}\n", "import { DEFAULT_NOW_PROVIDER } from '../constants';\nimport { CacheKeyManifest } from './key-manifest';\n\nimport {\n  CacheEntry,\n  ICache,\n  CacheKey,\n  CACHE_KEY_PREFIX,\n  WrappedCacheEntry,\n  DecodedToken,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  IdTokenEntry\n} from './shared';\n\nconst DEFAULT_EXPIRY_ADJUSTMENT_SECONDS = 0;\n\nexport class CacheManager {\n  private nowProvider: () => number | Promise<number>;\n\n  constructor(\n    private cache: ICache,\n    private keyManifest?: CacheKeyManifest,\n    nowProvider?: () => number | Promise<number>\n  ) {\n    this.nowProvider = nowProvider || DEFAULT_NOW_PROVIDER;\n  }\n\n  async setIdToken(\n    clientId: string,\n    idToken: string,\n    decodedToken: DecodedToken\n  ): Promise<void> {\n    const cacheKey = this.getIdTokenCacheKey(clientId);\n    await this.cache.set(cacheKey, {\n      id_token: idToken,\n      decodedToken\n    });\n    await this.keyManifest?.add(cacheKey);\n  }\n\n  async getIdToken(cacheKey: <PERSON>acheK<PERSON>): Promise<IdTokenEntry | undefined> {\n    const entry = await this.cache.get<IdTokenEntry>(\n      this.getIdTokenCacheKey(cacheKey.clientId)\n    );\n\n    if (!entry && cacheKey.scope && cacheKey.audience) {\n      const entryByScope = await this.get(cacheKey);\n\n      if (!entryByScope) {\n        return;\n      }\n\n      if (!entryByScope.id_token || !entryByScope.decodedToken) {\n        return;\n      }\n\n      return {\n        id_token: entryByScope.id_token,\n        decodedToken: entryByScope.decodedToken\n      };\n    }\n\n    if (!entry) {\n      return;\n    }\n\n    return { id_token: entry.id_token, decodedToken: entry.decodedToken };\n  }\n\n  async get(\n    cacheKey: CacheKey,\n    expiryAdjustmentSeconds = DEFAULT_EXPIRY_ADJUSTMENT_SECONDS\n  ): Promise<Partial<CacheEntry> | undefined> {\n    let wrappedEntry = await this.cache.get<WrappedCacheEntry>(\n      cacheKey.toKey()\n    );\n\n    if (!wrappedEntry) {\n      const keys = await this.getCacheKeys();\n\n      if (!keys) return;\n\n      const matchedKey = this.matchExistingCacheKey(cacheKey, keys);\n\n      if (matchedKey) {\n        wrappedEntry = await this.cache.get<WrappedCacheEntry>(matchedKey);\n      }\n    }\n\n    // If we still don't have an entry, exit.\n    if (!wrappedEntry) {\n      return;\n    }\n\n    const now = await this.nowProvider();\n    const nowSeconds = Math.floor(now / 1000);\n\n    if (wrappedEntry.expiresAt - expiryAdjustmentSeconds < nowSeconds) {\n      if (wrappedEntry.body.refresh_token) {\n        wrappedEntry.body = {\n          refresh_token: wrappedEntry.body.refresh_token\n        };\n\n        await this.cache.set(cacheKey.toKey(), wrappedEntry);\n        return wrappedEntry.body;\n      }\n\n      await this.cache.remove(cacheKey.toKey());\n      await this.keyManifest?.remove(cacheKey.toKey());\n\n      return;\n    }\n\n    return wrappedEntry.body;\n  }\n\n  async set(entry: CacheEntry): Promise<void> {\n    const cacheKey = new CacheKey({\n      clientId: entry.client_id,\n      scope: entry.scope,\n      audience: entry.audience\n    });\n\n    const wrappedEntry = await this.wrapCacheEntry(entry);\n\n    await this.cache.set(cacheKey.toKey(), wrappedEntry);\n    await this.keyManifest?.add(cacheKey.toKey());\n  }\n\n  async clear(clientId?: string): Promise<void> {\n    const keys = await this.getCacheKeys();\n\n    /* c8 ignore next */\n    if (!keys) return;\n\n    await keys\n      .filter(key => (clientId ? key.includes(clientId) : true))\n      .reduce(async (memo, key) => {\n        await memo;\n        await this.cache.remove(key);\n      }, Promise.resolve());\n\n    await this.keyManifest?.clear();\n  }\n\n  private async wrapCacheEntry(entry: CacheEntry): Promise<WrappedCacheEntry> {\n    const now = await this.nowProvider();\n    const expiresInTime = Math.floor(now / 1000) + entry.expires_in;\n\n    return {\n      body: entry,\n      expiresAt: expiresInTime\n    };\n  }\n\n  private async getCacheKeys(): Promise<string[] | undefined> {\n    if (this.keyManifest) {\n      return (await this.keyManifest.get())?.keys;\n    } else if (this.cache.allKeys) {\n      return this.cache.allKeys();\n    }\n  }\n\n  /**\n   * Returns the cache key to be used to store the id token\n   * @param clientId The client id used to link to the id token\n   * @returns The constructed cache key, as a string, to store the id token\n   */\n  private getIdTokenCacheKey(clientId: string) {\n    return new CacheKey(\n      { clientId },\n      CACHE_KEY_PREFIX,\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ).toKey();\n  }\n\n  /**\n   * Finds the corresponding key in the cache based on the provided cache key.\n   * The keys inside the cache are in the format {prefix}::{clientId}::{audience}::{scope}.\n   * The first key in the cache that satisfies the following conditions is returned\n   *  - `prefix` is strict equal to Auth0's internally configured `keyPrefix`\n   *  - `clientId` is strict equal to the `cacheKey.clientId`\n   *  - `audience` is strict equal to the `cacheKey.audience`\n   *  - `scope` contains at least all the `cacheKey.scope` values\n   *  *\n   * @param keyToMatch The provided cache key\n   * @param allKeys A list of existing cache keys\n   */\n  private matchExistingCacheKey(keyToMatch: CacheKey, allKeys: Array<string>) {\n    return allKeys.filter(key => {\n      const cacheKey = CacheKey.fromKey(key);\n      const scopeSet = new Set(cacheKey.scope && cacheKey.scope.split(' '));\n      const scopesToMatch = keyToMatch.scope?.split(' ') || [];\n\n      const hasAllScopes =\n        cacheKey.scope &&\n        scopesToMatch.reduce(\n          (acc, current) => acc && scopeSet.has(current),\n          true\n        );\n\n      return (\n        cacheKey.prefix === CACHE_KEY_PREFIX &&\n        cacheKey.clientId === keyToMatch.clientId &&\n        cacheKey.audience === keyToMatch.audience &&\n        hasAllScopes\n      );\n    })[0];\n  }\n}\n", "import { ClientStorage } from './storage';\n\nconst TRANSACTION_STORAGE_KEY_PREFIX = 'a0.spajs.txs';\n\ninterface Transaction {\n  nonce: string;\n  scope: string;\n  audience: string;\n  appState?: any;\n  code_verifier: string;\n  redirect_uri?: string;\n  organization?: string;\n  state?: string;\n}\n\nexport class TransactionManager {\n  private storageKey: string;\n\n  constructor(\n    private storage: ClientStorage,\n    private clientId: string,\n    private cookieDomain?: string\n  ) {\n    this.storageKey = `${TRANSACTION_STORAGE_KEY_PREFIX}.${this.clientId}`;\n  }\n\n  public create(transaction: Transaction) {\n    this.storage.save(this.storageKey, transaction, {\n      daysUntilExpire: 1,\n      cookieDomain: this.cookieDomain\n    });\n  }\n\n  public get(): Transaction | undefined {\n    return this.storage.get(this.storageKey);\n  }\n\n  public remove() {\n    this.storage.remove(this.storageKey, {\n      cookieDomain: this.cookieDomain\n    });\n  }\n}\n", "import { urlDecodeB64 } from './utils';\nimport { IdToken, JWTVerifyOptions } from './global';\n\nconst isNumber = (n: any) => typeof n === 'number';\n\nconst idTokendecoded = [\n  'iss',\n  'aud',\n  'exp',\n  'nbf',\n  'iat',\n  'jti',\n  'azp',\n  'nonce',\n  'auth_time',\n  'at_hash',\n  'c_hash',\n  'acr',\n  'amr',\n  'sub_jwk',\n  'cnf',\n  'sip_from_tag',\n  'sip_date',\n  'sip_callid',\n  'sip_cseq_num',\n  'sip_via_branch',\n  'orig',\n  'dest',\n  'mky',\n  'events',\n  'toe',\n  'txn',\n  'rph',\n  'sid',\n  'vot',\n  'vtm'\n];\n\nexport const decode = (token: string) => {\n  const parts = token.split('.');\n  const [header, payload, signature] = parts;\n\n  if (parts.length !== 3 || !header || !payload || !signature) {\n    throw new Error('ID token could not be decoded');\n  }\n  const payloadJSON = JSON.parse(urlDecodeB64(payload));\n  const claims: IdToken = { __raw: token };\n  const user: any = {};\n  Object.keys(payloadJSON).forEach(k => {\n    claims[k] = payloadJSON[k];\n    if (!idTokendecoded.includes(k)) {\n      user[k] = payloadJSON[k];\n    }\n  });\n  return {\n    encoded: { header, payload, signature },\n    header: JSON.parse(urlDecodeB64(header)),\n    claims,\n    user\n  };\n};\n\nexport const verify = (options: JWTVerifyOptions) => {\n  if (!options.id_token) {\n    throw new Error('ID token is required but missing');\n  }\n\n  const decoded = decode(options.id_token);\n\n  if (!decoded.claims.iss) {\n    throw new Error(\n      'Issuer (iss) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.claims.iss !== options.iss) {\n    throw new Error(\n      `Issuer (iss) claim mismatch in the ID token; expected \"${options.iss}\", found \"${decoded.claims.iss}\"`\n    );\n  }\n\n  if (!decoded.user.sub) {\n    throw new Error(\n      'Subject (sub) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.header.alg !== 'RS256') {\n    throw new Error(\n      `Signature algorithm of \"${decoded.header.alg}\" is not supported. Expected the ID token to be signed with \"RS256\".`\n    );\n  }\n\n  if (\n    !decoded.claims.aud ||\n    !(\n      typeof decoded.claims.aud === 'string' ||\n      Array.isArray(decoded.claims.aud)\n    )\n  ) {\n    throw new Error(\n      'Audience (aud) claim must be a string or array of strings present in the ID token'\n    );\n  }\n  if (Array.isArray(decoded.claims.aud)) {\n    if (!decoded.claims.aud.includes(options.aud)) {\n      throw new Error(\n        `Audience (aud) claim mismatch in the ID token; expected \"${\n          options.aud\n        }\" but was not one of \"${decoded.claims.aud.join(', ')}\"`\n      );\n    }\n    if (decoded.claims.aud.length > 1) {\n      if (!decoded.claims.azp) {\n        throw new Error(\n          'Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values'\n        );\n      }\n      if (decoded.claims.azp !== options.aud) {\n        throw new Error(\n          `Authorized Party (azp) claim mismatch in the ID token; expected \"${options.aud}\", found \"${decoded.claims.azp}\"`\n        );\n      }\n    }\n  } else if (decoded.claims.aud !== options.aud) {\n    throw new Error(\n      `Audience (aud) claim mismatch in the ID token; expected \"${options.aud}\" but found \"${decoded.claims.aud}\"`\n    );\n  }\n  if (options.nonce) {\n    if (!decoded.claims.nonce) {\n      throw new Error(\n        'Nonce (nonce) claim must be a string present in the ID token'\n      );\n    }\n    if (decoded.claims.nonce !== options.nonce) {\n      throw new Error(\n        `Nonce (nonce) claim mismatch in the ID token; expected \"${options.nonce}\", found \"${decoded.claims.nonce}\"`\n      );\n    }\n  }\n\n  if (options.max_age && !isNumber(decoded.claims.auth_time)) {\n    throw new Error(\n      'Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified'\n    );\n  }\n\n  /* c8 ignore next 5 */\n  if (decoded.claims.exp == null || !isNumber(decoded.claims.exp)) {\n    throw new Error(\n      'Expiration Time (exp) claim must be a number present in the ID token'\n    );\n  }\n  if (!isNumber(decoded.claims.iat)) {\n    throw new Error(\n      'Issued At (iat) claim must be a number present in the ID token'\n    );\n  }\n\n  const leeway = options.leeway || 60;\n  const now = new Date(options.now || Date.now());\n  const expDate = new Date(0);\n\n  expDate.setUTCSeconds(decoded.claims.exp + leeway);\n\n  if (now > expDate) {\n    throw new Error(\n      `Expiration Time (exp) claim error in the ID token; current time (${now}) is after expiration time (${expDate})`\n    );\n  }\n\n  if (decoded.claims.nbf != null && isNumber(decoded.claims.nbf)) {\n    const nbfDate = new Date(0);\n    nbfDate.setUTCSeconds(decoded.claims.nbf - leeway);\n    if (now < nbfDate) {\n      throw new Error(\n        `Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${now}) is before ${nbfDate}`\n      );\n    }\n  }\n\n  if (decoded.claims.auth_time != null && isNumber(decoded.claims.auth_time)) {\n    const authTimeDate = new Date(0);\n    authTimeDate.setUTCSeconds(\n      parseInt(decoded.claims.auth_time) + (options.max_age as number) + leeway\n    );\n\n    if (now > authTimeDate) {\n      throw new Error(\n        `Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${now}) is after last auth at ${authTimeDate}`\n      );\n    }\n  }\n\n  if (options.organization) {\n    const org = options.organization.trim();\n    if (org.startsWith('org_')) {\n      const orgId = org;\n      if (!decoded.claims.org_id) {\n        throw new Error(\n          'Organization ID (org_id) claim must be a string present in the ID token'\n        );\n      } else if (orgId !== decoded.claims.org_id) {\n        throw new Error(\n          `Organization ID (org_id) claim mismatch in the ID token; expected \"${orgId}\", found \"${decoded.claims.org_id}\"`\n        );\n      }\n    } else {\n      const orgName = org.toLowerCase();\n      // TODO should we verify if there is an `org_id` claim?\n      if (!decoded.claims.org_name) {\n        throw new Error(\n          'Organization Name (org_name) claim must be a string present in the ID token'\n        );\n      } else if (orgName !== decoded.claims.org_name) {\n        throw new Error(\n          `Organization Name (org_name) claim mismatch in the ID token; expected \"${orgName}\", found \"${decoded.claims.org_name}\"`\n        );\n      }\n    }\n  }\n\n  return decoded;\n};\n", "\"use strict\";\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nexports.__esModule = true;\r\nfunction stringifyAttribute(name, value) {\r\n    if (!value) {\r\n        return '';\r\n    }\r\n    var stringified = '; ' + name;\r\n    if (value === true) {\r\n        return stringified; // boolean attributes shouldn't have a value\r\n    }\r\n    return stringified + '=' + value;\r\n}\r\nfunction stringifyAttributes(attributes) {\r\n    if (typeof attributes.expires === 'number') {\r\n        var expires = new Date();\r\n        expires.setMilliseconds(expires.getMilliseconds() + attributes.expires * 864e+5);\r\n        attributes.expires = expires;\r\n    }\r\n    return stringifyAttribute('Expires', attributes.expires ? attributes.expires.toUTCString() : '')\r\n        + stringifyAttribute('Domain', attributes.domain)\r\n        + stringifyAttribute('Path', attributes.path)\r\n        + stringifyAttribute('Secure', attributes.secure)\r\n        + stringifyAttribute('SameSite', attributes.sameSite);\r\n}\r\nfunction encode(name, value, attributes) {\r\n    return encodeURIComponent(name)\r\n        .replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent) // allowed special characters\r\n        .replace(/\\(/g, '%28').replace(/\\)/g, '%29') // replace opening and closing parens\r\n        + '=' + encodeURIComponent(value)\r\n        // allowed special characters\r\n        .replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent)\r\n        + stringifyAttributes(attributes);\r\n}\r\nexports.encode = encode;\r\nfunction parse(cookieString) {\r\n    var result = {};\r\n    var cookies = cookieString ? cookieString.split('; ') : [];\r\n    var rdecode = /(%[\\dA-F]{2})+/gi;\r\n    for (var i = 0; i < cookies.length; i++) {\r\n        var parts = cookies[i].split('=');\r\n        var cookie = parts.slice(1).join('=');\r\n        if (cookie.charAt(0) === '\"') {\r\n            cookie = cookie.slice(1, -1);\r\n        }\r\n        try {\r\n            var name_1 = parts[0].replace(rdecode, decodeURIComponent);\r\n            result[name_1] = cookie.replace(rdecode, decodeURIComponent);\r\n        }\r\n        catch (e) {\r\n            // ignore cookies with invalid name/value encoding\r\n        }\r\n    }\r\n    return result;\r\n}\r\nexports.parse = parse;\r\nfunction getAll() {\r\n    return parse(document.cookie);\r\n}\r\nexports.getAll = getAll;\r\nfunction get(name) {\r\n    return getAll()[name];\r\n}\r\nexports.get = get;\r\nfunction set(name, value, attributes) {\r\n    document.cookie = encode(name, value, __assign({ path: '/' }, attributes));\r\n}\r\nexports.set = set;\r\nfunction remove(name, attributes) {\r\n    set(name, '', __assign(__assign({}, attributes), { expires: -1 }));\r\n}\r\nexports.remove = remove;\r\n", "import * as Cookies from 'es-cookie';\n\ninterface ClientStorageOptions {\n  daysUntilExpire?: number;\n  cookieDomain?: string;\n}\n\n/**\n * Defines a type that handles storage to/from a storage location\n */\nexport type ClientStorage = {\n  get<T extends Object>(key: string): T | undefined;\n  save(key: string, value: any, options?: ClientStorageOptions): void;\n  remove(key: string, options?: ClientStorageOptions): void;\n};\n\n/**\n * A storage protocol for marshalling data to/from cookies\n */\nexport const CookieStorage = {\n  get<T extends Object>(key: string) {\n    const value = Cookies.get(key);\n\n    if (typeof value === 'undefined') {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = {\n        secure: true,\n        sameSite: 'none'\n      };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(key, JSON.stringify(value), cookieAttributes);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n  }\n} as ClientStorage;\n\n/**\n * @ignore\n */\nconst LEGACY_PREFIX = '_legacy_';\n\n/**\n * Cookie storage that creates a cookie for modern and legacy browsers.\n * See: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\n */\nexport const CookieStorageWithLegacySameSite = {\n  get<T extends Object>(key: string) {\n    const value = CookieStorage.get<T>(key);\n\n    if (value) {\n      return value;\n    }\n\n    return CookieStorage.get<T>(`${LEGACY_PREFIX}${key}`);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = { secure: true };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(\n      `${LEGACY_PREFIX}${key}`,\n      JSON.stringify(value),\n      cookieAttributes\n    );\n    CookieStorage.save(key, value, options);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n    CookieStorage.remove(key, options);\n    CookieStorage.remove(`${LEGACY_PREFIX}${key}`, options);\n  }\n} as ClientStorage;\n\n/**\n * A storage protocol for marshalling data to/from session storage\n */\nexport const SessionStorage = {\n  get<T extends Object>(key: string) {\n    /* c8 ignore next 3 */\n    if (typeof sessionStorage === 'undefined') {\n      return;\n    }\n\n    const value = sessionStorage.getItem(key);\n\n    if (value == null) {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any): void {\n    sessionStorage.setItem(key, JSON.stringify(value));\n  },\n\n  remove(key: string) {\n    sessionStorage.removeItem(key);\n  }\n} as ClientStorage;\n", "const singlePromiseMap: Record<string, Promise<any>> = {};\n\nexport const singlePromise = <T>(\n  cb: () => Promise<T>,\n  key: string\n): Promise<T> => {\n  let promise: null | Promise<T> = singlePromiseMap[key];\n  if (!promise) {\n    promise = cb().finally(() => {\n      delete singlePromiseMap[key];\n      promise = null;\n    });\n    singlePromiseMap[key] = promise;\n  }\n  return promise;\n};\n\nexport const retryPromise = async (\n  cb: () => Promise<boolean>,\n  maxNumberOfRetries = 3\n) => {\n  for (let i = 0; i < maxNumberOfRetries; i++) {\n    if (await cb()) {\n      return true;\n    }\n  }\n\n  return false;\n};\n", "import {\n  CACHE_KEY_PREFIX,\n  ICache,\n  KeyManifestEntry,\n  MaybePromise\n} from './shared';\n\nexport class CacheKeyManifest {\n  private readonly manifestKey: string;\n\n  constructor(private cache: ICache, private clientId: string) {\n    this.manifestKey = this.createManifestKeyFrom(this.clientId);\n  }\n\n  async add(key: string): Promise<void> {\n    const keys = new Set(\n      (await this.cache.get<KeyManifestEntry>(this.manifestKey))?.keys || []\n    );\n\n    keys.add(key);\n\n    await this.cache.set<KeyManifestEntry>(this.manifestKey, {\n      keys: [...keys]\n    });\n  }\n\n  async remove(key: string): Promise<void> {\n    const entry = await this.cache.get<KeyManifestEntry>(this.manifestKey);\n\n    if (entry) {\n      const keys = new Set(entry.keys);\n      keys.delete(key);\n\n      if (keys.size > 0) {\n        return await this.cache.set(this.manifestKey, { keys: [...keys] });\n      }\n\n      return await this.cache.remove(this.manifestKey);\n    }\n  }\n\n  get(): MaybePromise<KeyManifestEntry | undefined> {\n    return this.cache.get<KeyManifestEntry>(this.manifestKey);\n  }\n\n  clear(): MaybePromise<void> {\n    return this.cache.remove(this.manifestKey);\n  }\n\n  private createManifestKeyFrom(clientId: string): string {\n    return `${CACHE_KEY_PREFIX}::${clientId}`;\n  }\n}\n", "import { ICache, InMemoryCache, LocalStorageCache } from './cache';\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  LogoutOptions\n} from './global';\nimport { getUniqueScopes } from './scope';\n\n/**\n * @ignore\n */\nexport const GET_TOKEN_SILENTLY_LOCK_KEY = 'auth0.lock.getTokenSilently';\n\n/**\n * @ignore\n */\nexport const buildOrganizationHintCookieName = (clientId: string) =>\n  `auth0.${clientId}.organization_hint`;\n\n/**\n * @ignore\n */\nexport const OLD_IS_AUTHENTICATED_COOKIE_NAME = 'auth0.is.authenticated';\n\n/**\n * @ignore\n */\nexport const buildIsAuthenticatedCookieName = (clientId: string) =>\n  `auth0.${clientId}.is.authenticated`;\n\n/**\n * @ignore\n */\nconst cacheLocationBuilders: Record<string, () => ICache> = {\n  memory: () => new InMemoryCache().enclosedCache,\n  localstorage: () => new LocalStorageCache()\n};\n\n/**\n * @ignore\n */\nexport const cacheFactory = (location: string) => {\n  return cacheLocationBuilders[location];\n};\n\n/**\n * @ignore\n */\nexport const getAuthorizeParams = (\n  clientOptions: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  },\n  scope: string,\n  authorizationParams: AuthorizationParams,\n  state: string,\n  nonce: string,\n  code_challenge: string,\n  redirect_uri: string | undefined,\n  response_mode: string | undefined\n): AuthorizeOptions => {\n  return {\n    client_id: clientOptions.clientId,\n    ...clientOptions.authorizationParams,\n    ...authorizationParams,\n    scope: getUniqueScopes(scope, authorizationParams.scope),\n    response_type: 'code',\n    response_mode: response_mode || 'query',\n    state,\n    nonce,\n    redirect_uri:\n      redirect_uri || clientOptions.authorizationParams.redirect_uri,\n    code_challenge,\n    code_challenge_method: 'S256'\n  };\n};\n\n/**\n * @ignore\n *\n * Function used to provide support for the deprecated onRedirect through openUrl.\n */\nexport const patchOpenUrlWithOnRedirect = <\n  T extends Pick<LogoutOptions, 'openUrl' | 'onRedirect'>\n>(\n  options: T\n) => {\n  const { openUrl, onRedirect, ...originalOptions } = options;\n\n  const result = {\n    ...originalOptions,\n    openUrl: openUrl === false || openUrl ? openUrl : onRedirect\n  };\n\n  return result as T;\n};\n", "import Lock from 'browser-tabs-lock';\n\nimport {\n  createQuery<PERSON>ara<PERSON>,\n  runPopup,\n  parseAuthenticationResult,\n  encode,\n  createRandomString,\n  runIframe,\n  sha256,\n  bufferToBase64UrlEncoded,\n  validateCrypto,\n  openPopup,\n  getDomain,\n  getTokenIssuer,\n  parseNumber\n} from './utils';\n\nimport { oauthToken } from './api';\n\nimport { getUniqueScopes } from './scope';\n\nimport {\n  InMemoryCache,\n  ICache,\n  <PERSON>ache<PERSON>ey,\n  CacheManager,\n  CacheEntry,\n  IdTokenEntry,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  DecodedToken\n} from './cache';\n\nimport { TransactionManager } from './transaction-manager';\nimport { verify as verifyIdToken } from './jwt';\nimport {\n  AuthenticationError,\n  GenericError,\n  MissingRefreshTokenError,\n  TimeoutError\n} from './errors';\n\nimport {\n  ClientStorage,\n  CookieStorage,\n  CookieStorageWithLegacySameSite,\n  SessionStorage\n} from './storage';\n\nimport {\n  CACHE_LOCATION_MEMORY,\n  DEFAULT_POPUP_CONFIG_OPTIONS,\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  MISSING_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_SCOPE,\n  DEFAULT_SESSION_CHECK_EXPIRY_DAYS,\n  DEFAULT_AUTH0_CLIENT,\n  INVALID_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_NOW_PROVIDER,\n  DEFAULT_FETCH_TIMEOUT_MS\n} from './constants';\n\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  RedirectLoginOptions,\n  PopupLoginOptions,\n  PopupConfigOptions,\n  RedirectLoginResult,\n  GetTokenSilentlyOptions,\n  GetTokenWithPopupOptions,\n  LogoutOptions,\n  CacheLocation,\n  LogoutUrlOptions,\n  User,\n  IdToken,\n  GetTokenSilentlyVerboseResponse,\n  TokenEndpointResponse\n} from './global';\n\n// @ts-ignore\nimport TokenWorker from './worker/token.worker.ts';\nimport { singlePromise, retryPromise } from './promise-utils';\nimport { CacheKeyManifest } from './cache/key-manifest';\nimport {\n  buildIsAuthenticatedCookieName,\n  buildOrganizationHintCookieName,\n  cacheFactory,\n  getAuthorizeParams,\n  GET_TOKEN_SILENTLY_LOCK_KEY,\n  OLD_IS_AUTHENTICATED_COOKIE_NAME,\n  patchOpenUrlWithOnRedirect\n} from './Auth0Client.utils';\nimport { CustomTokenExchangeOptions } from './TokenExchange';\n\n/**\n * @ignore\n */\ntype GetTokenSilentlyResult = TokenEndpointResponse & {\n  decodedToken: ReturnType<typeof verifyIdToken>;\n  scope: string;\n  oauthTokenScope?: string;\n  audience: string;\n};\n\n/**\n * @ignore\n */\nconst lock = new Lock();\n\n/**\n * Auth0 SDK for Single Page Applications using [Authorization Code Grant Flow with PKCE](https://auth0.com/docs/api-auth/tutorials/authorization-code-grant-pkce).\n */\nexport class Auth0Client {\n  private readonly transactionManager: TransactionManager;\n  private readonly cacheManager: CacheManager;\n  private readonly domainUrl: string;\n  private readonly tokenIssuer: string;\n  private readonly scope: string;\n  private readonly cookieStorage: ClientStorage;\n  private readonly sessionCheckExpiryDays: number;\n  private readonly orgHintCookieName: string;\n  private readonly isAuthenticatedCookieName: string;\n  private readonly nowProvider: () => number | Promise<number>;\n  private readonly httpTimeoutMs: number;\n  private readonly options: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  };\n  private readonly userCache: ICache = new InMemoryCache().enclosedCache;\n\n  private worker?: Worker;\n\n  private readonly defaultOptions: Partial<Auth0ClientOptions> = {\n    authorizationParams: {\n      scope: DEFAULT_SCOPE\n    },\n    useRefreshTokensFallback: false,\n    useFormData: true\n  };\n\n  constructor(options: Auth0ClientOptions) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options,\n      authorizationParams: {\n        ...this.defaultOptions.authorizationParams,\n        ...options.authorizationParams\n      }\n    };\n\n    typeof window !== 'undefined' && validateCrypto();\n\n    if (options.cache && options.cacheLocation) {\n      console.warn(\n        'Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`.'\n      );\n    }\n\n    let cacheLocation: CacheLocation | undefined;\n    let cache: ICache;\n\n    if (options.cache) {\n      cache = options.cache;\n    } else {\n      cacheLocation = options.cacheLocation || CACHE_LOCATION_MEMORY;\n\n      if (!cacheFactory(cacheLocation)) {\n        throw new Error(`Invalid cache location \"${cacheLocation}\"`);\n      }\n\n      cache = cacheFactory(cacheLocation)();\n    }\n\n    this.httpTimeoutMs = options.httpTimeoutInSeconds\n      ? options.httpTimeoutInSeconds * 1000\n      : DEFAULT_FETCH_TIMEOUT_MS;\n\n    this.cookieStorage =\n      options.legacySameSiteCookie === false\n        ? CookieStorage\n        : CookieStorageWithLegacySameSite;\n\n    this.orgHintCookieName = buildOrganizationHintCookieName(\n      this.options.clientId\n    );\n\n    this.isAuthenticatedCookieName = buildIsAuthenticatedCookieName(\n      this.options.clientId\n    );\n\n    this.sessionCheckExpiryDays =\n      options.sessionCheckExpiryDays || DEFAULT_SESSION_CHECK_EXPIRY_DAYS;\n\n    const transactionStorage = options.useCookiesForTransactions\n      ? this.cookieStorage\n      : SessionStorage;\n\n    // Construct the scopes based on the following:\n    // 1. Always include `openid`\n    // 2. Include the scopes provided in `authorizationParams. This defaults to `profile email`\n    // 3. Add `offline_access` if `useRefreshTokens` is enabled\n    this.scope = getUniqueScopes(\n      'openid',\n      this.options.authorizationParams.scope,\n      this.options.useRefreshTokens ? 'offline_access' : ''\n    );\n\n    this.transactionManager = new TransactionManager(\n      transactionStorage,\n      this.options.clientId,\n      this.options.cookieDomain\n    );\n\n    this.nowProvider = this.options.nowProvider || DEFAULT_NOW_PROVIDER;\n\n    this.cacheManager = new CacheManager(\n      cache,\n      !cache.allKeys\n        ? new CacheKeyManifest(cache, this.options.clientId)\n        : undefined,\n      this.nowProvider\n    );\n\n    this.domainUrl = getDomain(this.options.domain);\n    this.tokenIssuer = getTokenIssuer(this.options.issuer, this.domainUrl);\n\n    // Don't use web workers unless using refresh tokens in memory\n    if (\n      typeof window !== 'undefined' &&\n      window.Worker &&\n      this.options.useRefreshTokens &&\n      cacheLocation === CACHE_LOCATION_MEMORY\n    ) {\n      if (this.options.workerUrl) {\n        this.worker = new Worker(this.options.workerUrl);\n      } else {\n        this.worker = new TokenWorker();\n      }\n    }\n  }\n\n  private _url(path: string) {\n    const auth0Client = encodeURIComponent(\n      btoa(JSON.stringify(this.options.auth0Client || DEFAULT_AUTH0_CLIENT))\n    );\n    return `${this.domainUrl}${path}&auth0Client=${auth0Client}`;\n  }\n\n  private _authorizeUrl(authorizeOptions: AuthorizeOptions) {\n    return this._url(`/authorize?${createQueryParams(authorizeOptions)}`);\n  }\n\n  private async _verifyIdToken(\n    id_token: string,\n    nonce?: string,\n    organization?: string\n  ) {\n    const now = await this.nowProvider();\n\n    return verifyIdToken({\n      iss: this.tokenIssuer,\n      aud: this.options.clientId,\n      id_token,\n      nonce,\n      organization,\n      leeway: this.options.leeway,\n      max_age: parseNumber(this.options.authorizationParams.max_age),\n      now\n    });\n  }\n\n  private _processOrgHint(organization?: string) {\n    if (organization) {\n      this.cookieStorage.save(this.orgHintCookieName, organization, {\n        daysUntilExpire: this.sessionCheckExpiryDays,\n        cookieDomain: this.options.cookieDomain\n      });\n    } else {\n      this.cookieStorage.remove(this.orgHintCookieName, {\n        cookieDomain: this.options.cookieDomain\n      });\n    }\n  }\n\n  private async _prepareAuthorizeUrl(\n    authorizationParams: AuthorizationParams,\n    authorizeOptions?: Partial<AuthorizeOptions>,\n    fallbackRedirectUri?: string\n  ): Promise<{\n    scope: string;\n    audience: string;\n    redirect_uri?: string;\n    nonce: string;\n    code_verifier: string;\n    state: string;\n    url: string;\n  }> {\n    const state = encode(createRandomString());\n    const nonce = encode(createRandomString());\n    const code_verifier = createRandomString();\n    const code_challengeBuffer = await sha256(code_verifier);\n    const code_challenge = bufferToBase64UrlEncoded(code_challengeBuffer);\n\n    const params = getAuthorizeParams(\n      this.options,\n      this.scope,\n      authorizationParams,\n      state,\n      nonce,\n      code_challenge,\n      authorizationParams.redirect_uri ||\n        this.options.authorizationParams.redirect_uri ||\n        fallbackRedirectUri,\n      authorizeOptions?.response_mode\n    );\n\n    const url = this._authorizeUrl(params);\n\n    return {\n      nonce,\n      code_verifier,\n      scope: params.scope,\n      audience: params.audience || 'default',\n      redirect_uri: params.redirect_uri,\n      state,\n      url\n    };\n  }\n\n  /**\n   * ```js\n   * try {\n   *  await auth0.loginWithPopup(options);\n   * } catch(e) {\n   *  if (e instanceof PopupCancelledError) {\n   *    // Popup was closed before login completed\n   *  }\n   * }\n   * ```\n   *\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * IMPORTANT: This method has to be called from an event handler\n   * that was started by the user like a button click, for example,\n   * otherwise the popup will be blocked in most browsers.\n   *\n   * @param options\n   * @param config\n   */\n  public async loginWithPopup(\n    options?: PopupLoginOptions,\n    config?: PopupConfigOptions\n  ) {\n    options = options || {};\n    config = config || {};\n\n    if (!config.popup) {\n      config.popup = openPopup('');\n\n      if (!config.popup) {\n        throw new Error(\n          'Unable to open a popup for loginWithPopup - window.open returned `null`'\n        );\n      }\n    }\n\n    const params = await this._prepareAuthorizeUrl(\n      options.authorizationParams || {},\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    config.popup.location.href = params.url;\n\n    const codeResult = await runPopup({\n      ...config,\n      timeoutInSeconds:\n        config.timeoutInSeconds ||\n        this.options.authorizeTimeoutInSeconds ||\n        DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n    });\n\n    if (params.state !== codeResult.state) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization =\n      options.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    await this._requestToken(\n      {\n        audience: params.audience,\n        scope: params.scope,\n        code_verifier: params.code_verifier,\n        grant_type: 'authorization_code',\n        code: codeResult.code as string,\n        redirect_uri: params.redirect_uri\n      },\n      {\n        nonceIn: params.nonce,\n        organization\n      }\n    );\n  }\n\n  /**\n   * ```js\n   * const user = await auth0.getUser();\n   * ```\n   *\n   * Returns the user information if available (decoded\n   * from the `id_token`).\n   *\n   * @typeparam TUser The type to return, has to extend {@link User}.\n   */\n  public async getUser<TUser extends User>(): Promise<TUser | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.user as TUser;\n  }\n\n  /**\n   * ```js\n   * const claims = await auth0.getIdTokenClaims();\n   * ```\n   *\n   * Returns all claims from the id_token if available.\n   */\n  public async getIdTokenClaims(): Promise<IdToken | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.claims;\n  }\n\n  /**\n   * ```js\n   * await auth0.loginWithRedirect(options);\n   * ```\n   *\n   * Performs a redirect to `/authorize` using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated.\n   *\n   * @param options\n   */\n  public async loginWithRedirect<TAppState = any>(\n    options: RedirectLoginOptions<TAppState> = {}\n  ) {\n    const { openUrl, fragment, appState, ...urlOptions } =\n      patchOpenUrlWithOnRedirect(options);\n\n    const organization =\n      urlOptions.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    const { url, ...transaction } = await this._prepareAuthorizeUrl(\n      urlOptions.authorizationParams || {}\n    );\n\n    this.transactionManager.create({\n      ...transaction,\n      appState,\n      ...(organization && { organization })\n    });\n\n    const urlWithFragment = fragment ? `${url}#${fragment}` : url;\n\n    if (openUrl) {\n      await openUrl(urlWithFragment);\n    } else {\n      window.location.assign(urlWithFragment);\n    }\n  }\n\n  /**\n   * After the browser redirects back to the callback page,\n   * call `handleRedirectCallback` to handle success and error\n   * responses from Auth0. If the response is successful, results\n   * will be valid according to their expiration times.\n   */\n  public async handleRedirectCallback<TAppState = any>(\n    url: string = window.location.href\n  ): Promise<RedirectLoginResult<TAppState>> {\n    const queryStringFragments = url.split('?').slice(1);\n\n    if (queryStringFragments.length === 0) {\n      throw new Error('There are no query params available for parsing.');\n    }\n\n    const { state, code, error, error_description } = parseAuthenticationResult(\n      queryStringFragments.join('')\n    );\n\n    const transaction = this.transactionManager.get();\n\n    if (!transaction) {\n      throw new GenericError('missing_transaction', 'Invalid state');\n    }\n\n    this.transactionManager.remove();\n\n    if (error) {\n      throw new AuthenticationError(\n        error,\n        error_description || error,\n        state,\n        transaction.appState\n      );\n    }\n\n    // Transaction should have a `code_verifier` to do PKCE for CSRF protection\n    if (\n      !transaction.code_verifier ||\n      (transaction.state && transaction.state !== state)\n    ) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization = transaction.organization;\n    const nonceIn = transaction.nonce;\n    const redirect_uri = transaction.redirect_uri;\n\n    await this._requestToken(\n      {\n        audience: transaction.audience,\n        scope: transaction.scope,\n        code_verifier: transaction.code_verifier,\n        grant_type: 'authorization_code',\n        code: code as string,\n        ...(redirect_uri ? { redirect_uri } : {})\n      },\n      { nonceIn, organization }\n    );\n\n    return {\n      appState: transaction.appState\n    };\n  }\n\n  /**\n   * ```js\n   * await auth0.checkSession();\n   * ```\n   *\n   * Check if the user is logged in using `getTokenSilently`. The difference\n   * with `getTokenSilently` is that this doesn't return a token, but it will\n   * pre-fill the token cache.\n   *\n   * This method also heeds the `auth0.{clientId}.is.authenticated` cookie, as an optimization\n   *  to prevent calling Auth0 unnecessarily. If the cookie is not present because\n   * there was no previous login (or it has expired) then tokens will not be refreshed.\n   *\n   * It should be used for silently logging in the user when you instantiate the\n   * `Auth0Client` constructor. You should not need this if you are using the\n   * `createAuth0Client` factory.\n   *\n   * **Note:** the cookie **may not** be present if running an app using a private tab, as some\n   * browsers clear JS cookie data and local storage when the tab or page is closed, or on page reload. This effectively\n   * means that `checkSession` could silently return without authenticating the user on page refresh when\n   * using a private tab, despite having previously logged in. As a workaround, use `getTokenSilently` instead\n   * and handle the possible `login_required` error [as shown in the readme](https://github.com/auth0/auth0-spa-js#creating-the-client).\n   *\n   * @param options\n   */\n  public async checkSession(options?: GetTokenSilentlyOptions) {\n    if (!this.cookieStorage.get(this.isAuthenticatedCookieName)) {\n      if (!this.cookieStorage.get(OLD_IS_AUTHENTICATED_COOKIE_NAME)) {\n        return;\n      } else {\n        // Migrate the existing cookie to the new name scoped by client ID\n        this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n          daysUntilExpire: this.sessionCheckExpiryDays,\n          cookieDomain: this.options.cookieDomain\n        });\n\n        this.cookieStorage.remove(OLD_IS_AUTHENTICATED_COOKIE_NAME);\n      }\n    }\n\n    try {\n      await this.getTokenSilently(options);\n    } catch (_) {}\n  }\n\n  /**\n   * Fetches a new access token and returns the response from the /oauth/token endpoint, omitting the refresh token.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions & { detailedResponse: true }\n  ): Promise<GetTokenSilentlyVerboseResponse>;\n\n  /**\n   * Fetches a new access token and returns it.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options?: GetTokenSilentlyOptions\n  ): Promise<string>;\n\n  /**\n   * Fetches a new access token, and either returns just the access token (the default) or the response from the /oauth/token endpoint, depending on the `detailedResponse` option.\n   *\n   * ```js\n   * const token = await auth0.getTokenSilently(options);\n   * ```\n   *\n   * If there's a valid token stored and it has more than 60 seconds\n   * remaining before expiration, return the token. Otherwise, attempt\n   * to obtain a new token.\n   *\n   * A new token will be obtained either by opening an iframe or a\n   * refresh token (if `useRefreshTokens` is `true`).\n\n   * If iframes are used, opens an iframe with the `/authorize` URL\n   * using the parameters provided as arguments. Random and secure `state`\n   * and `nonce` parameters will be auto-generated. If the response is successful,\n   * results will be validated according to their expiration times.\n   *\n   * If refresh tokens are used, the token endpoint is called directly with the\n   * 'refresh_token' grant. If no refresh token is available to make this call,\n   * the SDK will only fall back to using an iframe to the '/authorize' URL if \n   * the `useRefreshTokensFallback` setting has been set to `true`. By default this\n   * setting is `false`.\n   *\n   * This method may use a web worker to perform the token call if the in-memory\n   * cache is used.\n   *\n   * If an `audience` value is given to this function, the SDK always falls\n   * back to using an iframe to make the token exchange.\n   *\n   * Note that in all cases, falling back to an iframe requires access to\n   * the `auth0` cookie.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions = {}\n  ): Promise<undefined | string | GetTokenSilentlyVerboseResponse> {\n    const localOptions: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    } = {\n      cacheMode: 'on',\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    const result = await singlePromise(\n      () => this._getTokenSilently(localOptions),\n      `${this.options.clientId}::${localOptions.authorizationParams.audience}::${localOptions.authorizationParams.scope}`\n    );\n\n    return options.detailedResponse ? result : result?.access_token;\n  }\n\n  private async _getTokenSilently(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const { cacheMode, ...getTokenOptions } = options;\n\n    // Check the cache before acquiring the lock to avoid the latency of\n    // `lock.acquireLock` when the cache is populated.\n    if (cacheMode !== 'off') {\n      const entry = await this._getEntryFromCache({\n        scope: getTokenOptions.authorizationParams.scope,\n        audience: getTokenOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      });\n\n      if (entry) {\n        return entry;\n      }\n    }\n\n    if (cacheMode === 'cache-only') {\n      return;\n    }\n\n    if (\n      await retryPromise(\n        () => lock.acquireLock(GET_TOKEN_SILENTLY_LOCK_KEY, 5000),\n        10\n      )\n    ) {\n      try {\n        window.addEventListener('pagehide', this._releaseLockOnPageHide);\n\n        // Check the cache a second time, because it may have been populated\n        // by a previous call while this call was waiting to acquire the lock.\n        if (cacheMode !== 'off') {\n          const entry = await this._getEntryFromCache({\n            scope: getTokenOptions.authorizationParams.scope,\n            audience: getTokenOptions.authorizationParams.audience || 'default',\n            clientId: this.options.clientId\n          });\n\n          if (entry) {\n            return entry;\n          }\n        }\n\n        const authResult = this.options.useRefreshTokens\n          ? await this._getTokenUsingRefreshToken(getTokenOptions)\n          : await this._getTokenFromIFrame(getTokenOptions);\n\n        const { id_token, access_token, oauthTokenScope, expires_in } =\n          authResult;\n\n        return {\n          id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        };\n      } finally {\n        await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n        window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n      }\n    } else {\n      throw new TimeoutError();\n    }\n  }\n\n  /**\n   * ```js\n   * const token = await auth0.getTokenWithPopup(options);\n   * ```\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * @param options\n   * @param config\n   */\n  public async getTokenWithPopup(\n    options: GetTokenWithPopupOptions = {},\n    config: PopupConfigOptions = {}\n  ) {\n    const localOptions = {\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    config = {\n      ...DEFAULT_POPUP_CONFIG_OPTIONS,\n      ...config\n    };\n\n    await this.loginWithPopup(localOptions, config);\n\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: localOptions.authorizationParams.scope,\n        audience: localOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    return cache!.access_token;\n  }\n\n  /**\n   * ```js\n   * const isAuthenticated = await auth0.isAuthenticated();\n   * ```\n   *\n   * Returns `true` if there's valid information stored,\n   * otherwise returns `false`.\n   *\n   */\n  public async isAuthenticated() {\n    const user = await this.getUser();\n    return !!user;\n  }\n\n  /**\n   * ```js\n   * await auth0.buildLogoutUrl(options);\n   * ```\n   *\n   * Builds a URL to the logout endpoint using the parameters provided as arguments.\n   * @param options\n   */\n  private _buildLogoutUrl(options: LogoutUrlOptions): string {\n    if (options.clientId !== null) {\n      options.clientId = options.clientId || this.options.clientId;\n    } else {\n      delete options.clientId;\n    }\n\n    const { federated, ...logoutOptions } = options.logoutParams || {};\n    const federatedQuery = federated ? `&federated` : '';\n    const url = this._url(\n      `/v2/logout?${createQueryParams({\n        clientId: options.clientId,\n        ...logoutOptions\n      })}`\n    );\n\n    return url + federatedQuery;\n  }\n\n  /**\n   * ```js\n   * await auth0.logout(options);\n   * ```\n   *\n   * Clears the application session and performs a redirect to `/v2/logout`, using\n   * the parameters provided as arguments, to clear the Auth0 session.\n   *\n   * If the `federated` option is specified it also clears the Identity Provider session.\n   * [Read more about how Logout works at Auth0](https://auth0.com/docs/logout).\n   *\n   * @param options\n   */\n  public async logout(options: LogoutOptions = {}): Promise<void> {\n    const { openUrl, ...logoutOptions } = patchOpenUrlWithOnRedirect(options);\n\n    if (options.clientId === null) {\n      await this.cacheManager.clear();\n    } else {\n      await this.cacheManager.clear(options.clientId || this.options.clientId);\n    }\n\n    this.cookieStorage.remove(this.orgHintCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.cookieStorage.remove(this.isAuthenticatedCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.userCache.remove(CACHE_KEY_ID_TOKEN_SUFFIX);\n\n    const url = this._buildLogoutUrl(logoutOptions);\n\n    if (openUrl) {\n      await openUrl(url);\n    } else if (openUrl !== false) {\n      window.location.assign(url);\n    }\n  }\n\n  private async _getTokenFromIFrame(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const params: AuthorizationParams & { scope: string } = {\n      ...options.authorizationParams,\n      prompt: 'none'\n    };\n\n    const orgHint = this.cookieStorage.get<string>(this.orgHintCookieName);\n\n    if (orgHint && !params.organization) {\n      params.organization = orgHint;\n    }\n\n    const {\n      url,\n      state: stateIn,\n      nonce: nonceIn,\n      code_verifier,\n      redirect_uri,\n      scope,\n      audience\n    } = await this._prepareAuthorizeUrl(\n      params,\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    try {\n      // When a browser is running in a Cross-Origin Isolated context, using iframes is not possible.\n      // It doesn't throw an error but times out instead, so we should exit early and inform the user about the reason.\n      // https://developer.mozilla.org/en-US/docs/Web/API/crossOriginIsolated\n      if ((window as any).crossOriginIsolated) {\n        throw new GenericError(\n          'login_required',\n          'The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.'\n        );\n      }\n\n      const authorizeTimeout =\n        options.timeoutInSeconds || this.options.authorizeTimeoutInSeconds;\n\n      const codeResult = await runIframe(url, this.domainUrl, authorizeTimeout);\n\n      if (stateIn !== codeResult.state) {\n        throw new GenericError('state_mismatch', 'Invalid state');\n      }\n\n      const tokenResult = await this._requestToken(\n        {\n          ...options.authorizationParams,\n          code_verifier,\n          code: codeResult.code as string,\n          grant_type: 'authorization_code',\n          redirect_uri,\n          timeout: options.authorizationParams.timeout || this.httpTimeoutMs\n        },\n        {\n          nonceIn,\n          organization: params.organization\n        }\n      );\n\n      return {\n        ...tokenResult,\n        scope: scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: audience\n      };\n    } catch (e) {\n      if (e.error === 'login_required') {\n        this.logout({\n          openUrl: false\n        });\n      }\n      throw e;\n    }\n  }\n\n  private async _getTokenUsingRefreshToken(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: options.authorizationParams.scope,\n        audience: options.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    // If you don't have a refresh token in memory\n    // and you don't have a refresh token in web worker memory\n    // and useRefreshTokensFallback was explicitly enabled\n    // fallback to an iframe\n    if ((!cache || !cache.refresh_token) && !this.worker) {\n      if (this.options.useRefreshTokensFallback) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw new MissingRefreshTokenError(\n        options.authorizationParams.audience || 'default',\n        options.authorizationParams.scope\n      );\n    }\n\n    const redirect_uri =\n      options.authorizationParams.redirect_uri ||\n      this.options.authorizationParams.redirect_uri ||\n      window.location.origin;\n\n    const timeout =\n      typeof options.timeoutInSeconds === 'number'\n        ? options.timeoutInSeconds * 1000\n        : null;\n\n    try {\n      const tokenResult = await this._requestToken({\n        ...options.authorizationParams,\n        grant_type: 'refresh_token',\n        refresh_token: cache && cache.refresh_token,\n        redirect_uri,\n        ...(timeout && { timeout })\n      });\n\n      return {\n        ...tokenResult,\n        scope: options.authorizationParams.scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: options.authorizationParams.audience || 'default'\n      };\n    } catch (e) {\n      if (\n        // The web worker didn't have a refresh token in memory so\n        // fallback to an iframe.\n        (e.message.indexOf(MISSING_REFRESH_TOKEN_ERROR_MESSAGE) > -1 ||\n          // A refresh token was found, but is it no longer valid\n          // and useRefreshTokensFallback is explicitly enabled. Fallback to an iframe.\n          (e.message &&\n            e.message.indexOf(INVALID_REFRESH_TOKEN_ERROR_MESSAGE) > -1)) &&\n        this.options.useRefreshTokensFallback\n      ) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw e;\n    }\n  }\n\n  private async _saveEntryInCache(\n    entry: CacheEntry & { id_token: string; decodedToken: DecodedToken }\n  ) {\n    const { id_token, decodedToken, ...entryWithoutIdToken } = entry;\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, {\n      id_token,\n      decodedToken\n    });\n\n    await this.cacheManager.setIdToken(\n      this.options.clientId,\n      entry.id_token,\n      entry.decodedToken\n    );\n\n    await this.cacheManager.set(entryWithoutIdToken);\n  }\n\n  private async _getIdTokenFromCache() {\n    const audience = this.options.authorizationParams.audience || 'default';\n\n    const cache = await this.cacheManager.getIdToken(\n      new CacheKey({\n        clientId: this.options.clientId,\n        audience,\n        scope: this.scope\n      })\n    );\n\n    const currentCache = this.userCache.get<IdTokenEntry>(\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ) as IdTokenEntry;\n\n    // If the id_token in the cache matches the value we previously cached in memory return the in-memory\n    // value so that object comparison will work\n    if (cache && cache.id_token === currentCache?.id_token) {\n      return currentCache;\n    }\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, cache);\n    return cache;\n  }\n\n  private async _getEntryFromCache({\n    scope,\n    audience,\n    clientId\n  }: {\n    scope: string;\n    audience: string;\n    clientId: string;\n  }): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const entry = await this.cacheManager.get(\n      new CacheKey({\n        scope,\n        audience,\n        clientId\n      }),\n      60 // get a new token if within 60 seconds of expiring\n    );\n\n    if (entry && entry.access_token) {\n      const { access_token, oauthTokenScope, expires_in } = entry as CacheEntry;\n      const cache = await this._getIdTokenFromCache();\n      return (\n        cache && {\n          id_token: cache.id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        }\n      );\n    }\n  }\n\n  /**\n   * Releases any lock acquired by the current page that's not released yet\n   *\n   * Get's called on the `pagehide` event.\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/pagehide_event\n   */\n  private _releaseLockOnPageHide = async () => {\n    await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n\n    window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n  };\n\n  private async _requestToken(\n    options:\n      | PKCERequestTokenOptions\n      | RefreshTokenRequestTokenOptions\n      | TokenExchangeRequestOptions,\n    additionalParameters?: RequestTokenAdditionalParameters\n  ) {\n    const { nonceIn, organization } = additionalParameters || {};\n    const authResult = await oauthToken(\n      {\n        baseUrl: this.domainUrl,\n        client_id: this.options.clientId,\n        auth0Client: this.options.auth0Client,\n        useFormData: this.options.useFormData,\n        timeout: this.httpTimeoutMs,\n        ...options\n      },\n      this.worker\n    );\n\n    const decodedToken = await this._verifyIdToken(\n      authResult.id_token,\n      nonceIn,\n      organization\n    );\n\n    await this._saveEntryInCache({\n      ...authResult,\n      decodedToken,\n      scope: options.scope,\n      audience: options.audience || 'default',\n      ...(authResult.scope ? { oauthTokenScope: authResult.scope } : null),\n      client_id: this.options.clientId\n    });\n\n    this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n      daysUntilExpire: this.sessionCheckExpiryDays,\n      cookieDomain: this.options.cookieDomain\n    });\n\n    this._processOrgHint(organization || decodedToken.claims.org_id);\n\n    return { ...authResult, decodedToken };\n  }\n\n  /*\n  Custom Token Exchange\n  * **Implementation Notes:**\n  * - Ensure that the `subject_token` provided has been securely obtained and is valid according\n  *   to your external identity provider's policies before invoking this function.\n  * - The function leverages internal helper methods:\n  *   - `validateTokenType` confirms that the `subject_token_type` is supported.\n  *   - `getUniqueScopes` merges and de-duplicates scopes between the provided options and\n  *     the instance's default scopes.\n  *   - `_requestToken` performs the actual HTTP request to the token endpoint.\n  */\n\n  /**\n   * Exchanges an external subject token for an Auth0 token via a token exchange request.\n   *\n   * @param {CustomTokenExchangeOptions} options - The options required to perform the token exchange.\n   *\n   * @returns {Promise<TokenEndpointResponse>} A promise that resolves to the token endpoint response,\n   * which contains the issued Auth0 tokens.\n   *\n   * This method implements the token exchange grant as specified in RFC 8693 by first validating\n   * the provided subject token type and then constructing a token request to the /oauth/token endpoint.\n   * The request includes the following parameters:\n   *\n   * - `grant_type`: Hard-coded to \"urn:ietf:params:oauth:grant-type:token-exchange\".\n   * - `subject_token`: The external token provided via the options.\n   * - `subject_token_type`: The type of the external token (validated by this function).\n   * - `scope`: A unique set of scopes, generated by merging the scopes supplied in the options\n   *            with the SDK’s default scopes.\n   * - `audience`: The target audience, as determined by the SDK's authorization configuration.\n   *\n   * **Example Usage:**\n   *\n   * ```\n   * // Define the token exchange options\n   * const options: CustomTokenExchangeOptions = {\n   *   subject_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6Ikp...',\n   *   subject_token_type: 'urn:acme:legacy-system-token',\n   *   scope: ['openid', 'profile']\n   * };\n   *\n   * // Exchange the external token for Auth0 tokens\n   * try {\n   *   const tokenResponse = await instance.exchangeToken(options);\n   *   console.log('Token response:', tokenResponse);\n   * } catch (error) {\n   *   console.error('Token exchange failed:', error);\n   * }\n   * ```\n   */\n  async exchangeToken(\n    options: CustomTokenExchangeOptions\n  ): Promise<TokenEndpointResponse> {\n    return this._requestToken({\n      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',\n      subject_token: options.subject_token,\n      subject_token_type: options.subject_token_type,\n      scope: getUniqueScopes(options.scope, this.scope),\n      audience: this.options.authorizationParams.audience\n    });\n  }\n}\n\ninterface BaseRequestTokenOptions {\n  audience?: string;\n  scope: string;\n  timeout?: number;\n  redirect_uri?: string;\n}\n\ninterface PKCERequestTokenOptions extends BaseRequestTokenOptions {\n  code: string;\n  grant_type: 'authorization_code';\n  code_verifier: string;\n}\n\ninterface RefreshTokenRequestTokenOptions extends BaseRequestTokenOptions {\n  grant_type: 'refresh_token';\n  refresh_token?: string;\n}\n\ninterface TokenExchangeRequestOptions extends BaseRequestTokenOptions {\n  grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange';\n  subject_token: string;\n  subject_token_type: string;\n  actor_token?: string;\n  actor_token_type?: string;\n}\n\ninterface RequestTokenAdditionalParameters {\n  nonceIn?: string;\n  organization?: string;\n}\n", "import { ICache } from './cache';\n\nexport interface AuthorizationParams {\n  /**\n   * - `'page'`: displays the UI with a full page view\n   * - `'popup'`: displays the UI with a popup window\n   * - `'touch'`: displays the UI in a way that leverages a touch interface\n   * - `'wap'`: displays the UI with a \"feature phone\" type interface\n   */\n  display?: 'page' | 'popup' | 'touch' | 'wap';\n\n  /**\n   * - `'none'`: do not prompt user for login or consent on reauthentication\n   * - `'login'`: prompt user for reauthentication\n   * - `'consent'`: prompt user for consent before processing request\n   * - `'select_account'`: prompt user to select an account\n   */\n  prompt?: 'none' | 'login' | 'consent' | 'select_account';\n\n  /**\n   * Maximum allowable elapsed time (in seconds) since authentication.\n   * If the last time the user authenticated is greater than this value,\n   * the user must be reauthenticated.\n   */\n  max_age?: string | number;\n\n  /**\n   * The space-separated list of language tags, ordered by preference.\n   * For example: `'fr-CA fr en'`.\n   */\n  ui_locales?: string;\n\n  /**\n   * Previously issued ID Token.\n   */\n  id_token_hint?: string;\n\n  /**\n   * Provides a hint to Auth0 as to what flow should be displayed.\n   * The default behavior is to show a login page but you can override\n   * this by passing 'signup' to show the signup page instead.\n   *\n   * This only affects the New Universal Login Experience.\n   */\n  screen_hint?: 'signup' | 'login' | string;\n\n  /**\n   * The user's email address or other identifier. When your app knows\n   * which user is trying to authenticate, you can provide this parameter\n   * to pre-fill the email box or select the right session for sign-in.\n   *\n   * This currently only affects the classic Lock experience.\n   */\n  login_hint?: string;\n\n  acr_values?: string;\n\n  /**\n   * The default scope to be used on authentication requests.\n   *\n   * This defaults to `profile email` if not set. If you are setting extra scopes and require\n   * `profile` and `email` to be included then you must include them in the provided scope.\n   *\n   * Note: The `openid` scope is **always applied** regardless of this setting.\n   */\n  scope?: string;\n\n  /**\n   * The default audience to be used for requesting API access.\n   */\n  audience?: string;\n\n  /**\n   * The name of the connection configured for your application.\n   * If null, it will redirect to the Auth0 Login Page and show\n   * the Login Widget.\n   */\n  connection?: string;\n\n  /**\n   * The organization to log in to.\n   *\n   * This will specify an `organization` parameter in your user's login request.\n   *\n   * - If you provide an Organization ID (a string with the prefix `org_`), it will be validated against the `org_id` claim of your user's ID Token. The validation is case-sensitive.\n   * - If you provide an Organization Name (a string *without* the prefix `org_`), it will be validated against the `org_name` claim of your user's ID Token. The validation is case-insensitive.\n   *\n   */\n  organization?: string;\n\n  /**\n   * The Id of an invitation to accept. This is available from the user invitation URL that is given when participating in a user invitation flow.\n   */\n  invitation?: string;\n\n  /**\n   * The default URL where Auth0 will redirect your browser to with\n   * the authentication result. It must be whitelisted in\n   * the \"Allowed Callback URLs\" field in your Auth0 Application's\n   * settings. If not provided here, it should be provided in the other\n   * methods that provide authentication.\n   */\n  redirect_uri?: string;\n\n  /**\n   * If you need to send custom parameters to the Authorization Server,\n   * make sure to use the original parameter name.\n   */\n  [key: string]: any;\n}\n\ninterface BaseLoginOptions {\n  /**\n   * URL parameters that will be sent back to the Authorization Server. This can be known parameters\n   * defined by Auth0 or custom parameters that you define.\n   */\n  authorizationParams?: AuthorizationParams;\n}\n\nexport interface Auth0ClientOptions extends BaseLoginOptions {\n  /**\n   * Your Auth0 account domain such as `'example.auth0.com'`,\n   * `'example.eu.auth0.com'` or , `'example.mycompany.com'`\n   * (when using [custom domains](https://auth0.com/docs/custom-domains))\n   */\n  domain: string;\n  /**\n   * The issuer to be used for validation of JWTs, optionally defaults to the domain above\n   */\n  issuer?: string;\n  /**\n   * The Client ID found on your Application settings page\n   */\n  clientId: string;\n  /**\n   * The value in seconds used to account for clock skew in JWT expirations.\n   * Typically, this value is no more than a minute or two at maximum.\n   * Defaults to 60s.\n   */\n  leeway?: number;\n\n  /**\n   * The location to use when storing cache data. Valid values are `memory` or `localstorage`.\n   * The default setting is `memory`.\n   *\n   * Read more about [changing storage options in the Auth0 docs](https://auth0.com/docs/libraries/auth0-single-page-app-sdk#change-storage-options)\n   */\n  cacheLocation?: CacheLocation;\n\n  /**\n   * Specify a custom cache implementation to use for token storage and retrieval. This setting takes precedence over `cacheLocation` if they are both specified.\n   */\n  cache?: ICache;\n\n  /**\n   * If true, refresh tokens are used to fetch new access tokens from the Auth0 server. If false, the legacy technique of using a hidden iframe and the `authorization_code` grant with `prompt=none` is used.\n   * The default setting is `false`.\n   *\n   * **Note**: Use of refresh tokens must be enabled by an administrator on your Auth0 client application.\n   */\n  useRefreshTokens?: boolean;\n\n  /**\n   * If true, fallback to the technique of using a hidden iframe and the `authorization_code` grant with `prompt=none` when unable to use refresh tokens. If false, the iframe fallback is not used and\n   * errors relating to a failed `refresh_token` grant should be handled appropriately. The default setting is `false`.\n   *\n   * **Note**: There might be situations where doing silent auth with a Web Message response from an iframe is not possible,\n   * like when you're serving your application from the file system or a custom protocol (like in a Desktop or Native app).\n   * In situations like this you can disable the iframe fallback and handle the failed `refresh_token` grant and prompt the user to login interactively with `loginWithRedirect` or `loginWithPopup`.\"\n   *\n   * E.g. Using the `file:` protocol in an Electron application does not support that legacy technique.\n   *\n   * @example\n   * let token: string;\n   * try {\n   *   token = await auth0.getTokenSilently();\n   * } catch (e) {\n   *   if (e.error === 'missing_refresh_token' || e.error === 'invalid_grant') {\n   *     auth0.loginWithRedirect();\n   *   }\n   * }\n   */\n  useRefreshTokensFallback?: boolean;\n\n  /**\n   * A maximum number of seconds to wait before declaring background calls to /authorize as failed for timeout\n   * Defaults to 60s.\n   */\n  authorizeTimeoutInSeconds?: number;\n\n  /**\n   * Specify the timeout for HTTP calls using `fetch`. The default is 10 seconds.\n   */\n  httpTimeoutInSeconds?: number;\n\n  /**\n   * Internal property to send information about the client to the authorization server.\n   * @internal\n   */\n  auth0Client?: {\n    name: string;\n    version: string;\n    env?: { [key: string]: string };\n  };\n\n  /**\n   * Sets an additional cookie with no SameSite attribute to support legacy browsers\n   * that are not compatible with the latest SameSite changes.\n   * This will log a warning on modern browsers, you can disable the warning by setting\n   * this to false but be aware that some older useragents will not work,\n   * See https://www.chromium.org/updates/same-site/incompatible-clients\n   * Defaults to true\n   */\n  legacySameSiteCookie?: boolean;\n\n  /**\n   * If `true`, the SDK will use a cookie when storing information about the auth transaction while\n   * the user is going through the authentication flow on the authorization server.\n   *\n   * The default is `false`, in which case the SDK will use session storage.\n   *\n   * @notes\n   *\n   * You might want to enable this if you rely on your users being able to authenticate using flows that\n   * may end up spanning across multiple tabs (e.g. magic links) or you cannot otherwise rely on session storage being available.\n   */\n  useCookiesForTransactions?: boolean;\n\n  /**\n   * Number of days until the cookie `auth0.is.authenticated` will expire\n   * Defaults to 1.\n   */\n  sessionCheckExpiryDays?: number;\n\n  /**\n   * The domain the cookie is accessible from. If not set, the cookie is scoped to\n   * the current domain, including the subdomain.\n   *\n   * Note: setting this incorrectly may cause silent authentication to stop working\n   * on page load.\n   *\n   *\n   * To keep a user logged in across multiple subdomains set this to your\n   * top-level domain and prefixed with a `.` (eg: `.example.com`).\n   */\n  cookieDomain?: string;\n\n  /**\n   * If true, data to the token endpoint is transmitted as x-www-form-urlencoded data, if false it will be transmitted as JSON. The default setting is `true`.\n   *\n   * **Note:** Setting this to `false` may affect you if you use Auth0 Rules and are sending custom, non-primitive data. If you disable this,\n   * please verify that your Auth0 Rules continue to work as intended.\n   */\n  useFormData?: boolean;\n\n  /**\n   * Modify the value used as the current time during the token validation.\n   *\n   * **Note**: Using this improperly can potentially compromise the token validation.\n   */\n  nowProvider?: () => Promise<number> | number;\n\n  /**\n   * If provided, the SDK will load the token worker from this URL instead of the integrated `blob`. An example of when this is useful is if you have strict\n   * Content-Security-Policy (CSP) and wish to avoid needing to set `worker-src: blob:`. We recommend either serving the worker, which you can find in the module \n   * at `<module_path>/dist/auth0-spa-js.worker.production.js`, from the same host as your application or using the Auth0 CDN \n   * `https://cdn.auth0.com/js/auth0-spa-js/<version>/auth0-spa-js.worker.production.js`.\n   * \n   * **Note**: The worker is only used when `useRefreshTokens: true`, `cacheLocation: 'memory'`, and the `cache` is not custom.\n   */\n  workerUrl?: string;\n}\n\n/**\n * The possible locations where tokens can be stored\n */\nexport type CacheLocation = 'memory' | 'localstorage';\n\n/**\n * @ignore\n */\nexport interface AuthorizeOptions extends AuthorizationParams {\n  response_type: string;\n  response_mode: string;\n  redirect_uri?: string;\n  nonce: string;\n  state: string;\n  scope: string;\n  code_challenge: string;\n  code_challenge_method: string;\n}\n\nexport interface RedirectLoginOptions<TAppState = any>\n  extends BaseLoginOptions {\n  /**\n   * Used to store state before doing the redirect\n   */\n  appState?: TAppState;\n  /**\n   * Used to add to the URL fragment before redirecting\n   */\n  fragment?: string;\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * const client = new Auth0Client({\n   *   async onRedirect(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   * @deprecated since v2.0.1, use `openUrl` instead.\n   */\n  onRedirect?: (url: string) => Promise<void>;\n\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * const client = new Auth0Client({\n   *   openUrl(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   *\n   * @example\n   * import { Browser } from '@capacitor/browser';\n   *\n   * const client = new Auth0Client({\n   *   async openUrl(url) {\n   *     await Browser.open({ url });\n   *   }\n   * });\n   */\n  openUrl?: (url: string) => Promise<void> | void;\n}\n\nexport interface RedirectLoginResult<TAppState = any> {\n  /**\n   * State stored when the redirect request was made\n   */\n  appState?: TAppState;\n}\n\nexport interface PopupLoginOptions extends BaseLoginOptions {}\n\nexport interface PopupConfigOptions {\n  /**\n   * The number of seconds to wait for a popup response before\n   * throwing a timeout error. Defaults to 60s\n   */\n  timeoutInSeconds?: number;\n\n  /**\n   * Accepts an already-created popup window to use. If not specified, the SDK\n   * will create its own. This may be useful for platforms like iOS that have\n   * security restrictions around when popups can be invoked (e.g. from a user click event)\n   */\n  popup?: any;\n}\n\nexport interface GetTokenSilentlyOptions {\n  /**\n   * When `off`, ignores the cache and always sends a\n   * request to Auth0.\n   * When `cache-only`, only reads from the cache and never sends a request to Auth0.\n   * Defaults to `on`, where it both reads from the cache and sends a request to Auth0 as needed.\n   */\n  cacheMode?: 'on' | 'off' | 'cache-only';\n\n  /**\n   * Parameters that will be sent back to Auth0 as part of a request.\n   */\n  authorizationParams?: {\n    /**\n     * There's no actual redirect when getting a token silently,\n     * but, according to the spec, a `redirect_uri` param is required.\n     * Auth0 uses this parameter to validate that the current `origin`\n     * matches the `redirect_uri` `origin` when sending the response.\n     * It must be whitelisted in the \"Allowed Web Origins\" in your\n     * Auth0 Application's settings.\n     */\n    redirect_uri?: string;\n\n    /**\n     * The scope that was used in the authentication request\n     */\n    scope?: string;\n\n    /**\n     * The audience that was used in the authentication request\n     */\n    audience?: string;\n\n    /**\n     * If you need to send custom parameters to the Authorization Server,\n     * make sure to use the original parameter name.\n     */\n    [key: string]: any;\n  };\n\n  /** A maximum number of seconds to wait before declaring the background /authorize call as failed for timeout\n   * Defaults to 60s.\n   */\n  timeoutInSeconds?: number;\n\n  /**\n   * If true, the full response from the /oauth/token endpoint (or the cache, if the cache was used) is returned\n   * (minus `refresh_token` if one was issued). Otherwise, just the access token is returned.\n   *\n   * The default is `false`.\n   */\n  detailedResponse?: boolean;\n}\n\nexport interface GetTokenWithPopupOptions extends PopupLoginOptions {\n  /**\n   * When `off`, ignores the cache and always sends a request to Auth0.\n   * When `cache-only`, only reads from the cache and never sends a request to Auth0.\n   * Defaults to `on`, where it both reads from the cache and sends a request to Auth0 as needed.\n   */\n  cacheMode?: 'on' | 'off' | 'cache-only';\n}\n\nexport interface LogoutUrlOptions {\n  /**\n   * The `clientId` of your application.\n   *\n   * If this property is not set, then the `clientId` that was used during initialization of the SDK is sent to the logout endpoint.\n   *\n   * If this property is set to `null`, then no client ID value is sent to the logout endpoint.\n   *\n   * [Read more about how redirecting after logout works](https://auth0.com/docs/logout/guides/redirect-users-after-logout)\n   */\n  clientId?: string | null;\n\n  /**\n   * Parameters to pass to the logout endpoint. This can be known parameters defined by Auth0 or custom parameters\n   * you wish to provide.\n   */\n  logoutParams?: {\n    /**\n     * When supported by the upstream identity provider,\n     * forces the user to logout of their identity provider\n     * and from Auth0.\n     * [Read more about how federated logout works at Auth0](https://auth0.com/docs/logout/guides/logout-idps)\n     */\n    federated?: boolean;\n    /**\n     * The URL where Auth0 will redirect your browser to after the logout.\n     *\n     * **Note**: If the `client_id` parameter is included, the\n     * `returnTo` URL that is provided must be listed in the\n     * Application's \"Allowed Logout URLs\" in the Auth0 dashboard.\n     * However, if the `client_id` parameter is not included, the\n     * `returnTo` URL must be listed in the \"Allowed Logout URLs\" at\n     * the account level in the Auth0 dashboard.\n     *\n     * [Read more about how redirecting after logout works](https://auth0.com/docs/logout/guides/redirect-users-after-logout)\n     */\n    returnTo?: string;\n\n    /**\n     * If you need to send custom parameters to the logout endpoint, make sure to use the original parameter name.\n     */\n    [key: string]: any;\n  };\n}\n\nexport interface LogoutOptions extends LogoutUrlOptions {\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * await auth0.logout({\n   *   async onRedirect(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   * @deprecated since v2.0.1, use `openUrl` instead.\n   */\n  onRedirect?: (url: string) => Promise<void>;\n\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * Set to `false` to disable the redirect, or provide a function to handle the actual redirect yourself.\n   *\n   * @example\n   * await auth0.logout({\n   *   openUrl(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   *\n   * @example\n   * import { Browser } from '@capacitor/browser';\n   *\n   * await auth0.logout({\n   *   async openUrl(url) {\n   *     await Browser.open({ url });\n   *   }\n   * });\n   */\n  openUrl?: false | ((url: string) => Promise<void> | void);\n}\n\n/**\n * @ignore\n */\nexport interface AuthenticationResult {\n  state: string;\n  code?: string;\n  error?: string;\n  error_description?: string;\n}\n\n/**\n * @ignore\n */\nexport interface TokenEndpointOptions {\n  baseUrl: string;\n  client_id: string;\n  grant_type: string;\n  timeout?: number;\n  auth0Client: any;\n  useFormData?: boolean;\n  [key: string]: any;\n}\n\nexport type TokenEndpointResponse = {\n  id_token: string;\n  access_token: string;\n  refresh_token?: string;\n  expires_in: number;\n  scope?: string;\n};\n\n/**\n * @ignore\n */\nexport interface OAuthTokenOptions extends TokenEndpointOptions {\n  code_verifier: string;\n  code: string;\n  redirect_uri: string;\n  audience: string;\n  scope: string;\n}\n\n/**\n * @ignore\n */\nexport interface RefreshTokenOptions extends TokenEndpointOptions {\n  refresh_token: string;\n}\n\n/**\n * @ignore\n */\nexport interface JWTVerifyOptions {\n  iss: string;\n  aud: string;\n  id_token: string;\n  nonce?: string;\n  leeway?: number;\n  max_age?: number;\n  organization?: string;\n  now?: number;\n}\n\nexport interface IdToken {\n  __raw: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  middle_name?: string;\n  nickname?: string;\n  preferred_username?: string;\n  profile?: string;\n  picture?: string;\n  website?: string;\n  email?: string;\n  email_verified?: boolean;\n  gender?: string;\n  birthdate?: string;\n  zoneinfo?: string;\n  locale?: string;\n  phone_number?: string;\n  phone_number_verified?: boolean;\n  address?: string;\n  updated_at?: string;\n  iss?: string;\n  aud?: string;\n  exp?: number;\n  nbf?: number;\n  iat?: number;\n  jti?: string;\n  azp?: string;\n  nonce?: string;\n  auth_time?: string;\n  at_hash?: string;\n  c_hash?: string;\n  acr?: string;\n  amr?: string[];\n  sub_jwk?: string;\n  cnf?: string;\n  sid?: string;\n  org_id?: string;\n  org_name?: string;\n  [key: string]: any;\n}\n\nexport class User {\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  middle_name?: string;\n  nickname?: string;\n  preferred_username?: string;\n  profile?: string;\n  picture?: string;\n  website?: string;\n  email?: string;\n  email_verified?: boolean;\n  gender?: string;\n  birthdate?: string;\n  zoneinfo?: string;\n  locale?: string;\n  phone_number?: string;\n  phone_number_verified?: boolean;\n  address?: string;\n  updated_at?: string;\n  sub?: string;\n  [key: string]: any;\n}\n\n/**\n * @ignore\n */\nexport type FetchOptions = {\n  method?: string;\n  headers?: Record<string, string>;\n  credentials?: 'include' | 'omit';\n  body?: string;\n  signal?: AbortSignal;\n};\n\nexport type GetTokenSilentlyVerboseResponse = Omit<\n  TokenEndpointResponse,\n  'refresh_token'\n>;\n", "import { Auth0Client } from './Auth0Client';\nimport { Auth0ClientOptions } from './global';\n\nimport './global';\n\nexport * from './global';\n\n/**\n * Asynchronously creates the Auth0Client instance and calls `checkSession`.\n *\n * **Note:** There are caveats to using this in a private browser tab, which may not silently authenticae\n * a user on page refresh. Please see [the checkSession docs](https://auth0.github.io/auth0-spa-js/classes/Auth0Client.html#checksession) for more info.\n *\n * @param options The client options\n * @returns An instance of Auth0Client\n */\nexport async function createAuth0Client(options: Auth0ClientOptions) {\n  const auth0 = new Auth0Client(options);\n  await auth0.checkSession();\n  return auth0;\n}\n\nexport { Auth0Client };\n\nexport {\n  GenericError,\n  AuthenticationError,\n  TimeoutError,\n  PopupTimeoutError,\n  PopupCancelledError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport {\n  ICache,\n  LocalStorageCache,\n  InMemoryCache,\n  Cacheable,\n  DecodedToken,\n  CacheEntry,\n  WrappedCacheEntry,\n  KeyManifestEntry,\n  MaybePromise,\n  CacheKey,\n  CacheKeyData\n} from './cache';\n"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "SuppressedError", "error", "suppressed", "message", "Error", "name", "defineProperty", "exports", "value", "ProcessLocking", "_this", "this", "locked", "Map", "addToLocked", "key", "toAdd", "callbacks", "get", "undefined", "set", "unshift", "isLocked", "has", "lock", "Promise", "resolve", "reject", "unlock", "delete", "toCall", "pop", "setTimeout", "getInstance", "instance", "getLock", "default", "__awaiter", "thisArg", "_arguments", "P", "generator", "fulfilled", "step", "next", "rejected", "result", "done", "then", "apply", "__generator", "body", "_", "label", "sent", "trys", "ops", "f", "y", "g", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "TypeError", "push", "LOCK_STORAGE_KEY", "DEFAULT_STORAGE_HANDLER", "index", "_a", "getItem", "clear", "window", "localStorage", "removeItem", "setItem", "keySync", "getItemSync", "clearSync", "removeItemSync", "setItemSync", "delay", "milliseconds", "generateRandomString", "CHARS", "randomstring", "INDEX", "Math", "floor", "random", "getLockId", "Date", "now", "toString", "SuperTokensLock", "storageHandler", "acquiredIatSet", "Set", "id", "acquireLock", "bind", "releaseLock", "releaseLock__private__", "waitForSomethingToChange", "refreshLockWhileAcquired", "waiters", "<PERSON><PERSON><PERSON>", "timeout", "iat", "MAX_TIME", "STORAGE_KEY", "STORAGE", "lock<PERSON>bj", "TIMEOUT_KEY", "lockObjPostDelay", "parsedLockObjPostDelay", "JSON", "stringify", "timeout<PERSON><PERSON>", "timeAcquired", "timeRefreshed", "parse", "add", "lock<PERSON><PERSON><PERSON><PERSON>", "storageKey", "parsedLockObj", "processLock_1", "resolvedCalled", "startedAt", "MIN_TIME_TO_WAIT", "removedListeners", "stopWaiting", "removeEventListener", "removeFromWaiting", "clearTimeout", "timeOutId", "timeToWait", "addEventListener", "addToWaiting", "max", "func", "filter", "notify<PERSON><PERSON><PERSON>", "slice", "for<PERSON>ach", "parsedlockObj", "MIN_ALLOWED_TIME", "KEYS", "currIndex", "LOCK_KEY", "includes", "version", "DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS", "DEFAULT_POPUP_CONFIG_OPTIONS", "timeoutInSeconds", "DEFAULT_SILENT_TOKEN_RETRY_COUNT", "CLEANUP_IFRAME_TIMEOUT_IN_SECONDS", "DEFAULT_FETCH_TIMEOUT_MS", "CACHE_LOCATION_MEMORY", "MISSING_REFRESH_TOKEN_ERROR_MESSAGE", "INVALID_REFRESH_TOKEN_ERROR_MESSAGE", "DEFAULT_SCOPE", "DEFAULT_SESSION_CHECK_EXPIRY_DAYS", "DEFAULT_AUTH0_CLIENT", "DEFAULT_NOW_PROVIDER", "GenericError", "constructor", "error_description", "super", "setPrototypeOf", "static", "AuthenticationError", "state", "appState", "TimeoutError", "PopupTimeoutError", "popup", "PopupCancelledError", "MfaRequiredError", "mfa_token", "MissingRefreshTokenError", "audience", "scope", "valueOrEmptyString", "exclude", "parseAuthenticationResult", "queryString", "substring", "searchParams", "URLSearchParams", "code", "runIframe", "authorizeUrl", "<PERSON><PERSON><PERSON><PERSON>", "res", "rej", "iframe", "document", "createElement", "setAttribute", "style", "display", "removeIframe", "contains", "<PERSON><PERSON><PERSON><PERSON>", "iframeEventHandler", "timeoutSetTimeoutId", "origin", "data", "type", "eventSource", "source", "close", "response", "fromPayload", "append<PERSON><PERSON><PERSON>", "openPopup", "url", "width", "height", "left", "screenX", "innerWidth", "top", "screenY", "innerHeight", "open", "runPopup", "config", "popupEventListener", "popupTimer", "setInterval", "closed", "clearInterval", "timeoutId", "getCrypto", "crypto", "createRandomString", "charset", "randomValues", "Array", "from", "getRandomValues", "Uint8Array", "encode", "btoa", "stripUndefined", "params", "keys", "k", "reduce", "acc", "assign", "createQueryParams", "clientId", "client_id", "sha256", "async", "digestOp", "subtle", "digest", "TextEncoder", "urlEncodeB64", "input", "b64Chars", "replace", "m", "decodeB64", "decodeURIComponent", "atob", "split", "map", "c", "charCodeAt", "join", "urlDecodeB64", "bufferToBase64UrlEncoded", "ie11SafeInput", "String", "fromCharCode", "validateCrypto", "getDomain", "domainUrl", "test", "getT<PERSON><PERSON><PERSON><PERSON>", "issuer", "startsWith", "parseNumber", "parseInt", "sendMessage", "to", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "createAbortController", "AbortController", "dofetch", "fetchUrl", "fetchOptions", "fetch", "ok", "json", "fetchWithoutWorker", "controller", "signal", "race", "abort", "finally", "fetchWithWorker", "worker", "useFormData", "auth", "switchFetch", "getJSON", "options", "fetchError", "errorMessage", "oauthToken", "baseUrl", "auth0Client", "method", "headers", "dedupe", "arr", "getUniqueScopes", "scopes", "Boolean", "trim", "CACHE_KEY_PREFIX", "CACHE_KEY_ID_TOKEN_SUFFIX", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "suffix", "to<PERSON><PERSON>", "entry", "LocalStorageCache", "payload", "remove", "allKeys", "InMemoryCache", "enclosedCache", "cache", "cacheEntry", "DEFAULT_EXPIRY_ADJUSTMENT_SECONDS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyManifest", "nowProvider", "idToken", "decodedToken", "cache<PERSON>ey", "getIdTokenCache<PERSON>ey", "id_token", "entryByScope", "expiryAdjustmentSeconds", "wrappedEntry", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "matchExisting<PERSON>ache<PERSON>ey", "nowSeconds", "expiresAt", "refresh_token", "wrapCacheEntry", "memo", "expiresInTime", "expires_in", "keyToMatch", "fromKey", "scopeSet", "scopesToMatch", "hasAllScopes", "current", "TRANSACTION_STORAGE_KEY_PREFIX", "TransactionManager", "storage", "cookieDomain", "create", "transaction", "save", "daysUntilExpire", "isNumber", "idTokendecoded", "decode", "token", "parts", "header", "signature", "payloadJSON", "claims", "__raw", "user", "encoded", "verify", "decoded", "iss", "sub", "alg", "aud", "isArray", "azp", "nonce", "max_age", "auth_time", "exp", "leeway", "expDate", "setUTCSeconds", "nbf", "nbfDate", "authTimeDate", "organization", "org", "orgId", "org_id", "orgName", "toLowerCase", "org_name", "__assign", "arguments", "__esModule", "stringifyAttribute", "stringified", "stringifyAttributes", "attributes", "expires", "setMilliseconds", "getMilliseconds", "toUTCString", "domain", "path", "secure", "sameSite", "encodeURIComponent", "cookieString", "cookies", "rdecode", "cookie", "char<PERSON>t", "name_1", "getAll", "Cookie<PERSON>torage", "Cookies.get", "cookieAttributes", "location", "protocol", "Cookies.set", "Cookies.remove", "LEGACY_PREFIX", "CookieStorageWithLegacySameSite", "SessionStorage", "sessionStorage", "singlePromiseMap", "singlePromise", "cb", "promise", "retryPromise", "maxNumberOfRetries", "CacheKeyManifest", "manifest<PERSON>ey", "createManifestKeyFrom", "size", "GET_TOKEN_SILENTLY_LOCK_KEY", "buildOrganizationHintCookieName", "OLD_IS_AUTHENTICATED_COOKIE_NAME", "buildIsAuthenticatedCookieName", "cacheLocationBuilders", "memory", "localstorage", "cacheFactory", "getAuthorizeParams", "clientOptions", "authorizationParams", "code_challenge", "redirect_uri", "response_mode", "response_type", "code_challenge_method", "patchOpenUrlWithOnRedirect", "openUrl", "onRedirect", "originalOptions", "Lock", "Auth0Client", "userCache", "defaultOptions", "useRefreshTokensFallback", "_releaseLockOnPageHide", "cacheLocation", "console", "warn", "httpTimeoutMs", "httpTimeoutInSeconds", "cookieStorage", "legacySameSiteCookie", "orgHintCookieName", "isAuthenticatedCookieName", "sessionCheckExpiryDays", "transactionStorage", "useCookiesForTransactions", "useRefreshTokens", "transactionManager", "cacheManager", "token<PERSON>ssuer", "Worker", "workerUrl", "TokenWorker", "_url", "_authorizeUrl", "authorizeOptions", "verifyIdToken", "_processOrgHint", "fallbackRedirectUri", "code_verifier", "code_challengeBuffer", "_prepareAuthorizeUrl", "href", "codeResult", "authorizeTimeoutInSeconds", "_requestToken", "grant_type", "nonceIn", "_getIdTokenFromCache", "_b", "fragment", "urlOptions", "_c", "urlWithFragment", "queryStringFragments", "getTokenSilently", "localOptions", "cacheMode", "_getTokenSilently", "detailedResponse", "access_token", "getTokenOptions", "_getEntryFromCache", "authResult", "_getTokenUsingRefreshToken", "_getTokenFromIFrame", "oauthTokenScope", "loginWithPopup", "getUser", "_buildLogoutUrl", "logoutParams", "federated", "logoutOptions", "federatedQuery", "prompt", "orgHint", "stateIn", "crossOriginIsolated", "authorizeTimeout", "tokenResult", "logout", "entryWithoutIdToken", "setIdToken", "getIdToken", "currentCache", "additionalParameters", "_verifyIdToken", "_saveEntryInCache", "subject_token", "subject_token_type", "User", "createAuth0Client", "auth0", "checkSession"], "mappings": ";;;;;;AA0CO,SAASA,OAAOC,GAAGC;IACtB,IAAIC,IAAI,CAAA;IACR,KAAK,IAAIC,KAAKH,GAAG,IAAII,OAAOC,UAAUC,eAAeC,KAAKP,GAAGG,MAAMF,EAAEO,QAAQL,KAAK,GAC9ED,EAAEC,KAAKH,EAAEG;IACb,IAAIH,KAAK,eAAeI,OAAOK,0BAA0B,YACrD,KAAK,IAAIC,IAAI,GAAGP,IAAIC,OAAOK,sBAAsBT,IAAIU,IAAIP,EAAEQ,QAAQD,KAAK;QACpE,IAAIT,EAAEO,QAAQL,EAAEO,MAAM,KAAKN,OAAOC,UAAUO,qBAAqBL,KAAKP,GAAGG,EAAEO,KACvER,EAAEC,EAAEO,MAAMV,EAAEG,EAAEO;AACrB;IACL,OAAOR;AACX;;OAuQ8BW,oBAAoB,aAAaA,kBAAkB,SAAUC,OAAOC,YAAYC;IAC1G,IAAIf,IAAI,IAAIgB,MAAMD;IAClB,OAAOf,EAAEiB,OAAO,mBAAmBjB,EAAEa,QAAQA,OAAOb,EAAEc,aAAaA,YAAYd;AACnF;;;;;;;;;;;;;;;IC7TAG,OAAOe,eAAeC,SAAS,cAAc;QAAEC,OAAO;;IACtD,IAAIC,iBAAgC;QAChC,SAASA;YACL,IAAIC,QAAQC;YACZA,KAAKC,SAAS,IAAIC;YAClBF,KAAKG,cAAc,SAAUC,KAAKC;gBAC9B,IAAIC,YAAYP,MAAME,OAAOM,IAAIH;gBACjC,IAAIE,cAAcE,WAAW;oBACzB,IAAIH,UAAUG,WAAW;wBACrBT,MAAME,OAAOQ,IAAIL,KAAK;AACzB,2BACI;wBACDL,MAAME,OAAOQ,IAAIL,KAAK,EAACC;AAC1B;AACJ,uBACI;oBACD,IAAIA,UAAUG,WAAW;wBACrBF,UAAUI,QAAQL;wBAClBN,MAAME,OAAOQ,IAAIL,KAAKE;AACzB;AACJ;AACb;YACQN,KAAKW,WAAW,SAAUP;gBACtB,OAAOL,MAAME,OAAOW,IAAIR;AACpC;YACQJ,KAAKa,OAAO,SAAUT;gBAClB,OAAO,IAAIU,SAAQ,SAAUC,SAASC;oBAClC,IAAIjB,MAAMY,SAASP,MAAM;wBACrBL,MAAMI,YAAYC,KAAKW;AAC1B,2BACI;wBACDhB,MAAMI,YAAYC;wBAClBW;AACH;AACjB;AACA;YACQf,KAAKiB,SAAS,SAAUb;gBACpB,IAAIE,YAAYP,MAAME,OAAOM,IAAIH;gBACjC,IAAIE,cAAcE,aAAaF,UAAUnB,WAAW,GAAG;oBACnDY,MAAME,OAAOiB,OAAOd;oBACpB;AACH;gBACD,IAAIe,SAASb,UAAUc;gBACvBrB,MAAME,OAAOQ,IAAIL,KAAKE;gBACtB,IAAIa,WAAWX,WAAW;oBACtBa,WAAWF,QAAQ;AACtB;AACb;AACK;QACDrB,eAAewB,cAAc;YACzB,IAAIxB,eAAeyB,aAAaf,WAAW;gBACvCV,eAAeyB,WAAW,IAAIzB;AACjC;YACD,OAAOA,eAAeyB;AAC9B;QACI,OAAOzB;AACX;IACA,SAAS0B;QACL,OAAO1B,eAAewB;AAC1B;IACA1B,QAAA6B,UAAkBD;;;;;;IC5DlB,IAAIE,YAAa1B,kBAAQA,eAAK0B,aAAc,SAAUC,SAASC,YAAYC,GAAGC;QAC1E,OAAO,KAAKD,MAAMA,IAAIf,WAAU,SAAUC,SAASC;YAC/C,SAASe,UAAUlC;gBAAS;oBAAMmC,KAAKF,UAAUG,KAAKpC;kBAAW,OAAOpB;oBAAKuC,OAAOvC;;AAAO;YAC3F,SAASyD,SAASrC;gBAAS;oBAAMmC,KAAKF,UAAU,SAASjC;kBAAW,OAAOpB;oBAAKuC,OAAOvC;;AAAO;YAC9F,SAASuD,KAAKG;gBAAUA,OAAOC,OAAOrB,QAAQoB,OAAOtC,SAAS,IAAIgC,GAAE,SAAUd;oBAAWA,QAAQoB,OAAOtC;oBAAWwC,KAAKN,WAAWG;AAAY;YAC/IF,MAAMF,YAAYA,UAAUQ,MAAMX,SAASC,cAAc,KAAKK;AACtE;AACA;IACA,IAAIM,cAAevC,kBAAQA,eAAKuC,eAAgB,SAAUZ,SAASa;QAC/D,IAAIC,IAAI;YAAEC,OAAO;YAAGC,MAAM;gBAAa,IAAIjE,EAAE,KAAK,GAAG,MAAMA,EAAE;gBAAI,OAAOA,EAAE;AAAK;YAAEkE,MAAM;YAAIC,KAAK;WAAMC,GAAGC,GAAGrE,GAAGsE;QAC/G,OAAOA,IAAI;YAAEf,MAAMgB,KAAK;YAAIC,OAASD,KAAK;YAAIE,QAAUF,KAAK;kBAAaG,WAAW,eAAeJ,EAAEI,OAAOC,YAAY;YAAa,OAAOrD;AAAO,YAAGgD;QACvJ,SAASC,KAAKK;YAAK,OAAO,SAAUC;gBAAK,OAAOvB,KAAK,EAACsB,GAAGC;AAAM;AAAG;QAClE,SAASvB,KAAKwB;YACV,IAAIV,GAAG,MAAM,IAAIW,UAAU;YAC3B,OAAOhB;gBACH,IAAIK,IAAI,GAAGC,MAAMrE,IAAI8E,GAAG,KAAK,IAAIT,EAAE,YAAYS,GAAG,KAAKT,EAAE,cAAcrE,IAAIqE,EAAE,cAAcrE,EAAEK,KAAKgE;gBAAI,KAAKA,EAAEd,WAAWvD,IAAIA,EAAEK,KAAKgE,GAAGS,GAAG,KAAKpB,MAAM,OAAO1D;gBAC3J,IAAIqE,IAAI,GAAGrE,GAAG8E,KAAK,EAACA,GAAG,KAAK,GAAG9E,EAAEmB;gBACjC,QAAQ2D,GAAG;kBACP,KAAK;kBAAG,KAAK;oBAAG9E,IAAI8E;oBAAI;;kBACxB,KAAK;oBAAGf,EAAEC;oBAAS,OAAO;wBAAE7C,OAAO2D,GAAG;wBAAIpB,MAAM;;;kBAChD,KAAK;oBAAGK,EAAEC;oBAASK,IAAIS,GAAG;oBAAIA,KAAK,EAAC;oBAAI;;kBACxC,KAAK;oBAAGA,KAAKf,EAAEI,IAAIzB;oBAAOqB,EAAEG,KAAKxB;oBAAO;;kBACxC;oBACI,MAAM1C,IAAI+D,EAAEG,MAAMlE,IAAIA,EAAES,SAAS,KAAKT,EAAEA,EAAES,SAAS,QAAQqE,GAAG,OAAO,KAAKA,GAAG,OAAO,IAAI;wBAAEf,IAAI;wBAAG;AAAW;oBAC5G,IAAIe,GAAG,OAAO,OAAO9E,KAAM8E,GAAG,KAAK9E,EAAE,MAAM8E,GAAG,KAAK9E,EAAE,KAAM;wBAAE+D,EAAEC,QAAQc,GAAG;wBAAI;AAAQ;oBACtF,IAAIA,GAAG,OAAO,KAAKf,EAAEC,QAAQhE,EAAE,IAAI;wBAAE+D,EAAEC,QAAQhE,EAAE;wBAAIA,IAAI8E;wBAAI;AAAQ;oBACrE,IAAI9E,KAAK+D,EAAEC,QAAQhE,EAAE,IAAI;wBAAE+D,EAAEC,QAAQhE,EAAE;wBAAI+D,EAAEI,IAAIa,KAAKF;wBAAK;AAAQ;oBACnE,IAAI9E,EAAE,IAAI+D,EAAEI,IAAIzB;oBAChBqB,EAAEG,KAAKxB;oBAAO;;gBAEtBoC,KAAKhB,KAAKzD,KAAK4C,SAASc;cAC1B,OAAOhE;gBAAK+E,KAAK,EAAC,GAAG/E;gBAAIsE,IAAI;AAAE,cAAW;gBAAED,IAAIpE,IAAI;AAAI;YAC1D,IAAI8E,GAAG,KAAK,GAAG,MAAMA,GAAG;YAAI,OAAO;gBAAE3D,OAAO2D,GAAG,KAAKA,GAAG,UAAU;gBAAGpB,MAAM;;AAC7E;AACL;IACA,IAAIrC,QAAQC;IACZpB,OAAOe,eAAeC,SAAS,cAAc;QAAEC,OAAO;;IAkBtD,IAAI8D,mBAAmB;IACvB,IAAIC,0BAA0B;QAC1BxD,KAAK,SAAUyD;YAAS,OAAOnC,UAAU3B,YAAY,QAAQ,IAAG;gBAC5D,OAAOwC,YAAYvC,OAAM,SAAU8D;oBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;QACLsE,SAAS,SAAU3D;YAAO,OAAOsB,UAAU3B,YAAY,QAAQ,IAAG;gBAC9D,OAAOwC,YAAYvC,OAAM,SAAU8D;oBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;QACLuE,OAAO;YAAc,OAAOtC,UAAU3B,YAAY,QAAQ,IAAG;gBACzD,OAAOwC,YAAYvC,OAAM,SAAU8D;oBAC/B,OAAO,EAAC,GAAcG,OAAOC,aAAaF;AACtD;AACK;AAAI;QACLG,YAAY,SAAU/D;YAAO,OAAOsB,UAAU3B,YAAY,QAAQ,IAAG;gBACjE,OAAOwC,YAAYvC,OAAM,SAAU8D;oBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;QACL2E,SAAS,SAAUhE,KAAKP;YAAS,OAAO6B,UAAU3B,YAAY,QAAQ,IAAG;gBACrE,OAAOwC,YAAYvC,OAAM,SAAU8D;oBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;QACL4E,SAAS,SAAUR;YACf,OAAOI,OAAOC,aAAa9D,IAAIyD;AAClC;QACDS,aAAa,SAAUlE;YACnB,OAAO6D,OAAOC,aAAaH,QAAQ3D;AACtC;QACDmE,WAAW;YACP,OAAON,OAAOC,aAAaF;AAC9B;QACDQ,gBAAgB,SAAUpE;YACtB,OAAO6D,OAAOC,aAAaC,WAAW/D;AACzC;QACDqE,aAAa,SAAUrE,KAAKP;YACxB,OAAOoE,OAAOC,aAAaE,QAAQhE,KAAKP;AAC3C;;IAOL,SAAS6E,MAAMC;QACX,OAAO,IAAI7D,SAAQ,SAAUC;YAAW,OAAOM,WAAWN,SAAS4D;AAAc;AACrF;IAOA,SAASC,qBAAqBzF;QAC1B,IAAI0F,QAAQ;QACZ,IAAIC,eAAe;QACnB,KAAK,IAAI5F,IAAI,GAAGA,IAAIC,QAAQD,KAAK;YAC7B,IAAI6F,QAAQC,KAAKC,MAAMD,KAAKE,WAAWL,MAAM1F;YAC7C2F,gBAAgBD,MAAME;AACzB;QACD,OAAOD;AACX;IAMA,SAASK;QACL,OAAOC,KAAKC,MAAMC,aAAaV,qBAAqB;AACxD;IACA,IAAIW,kBAAiC;QACjC,SAASA,gBAAgBC;YACrBxF,KAAKyF,iBAAiB,IAAIC;YAC1B1F,KAAKwF,iBAAiBhF;YACtBR,KAAK2F,KAAKR;YACVnF,KAAK4F,cAAc5F,KAAK4F,YAAYC,KAAK7F;YACzCA,KAAK8F,cAAc9F,KAAK8F,YAAYD,KAAK7F;YACzCA,KAAK+F,yBAAyB/F,KAAK+F,uBAAuBF,KAAK7F;YAC/DA,KAAKgG,2BAA2BhG,KAAKgG,yBAAyBH,KAAK7F;YACnEA,KAAKiG,2BAA2BjG,KAAKiG,yBAAyBJ,KAAK7F;YACnEA,KAAKwF,iBAAiBA;YACtB,IAAID,gBAAgBW,YAAY1F,WAAW;gBACvC+E,gBAAgBW,UAAU;AAC7B;AACJ;QAWDX,gBAAgB1G,UAAU+G,cAAc,SAAUO,SAASC;YACvD,IAAIA,iBAAiB,GAAG;gBAAEA,UAAU;AAAO;YAC3C,OAAO1E,UAAU1B,WAAW,QAAQ,IAAG;gBACnC,IAAIqG,KAAKC,UAAUC,aAAaC,SAASC,SAASC,aAAaC,kBAAkBC;gBACjF,OAAOrE,YAAYvC,OAAM,SAAU8D;oBAC/B,QAAQA,GAAGpB;sBACP,KAAK;wBACD2D,MAAMjB,KAAKC,QAAQT,qBAAqB;wBACxC0B,WAAWlB,KAAKC,QAAQe;wBACxBG,cAAc5C,mBAAmB,MAAMwC;wBACvCK,UAAUxG,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;wBAC7E1B,GAAGpB,QAAQ;;sBACf,KAAK;wBACD,MAAM0C,KAAKC,QAAQiB,WAAW,OAAO,EAAC,GAAa;wBACnD,OAAO,EAAC,GAAa5B,MAAM;;sBAC/B,KAAK;wBACDZ,GAAGnB;wBACH8D,UAAUD,QAAQlC,YAAYiC;wBAC9B,MAAME,YAAY,OAAO,OAAO,EAAC,GAAa;wBAC9CC,cAAc1G,KAAK2F,KAAK,MAAMQ,UAAU,MAAME;wBAE9C,OAAO,EAAC,GAAa3B,MAAMM,KAAKC,MAAMD,KAAKE,WAAW;;sBAC1D,KAAK;wBAEDpB,GAAGnB;wBACH6D,QAAQ/B,YAAY8B,aAAaM,KAAKC,UAAU;4BAC5CnB,IAAI3F,KAAK2F;4BACTU,KAAKA;4BACLU,YAAYL;4BACZM,cAAc5B,KAAKC;4BACnB4B,eAAe7B,KAAKC;;wBAExB,OAAO,EAAC,GAAaX,MAAM;;sBAC/B,KAAK;wBACDZ,GAAGnB;wBACHgE,mBAAmBH,QAAQlC,YAAYiC;wBACvC,IAAII,qBAAqB,MAAM;4BAC3BC,yBAAyBC,KAAKK,MAAMP;4BACpC,IAAIC,uBAAuBjB,OAAO3F,KAAK2F,MAAMiB,uBAAuBP,QAAQA,KAAK;gCAC7ErG,KAAKyF,eAAe0B,IAAId;gCACxBrG,KAAKiG,yBAAyBM,aAAaF;gCAC3C,OAAO,EAAC,GAAc;AACzB;AACJ;wBACD,OAAO,EAAC,GAAa;;sBACzB,KAAK;wBACDd,gBAAgB6B,cAAcpH,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;wBACjG,OAAO,EAAC,GAAaxF,KAAKgG,yBAAyBM;;sBACvD,KAAK;wBACDxC,GAAGnB;wBACHmB,GAAGpB,QAAQ;;sBACf,KAAK;wBACD2D,MAAMjB,KAAKC,QAAQT,qBAAqB;wBACxC,OAAO,EAAC,GAAa;;sBACzB,KAAK;wBAAG,OAAO,EAAC,GAAc;;AAElD;AACA;AACA;QACIW,gBAAgB1G,UAAUoH,2BAA2B,SAAUoB,YAAYhB;YACvE,OAAO3E,UAAU1B,WAAW,QAAQ,IAAG;gBACnC,IAAID,QAAQC;gBACZ,OAAOuC,YAAYvC,OAAM,SAAU8D;oBAC/BzC,YAAW;wBAAc,OAAOK,UAAU3B,YAAY,QAAQ,IAAG;4BAC7D,IAAIyG,SAASC,SAASa;4BACtB,OAAO/E,YAAYvC,OAAM,SAAU8D;gCAC/B,QAAQA,GAAGpB;kCACP,KAAK;oCAAG,OAAO,EAAC,GAAa6E,YAAc9F,UAAUZ,KAAKwF;;kCAC1D,KAAK;oCACDvC,GAAGnB;oCACH,KAAK3C,KAAKyF,eAAe7E,IAAIyF,MAAM;wCAC/BkB,YAAc9F,UAAUR,OAAOoF;wCAC/B,OAAO,EAAC;AACX;oCACDG,UAAUxG,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;oCAC7EiB,UAAUD,QAAQlC,YAAY+C;oCAC9B,IAAIZ,YAAY,MAAM;wCAClBa,gBAAgBT,KAAKK,MAAMT;wCAC3Ba,cAAcL,gBAAgB7B,KAAKC;wCACnCmB,QAAQ/B,YAAY4C,YAAYR,KAAKC,UAAUQ;wCAC/CC,YAAc9F,UAAUR,OAAOoF;AAClC,2CACI;wCACDkB,YAAc9F,UAAUR,OAAOoF;wCAC/B,OAAO,EAAC;AACX;oCACDrG,KAAKiG,yBAAyBoB,YAAYhB;oCAC1C,OAAO,EAAC;;AAExC;AACA;AAAmB,wBAAI;oBACP,OAAO,EAAC;AACxB;AACA;AACA;QACId,gBAAgB1G,UAAUmH,2BAA2B,SAAUM;YAC3D,OAAO5E,UAAU1B,WAAW,QAAQ,IAAG;gBACnC,OAAOuC,YAAYvC,OAAM,SAAU8D;oBAC/B,QAAQA,GAAGpB;sBACP,KAAK;wBAAG,OAAO,EAAC,GAAa,IAAI5B,SAAQ,SAAUC;4BAC3C,IAAIyG,iBAAiB;4BACrB,IAAIC,YAAYrC,KAAKC;4BACrB,IAAIqC,mBAAmB;4BACvB,IAAIC,mBAAmB;4BACvB,SAASC;gCACL,KAAKD,kBAAkB;oCACnB1D,OAAO4D,oBAAoB,WAAWD;oCACtCrC,gBAAgBuC,kBAAkBF;oCAClCG,aAAaC;oCACbL,mBAAmB;AACtB;gCACD,KAAKH,gBAAgB;oCACjBA,iBAAiB;oCACjB,IAAIS,aAAaP,oBAAoBtC,KAAKC,QAAQoC;oCAClD,IAAIQ,aAAa,GAAG;wCAChB5G,WAAWN,SAASkH;AACvB,2CACI;wCACDlH,QAAQ;AACX;AACJ;AACJ;4BACDkD,OAAOiE,iBAAiB,WAAWN;4BACnCrC,gBAAgB4C,aAAaP;4BAC7B,IAAII,YAAY3G,WAAWuG,aAAa5C,KAAKoD,IAAI,GAAG9B,WAAWlB,KAAKC;AACvE;;sBACL,KAAK;wBACDvB,GAAGnB;wBACH,OAAO,EAAC;;AAEhC;AACA;AACA;QACI4C,gBAAgB4C,eAAe,SAAUE;YACrCrI,KAAK8H,kBAAkBO;YACvB,IAAI9C,gBAAgBW,YAAY1F,WAAW;gBACvC;AACH;YACD+E,gBAAgBW,QAAQxC,KAAK2E;AACrC;QACI9C,gBAAgBuC,oBAAoB,SAAUO;YAC1C,IAAI9C,gBAAgBW,YAAY1F,WAAW;gBACvC;AACH;YACD+E,gBAAgBW,UAAUX,gBAAgBW,QAAQoC,QAAO,SAAUpJ;gBAAK,OAAOA,MAAMmJ;AAAO;AACpG;QACI9C,gBAAgBgD,gBAAgB;YAC5B,IAAIhD,gBAAgBW,YAAY1F,WAAW;gBACvC;AACH;YACD,IAAI0F,UAAUX,gBAAgBW,QAAQsC;YACtCtC,QAAQuC,SAAQ,SAAUvJ;gBAAK,OAAOA;AAAI;AAClD;QAQIqG,gBAAgB1G,UAAUiH,cAAc,SAAUK;YAC9C,OAAOzE,UAAU1B,WAAW,QAAQ,IAAG;gBACnC,OAAOuC,YAAYvC,OAAM,SAAU8D;oBAC/B,QAAQA,GAAGpB;sBACP,KAAK;wBAAG,OAAO,EAAC,GAAa1C,KAAK+F,uBAAuBI;;sBACzD,KAAK;wBAAG,OAAO,EAAC,GAAcrC,GAAGnB;;AAErD;AACA;AACA;QAQI4C,gBAAgB1G,UAAUkH,yBAAyB,SAAUI;YACzD,OAAOzE,UAAU1B,WAAW,QAAQ,IAAG;gBACnC,IAAIwG,SAASD,aAAaE,SAASiC;gBACnC,OAAOnG,YAAYvC,OAAM,SAAU8D;oBAC/B,QAAQA,GAAGpB;sBACP,KAAK;wBACD8D,UAAUxG,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;wBAC7Ee,cAAc5C,mBAAmB,MAAMwC;wBACvCM,UAAUD,QAAQlC,YAAYiC;wBAC9B,IAAIE,YAAY,MAAM;4BAClB,OAAO,EAAC;AACX;wBACDiC,gBAAgB7B,KAAKK,MAAMT;wBAC3B,MAAMiC,cAAc/C,OAAO3F,KAAK2F,KAAK,OAAO,EAAC,GAAa;wBAC1D,OAAO,EAAC,GAAa4B,YAAc9F,UAAUZ,KAAK6H,cAAcrC;;sBACpE,KAAK;wBACDvC,GAAGnB;wBACH3C,KAAKyF,eAAevE,OAAOwH,cAAcrC;wBACzCG,QAAQhC,eAAe+B;wBACvBgB,YAAc9F,UAAUR,OAAOyH,cAAcrC;wBAC7Cd,gBAAgBgD;wBAChBzE,GAAGpB,QAAQ;;sBACf,KAAK;wBAAG,OAAO,EAAC;;AAEpC;AACA;AACA;QAOI6C,gBAAgB6B,gBAAgB,SAAU5B;YACtC,IAAImD,mBAAmBvD,KAAKC,QAAQ;YACpC,IAAImB,UAAUhB;YACd,IAAIoD,OAAO;YACX,IAAIC,YAAY;YAChB,OAAO,MAAM;gBACT,IAAIzI,MAAMoG,QAAQnC,QAAQwE;gBAC1B,IAAIzI,QAAQ,MAAM;oBACd;AACH;gBACDwI,KAAKlF,KAAKtD;gBACVyI;AACH;YACD,IAAIN,gBAAgB;YACpB,KAAK,IAAIrJ,IAAI,GAAGA,IAAI0J,KAAKzJ,QAAQD,KAAK;gBAClC,IAAI4J,WAAWF,KAAK1J;gBACpB,IAAI4J,SAASC,SAASpF,mBAAmB;oBACrC,IAAI8C,UAAUD,QAAQlC,YAAYwE;oBAClC,IAAIrC,YAAY,MAAM;wBAClB,IAAIiC,gBAAgB7B,KAAKK,MAAMT;wBAC/B,IAAKiC,cAAczB,kBAAkBzG,aAAakI,cAAc1B,eAAe2B,oBAC1ED,cAAczB,kBAAkBzG,aAAakI,cAAczB,gBAAgB0B,kBAAmB;4BAC/FnC,QAAQhC,eAAesE;4BACvBP,gBAAgB;AACnB;AACJ;AACJ;AACJ;YACD,IAAIA,eAAe;gBACfhD,gBAAgBgD;AACnB;AACT;QACIhD,gBAAgBW,UAAU1F;QAC1B,OAAO+E;AACX;IACA3F,QAAA6B,UAAkB8D;;;;;AC/YlB,IAAAyD,UAAe;;ACMR,MAAMC,uCAAuC;;AAK7C,MAAMC,+BAAmD;IAC9DC,kBAAkBF;;;AAMb,MAAMG,mCAAmC;;AAKzC,MAAMC,oCAAoC;;AAK1C,MAAMC,2BAA2B;;AAEjC,MAAMC,wBAAwB;;AAM9B,MAAMC,sCAAsC;;AAK5C,MAAMC,sCAAsC;;AAK5C,MAAMC,gBAAgB;;AAKtB,MAAMC,oCAAoC;;AAK1C,MAAMC,uBAAuB;IAClClK,MAAM;IACNsJ,SAASA;;;AAGJ,MAAMa,uBAAuB,MAAMzE,KAAKC;;AC1DzC,MAAOyE,qBAAqBrK;IAChCsK,YAAmBzK,OAAsB0K;QACvCC,MAAMD;QADWhK,KAAKV,QAALA;QAAsBU,KAAiBgK,oBAAjBA;QAEvCpL,OAAOsL,eAAelK,MAAM8J,aAAajL;AAC1C;IAEDsL,oBAAmB7K,OACjBA,OAAK0K,mBACLA;QAKA,OAAO,IAAIF,aAAaxK,OAAO0K;AAChC;;;AAOG,MAAOI,4BAA4BN;IACvCC,YACEzK,OACA0K,mBACOK,OACAC,WAAgB;QAEvBL,MAAM3K,OAAO0K;QAHNhK,KAAKqK,QAALA;QACArK,KAAQsK,WAARA;QAIP1L,OAAOsL,eAAelK,MAAMoK,oBAAoBvL;AACjD;;;AAOG,MAAO0L,qBAAqBT;IAChCC;QACEE,MAAM,WAAW;QAEjBrL,OAAOsL,eAAelK,MAAMuK,aAAa1L;AAC1C;;;AAMG,MAAO2L,0BAA0BD;IACrCR,YAAmBU;QACjBR;QADiBjK,KAAKyK,QAALA;QAGjB7L,OAAOsL,eAAelK,MAAMwK,kBAAkB3L;AAC/C;;;AAGG,MAAO6L,4BAA4BZ;IACvCC,YAAmBU;QACjBR,MAAM,aAAa;QADFjK,KAAKyK,QAALA;QAGjB7L,OAAOsL,eAAelK,MAAM0K,oBAAoB7L;AACjD;;;AAMG,MAAO8L,yBAAyBb;IACpCC,YACEzK,OACA0K,mBACOY;QAEPX,MAAM3K,OAAO0K;QAFNhK,KAAS4K,YAATA;QAIPhM,OAAOsL,eAAelK,MAAM2K,iBAAiB9L;AAC9C;;;AAMG,MAAOgM,iCAAiCf;IAC5CC,YAAmBe,UAAyBC;QAC1Cd,MACE,yBACA,qCAAqCe,mBAAmBF,UAAU,EAChE,0BACcE,mBAAmBD;QALpB/K,KAAQ8K,WAARA;QAAyB9K,KAAK+K,QAALA;QAO1CnM,OAAOsL,eAAelK,MAAM6K,yBAAyBhM;AACtD;;;AASH,SAASmM,mBAAmBnL,OAAeoL,UAAoB;IAC7D,OAAOpL,UAAUoL,QAAQlC,SAASlJ,SAASA,QAAQ;AACrD;;AC5FO,MAAMqL,4BACXC;IAEA,IAAIA,YAAYnM,QAAQ,QAAQ,GAAG;QACjCmM,cAAcA,YAAYC,UAAU,GAAGD,YAAYnM,QAAQ;AAC5D;IAED,MAAMqM,eAAe,IAAIC,gBAAgBH;IAEzC,OAAO;QACLd,OAAOgB,aAAa9K,IAAI;QACxBgL,MAAMF,aAAa9K,IAAI,WAAWC;QAClClB,OAAO+L,aAAa9K,IAAI,YAAYC;QACpCwJ,mBAAmBqB,aAAa9K,IAAI,wBAAwBC;;AAC7D;;AAGI,MAAMgL,YAAY,CACvBC,cACAC,aACAvC,mBAA2BF,yCAEpB,IAAInI,SAA8B,CAAC6K,KAAKC;IAC7C,MAAMC,SAAS5H,OAAO6H,SAASC,cAAc;IAE7CF,OAAOG,aAAa,SAAS;IAC7BH,OAAOG,aAAa,UAAU;IAC9BH,OAAOI,MAAMC,UAAU;IAEvB,MAAMC,eAAe;QACnB,IAAIlI,OAAO6H,SAAStJ,KAAK4J,SAASP,SAAS;YACzC5H,OAAO6H,SAAStJ,KAAK6J,YAAYR;YACjC5H,OAAO4D,oBAAoB,WAAWyE,oBAAoB;AAC3D;AAAA;IAGH,IAAIA;IAEJ,MAAMC,sBAAsBlL,YAAW;QACrCuK,IAAI,IAAIrB;QACR4B;AAAc,QACbhD,mBAAmB;IAEtBmD,qBAAqB,SAAU7N;QAC7B,IAAIA,EAAE+N,UAAUd,aAAa;QAC7B,KAAKjN,EAAEgO,QAAQhO,EAAEgO,KAAKC,SAAS,0BAA0B;QAEzD,MAAMC,cAAclO,EAAEmO;QAEtB,IAAID,aAAa;YACdA,YAAoBE;AACtB;QAEDpO,EAAEgO,KAAKK,SAASxN,QACZsM,IAAI9B,aAAaiD,YAAYtO,EAAEgO,KAAKK,aACpCnB,IAAIlN,EAAEgO,KAAKK;QAEf/E,aAAawE;QACbtI,OAAO4D,oBAAoB,WAAWyE,oBAAoB;QAI1DjL,WAAW8K,cAAc9C,oCAAoC;AAC/D;IAEApF,OAAOiE,iBAAiB,WAAWoE,oBAAoB;IACvDrI,OAAO6H,SAAStJ,KAAKwK,YAAYnB;IACjCA,OAAOG,aAAa,OAAOP;AAAa;;AAIrC,MAAMwB,YAAaC;IACxB,MAAMC,QAAQ;IACd,MAAMC,SAAS;IACf,MAAMC,OAAOpJ,OAAOqJ,WAAWrJ,OAAOsJ,aAAaJ,SAAS;IAC5D,MAAMK,MAAMvJ,OAAOwJ,WAAWxJ,OAAOyJ,cAAcN,UAAU;IAE7D,OAAOnJ,OAAO0J,KACZT,KACA,yBACA,QAAQG,YAAYG,aAAaL,gBAAgBC;AAClD;;AAGI,MAAMQ,WAAYC,UAChB,IAAI/M,SAA8B,CAACC,SAASC;IACjD,IAAI8M;IAGJ,MAAMC,aAAaC,aAAY;QAC7B,IAAIH,OAAOpD,SAASoD,OAAOpD,MAAMwD,QAAQ;YACvCC,cAAcH;YACdhG,aAAaoG;YACblK,OAAO4D,oBAAoB,WAAWiG,oBAAoB;YAC1D9M,OAAO,IAAI0J,oBAAoBmD,OAAOpD;AACvC;AAAA,QACA;IAEH,MAAM0D,YAAY9M,YAAW;QAC3B6M,cAAcH;QACd/M,OAAO,IAAIwJ,kBAAkBqD,OAAOpD;QACpCxG,OAAO4D,oBAAoB,WAAWiG,oBAAoB;AAAM,SAC9DD,OAAO1E,oBAAoBF,wCAAwC;IAEvE6E,qBAAqB,SAAUrP;QAC7B,KAAKA,EAAEgO,QAAQhO,EAAEgO,KAAKC,SAAS,0BAA0B;YACvD;AACD;QAED3E,aAAaoG;QACbD,cAAcH;QACd9J,OAAO4D,oBAAoB,WAAWiG,oBAAoB;QAC1DD,OAAOpD,MAAMoC;QAEb,IAAIpO,EAAEgO,KAAKK,SAASxN,OAAO;YACzB,OAAO0B,OAAO8I,aAAaiD,YAAYtO,EAAEgO,KAAKK;AAC/C;QAED/L,QAAQtC,EAAEgO,KAAKK;AACjB;IAEA7I,OAAOiE,iBAAiB,WAAW4F;AAAmB;;AAInD,MAAMM,YAAY,MAChBnK,OAAOoK;;AAGT,MAAMC,qBAAqB;IAChC,MAAMC,UACJ;IACF,IAAIrJ,SAAS;IACb,MAAMsJ,eAAeC,MAAMC,KACzBN,YAAYO,gBAAgB,IAAIC,WAAW;IAE7CJ,aAAa/F,SAAQlF,KAAM2B,UAAUqJ,QAAQhL,IAAIgL,QAAQpP;IACzD,OAAO+F;AAAM;;AAGR,MAAM2J,SAAUhP,SAAkBiP,KAAKjP;;AAG9C,MAAMkP,iBAAkBC,UACfpQ,OAAOqQ,KAAKD,QAChB1G,QAAO4G,YAAYF,OAAOE,OAAO,cACjCC,QAAO,CAACC,KAAKhP,QAAQxB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAMD,MAAG;IAAEhP,CAACA,MAAM4O,OAAO5O;KAAS,CAAA;;AAGrD,MAAMkP,oBAAqBxL;SAAEyL,UAAUC,aAAS1L,IAAKkL,SAAMzQ,OAAAuF,IAAhC;IAChC,OAAO,IAAIwH,gBACTyD,eAAiBnQ,OAAAyQ,OAAA;QAAAG;OAAcR,UAC/B1J;AAAU;;AAGP,MAAMmK,SAASC,MAAOlR;IAC3B,MAAMmR,WAAgBvB,YAAYwB,OAAOC,OACvC;QAAEnQ,MAAM;QACR,IAAIoQ,aAAcjB,OAAOrQ;IAG3B,aAAamR;AAAQ;;AAGvB,MAAMI,eAAgBC;IACpB,MAAMC,WAAwC;QAAE,KAAK;QAAK,KAAK;QAAK,KAAK;;IACzE,OAAOD,MAAME,QAAQ,WAAWC,KAAcF,SAASE;AAAG;;AAI5D,MAAMC,YAAaJ,SACjBK,mBACEC,KAAKN,OACFO,MAAM,IACNC,KAAIC,KACI,OAAO,OAAOA,EAAEC,WAAW,GAAGpL,SAAS,KAAKkD,OAAO,KAE3DmI,KAAK;;AAGL,MAAMC,eAAgBZ,SAC3BI,UAAUJ,MAAME,QAAQ,MAAM,KAAKA,QAAQ,MAAM;;AAE5C,MAAMW,2BAA4Bb;IACvC,MAAMc,gBAAgB,IAAIlC,WAAWoB;IACrC,OAAOD,aACL9L,OAAO6K,KAAKiC,OAAOC,gBAAgBvC,MAAMC,KAAKoC;AAC/C;;AAGI,MAAMG,iBAAiB;IAC5B,KAAK7C,aAAa;QAChB,MAAM,IAAI3O,MACR;AAEH;IACD,WAAW2O,YAAYwB,WAAW,aAAa;QAC7C,MAAM,IAAInQ,MAAM;AAGjB;AAAA;;AAMI,MAAMyR,YAAaC;IACxB,KAAK,eAAeC,KAAKD,YAAY;QACnC,OAAO,WAAWA;AACnB;IAED,OAAOA;AAAS;;AAMX,MAAME,iBAAiB,CAC5BC,QACAH;IAEA,IAAIG,QAAQ;QACV,OAAOA,OAAOC,WAAW,cAAcD,SAAS,WAAWA;AAC5D;IAED,OAAO,GAAGH;AAAY;;AAGjB,MAAMK,cAAe3R;IAC1B,WAAWA,UAAU,UAAU;QAC7B,OAAOA;AACR;IACD,OAAO4R,SAAS5R,OAAO,OAAOW;AAAS;;AC/OlC,MAAMkR,cAAc,CAAClS,SAAoCmS,OAC9D,IAAI7Q,SAAQ,SAAUC,SAASC;IAC7B,MAAM4Q,iBAAiB,IAAIC;IAE3BD,eAAeE,MAAMC,YAAY,SAAUC;QAEzC,IAAIA,MAAMvF,KAAKnN,OAAO;YACpB0B,OAAO,IAAIvB,MAAMuS,MAAMvF,KAAKnN;AAC7B,eAAM;YACLyB,QAAQiR,MAAMvF;AACf;QACDmF,eAAeE,MAAMjF;AACvB;IAEA8E,GAAGM,YAAYzS,SAAS,EAACoS,eAAeM;AAC1C;;ACTK,MAAMC,wBAAwB,MAAM,IAAIC;;AAE/C,MAAMC,UAAU3C,OAAO4C,UAAkBC;IACvC,MAAMzF,iBAAiB0F,MAAMF,UAAUC;IAEvC,OAAO;QACLE,IAAI3F,SAAS2F;QACbC,YAAY5F,SAAS4F;;AACtB;;AAGH,MAAMC,qBAAqBjD,OACzB4C,UACAC,cACAnM;IAEA,MAAMwM,aAAaT;IACnBI,aAAaM,SAASD,WAAWC;IAEjC,IAAI1E;IAGJ,OAAOrN,QAAQgS,KAAK,EAClBT,QAAQC,UAAUC,eAElB,IAAIzR,SAAQ,CAAC2B,GAAGzB;QACdmN,YAAY9M,YAAW;YACrBuR,WAAWG;YACX/R,OAAO,IAAIvB,MAAM;AAAkC,YAClD2G;AAAQ,WAEZ4M,SAAQ;QACTjL,aAAaoG;AAAU;AACvB;;AAGJ,MAAM8E,kBAAkBvD,OACtB4C,UACAxH,UACAC,OACAwH,cACAnM,SACA8M,QACAC,gBAEOzB,YACL;IACE0B,MAAM;QACJtI;QACAC;;IAEF3E;IACAkM;IACAC;IACAY;GAEFD;;AAIG,MAAMG,cAAc3D,OACzB4C,UACAxH,UACAC,OACAwH,cACAW,QACAC,aACA/M,UAAUkD;IAEV,IAAI4J,QAAQ;QACV,OAAOD,gBACLX,UACAxH,UACAC,OACAwH,cACAnM,SACA8M,QACAC;AAEH,WAAM;QACL,OAAOR,mBAAmBL,UAAUC,cAAcnM;AACnD;AAAA;;AAGIsJ,eAAe4D,QACpBpG,KACA9G,SACA0E,UACAC,OACAwI,SACAL,QACAC;IAEA,IAAIK,aAA2B;IAC/B,IAAI1G;IAEJ,KAAK,IAAI5N,IAAI,GAAGA,IAAIkK,kCAAkClK,KAAK;QACzD;YACE4N,iBAAiBuG,YACfnG,KACApC,UACAC,OACAwI,SACAL,QACAC,aACA/M;YAEFoN,aAAa;YACb;AAOD,UANC,OAAO/U;YAKP+U,aAAa/U;AACd;AACF;IAED,IAAI+U,YAAY;QACd,MAAMA;AACP;IAED,MACE1P,KAEEgJ,SAAQ4F,OAFVpT,OAAQA,OAAK0K,mBAAEA,qBAAiBlG,IAAK2I,OAAIlO,OAAAuF,IAAnC,oCADF2O,IAEJA,MACE3F;IAEJ,KAAK2F,IAAI;QACP,MAAMgB,eACJzJ,qBAAqB,+BAA+BkD;QAEtD,IAAI5N,UAAU,gBAAgB;YAC5B,MAAM,IAAIqL,iBAAiBrL,OAAOmU,cAAchH,KAAK7B;AACtD;QAED,IAAItL,UAAU,yBAAyB;YACrC,MAAM,IAAIuL,yBAAyBC,UAAUC;AAC9C;QAED,MAAM,IAAIjB,aAAaxK,SAAS,iBAAiBmU;AAClD;IAED,OAAOhH;AACT;;ACvJOiD,eAAegE,WACpB5P,IASAoP;IATA,KAAAS,SACEA,SAAOvN,SACPA,SAAO0E,UACPA,UAAQC,OACRA,OAAK6I,aACLA,aAAWT,aACXA,eAEqBrP,IADlByP,UAAOhV,OAAAuF,IAPZ;IAWA,MAAMtB,OAAO2Q,cACT7D,kBAAkBiE,WAClB1M,KAAKC,UAAUyM;IAEnB,aAAaD,QACX,GAAGK,uBACHvN,SACA0E,YAAY,WACZC,OACA;QACE8I,QAAQ;QACRrR;QACAsR,SAAS;YACP,gBAAgBX,cACZ,sCACA;YACJ,gBAAgBrE,KACdjI,KAAKC,UAAU8M,eAAehK;;OAIpCsJ,QACAC;AAEJ;;ACtCA,MAAMY,SAAUC,OAAkBvF,MAAMC,KAAK,IAAIhJ,IAAIsO;;AAW9C,MAAMC,kBAAkB,IAAIC,WAC1BH,OAAOG,OAAO5L,OAAO6L,SAASxD,KAAK,KAAKyD,OAAO7D,MAAM,QAAQI,KAAK;;ACbpE,MAAM0D,mBAAmB;;AACzB,MAAMC,4BAA4B;;MAQ5BC;IAKXxK,YACE0C,MACO+H,SAAiBH,kBACjBI;QADAzU,KAAMwU,SAANA;QACAxU,KAAMyU,SAANA;QAEPzU,KAAKuP,WAAW9C,KAAK8C;QACrBvP,KAAK+K,QAAQ0B,KAAK1B;QAClB/K,KAAK8K,WAAW2B,KAAK3B;AACtB;IAMD4J;QACE,OAAO,EAAC1U,KAAKwU,QAAQxU,KAAKuP,UAAUvP,KAAK8K,UAAU9K,KAAK+K,OAAO/K,KAAKyU,SACjEnM,OAAO6L,SACPxD,KAAK;AACT;IAODxG,eAAe/J;QACb,OAAOoU,QAAQjF,UAAUzE,UAAUC,SAAS3K,IAAImQ,MAAM;QAEtD,OAAO,IAAIgE,SAAS;YAAEhF;YAAUxE;YAAOD;WAAY0J;AACpD;IAODrK,sBAAsBwK;QACpB,OAAM5J,OAAEA,OAAKD,UAAEA,UAAU0E,WAAWD,YAAaoF;QAEjD,OAAO,IAAIJ,SAAS;YAClBxJ;YACAD;YACAyE;;AAEH;;;MC1DUqF;IACJnU,IAAmBL,KAAauU;QACrCzQ,aAAaE,QAAQhE,KAAKyG,KAAKC,UAAU6N;AAC1C;IAEMpU,IAAmBH;QACxB,MAAMsS,OAAOzO,OAAOC,aAAaH,QAAQ3D;QAEzC,KAAKsS,MAAM;QAEX;YACE,MAAMmC,UAAUhO,KAAKK,MAAMwL;YAC3B,OAAOmC;AAIR,UAFC,OAAOpW;YACP;AACD;AACF;IAEMqW,OAAO1U;QACZ8D,aAAaC,WAAW/D;AACzB;IAEM2U;QACL,OAAOnW,OAAOqQ,KAAKhL,OAAOC,cAAcoE,QAAOlI,OAC7CA,IAAImR,WAAW8C;AAElB;;;MC3BUW;IAAbjL;QACS/J,KAAAiV,gBAAwB;YAC7B,IAAIC,QAAiC,CAAA;YAErC,OAAO;gBACLzU,IAAmBL,KAAauU;oBAC9BO,MAAM9U,OAAOuU;AACd;gBAEDpU,IAAmBH;oBACjB,MAAM+U,aAAaD,MAAM9U;oBAEzB,KAAK+U,YAAY;wBACf;AACD;oBAED,OAAOA;AACR;gBAEDL,OAAO1U;2BACE8U,MAAM9U;AACd;gBAED2U;oBACE,OAAOnW,OAAOqQ,KAAKiG;AACpB;;AAEJ,SA1B8B;AA2BhC;;;AChBD,MAAME,oCAAoC;;MAE7BC;IAGXtL,YACUmL,OACAI,aACRC;QAFQvV,KAAKkV,QAALA;QACAlV,KAAWsV,cAAXA;QAGRtV,KAAKuV,cAAcA,eAAe1L;AACnC;IAED6F,iBACEH,UACAiG,SACAC;;QAEA,MAAMC,WAAW1V,KAAK2V,mBAAmBpG;cACnCvP,KAAKkV,MAAMzU,IAAIiV,UAAU;YAC7BE,UAAUJ;YACVC;;gBAEI3R,KAAA9D,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAqD,IAAIuO;AAC7B;IAEDhG,iBAAiBgG;QACf,MAAMf,cAAc3U,KAAKkV,MAAM3U,IAC7BP,KAAK2V,mBAAmBD,SAASnG;QAGnC,KAAKoF,SAASe,SAAS3K,SAAS2K,SAAS5K,UAAU;YACjD,MAAM+K,qBAAqB7V,KAAKO,IAAImV;YAEpC,KAAKG,cAAc;gBACjB;AACD;YAED,KAAKA,aAAaD,aAAaC,aAAaJ,cAAc;gBACxD;AACD;YAED,OAAO;gBACLG,UAAUC,aAAaD;gBACvBH,cAAcI,aAAaJ;;AAE9B;QAED,KAAKd,OAAO;YACV;AACD;QAED,OAAO;YAAEiB,UAAUjB,MAAMiB;YAAUH,cAAcd,MAAMc;;AACxD;IAED/F,UACEgG,UACAI,0BAA0BV;;QAE1B,IAAIW,qBAAqB/V,KAAKkV,MAAM3U,IAClCmV,SAAShB;QAGX,KAAKqB,cAAc;YACjB,MAAM9G,aAAajP,KAAKgW;YAExB,KAAK/G,MAAM;YAEX,MAAMgH,aAAajW,KAAKkW,sBAAsBR,UAAUzG;YAExD,IAAIgH,YAAY;gBACdF,qBAAqB/V,KAAKkV,MAAM3U,IAAuB0V;AACxD;AACF;QAGD,KAAKF,cAAc;YACjB;AACD;QAED,MAAM1Q,YAAYrF,KAAKuV;QACvB,MAAMY,aAAanR,KAAKC,MAAMI,MAAM;QAEpC,IAAI0Q,aAAaK,YAAYN,0BAA0BK,YAAY;YACjE,IAAIJ,aAAavT,KAAK6T,eAAe;gBACnCN,aAAavT,OAAO;oBAClB6T,eAAeN,aAAavT,KAAK6T;;sBAG7BrW,KAAKkV,MAAMzU,IAAIiV,SAAShB,SAASqB;gBACvC,OAAOA,aAAavT;AACrB;kBAEKxC,KAAKkV,MAAMJ,OAAOY,SAAShB;oBAC3B5Q,KAAA9D,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAgR,OAAOY,SAAShB;YAExC;AACD;QAED,OAAOqB,aAAavT;AACrB;IAEDkN,UAAUiF;;QACR,MAAMe,WAAW,IAAInB,SAAS;YAC5BhF,UAAUoF,MAAMnF;YAChBzE,OAAO4J,MAAM5J;YACbD,UAAU6J,MAAM7J;;QAGlB,MAAMiL,qBAAqB/V,KAAKsW,eAAe3B;cAEzC3U,KAAKkV,MAAMzU,IAAIiV,SAAShB,SAASqB;gBACjCjS,KAAA9D,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAqD,IAAIuO,SAAShB;AACtC;IAEDhF,YAAYH;;QACV,MAAMN,aAAajP,KAAKgW;QAGxB,KAAK/G,MAAM;cAELA,KACH3G,QAAOlI,OAAQmP,WAAWnP,IAAI2I,SAASwG,YAAY,OACnDJ,QAAOO,OAAO6G,MAAMnW;kBACbmW;kBACAvW,KAAKkV,MAAMJ,OAAO1U;AAAI,YAC3BU,QAAQC;qBAEPf,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAE;AACzB;IAEO0L,qBAAqBiF;QAC3B,MAAMtP,YAAYrF,KAAKuV;QACvB,MAAMiB,gBAAgBxR,KAAKC,MAAMI,MAAM,OAAQsP,MAAM8B;QAErD,OAAO;YACLjU,MAAMmS;YACNyB,WAAWI;;AAEd;IAEO9G;;QACN,IAAI1P,KAAKsV,aAAa;YACpB,QAAOxR,WAAO9D,KAAKsV,YAAY/U,WAAQ,QAAAuD,YAAA,SAAA,IAAAA,GAAAmL;AACxC,eAAM,IAAIjP,KAAKkV,MAAMH,SAAS;YAC7B,OAAO/U,KAAKkV,MAAMH;AACnB;AACF;IAOOY,mBAAmBpG;QACzB,OAAO,IAAIgF,SACT;YAAEhF;WACF8E,kBACAC,2BACAI;AACH;IAcOwB,sBAAsBQ,YAAsB3B;QAClD,OAAOA,QAAQzM,QAAOlI;;YACpB,MAAMsV,WAAWnB,SAASoC,QAAQvW;YAClC,MAAMwW,WAAW,IAAIlR,IAAIgQ,SAAS3K,SAAS2K,SAAS3K,MAAMwF,MAAM;YAChE,MAAMsG,kBAAgB/S,KAAA4S,WAAW3L,WAAO,QAAAjH,YAAA,SAAA,IAAAA,GAAAyM,MAAM,SAAQ;YAEtD,MAAMuG,eACJpB,SAAS3K,SACT8L,cAAc1H,QACZ,CAACC,KAAK2H,YAAY3H,OAAOwH,SAAShW,IAAImW,WACtC;YAGJ,OACErB,SAASlB,WAAWH,oBACpBqB,SAASnG,aAAamH,WAAWnH,YACjCmG,SAAS5K,aAAa4L,WAAW5L,YACjCgM;AACA,YACD;AACJ;;;AC9MH,MAAME,iCAAiC;;MAa1BC;IAGXlN,YACUmN,SACA3H,UACA4H;QAFAnX,KAAOkX,UAAPA;QACAlX,KAAQuP,WAARA;QACAvP,KAAYmX,eAAZA;QAERnX,KAAKqH,aAAa,GAAG2P,kCAAkChX,KAAKuP;AAC7D;IAEM6H,OAAOC;QACZrX,KAAKkX,QAAQI,KAAKtX,KAAKqH,YAAYgQ,aAAa;YAC9CE,iBAAiB;YACjBJ,cAAcnX,KAAKmX;;AAEtB;IAEM5W;QACL,OAAOP,KAAKkX,QAAQ3W,IAAIP,KAAKqH;AAC9B;IAEMyN;QACL9U,KAAKkX,QAAQpC,OAAO9U,KAAKqH,YAAY;YACnC8P,cAAcnX,KAAKmX;;AAEtB;;;ACtCH,MAAMK,WAAYlU,YAAkBA,MAAM;;AAE1C,MAAMmU,iBAAiB,EACrB,OACA,OACA,OACA,OACA,OACA,OACA,OACA,SACA,aACA,WACA,UACA,OACA,OACA,WACA,OACA,gBACA,YACA,cACA,gBACA,kBACA,QACA,QACA,OACA,UACA,OACA,OACA,OACA,OACA,OACA;;AAGK,MAAMC,SAAUC;IACrB,MAAMC,QAAQD,MAAMpH,MAAM;IAC1B,OAAOsH,QAAQhD,SAASiD,aAAaF;IAErC,IAAIA,MAAMzY,WAAW,MAAM0Y,WAAWhD,YAAYiD,WAAW;QAC3D,MAAM,IAAIrY,MAAM;AACjB;IACD,MAAMsY,cAAclR,KAAKK,MAAM0J,aAAaiE;IAC5C,MAAMmD,SAAkB;QAAEC,OAAON;;IACjC,MAAMO,OAAY,CAAA;IAClBtZ,OAAOqQ,KAAK8I,aAAatP,SAAQyG;QAC/B8I,OAAO9I,KAAK6I,YAAY7I;QACxB,KAAKuI,eAAe1O,SAASmG,IAAI;YAC/BgJ,KAAKhJ,KAAK6I,YAAY7I;AACvB;AAAA;IAEH,OAAO;QACLiJ,SAAS;YAAEN;YAAQhD;YAASiD;;QAC5BD,QAAQhR,KAAKK,MAAM0J,aAAaiH;QAChCG;QACAE;;AACD;;AAGI,MAAME,SAAU7E;IACrB,KAAKA,QAAQqC,UAAU;QACrB,MAAM,IAAInW,MAAM;AACjB;IAED,MAAM4Y,UAAUX,OAAOnE,QAAQqC;IAE/B,KAAKyC,QAAQL,OAAOM,KAAK;QACvB,MAAM,IAAI7Y,MACR;AAEH;IAED,IAAI4Y,QAAQL,OAAOM,QAAQ/E,QAAQ+E,KAAK;QACtC,MAAM,IAAI7Y,MACR,0DAA0D8T,QAAQ+E,gBAAgBD,QAAQL,OAAOM;AAEpG;IAED,KAAKD,QAAQH,KAAKK,KAAK;QACrB,MAAM,IAAI9Y,MACR;AAEH;IAED,IAAI4Y,QAAQR,OAAOW,QAAQ,SAAS;QAClC,MAAM,IAAI/Y,MACR,2BAA2B4Y,QAAQR,OAAOW;AAE7C;IAED,KACGH,QAAQL,OAAOS,gBAEPJ,QAAQL,OAAOS,QAAQ,YAC9BhK,MAAMiK,QAAQL,QAAQL,OAAOS,OAE/B;QACA,MAAM,IAAIhZ,MACR;AAEH;IACD,IAAIgP,MAAMiK,QAAQL,QAAQL,OAAOS,MAAM;QACrC,KAAKJ,QAAQL,OAAOS,IAAI1P,SAASwK,QAAQkF,MAAM;YAC7C,MAAM,IAAIhZ,MACR,4DACE8T,QAAQkF,4BACeJ,QAAQL,OAAOS,IAAI9H,KAAK;AAEpD;QACD,IAAI0H,QAAQL,OAAOS,IAAItZ,SAAS,GAAG;YACjC,KAAKkZ,QAAQL,OAAOW,KAAK;gBACvB,MAAM,IAAIlZ,MACR;AAEH;YACD,IAAI4Y,QAAQL,OAAOW,QAAQpF,QAAQkF,KAAK;gBACtC,MAAM,IAAIhZ,MACR,oEAAoE8T,QAAQkF,gBAAgBJ,QAAQL,OAAOW;AAE9G;AACF;AACF,WAAM,IAAIN,QAAQL,OAAOS,QAAQlF,QAAQkF,KAAK;QAC7C,MAAM,IAAIhZ,MACR,4DAA4D8T,QAAQkF,mBAAmBJ,QAAQL,OAAOS;AAEzG;IACD,IAAIlF,QAAQqF,OAAO;QACjB,KAAKP,QAAQL,OAAOY,OAAO;YACzB,MAAM,IAAInZ,MACR;AAEH;QACD,IAAI4Y,QAAQL,OAAOY,UAAUrF,QAAQqF,OAAO;YAC1C,MAAM,IAAInZ,MACR,2DAA2D8T,QAAQqF,kBAAkBP,QAAQL,OAAOY;AAEvG;AACF;IAED,IAAIrF,QAAQsF,YAAYrB,SAASa,QAAQL,OAAOc,YAAY;QAC1D,MAAM,IAAIrZ,MACR;AAEH;IAGD,IAAI4Y,QAAQL,OAAOe,OAAO,SAASvB,SAASa,QAAQL,OAAOe,MAAM;QAC/D,MAAM,IAAItZ,MACR;AAEH;IACD,KAAK+X,SAASa,QAAQL,OAAO3R,MAAM;QACjC,MAAM,IAAI5G,MACR;AAEH;IAED,MAAMuZ,SAASzF,QAAQyF,UAAU;IACjC,MAAM3T,MAAM,IAAID,KAAKmO,QAAQlO,OAAOD,KAAKC;IACzC,MAAM4T,UAAU,IAAI7T,KAAK;IAEzB6T,QAAQC,cAAcb,QAAQL,OAAOe,MAAMC;IAE3C,IAAI3T,MAAM4T,SAAS;QACjB,MAAM,IAAIxZ,MACR,oEAAoE4F,kCAAkC4T;AAEzG;IAED,IAAIZ,QAAQL,OAAOmB,OAAO,QAAQ3B,SAASa,QAAQL,OAAOmB,MAAM;QAC9D,MAAMC,UAAU,IAAIhU,KAAK;QACzBgU,QAAQF,cAAcb,QAAQL,OAAOmB,MAAMH;QAC3C,IAAI3T,MAAM+T,SAAS;YACjB,MAAM,IAAI3Z,MACR,+GAA+G4F,kBAAkB+T;AAEpI;AACF;IAED,IAAIf,QAAQL,OAAOc,aAAa,QAAQtB,SAASa,QAAQL,OAAOc,YAAY;QAC1E,MAAMO,eAAe,IAAIjU,KAAK;QAC9BiU,aAAaH,cACXzH,SAAS4G,QAAQL,OAAOc,aAAcvF,QAAQsF,UAAqBG;QAGrE,IAAI3T,MAAMgU,cAAc;YACtB,MAAM,IAAI5Z,MACR,uJAAuJ4F,8BAA8BgU;AAExL;AACF;IAED,IAAI9F,QAAQ+F,cAAc;QACxB,MAAMC,MAAMhG,QAAQ+F,aAAalF;QACjC,IAAImF,IAAIhI,WAAW,SAAS;YAC1B,MAAMiI,QAAQD;YACd,KAAKlB,QAAQL,OAAOyB,QAAQ;gBAC1B,MAAM,IAAIha,MACR;AAEH,mBAAM,IAAI+Z,UAAUnB,QAAQL,OAAOyB,QAAQ;gBAC1C,MAAM,IAAIha,MACR,sEAAsE+Z,kBAAkBnB,QAAQL,OAAOyB;AAE1G;AACF,eAAM;YACL,MAAMC,UAAUH,IAAII;YAEpB,KAAKtB,QAAQL,OAAO4B,UAAU;gBAC5B,MAAM,IAAIna,MACR;AAEH,mBAAM,IAAIia,YAAYrB,QAAQL,OAAO4B,UAAU;gBAC9C,MAAM,IAAIna,MACR,0EAA0Eia,oBAAoBrB,QAAQL,OAAO4B;AAEhH;AACF;AACF;IAED,OAAOvB;AAAO;;;IC9NhB,IAAIwB,WAAY7Z,kBAAQA,eAAK6Z,YAAa;QACtCA,WAAWjb,OAAOyQ,UAAU,SAAS3Q;YACjC,KAAK,IAAIF,GAAGU,IAAI,GAAGoE,IAAIwW,UAAU3a,QAAQD,IAAIoE,GAAGpE,KAAK;gBACjDV,IAAIsb,UAAU5a;gBACd,KAAK,IAAIP,KAAKH,GAAG,IAAII,OAAOC,UAAUC,eAAeC,KAAKP,GAAGG,IACzDD,EAAEC,KAAKH,EAAEG;AAChB;YACD,OAAOD;AACf;QACI,OAAOmb,SAASvX,MAAMtC,MAAM8Z;AAChC;IACAla,QAAkBma,aAAG;IACrB,SAASC,mBAAmBta,MAAMG;QAC9B,KAAKA,OAAO;YACR,OAAO;AACV;QACD,IAAIoa,cAAc,OAAOva;QACzB,IAAIG,UAAU,MAAM;YAChB,OAAOoa;AACV;QACD,OAAOA,cAAc,MAAMpa;AAC/B;IACA,SAASqa,oBAAoBC;QACzB,WAAWA,WAAWC,YAAY,UAAU;YACxC,IAAIA,UAAU,IAAIhV;YAClBgV,QAAQC,gBAAgBD,QAAQE,oBAAoBH,WAAWC,UAAU;YACzED,WAAWC,UAAUA;AACxB;QACD,OAAOJ,mBAAmB,WAAWG,WAAWC,UAAUD,WAAWC,QAAQG,gBAAgB,MACvFP,mBAAmB,UAAUG,WAAWK,UACxCR,mBAAmB,QAAQG,WAAWM,QACtCT,mBAAmB,UAAUG,WAAWO,UACxCV,mBAAmB,YAAYG,WAAWQ;AACpD;IACA,SAAS9L,OAAOnP,MAAMG,OAAOsa;QACzB,OAAOS,mBAAmBlb,MACrBwQ,QAAQ,4BAA4BG,oBACpCH,QAAQ,OAAO,OAAOA,QAAQ,OAAO,SACpC,MAAM0K,mBAAmB/a,OAE1BqQ,QAAQ,6DAA6DG,sBACpE6J,oBAAoBC;AAC9B;IACAva,QAAciP,SAAGA;IACjB,SAAS3H,MAAM2T;QACX,IAAI1Y,SAAS,CAAA;QACb,IAAI2Y,UAAUD,eAAeA,aAAatK,MAAM,QAAQ;QACxD,IAAIwK,UAAU;QACd,KAAK,IAAI7b,IAAI,GAAGA,IAAI4b,QAAQ3b,QAAQD,KAAK;YACrC,IAAI0Y,QAAQkD,QAAQ5b,GAAGqR,MAAM;YAC7B,IAAIyK,SAASpD,MAAMpP,MAAM,GAAGmI,KAAK;YACjC,IAAIqK,OAAOC,OAAO,OAAO,KAAK;gBAC1BD,SAASA,OAAOxS,MAAM,IAAI;AAC7B;YACD;gBACI,IAAI0S,SAAStD,MAAM,GAAG1H,QAAQ6K,SAAS1K;gBACvClO,OAAO+Y,UAAUF,OAAO9K,QAAQ6K,SAAS1K;AAI5C,cAFD,OAAO5R,IAEN;AACJ;QACD,OAAO0D;AACX;IACAvC,QAAasH,QAAGA;IAChB,SAASiU;QACL,OAAOjU,MAAM4E,SAASkP;AAC1B;IACApb,QAAcub,SAAGA;IACjB,SAAS5a,IAAIb;QACT,OAAOyb,SAASzb;AACpB;IACAE,QAAWW,MAAGA;IACd,SAASE,IAAIf,MAAMG,OAAOsa;QACtBrO,SAASkP,SAASnM,OAAOnP,MAAMG,OAAOga,SAAS;YAAEY,MAAM;WAAON;AAClE;IACAva,QAAWa,MAAGA;IACd,SAASqU,OAAOpV,MAAMya;QAClB1Z,IAAIf,MAAM,IAAIma,SAASA,SAAS,CAAA,GAAIM,aAAa;YAAEC,UAAU;;AACjE;IACAxa,QAAAkV,SAAiBA;;;;;;;;;;;;;;;;;AC9DV,MAAMsG,gBAAgB;IAC3B7a,IAAsBH;QACpB,MAAMP,QAAQwb,WAAYjb;QAE1B,WAAWP,UAAU,aAAa;YAChC;AACD;QAED,OAAUgH,KAAKK,MAAMrH;AACtB;IAEDyX,KAAKlX,KAAaP,OAAY0T;QAC5B,IAAI+H,mBAA6C,CAAA;QAEjD,IAAI,aAAarX,OAAOsX,SAASC,UAAU;YACzCF,mBAAmB;gBACjBZ,QAAQ;gBACRC,UAAU;;AAEb;QAED,IAAIpH,YAAA,QAAAA,8BAAAA,QAASgE,iBAAiB;YAC5B+D,iBAAiBlB,UAAU7G,QAAQgE;AACpC;QAED,IAAIhE,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;YACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;QAEDsE,WAAYrb,KAAKyG,KAAKC,UAAUjH,QAAQyb;AACzC;IAEDxG,OAAO1U,KAAamT;QAClB,IAAI+H,mBAA6C,CAAA;QAEjD,IAAI/H,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;YACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;QAEDuE,WAAetb,KAAKkb;AACrB;;;AAMH,MAAMK,gBAAgB;;AAMf,MAAMC,kCAAkC;IAC7Crb,IAAsBH;QACpB,MAAMP,QAAQub,cAAc7a,IAAOH;QAEnC,IAAIP,OAAO;YACT,OAAOA;AACR;QAED,OAAOub,cAAc7a,IAAO,GAAGob,gBAAgBvb;AAChD;IAEDkX,KAAKlX,KAAaP,OAAY0T;QAC5B,IAAI+H,mBAA6C,CAAA;QAEjD,IAAI,aAAarX,OAAOsX,SAASC,UAAU;YACzCF,mBAAmB;gBAAEZ,QAAQ;;AAC9B;QAED,IAAInH,YAAA,QAAAA,8BAAAA,QAASgE,iBAAiB;YAC5B+D,iBAAiBlB,UAAU7G,QAAQgE;AACpC;QAED,IAAIhE,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;YACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;QAEDsE,WACE,GAAGE,gBAAgBvb,OACnByG,KAAKC,UAAUjH,QACfyb;QAEFF,cAAc9D,KAAKlX,KAAKP,OAAO0T;AAChC;IAEDuB,OAAO1U,KAAamT;QAClB,IAAI+H,mBAA6C,CAAA;QAEjD,IAAI/H,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;YACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;QAEDuE,WAAetb,KAAKkb;QACpBF,cAActG,OAAO1U,KAAKmT;QAC1B6H,cAActG,OAAO,GAAG6G,gBAAgBvb,OAAOmT;AAChD;;;AAMI,MAAMsI,iBAAiB;IAC5Btb,IAAsBH;QAEpB,WAAW0b,mBAAmB,aAAa;YACzC;AACD;QAED,MAAMjc,QAAQic,eAAe/X,QAAQ3D;QAErC,IAAIP,SAAS,MAAM;YACjB;AACD;QAED,OAAUgH,KAAKK,MAAMrH;AACtB;IAEDyX,KAAKlX,KAAaP;QAChBic,eAAe1X,QAAQhE,KAAKyG,KAAKC,UAAUjH;AAC5C;IAEDiV,OAAO1U;QACL0b,eAAe3X,WAAW/D;AAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/IH,MAAM2b,mBAAiD,CAAA;;AAEhD,MAAMC,gBAAgB,CAC3BC,IACA7b;IAEA,IAAI8b,UAA6BH,iBAAiB3b;IAClD,KAAK8b,SAAS;QACZA,UAAUD,KAAKjJ,SAAQ;mBACd+I,iBAAiB3b;YACxB8b,UAAU;AAAI;QAEhBH,iBAAiB3b,OAAO8b;AACzB;IACD,OAAOA;AAAO;;AAGT,MAAMC,eAAezM,OAC1BuM,IACAG,qBAAqB;IAErB,KAAK,IAAIld,IAAI,GAAGA,IAAIkd,oBAAoBld,KAAK;QAC3C,UAAU+c,MAAM;YACd,OAAO;AACR;AACF;IAED,OAAO;AAAK;;MCpBDI;IAGXtS,YAAoBmL,OAAuB3F;QAAvBvP,KAAKkV,QAALA;QAAuBlV,KAAQuP,WAARA;QACzCvP,KAAKsc,cAActc,KAAKuc,sBAAsBvc,KAAKuP;AACpD;IAEDG,UAAUtP;;QACR,MAAM6O,OAAO,IAAIvJ,MACf5B,WAAO9D,KAAKkV,MAAM3U,IAAsBP,KAAKsc,kBAAa,QAAAxY,YAAA,SAAA,IAAAA,GAAEmL,SAAQ;QAGtEA,KAAK9H,IAAI/G;cAEHJ,KAAKkV,MAAMzU,IAAsBT,KAAKsc,aAAa;YACvDrN,MAAM,KAAIA;;AAEb;IAEDS,aAAatP;QACX,MAAMuU,cAAc3U,KAAKkV,MAAM3U,IAAsBP,KAAKsc;QAE1D,IAAI3H,OAAO;YACT,MAAM1F,OAAO,IAAIvJ,IAAIiP,MAAM1F;YAC3BA,KAAK/N,OAAOd;YAEZ,IAAI6O,KAAKuN,OAAO,GAAG;gBACjB,aAAaxc,KAAKkV,MAAMzU,IAAIT,KAAKsc,aAAa;oBAAErN,MAAM,KAAIA;;AAC3D;YAED,aAAajP,KAAKkV,MAAMJ,OAAO9U,KAAKsc;AACrC;AACF;IAED/b;QACE,OAAOP,KAAKkV,MAAM3U,IAAsBP,KAAKsc;AAC9C;IAEDtY;QACE,OAAOhE,KAAKkV,MAAMJ,OAAO9U,KAAKsc;AAC/B;IAEOC,sBAAsBhN;QAC5B,OAAO,GAAG8E,qBAAqB9E;AAChC;;;ACvCI,MAAMkN,8BAA8B;;AAKpC,MAAMC,kCAAmCnN,YAC9C,SAASA;;AAKJ,MAAMoN,mCAAmC;;AAKzC,MAAMC,iCAAkCrN,YAC7C,SAASA;;AAKX,MAAMsN,wBAAsD;IAC1DC,QAAQ,OAAM,IAAI9H,eAAgBC;IAClC8H,cAAc,MAAM,IAAInI;;;AAMnB,MAAMoI,eAAgBzB,YACpBsB,sBAAsBtB;;AAMxB,MAAM0B,qBAAqB,CAChCC,eAGAnS,OACAoS,qBACA9S,OACAuO,OACAwE,gBACAC,cACAC,kBAEA1e,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;IACEG,WAAW0N,cAAc3N;GACtB2N,cAAcC,sBACdA,sBACH;IAAApS,OAAOkJ,gBAAgBlJ,OAAOoS,oBAAoBpS;IAClDwS,eAAe;IACfD,eAAeA,iBAAiB;IAChCjT;IACAuO;IACAyE,cACEA,gBAAgBH,cAAcC,oBAAoBE;IACpDD;IACAI,uBAAuB;;;AASpB,MAAMC,6BAGXlK;IAEA,OAAMmK,SAAEA,SAAOC,YAAEA,cAAmCpK,SAApBqK,kBAAoBrf,OAAAgV,SAA9C,EAAA,WAAA;IAEN,MAAMpR,yCACDyb,kBAAe;QAClBF,SAASA,YAAY,SAASA,UAAUA,UAAUC;;IAGpD,OAAOxb;AAAW;;ACepB,MAAMtB,OAAO,IAAIgd;;MAKJC;IA2BX/T,YAAYwJ;QAZKvT,KAAA+d,aAAoB,IAAI/I,eAAgBC;QAIxCjV,KAAAge,iBAA8C;YAC7Db,qBAAqB;gBACnBpS,OAAOrB;;YAETuU,0BAA0B;YAC1B9K,aAAa;;QA27BPnT,KAAsBke,yBAAGxO;kBACzB7O,KAAKiF,YAAY2W;YAEvBxY,OAAO4D,oBAAoB,YAAY7H,KAAKke;AAAuB;QA17BnEle,KAAKuT,UACA3U,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAArP,KAAKge,iBACLzK,UACH;YAAA4J,qDACKnd,KAAKge,eAAeb,sBACpB5J,QAAQ4J;;eAIRlZ,WAAW,eAAegN;QAEjC,IAAIsC,QAAQ2B,SAAS3B,QAAQ4K,eAAe;YAC1CC,QAAQC,KACN;AAEH;QAED,IAAIF;QACJ,IAAIjJ;QAEJ,IAAI3B,QAAQ2B,OAAO;YACjBA,QAAQ3B,QAAQ2B;AACjB,eAAM;YACLiJ,gBAAgB5K,QAAQ4K,iBAAiB5U;YAEzC,KAAKyT,aAAamB,gBAAgB;gBAChC,MAAM,IAAI1e,MAAM,2BAA2B0e;AAC5C;YAEDjJ,QAAQ8H,aAAamB,cAAbnB;AACT;QAEDhd,KAAKse,gBAAgB/K,QAAQgL,uBACzBhL,QAAQgL,uBAAuB,MAC/BjV;QAEJtJ,KAAKwe,gBACHjL,QAAQkL,yBAAyB,QAC7BrD,gBACAQ;QAEN5b,KAAK0e,oBAAoBhC,gCACvB1c,KAAKuT,QAAQhE;QAGfvP,KAAK2e,4BAA4B/B,+BAC/B5c,KAAKuT,QAAQhE;QAGfvP,KAAK4e,yBACHrL,QAAQqL,0BAA0BjV;QAEpC,MAAMkV,qBAAqBtL,QAAQuL,4BAC/B9e,KAAKwe,gBACL3C;QAMJ7b,KAAK+K,QAAQkJ,gBACX,UACAjU,KAAKuT,QAAQ4J,oBAAoBpS,OACjC/K,KAAKuT,QAAQwL,mBAAmB,mBAAmB;QAGrD/e,KAAKgf,qBAAqB,IAAI/H,mBAC5B4H,oBACA7e,KAAKuT,QAAQhE,UACbvP,KAAKuT,QAAQ4D;QAGfnX,KAAKuV,cAAcvV,KAAKuT,QAAQgC,eAAe1L;QAE/C7J,KAAKif,eAAe,IAAI5J,aACtBH,QACCA,MAAMH,UACH,IAAIsH,iBAAiBnH,OAAOlV,KAAKuT,QAAQhE,YACzC/O,WACJR,KAAKuV;QAGPvV,KAAKmR,YAAYD,UAAUlR,KAAKuT,QAAQiH;QACxCxa,KAAKkf,cAAc7N,eAAerR,KAAKuT,QAAQjC,QAAQtR,KAAKmR;QAG5D,WACSlN,WAAW,eAClBA,OAAOkb,UACPnf,KAAKuT,QAAQwL,oBACbZ,kBAAkB5U,uBAClB;YACA,IAAIvJ,KAAKuT,QAAQ6L,WAAW;gBAC1Bpf,KAAKkT,SAAS,IAAIiM,OAAOnf,KAAKuT,QAAQ6L;AACvC,mBAAM;gBACLpf,KAAKkT,SAAS,IAAImM;AACnB;AACF;AACF;IAEOC,KAAK7E;QACX,MAAM7G,cAAcgH,mBAClB9L,KAAKjI,KAAKC,UAAU9G,KAAKuT,QAAQK,eAAehK;QAElD,OAAO,GAAG5J,KAAKmR,YAAYsJ,oBAAoB7G;AAChD;IAEO2L,cAAcC;QACpB,OAAOxf,KAAKsf,KAAK,cAAchQ,kBAAkBkQ;AAClD;IAEO9P,qBACNkG,UACAgD,OACAU;QAEA,MAAMjU,YAAYrF,KAAKuV;QAEvB,OAAOkK,OAAc;YACnBnH,KAAKtY,KAAKkf;YACVzG,KAAKzY,KAAKuT,QAAQhE;YAClBqG;YACAgD;YACAU;YACAN,QAAQhZ,KAAKuT,QAAQyF;YACrBH,SAASrH,YAAYxR,KAAKuT,QAAQ4J,oBAAoBtE;YACtDxT;;AAEH;IAEOqa,gBAAgBpG;QACtB,IAAIA,cAAc;YAChBtZ,KAAKwe,cAAclH,KAAKtX,KAAK0e,mBAAmBpF,cAAc;gBAC5D/B,iBAAiBvX,KAAK4e;gBACtBzH,cAAcnX,KAAKuT,QAAQ4D;;AAE9B,eAAM;YACLnX,KAAKwe,cAAc1J,OAAO9U,KAAK0e,mBAAmB;gBAChDvH,cAAcnX,KAAKuT,QAAQ4D;;AAE9B;AACF;IAEOzH,2BACNyN,qBACAqC,kBACAG;QAUA,MAAMtV,QAAQwE,OAAOP;QACrB,MAAMsK,QAAQ/J,OAAOP;QACrB,MAAMsR,gBAAgBtR;QACtB,MAAMuR,6BAA6BpQ,OAAOmQ;QAC1C,MAAMxC,iBAAiBvM,yBAAyBgP;QAEhD,MAAM7Q,SAASiO,mBACbjd,KAAKuT,SACLvT,KAAK+K,OACLoS,qBACA9S,OACAuO,OACAwE,gBACAD,oBAAoBE,gBAClBrd,KAAKuT,QAAQ4J,oBAAoBE,gBACjCsC,qBACFH,qBAAA,QAAAA,uCAAAA,iBAAkBlC;QAGpB,MAAMpQ,MAAMlN,KAAKuf,cAAcvQ;QAE/B,OAAO;YACL4J;YACAgH;YACA7U,OAAOiE,OAAOjE;YACdD,UAAUkE,OAAOlE,YAAY;YAC7BuS,cAAcrO,OAAOqO;YACrBhT;YACA6C;;AAEH;IAyBMwC,qBACL6D,SACA1F;;QAEA0F,UAAUA,WAAW;QACrB1F,SAASA,UAAU;QAEnB,KAAKA,OAAOpD,OAAO;YACjBoD,OAAOpD,QAAQwC,UAAU;YAEzB,KAAKY,OAAOpD,OAAO;gBACjB,MAAM,IAAIhL,MACR;AAEH;AACF;QAED,MAAMuP,eAAehP,KAAK8f,qBACxBvM,QAAQ4J,uBAAuB,CAAA,GAC/B;YAAEG,eAAe;WACjBrZ,OAAOsX,SAAS/O;QAGlBqB,OAAOpD,MAAM8Q,SAASwE,OAAO/Q,OAAO9B;QAEpC,MAAM8S,mBAAmBpS,SAAQhP,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAC5BxB,SAAM;YACT1E,kBACE0E,OAAO1E,oBACPnJ,KAAKuT,QAAQ0M,6BACbhX;;QAGJ,IAAI+F,OAAO3E,UAAU2V,WAAW3V,OAAO;YACrC,MAAM,IAAIP,aAAa,kBAAkB;AAC1C;QAED,MAAMwP,iBACJxV,KAAAyP,QAAQ4J,6DAAqB7D,iBAC7BtZ,KAAKuT,QAAQ4J,oBAAoB7D;cAE7BtZ,KAAKkgB,cACT;YACEpV,UAAUkE,OAAOlE;YACjBC,OAAOiE,OAAOjE;YACd6U,eAAe5Q,OAAO4Q;YACtBO,YAAY;YACZ5U,MAAMyU,WAAWzU;YACjB8R,cAAcrO,OAAOqO;WAEvB;YACE+C,SAASpR,OAAO4J;YAChBU;;AAGL;IAYM5J;;QACL,MAAMwF,cAAclV,KAAKqgB;QAEzB,QAAOvc,KAAAoR,UAAK,QAALA,eAAK,SAAA,IAALA,MAAOO,kBAAc,QAAA3R,YAAA,SAAA,IAAAA,GAAAoU;AAC7B;IASMxI;;QACL,MAAMwF,cAAclV,KAAKqgB;QAEzB,QAAOvc,KAAAoR,UAAK,QAALA,eAAK,SAAA,IAALA,MAAOO,kBAAc,QAAA3R,YAAA,SAAA,IAAAA,GAAAkU;AAC7B;IAaMtI,wBACL6D,UAA2C;;QAE3C,MAAM+M,KACJ7C,2BAA2BlK,WADvBmK,SAAEA,SAAO6C,UAAEA,UAAQjW,UAAEA,YACUgW,IADGE,aAAlCjiB,OAAA+hB,IAAA,EAAA,WAAA,YAAA;QAGN,MAAMhH,iBACJxV,KAAA0c,WAAWrD,6DAAqB7D,iBAChCtZ,KAAKuT,QAAQ4J,oBAAoB7D;QAEnC,MAAMmH,WAAgCzgB,KAAK8f,qBACzCU,WAAWrD,uBAAuB,MAD9BjQ,KAAEA,OAAGuT,IAAKpJ,cAAW9Y,OAAAkiB,IAArB,EAAuB;QAI7BzgB,KAAKgf,mBAAmB5H,OAAMxY,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACzBgI,cACH;YAAA/M;YACIgP,gBAAgB;YAAEA;;QAGxB,MAAMoH,kBAAkBH,WAAW,GAAGrT,OAAOqT,aAAarT;QAE1D,IAAIwQ,SAAS;kBACLA,QAAQgD;AACf,eAAM;YACLzc,OAAOsX,SAASlM,OAAOqR;AACxB;AACF;IAQMhR,6BACLxC,MAAcjJ,OAAOsX,SAASwE;QAE9B,MAAMY,uBAAuBzT,IAAIqD,MAAM,KAAK/H,MAAM;QAElD,IAAImY,qBAAqBxhB,WAAW,GAAG;YACrC,MAAM,IAAIM,MAAM;AACjB;QAED,OAAM4K,OAAEA,OAAKkB,MAAEA,MAAIjM,OAAEA,OAAK0K,mBAAEA,qBAAsBkB,0BAChDyV,qBAAqBhQ,KAAK;QAG5B,MAAM0G,cAAcrX,KAAKgf,mBAAmBze;QAE5C,KAAK8W,aAAa;YAChB,MAAM,IAAIvN,aAAa,uBAAuB;AAC/C;QAED9J,KAAKgf,mBAAmBlK;QAExB,IAAIxV,OAAO;YACT,MAAM,IAAI8K,oBACR9K,OACA0K,qBAAqB1K,OACrB+K,OACAgN,YAAY/M;AAEf;QAGD,KACG+M,YAAYuI,iBACZvI,YAAYhN,SAASgN,YAAYhN,UAAUA,OAC5C;YACA,MAAM,IAAIP,aAAa,kBAAkB;AAC1C;QAED,MAAMwP,eAAejC,YAAYiC;QACjC,MAAM8G,UAAU/I,YAAYuB;QAC5B,MAAMyE,eAAehG,YAAYgG;cAE3Brd,KAAKkgB,cAAathB,OAAAyQ,OAAA;YAEpBvE,UAAUuM,YAAYvM;YACtBC,OAAOsM,YAAYtM;YACnB6U,eAAevI,YAAYuI;YAC3BO,YAAY;YACZ5U,MAAMA;WACF8R,eAAe;YAAEA;YAAiB,CAAE,IAE1C;YAAE+C;YAAS9G;;QAGb,OAAO;YACLhP,UAAU+M,YAAY/M;;AAEzB;IA2BMoF,mBAAmB6D;QACxB,KAAKvT,KAAKwe,cAAcje,IAAIP,KAAK2e,4BAA4B;YAC3D,KAAK3e,KAAKwe,cAAcje,IAAIoc,mCAAmC;gBAC7D;AACD,mBAAM;gBAEL3c,KAAKwe,cAAclH,KAAKtX,KAAK2e,2BAA2B,MAAM;oBAC5DpH,iBAAiBvX,KAAK4e;oBACtBzH,cAAcnX,KAAKuT,QAAQ4D;;gBAG7BnX,KAAKwe,cAAc1J,OAAO6H;AAC3B;AACF;QAED;kBACQ3c,KAAK4gB,iBAAiBrN;AAChB,UAAZ,OAAO9Q,IAAK;AACf;IAwDMiN,uBACL6D,UAAmC;;QAEnC,MAAMsN,eAGJjiB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;YAAAyR,WAAW;WACRvN,UAAO;YACV4J,qBAAmBve,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,IACdrP,KAAKuT,QAAQ4J,sBACb5J,QAAQ4J,sBAAmB;gBAC9BpS,OAAOkJ,gBAAgBjU,KAAK+K,QAAOjH,KAAAyP,QAAQ4J,yBAAmB,QAAArZ,YAAA,SAAA,IAAAA,GAAEiH;;;QAIpE,MAAM5I,eAAe6Z,eACnB,MAAMhc,KAAK+gB,kBAAkBF,gBAC7B,GAAG7gB,KAAKuT,QAAQhE,aAAasR,aAAa1D,oBAAoBrS,aAAa+V,aAAa1D,oBAAoBpS;QAG9G,OAAOwI,QAAQyN,mBAAmB7e,SAASA,WAAA,QAAAA,6BAAAA,OAAQ8e;AACpD;IAEOvR,wBACN6D;QAIA,OAAMuN,WAAEA,aAAkCvN,SAApB2N,kBAAe3iB,OAAKgV,SAApC,EAAiC;QAIvC,IAAIuN,cAAc,OAAO;YACvB,MAAMnM,cAAc3U,KAAKmhB,mBAAmB;gBAC1CpW,OAAOmW,gBAAgB/D,oBAAoBpS;gBAC3CD,UAAUoW,gBAAgB/D,oBAAoBrS,YAAY;gBAC1DyE,UAAUvP,KAAKuT,QAAQhE;;YAGzB,IAAIoF,OAAO;gBACT,OAAOA;AACR;AACF;QAED,IAAImM,cAAc,cAAc;YAC9B;AACD;QAED,UACQ3E,cACJ,MAAMtb,KAAK+E,YAAY6W,6BAA6B,OACpD,KAEF;YACA;gBACExY,OAAOiE,iBAAiB,YAAYlI,KAAKke;gBAIzC,IAAI4C,cAAc,OAAO;oBACvB,MAAMnM,cAAc3U,KAAKmhB,mBAAmB;wBAC1CpW,OAAOmW,gBAAgB/D,oBAAoBpS;wBAC3CD,UAAUoW,gBAAgB/D,oBAAoBrS,YAAY;wBAC1DyE,UAAUvP,KAAKuT,QAAQhE;;oBAGzB,IAAIoF,OAAO;wBACT,OAAOA;AACR;AACF;gBAED,MAAMyM,aAAaphB,KAAKuT,QAAQwL,yBACtB/e,KAAKqhB,2BAA2BH,yBAChClhB,KAAKshB,oBAAoBJ;gBAEnC,OAAMtL,UAAEA,UAAQqL,cAAEA,cAAYM,iBAAEA,iBAAe9K,YAAEA,cAC/C2K;gBAEF,OAAAxiB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;oBACEuG;oBACAqL;mBACIM,kBAAkB;oBAAExW,OAAOwW;oBAAoB,OAAK;oBACxD9K;;AAKH,cAHS;sBACF5V,KAAKiF,YAAY2W;gBACvBxY,OAAO4D,oBAAoB,YAAY7H,KAAKke;AAC7C;AACF,eAAM;YACL,MAAM,IAAI3T;AACX;AACF;IAcMmF,wBACL6D,UAAoC,IACpC1F,SAA6B,CAAA;;QAE7B,MAAMgT,eAAYjiB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACbkE,UAAO;YACV4J,qBACKve,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAArP,KAAKuT,QAAQ4J,sBACb5J,QAAQ4J,sBAAmB;gBAC9BpS,OAAOkJ,gBAAgBjU,KAAK+K,QAAOjH,KAAAyP,QAAQ4J,yBAAmB,QAAArZ,YAAA,SAAA,IAAAA,GAAEiH;;;QAIpE8C,SACKjP,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,IAAAnG,+BACA2E;cAGC7N,KAAKwhB,eAAeX,cAAchT;QAExC,MAAMqH,cAAclV,KAAKif,aAAa1e,IACpC,IAAIgU,SAAS;YACXxJ,OAAO8V,aAAa1D,oBAAoBpS;YACxCD,UAAU+V,aAAa1D,oBAAoBrS,YAAY;YACvDyE,UAAUvP,KAAKuT,QAAQhE;;QAI3B,OAAO2F,MAAO+L;AACf;IAWMvR;QACL,MAAMwI,aAAalY,KAAKyhB;QACxB,SAASvJ;AACV;IAUOwJ,gBAAgBnO;QACtB,IAAIA,QAAQhE,aAAa,MAAM;YAC7BgE,QAAQhE,WAAWgE,QAAQhE,YAAYvP,KAAKuT,QAAQhE;AACrD,eAAM;mBACEgE,QAAQhE;AAChB;QAED,MAAMzL,KAAkCyP,QAAQoO,gBAAgB,CAAA,IAA1DC,WAAEA,iBAAcC,gBAAhBtjB,OAAAuF,IAAA,EAAA;QACN,MAAMge,iBAAiBF,YAAY,eAAe;QAClD,MAAM1U,MAAMlN,KAAKsf,KACf,cAAchQ,kBAAiB1Q,OAAAyQ,OAAA;YAC7BE,UAAUgE,QAAQhE;WACfsS;QAIP,OAAO3U,MAAM4U;AACd;IAeMpS,aAAa6D,UAAyB;QAC3C,MAAMzP,KAAgC2Z,2BAA2BlK,WAA3DmK,SAAEA,WAAO5Z,IAAK+d,gBAAdtjB,OAAAuF,IAAA,EAAA;QAEN,IAAIyP,QAAQhE,aAAa,MAAM;kBACvBvP,KAAKif,aAAajb;AACzB,eAAM;kBACChE,KAAKif,aAAajb,MAAMuP,QAAQhE,YAAYvP,KAAKuT,QAAQhE;AAChE;QAEDvP,KAAKwe,cAAc1J,OAAO9U,KAAK0e,mBAAmB;YAChDvH,cAAcnX,KAAKuT,QAAQ4D;;QAE7BnX,KAAKwe,cAAc1J,OAAO9U,KAAK2e,2BAA2B;YACxDxH,cAAcnX,KAAKuT,QAAQ4D;;QAE7BnX,KAAK+d,UAAUjJ,OAAOR;QAEtB,MAAMpH,MAAMlN,KAAK0hB,gBAAgBG;QAEjC,IAAInE,SAAS;kBACLA,QAAQxQ;AACf,eAAM,IAAIwQ,YAAY,OAAO;YAC5BzZ,OAAOsX,SAASlM,OAAOnC;AACxB;AACF;IAEOwC,0BACN6D;QAIA,MAAMvE,SACDpQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAAkE,QAAQ4J;YACX4E,QAAQ;;QAGV,MAAMC,UAAUhiB,KAAKwe,cAAcje,IAAYP,KAAK0e;QAEpD,IAAIsD,YAAYhT,OAAOsK,cAAc;YACnCtK,OAAOsK,eAAe0I;AACvB;QAED,OAAM9U,KACJA,KACA7C,OAAO4X,SACPrJ,OAAOwH,SAAOR,eACdA,eAAavC,cACbA,cAAYtS,OACZA,OAAKD,UACLA,kBACQ9K,KAAK8f,qBACb9Q,QACA;YAAEsO,eAAe;WACjBrZ,OAAOsX,SAAS/O;QAGlB;YAIE,IAAKvI,OAAeie,qBAAqB;gBACvC,MAAM,IAAIpY,aACR,kBACA;AAEH;YAED,MAAMqY,mBACJ5O,QAAQpK,oBAAoBnJ,KAAKuT,QAAQ0M;YAE3C,MAAMD,mBAAmBxU,UAAU0B,KAAKlN,KAAKmR,WAAWgR;YAExD,IAAIF,YAAYjC,WAAW3V,OAAO;gBAChC,MAAM,IAAIP,aAAa,kBAAkB;AAC1C;YAED,MAAMsY,oBAAoBpiB,KAAKkgB,cAExBthB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAAkE,QAAQ4J;gBACXyC;gBACArU,MAAMyU,WAAWzU;gBACjB4U,YAAY;gBACZ9C;gBACAjX,SAASmN,QAAQ4J,oBAAoB/W,WAAWpG,KAAKse;gBAEvD;gBACE8B;gBACA9G,cAActK,OAAOsK;;YAIzB,OAAA1a,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACK+S,cAAW;gBACdrX,OAAOA;gBACPwW,iBAAiBa,YAAYrX;gBAC7BD,UAAUA;;AASb,UAPC,OAAOrM;YACP,IAAIA,EAAEa,UAAU,kBAAkB;gBAChCU,KAAKqiB,OAAO;oBACV3E,SAAS;;AAEZ;YACD,MAAMjf;AACP;AACF;IAEOiR,iCACN6D;QAIA,MAAM2B,cAAclV,KAAKif,aAAa1e,IACpC,IAAIgU,SAAS;YACXxJ,OAAOwI,QAAQ4J,oBAAoBpS;YACnCD,UAAUyI,QAAQ4J,oBAAoBrS,YAAY;YAClDyE,UAAUvP,KAAKuT,QAAQhE;;QAQ3B,MAAM2F,UAAUA,MAAMmB,mBAAmBrW,KAAKkT,QAAQ;YACpD,IAAIlT,KAAKuT,QAAQ0K,0BAA0B;gBACzC,aAAaje,KAAKshB,oBAAoB/N;AACvC;YAED,MAAM,IAAI1I,yBACR0I,QAAQ4J,oBAAoBrS,YAAY,WACxCyI,QAAQ4J,oBAAoBpS;AAE/B;QAED,MAAMsS,eACJ9J,QAAQ4J,oBAAoBE,gBAC5Brd,KAAKuT,QAAQ4J,oBAAoBE,gBACjCpZ,OAAOsX,SAAS/O;QAElB,MAAMpG,iBACGmN,QAAQpK,qBAAqB,WAChCoK,QAAQpK,mBAAmB,MAC3B;QAEN;YACE,MAAMiZ,oBAAoBpiB,KAAKkgB,cAAathB,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACvCkE,QAAQ4J,sBAAmB;gBAC9BgD,YAAY;gBACZ9J,eAAenB,SAASA,MAAMmB;gBAC9BgH;gBACIjX,WAAW;gBAAEA;;YAGnB,OACKxH,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAA+S,cACH;gBAAArX,OAAOwI,QAAQ4J,oBAAoBpS;gBACnCwW,iBAAiBa,YAAYrX;gBAC7BD,UAAUyI,QAAQ4J,oBAAoBrS,YAAY;;AAiBrD,UAfC,OAAOrM;YACP,KAGGA,EAAEe,QAAQR,QAAQwK,wCAAwC,KAGxD/K,EAAEe,WACDf,EAAEe,QAAQR,QAAQyK,wCAAwC,MAC9DzJ,KAAKuT,QAAQ0K,0BACb;gBACA,aAAaje,KAAKshB,oBAAoB/N;AACvC;YAED,MAAM9U;AACP;AACF;IAEOiR,wBACNiF;QAEA,OAAMiB,UAAEA,UAAQH,cAAEA,gBAAyCd,OAAxB2N,sBAAwB/jB,OAAAoW,OAArD,EAAA,YAAA;QAEN3U,KAAK+d,UAAUtd,IAAI6T,2BAA2B;YAC5CsB;YACAH;;cAGIzV,KAAKif,aAAasD,WACtBviB,KAAKuT,QAAQhE,UACboF,MAAMiB,UACNjB,MAAMc;cAGFzV,KAAKif,aAAaxe,IAAI6hB;AAC7B;IAEO5S;QACN,MAAM5E,WAAW9K,KAAKuT,QAAQ4J,oBAAoBrS,YAAY;QAE9D,MAAMoK,cAAclV,KAAKif,aAAauD,WACpC,IAAIjO,SAAS;YACXhF,UAAUvP,KAAKuT,QAAQhE;YACvBzE;YACAC,OAAO/K,KAAK+K;;QAIhB,MAAM0X,eAAeziB,KAAK+d,UAAUxd,IAClC+T;QAKF,IAAIY,SAASA,MAAMU,cAAa6M,iBAAA,QAAAA,sBAAA,SAAA,IAAAA,aAAc7M,WAAU;YACtD,OAAO6M;AACR;QAEDziB,KAAK+d,UAAUtd,IAAI6T,2BAA2BY;QAC9C,OAAOA;AACR;IAEOxF,0BAAyB3E,OAC/BA,OAAKD,UACLA,UAAQyE,UACRA;QAMA,MAAMoF,cAAc3U,KAAKif,aAAa1e,IACpC,IAAIgU,SAAS;YACXxJ;YACAD;YACAyE;YAEF;QAGF,IAAIoF,SAASA,MAAMsM,cAAc;YAC/B,OAAMA,cAAEA,cAAYM,iBAAEA,iBAAe9K,YAAEA,cAAe9B;YACtD,MAAMO,cAAclV,KAAKqgB;YACzB,OACEnL,SACEtW,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;gBAAAuG,UAAUV,MAAMU;gBAChBqL;eACIM,kBAAkB;gBAAExW,OAAOwW;gBAAoB,OAAK;gBACxD9K;;AAGL;AACF;IAcO/G,oBACN6D,SAIAmP;QAEA,OAAMtC,SAAEA,SAAO9G,cAAEA,gBAAiBoJ,wBAAwB,CAAA;QAC1D,MAAMtB,mBAAmB1N;YAErBC,SAAS3T,KAAKmR;YACd3B,WAAWxP,KAAKuT,QAAQhE;YACxBqE,aAAa5T,KAAKuT,QAAQK;YAC1BT,aAAanT,KAAKuT,QAAQJ;YAC1B/M,SAASpG,KAAKse;WACX/K,UAELvT,KAAKkT;QAGP,MAAMuC,qBAAqBzV,KAAK2iB,eAC9BvB,WAAWxL,UACXwK,SACA9G;cAGItZ,KAAK4iB,kBAAiBhkB,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACvB+R,aACH;YAAA3L;YACA1K,OAAOwI,QAAQxI;YACfD,UAAUyI,QAAQzI,YAAY;YAC1BsW,WAAWrW,QAAQ;YAAEwW,iBAAiBH,WAAWrW;YAAU,OAC/D;YAAAyE,WAAWxP,KAAKuT,QAAQhE;;QAG1BvP,KAAKwe,cAAclH,KAAKtX,KAAK2e,2BAA2B,MAAM;YAC5DpH,iBAAiBvX,KAAK4e;YACtBzH,cAAcnX,KAAKuT,QAAQ4D;;QAG7BnX,KAAK0f,gBAAgBpG,gBAAgB7D,aAAauC,OAAOyB;QAEzD,OAAY7a,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAA+R,aAAY;YAAA3L;;AACzB;IAoDD/F,oBACE6D;QAEA,OAAOvT,KAAKkgB,cAAc;YACxBC,YAAY;YACZ0C,eAAetP,QAAQsP;YACvBC,oBAAoBvP,QAAQuP;YAC5B/X,OAAOkJ,gBAAgBV,QAAQxI,OAAO/K,KAAK+K;YAC3CD,UAAU9K,KAAKuT,QAAQ4J,oBAAoBrS;;AAE9C;;;MChlBUiY;;ACplBNrT,eAAesT,kBAAkBzP;IACtC,MAAM0P,QAAQ,IAAInF,YAAYvK;UACxB0P,MAAMC;IACZ,OAAOD;AACT;;;;;;;;;;;;;;;;;;;;;;;;;;"}