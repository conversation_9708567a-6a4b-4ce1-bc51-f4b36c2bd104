/**
 * Configuration centrale pour l'API Gateway Nouri
 */

import dotenv from 'dotenv';
import path from 'path';

// Charger les variables d'environnement
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

interface Config {
  app: {
    name: string;
    version: string;
    environment: string;
  };
  server: {
    port: number;
    host: string;
  };
  database: {
    url: string;
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };
  redis: {
    url: string;
    host: string;
    port: number;
  };
  auth: {
    jwtSecret: string;
    jwtExpiresIn: string;
    auth0Domain: string;
    auth0ClientId: string;
    auth0ClientSecret: string;
    auth0Audience: string;
  };
  security: {
    encryptionKey: string;
    hashSaltRounds: number;
  };
  cors: {
    origin: string[];
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  services: {
    userService: string;
    bankingService: string;
    aiEngine: string;
    chatbotService: string;
    budgetService: string;
    notificationService: string;
    educationService: string;
    reportingService: string;
  };
  external: {
    openaiApiKey: string;
    bankApiUrl: string;
    bankApiKey: string;
    sendgridApiKey: string;
    twilioAccountSid: string;
    twilioAuthToken: string;
  };
  monitoring: {
    logLevel: string;
    enableMetrics: boolean;
  };
  localization: {
    defaultLanguage: string;
    supportedLanguages: string[];
    defaultCurrency: string;
    timezone: string;
  };
}

const config: Config = {
  app: {
    name: process.env.APP_NAME || 'Nouri Financial Coach',
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  },

  server: {
    port: parseInt(process.env.API_GATEWAY_PORT || '3000', 10),
    host: process.env.HOST || '0.0.0.0',
  },

  database: {
    url: process.env.DATABASE_URL || 'postgresql://nouri_user:nouri_password@localhost:5432/nouri_db',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    name: process.env.DB_NAME || 'nouri_db',
    user: process.env.DB_USER || 'nouri_user',
    password: process.env.DB_PASSWORD || 'nouri_password',
  },

  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
  },

  auth: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-min-32-chars',
    jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h',
    auth0Domain: process.env.AUTH0_DOMAIN || '',
    auth0ClientId: process.env.AUTH0_CLIENT_ID || '',
    auth0ClientSecret: process.env.AUTH0_CLIENT_SECRET || '',
    auth0Audience: process.env.AUTH0_AUDIENCE || 'https://api.nouri.tn',
  },

  security: {
    encryptionKey: process.env.ENCRYPTION_KEY || 'your-aes-256-encryption-key-32-chars',
    hashSaltRounds: parseInt(process.env.HASH_SALT_ROUNDS || '12', 10),
  },

  cors: {
    origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:5173', 'http://localhost:3000'],
  },

  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  services: {
    userService: `http://localhost:${process.env.USER_SERVICE_PORT || '3001'}`,
    bankingService: `http://localhost:${process.env.BANKING_SERVICE_PORT || '3002'}`,
    aiEngine: `http://localhost:${process.env.AI_ENGINE_PORT || '8000'}`,
    chatbotService: `http://localhost:${process.env.CHATBOT_SERVICE_PORT || '8001'}`,
    budgetService: `http://localhost:${process.env.BUDGET_SERVICE_PORT || '3003'}`,
    notificationService: `http://localhost:${process.env.NOTIFICATION_SERVICE_PORT || '3004'}`,
    educationService: `http://localhost:${process.env.EDUCATION_SERVICE_PORT || '3005'}`,
    reportingService: `http://localhost:${process.env.REPORTING_SERVICE_PORT || '3006'}`,
  },

  external: {
    openaiApiKey: process.env.OPENAI_API_KEY || '',
    bankApiUrl: process.env.BANK_API_BASE_URL || 'https://api.bank.tn',
    bankApiKey: process.env.BANK_API_KEY || '',
    sendgridApiKey: process.env.SENDGRID_API_KEY || '',
    twilioAccountSid: process.env.TWILIO_ACCOUNT_SID || '',
    twilioAuthToken: process.env.TWILIO_AUTH_TOKEN || '',
  },

  monitoring: {
    logLevel: process.env.LOG_LEVEL || 'info',
    enableMetrics: process.env.ENABLE_METRICS === 'true',
  },

  localization: {
    defaultLanguage: process.env.DEFAULT_LANGUAGE || 'fr',
    supportedLanguages: process.env.SUPPORTED_LANGUAGES?.split(',') || ['ar', 'fr', 'en'],
    defaultCurrency: process.env.DEFAULT_CURRENCY || 'TND',
    timezone: process.env.TIMEZONE || 'Africa/Tunis',
  },
};

// Validation de la configuration
const validateConfig = (): void => {
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`Variables d'environnement manquantes: ${missingVars.join(', ')}`);
  }

  // Validation des clés de sécurité
  if (config.auth.jwtSecret.length < 32) {
    throw new Error('JWT_SECRET doit contenir au moins 32 caractères');
  }

  if (config.security.encryptionKey.length !== 32) {
    throw new Error('ENCRYPTION_KEY doit contenir exactement 32 caractères');
  }
};

// Valider la configuration au démarrage
if (process.env.NODE_ENV !== 'test') {
  validateConfig();
}

export { config, validateConfig };
