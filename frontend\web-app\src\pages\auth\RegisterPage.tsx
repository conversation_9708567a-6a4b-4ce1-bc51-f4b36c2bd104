/**
 * Streamlined Register Page with Mock Authentication
 */

import React, { useContext } from 'react'
import { Box, Typography, Button, Container, Card, CardContent, Divider } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { ROUTES } from '@/config'
import { AuthContext } from '@/App'

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate()
  const { login } = useContext(AuthContext)

  const handleDemoRegister = () => {
    login()
    navigate(ROUTES.DASHBOARD)
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 2
      }}
    >
      <Container maxWidth="sm">
        <Card sx={{ borderRadius: 3, boxShadow: 3 }}>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h3" component="h1" sx={{ color: 'primary.main', fontWeight: 'bold', mb: 1 }}>
                🇹🇳 Nouri
              </Typography>
              <Typography variant="h4" gutterBottom>
                Inscription
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Rejoignez des milliers d'utilisateurs qui gèrent leurs finances avec Nouri
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button
                variant="contained"
                size="large"
                fullWidth
                sx={{ py: 1.5, fontSize: '1.1rem' }}
                onClick={handleDemoRegister}
              >
                🚀 Créer un compte (Demo)
              </Button>

              <Divider sx={{ my: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  ou
                </Typography>
              </Divider>

              <Button
                variant="outlined"
                size="large"
                fullWidth
                sx={{ py: 1.5 }}
                onClick={() => navigate(ROUTES.LOGIN)}
              >
                J'ai déjà un compte
              </Button>
            </Box>

            <Box sx={{ textAlign: 'center', mt: 4 }}>
              <Button
                variant="text"
                onClick={() => navigate(ROUTES.HOME)}
                sx={{ color: 'text.secondary' }}
              >
                ← Retour à l'accueil
              </Button>
            </Box>

            <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.50', borderRadius: 2 }}>
              <Typography variant="body2" color="text.secondary" align="center">
                <strong>Mode Démo:</strong> Explorez toutes les fonctionnalités sans inscription réelle
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Box>
  )
}

export default RegisterPage
