{"author": "Auth0", "name": "@auth0/auth0-spa-js", "description": "Auth0 SDK for Single Page Applications using Authorization Code Grant Flow with PKCE", "license": "MIT", "version": "2.2.0", "main": "dist/lib/auth0-spa-js.cjs.js", "types": "dist/typings/index.d.ts", "module": "dist/auth0-spa-js.production.esm.js", "scripts": {"dev": "rimraf dist && rollup -c --watch", "start": "npm run dev", "docs": "typedoc --options ./typedoc.js src", "build": "rimraf dist && rollup -m -c --environment NODE_ENV:production && npm run test:es-check", "build:stats": "rimraf dist && rollup -m -c --environment NODE_ENV:production --environment WITH_STATS:true && npm run test:es-check && open bundle-stats/index.html", "lint:security": "eslint ./src --ext ts --no-eslintrc --config ./.eslintrc.security", "test": "jest --coverage --silent", "test:watch": "jest --coverage --watch", "test:debug": "node --inspect node_modules/.bin/jest --runInBand", "test:open:integration": "cypress open", "test:watch:integration": "concurrently --raw npm:dev 'npm:test:open:integration'", "test:es-check": "npm run test:es-check:es2017 && npm run test:es-check:es2017:module", "test:es-check:es2017": "es-check es2017 'dist/auth0-spa-js.production.js'", "test:es-check:es2017:module": "es-check es2017 'dist/auth0-spa-js.production.esm.js' --module ", "test:integration:server": "npm run dev", "test:integration:tests": "wait-on http://localhost:3000/ && cypress run", "test:integration": "concurrently --raw --kill-others --success first npm:test:integration:server npm:test:integration:tests", "serve:coverage": "serve coverage/lcov-report -n", "serve:stats": "serve bundle-stats -n", "print-bundle-size": "node ./scripts/print-bundle-size.mjs", "prepack": "npm run build && node ./scripts/prepack", "publish:cdn": "ccu --trace"}, "devDependencies": {"@auth0/component-cdn-uploader": "github:auth0/component-cdn-uploader#v2.2.2", "@rollup/plugin-replace": "^4.0.0", "@types/cypress": "^1.1.3", "@types/jest": "^28.1.7", "@typescript-eslint/eslint-plugin-tslint": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "browser-tabs-lock": "^1.2.15", "browserstack-cypress-cli": "1.28.0", "cli-table": "^0.3.6", "concurrently": "^7.3.0", "cypress": "13.6.1", "es-check": "^7.0.1", "es-cookie": "~1.3.2", "eslint": "^8.22.0", "eslint-plugin-security": "^1.5.0", "gzip-size": "^7.0.0", "husky": "^7.0.4", "idtoken-verifier": "^2.2.2", "jest": "^28.1.3", "jest-environment-jsdom": "^28.1.3", "jest-fetch-mock": "^3.0.3", "jest-junit": "^14.0.0", "jest-localstorage-mock": "^2.4.22", "jsonwebtoken": "^9.0.0", "oidc-provider": "^7.14.0", "prettier": "^2.7.1", "pretty-quick": "^3.1.2", "rimraf": "^3.0.2", "rollup": "^2.78.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-dev": "^1.1.3", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.6.3", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.36.0", "rollup-plugin-visualizer": "^5.7.1", "rollup-plugin-web-worker-loader": "^1.6.1", "serve": "^14.0.1", "ts-jest": "^28.0.8", "tslib": "^2.4.0", "typedoc": "^0.25.1", "typescript": "^4.7.4", "wait-on": "^7.2.0"}, "files": ["src", "dist"], "repository": {"type": "git", "url": "git://github.com/auth0/auth0-spa-js.git"}, "bugs": {"url": "https://github.com/auth0/auth0-spa-js/issues"}, "homepage": "https://github.com/auth0/auth0-spa-js#readme", "keywords": ["auth0", "login", "Authorization Code Grant Flow", "PKCE", "Single Page Application authentication", "SPA authentication"], "ccu": {"name": "auth0-spa-js", "cdn": "https://cdn.auth0.com", "mainBundleFile": "auth0-spa-js.production.js", "bucket": "assets.us.auth0.com", "localPath": "dist", "digest": {"hashes": ["sha384"], "extensions": [".js"]}}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}}