{"version": 3, "file": "auth0-spa-js.development.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../node_modules/browser-tabs-lock/processLock.js", "../node_modules/browser-tabs-lock/index.js", "../src/version.ts", "../src/constants.ts", "../src/errors.ts", "../src/utils.ts", "../src/worker/worker.utils.ts", "../src/http.ts", "../src/api.ts", "../src/scope.ts", "../src/cache/shared.ts", "../src/cache/cache-localstorage.ts", "../src/cache/cache-memory.ts", "../src/cache/cache-manager.ts", "../src/transaction-manager.ts", "../src/jwt.ts", "../node_modules/es-cookie/src/es-cookie.js", "../src/storage.ts", "../src/promise-utils.ts", "../src/cache/key-manifest.ts", "../src/Auth0Client.utils.ts", "../src/Auth0Client.ts", "../src/global.ts", "../src/index.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ProcessLocking = /** @class */ (function () {\n    function ProcessLocking() {\n        var _this = this;\n        this.locked = new Map();\n        this.addToLocked = function (key, toAdd) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined) {\n                if (toAdd === undefined) {\n                    _this.locked.set(key, []);\n                }\n                else {\n                    _this.locked.set(key, [toAdd]);\n                }\n            }\n            else {\n                if (toAdd !== undefined) {\n                    callbacks.unshift(toAdd);\n                    _this.locked.set(key, callbacks);\n                }\n            }\n        };\n        this.isLocked = function (key) {\n            return _this.locked.has(key);\n        };\n        this.lock = function (key) {\n            return new Promise(function (resolve, reject) {\n                if (_this.isLocked(key)) {\n                    _this.addToLocked(key, resolve);\n                }\n                else {\n                    _this.addToLocked(key);\n                    resolve();\n                }\n            });\n        };\n        this.unlock = function (key) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined || callbacks.length === 0) {\n                _this.locked.delete(key);\n                return;\n            }\n            var toCall = callbacks.pop();\n            _this.locked.set(key, callbacks);\n            if (toCall !== undefined) {\n                setTimeout(toCall, 0);\n            }\n        };\n    }\n    ProcessLocking.getInstance = function () {\n        if (ProcessLocking.instance === undefined) {\n            ProcessLocking.instance = new ProcessLocking();\n        }\n        return ProcessLocking.instance;\n    };\n    return ProcessLocking;\n}());\nfunction getLock() {\n    return ProcessLocking.getInstance();\n}\nexports.default = getLock;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar _this = this;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar processLock_1 = require(\"./processLock\");\n/**\n * @author: SuperTokens (https://github.com/supertokens)\n * This library was created as a part of a larger project, SuperTokens(https://supertokens.io) - the best session management solution.\n * You can also check out our other projects on https://github.com/supertokens\n *\n * To contribute to this package visit https://github.com/supertokens/browser-tabs-lock\n * If you face any problems you can file an issue on https://github.com/supertokens/browser-tabs-lock/issues\n *\n * If you have any questions or if you just want to say hi visit https://supertokens.io/discord\n */\n/**\n * @constant\n * @type {string}\n * @default\n * @description All the locks taken by this package will have this as prefix\n*/\nvar LOCK_STORAGE_KEY = 'browser-tabs-lock-key';\nvar DEFAULT_STORAGE_HANDLER = {\n    key: function (index) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    getItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    clear: function () { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, window.localStorage.clear()];\n        });\n    }); },\n    removeItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    setItem: function (key, value) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    keySync: function (index) {\n        return window.localStorage.key(index);\n    },\n    getItemSync: function (key) {\n        return window.localStorage.getItem(key);\n    },\n    clearSync: function () {\n        return window.localStorage.clear();\n    },\n    removeItemSync: function (key) {\n        return window.localStorage.removeItem(key);\n    },\n    setItemSync: function (key, value) {\n        return window.localStorage.setItem(key, value);\n    },\n};\n/**\n * @function delay\n * @param {number} milliseconds - How long the delay should be in terms of milliseconds\n * @returns {Promise<void>}\n */\nfunction delay(milliseconds) {\n    return new Promise(function (resolve) { return setTimeout(resolve, milliseconds); });\n}\n/**\n * @function generateRandomString\n * @params {number} length - How long the random string should be\n * @returns {string}\n * @description returns random string whose length is equal to the length passed as parameter\n */\nfunction generateRandomString(length) {\n    var CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz';\n    var randomstring = '';\n    for (var i = 0; i < length; i++) {\n        var INDEX = Math.floor(Math.random() * CHARS.length);\n        randomstring += CHARS[INDEX];\n    }\n    return randomstring;\n}\n/**\n * @function getLockId\n * @returns {string}\n * @description Generates an id which will be unique for the browser tab\n */\nfunction getLockId() {\n    return Date.now().toString() + generateRandomString(15);\n}\nvar SuperTokensLock = /** @class */ (function () {\n    function SuperTokensLock(storageHandler) {\n        this.acquiredIatSet = new Set();\n        this.storageHandler = undefined;\n        this.id = getLockId();\n        this.acquireLock = this.acquireLock.bind(this);\n        this.releaseLock = this.releaseLock.bind(this);\n        this.releaseLock__private__ = this.releaseLock__private__.bind(this);\n        this.waitForSomethingToChange = this.waitForSomethingToChange.bind(this);\n        this.refreshLockWhileAcquired = this.refreshLockWhileAcquired.bind(this);\n        this.storageHandler = storageHandler;\n        if (SuperTokensLock.waiters === undefined) {\n            SuperTokensLock.waiters = [];\n        }\n    }\n    /**\n     * @async\n     * @memberOf Lock\n     * @function acquireLock\n     * @param {string} lockKey - Key for which the lock is being acquired\n     * @param {number} [timeout=5000] - Maximum time for which the function will wait to acquire the lock\n     * @returns {Promise<boolean>}\n     * @description Will return true if lock is being acquired, else false.\n     *              Also the lock can be acquired for maximum 10 secs\n     */\n    SuperTokensLock.prototype.acquireLock = function (lockKey, timeout) {\n        if (timeout === void 0) { timeout = 5000; }\n        return __awaiter(this, void 0, void 0, function () {\n            var iat, MAX_TIME, STORAGE_KEY, STORAGE, lockObj, TIMEOUT_KEY, lockObjPostDelay, parsedLockObjPostDelay;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        iat = Date.now() + generateRandomString(4);\n                        MAX_TIME = Date.now() + timeout;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        _a.label = 1;\n                    case 1:\n                        if (!(Date.now() < MAX_TIME)) return [3 /*break*/, 8];\n                        return [4 /*yield*/, delay(30)];\n                    case 2:\n                        _a.sent();\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (!(lockObj === null)) return [3 /*break*/, 5];\n                        TIMEOUT_KEY = this.id + \"-\" + lockKey + \"-\" + iat;\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        return [4 /*yield*/, delay(Math.floor(Math.random() * 25))];\n                    case 3:\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        _a.sent();\n                        STORAGE.setItemSync(STORAGE_KEY, JSON.stringify({\n                            id: this.id,\n                            iat: iat,\n                            timeoutKey: TIMEOUT_KEY,\n                            timeAcquired: Date.now(),\n                            timeRefreshed: Date.now()\n                        }));\n                        return [4 /*yield*/, delay(30)];\n                    case 4:\n                        _a.sent(); // this is to prevent race conditions. This time must be more than the time it takes for storage.setItem\n                        lockObjPostDelay = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObjPostDelay !== null) {\n                            parsedLockObjPostDelay = JSON.parse(lockObjPostDelay);\n                            if (parsedLockObjPostDelay.id === this.id && parsedLockObjPostDelay.iat === iat) {\n                                this.acquiredIatSet.add(iat);\n                                this.refreshLockWhileAcquired(STORAGE_KEY, iat);\n                                return [2 /*return*/, true];\n                            }\n                        }\n                        return [3 /*break*/, 7];\n                    case 5:\n                        SuperTokensLock.lockCorrector(this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler);\n                        return [4 /*yield*/, this.waitForSomethingToChange(MAX_TIME)];\n                    case 6:\n                        _a.sent();\n                        _a.label = 7;\n                    case 7:\n                        iat = Date.now() + generateRandomString(4);\n                        return [3 /*break*/, 1];\n                    case 8: return [2 /*return*/, false];\n                }\n            });\n        });\n    };\n    SuperTokensLock.prototype.refreshLockWhileAcquired = function (storageKey, iat) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {\n                    var STORAGE, lockObj, parsedLockObj;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0: return [4 /*yield*/, processLock_1.default().lock(iat)];\n                            case 1:\n                                _a.sent();\n                                if (!this.acquiredIatSet.has(iat)) {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                                lockObj = STORAGE.getItemSync(storageKey);\n                                if (lockObj !== null) {\n                                    parsedLockObj = JSON.parse(lockObj);\n                                    parsedLockObj.timeRefreshed = Date.now();\n                                    STORAGE.setItemSync(storageKey, JSON.stringify(parsedLockObj));\n                                    processLock_1.default().unlock(iat);\n                                }\n                                else {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                this.refreshLockWhileAcquired(storageKey, iat);\n                                return [2 /*return*/];\n                        }\n                    });\n                }); }, 1000);\n                return [2 /*return*/];\n            });\n        });\n    };\n    SuperTokensLock.prototype.waitForSomethingToChange = function (MAX_TIME) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, new Promise(function (resolve) {\n                            var resolvedCalled = false;\n                            var startedAt = Date.now();\n                            var MIN_TIME_TO_WAIT = 50; // ms\n                            var removedListeners = false;\n                            function stopWaiting() {\n                                if (!removedListeners) {\n                                    window.removeEventListener('storage', stopWaiting);\n                                    SuperTokensLock.removeFromWaiting(stopWaiting);\n                                    clearTimeout(timeOutId);\n                                    removedListeners = true;\n                                }\n                                if (!resolvedCalled) {\n                                    resolvedCalled = true;\n                                    var timeToWait = MIN_TIME_TO_WAIT - (Date.now() - startedAt);\n                                    if (timeToWait > 0) {\n                                        setTimeout(resolve, timeToWait);\n                                    }\n                                    else {\n                                        resolve(null);\n                                    }\n                                }\n                            }\n                            window.addEventListener('storage', stopWaiting);\n                            SuperTokensLock.addToWaiting(stopWaiting);\n                            var timeOutId = setTimeout(stopWaiting, Math.max(0, MAX_TIME - Date.now()));\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    SuperTokensLock.addToWaiting = function (func) {\n        this.removeFromWaiting(func);\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters.push(func);\n    };\n    SuperTokensLock.removeFromWaiting = function (func) {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters = SuperTokensLock.waiters.filter(function (i) { return i !== func; });\n    };\n    SuperTokensLock.notifyWaiters = function () {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        var waiters = SuperTokensLock.waiters.slice(); // so that if Lock.waiters is changed it's ok.\n        waiters.forEach(function (i) { return i(); });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.releaseLock__private__(lockKey)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock__private__ = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            var STORAGE, STORAGE_KEY, lockObj, parsedlockObj;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObj === null) {\n                            return [2 /*return*/];\n                        }\n                        parsedlockObj = JSON.parse(lockObj);\n                        if (!(parsedlockObj.id === this.id)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, processLock_1.default().lock(parsedlockObj.iat)];\n                    case 1:\n                        _a.sent();\n                        this.acquiredIatSet.delete(parsedlockObj.iat);\n                        STORAGE.removeItemSync(STORAGE_KEY);\n                        processLock_1.default().unlock(parsedlockObj.iat);\n                        SuperTokensLock.notifyWaiters();\n                        _a.label = 2;\n                    case 2: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * @function lockCorrector\n     * @returns {void}\n     * @description If a lock is acquired by a tab and the tab is closed before the lock is\n     *              released, this function will release those locks\n     */\n    SuperTokensLock.lockCorrector = function (storageHandler) {\n        var MIN_ALLOWED_TIME = Date.now() - 5000;\n        var STORAGE = storageHandler;\n        var KEYS = [];\n        var currIndex = 0;\n        while (true) {\n            var key = STORAGE.keySync(currIndex);\n            if (key === null) {\n                break;\n            }\n            KEYS.push(key);\n            currIndex++;\n        }\n        var notifyWaiters = false;\n        for (var i = 0; i < KEYS.length; i++) {\n            var LOCK_KEY = KEYS[i];\n            if (LOCK_KEY.includes(LOCK_STORAGE_KEY)) {\n                var lockObj = STORAGE.getItemSync(LOCK_KEY);\n                if (lockObj !== null) {\n                    var parsedlockObj = JSON.parse(lockObj);\n                    if ((parsedlockObj.timeRefreshed === undefined && parsedlockObj.timeAcquired < MIN_ALLOWED_TIME) ||\n                        (parsedlockObj.timeRefreshed !== undefined && parsedlockObj.timeRefreshed < MIN_ALLOWED_TIME)) {\n                        STORAGE.removeItemSync(LOCK_KEY);\n                        notifyWaiters = true;\n                    }\n                }\n            }\n        }\n        if (notifyWaiters) {\n            SuperTokensLock.notifyWaiters();\n        }\n    };\n    SuperTokensLock.waiters = undefined;\n    return SuperTokensLock;\n}());\nexports.default = SuperTokensLock;\n", "export default '2.2.0';\n", "import { PopupConfigOptions } from './global';\nimport version from './version';\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS = 60;\n\n/**\n * @ignore\n */\nexport const DEFAULT_POPUP_CONFIG_OPTIONS: PopupConfigOptions = {\n  timeoutInSeconds: DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n};\n\n/**\n * @ignore\n */\nexport const DEFAULT_SILENT_TOKEN_RETRY_COUNT = 3;\n\n/**\n * @ignore\n */\nexport const CLEANUP_IFRAME_TIMEOUT_IN_SECONDS = 2;\n\n/**\n * @ignore\n */\nexport const DEFAULT_FETCH_TIMEOUT_MS = 10000;\n\nexport const CACHE_LOCATION_MEMORY = 'memory';\nexport const CACHE_LOCATION_LOCAL_STORAGE = 'localstorage';\n\n/**\n * @ignore\n */\nexport const MISSING_REFRESH_TOKEN_ERROR_MESSAGE = 'Missing Refresh Token';\n\n/**\n * @ignore\n */\nexport const INVALID_REFRESH_TOKEN_ERROR_MESSAGE = 'invalid refresh token';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SCOPE = 'openid profile email';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SESSION_CHECK_EXPIRY_DAYS = 1;\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTH0_CLIENT = {\n  name: 'auth0-spa-js',\n  version: version\n};\n\nexport const DEFAULT_NOW_PROVIDER = () => Date.now();\n", "/**\n * Thrown when network requests to the Auth server fail.\n */\nexport class GenericError extends Error {\n  constructor(public error: string, public error_description: string) {\n    super(error_description);\n    Object.setPrototypeOf(this, GenericError.prototype);\n  }\n\n  static fromPayload({\n    error,\n    error_description\n  }: {\n    error: string;\n    error_description: string;\n  }) {\n    return new GenericError(error, error_description);\n  }\n}\n\n/**\n * Thrown when handling the redirect callback fails, will be one of Auth0's\n * Authentication API's Standard Error Responses: https://auth0.com/docs/api/authentication?javascript#standard-error-responses\n */\nexport class AuthenticationError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public state: string,\n    public appState: any = null\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, AuthenticationError.prototype);\n  }\n}\n\n/**\n * Thrown when silent auth times out (usually due to a configuration issue) or\n * when network requests to the Auth server timeout.\n */\nexport class TimeoutError extends GenericError {\n  constructor() {\n    super('timeout', 'Timeout');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Error thrown when the login popup times out (if the user does not complete auth)\n */\nexport class PopupTimeoutError extends TimeoutError {\n  constructor(public popup: Window) {\n    super();\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupTimeoutError.prototype);\n  }\n}\n\nexport class PopupCancelledError extends GenericError {\n  constructor(public popup: Window) {\n    super('cancelled', 'Popup closed');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupCancelledError.prototype);\n  }\n}\n\n/**\n * Error thrown when the token exchange results in a `mfa_required` error\n */\nexport class MfaRequiredError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public mfa_token: string\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, MfaRequiredError.prototype);\n  }\n}\n\n/**\n * Error thrown when there is no refresh token to use\n */\nexport class MissingRefreshTokenError extends GenericError {\n  constructor(public audience: string, public scope: string) {\n    super(\n      'missing_refresh_token',\n      `Missing Refresh Token (audience: '${valueOrEmptyString(audience, [\n        'default'\n      ])}', scope: '${valueOrEmptyString(scope)}')`\n    );\n    Object.setPrototypeOf(this, MissingRefreshTokenError.prototype);\n  }\n}\n\n/**\n * Returns an empty string when value is falsy, or when it's value is included in the exclude argument.\n * @param value The value to check\n * @param exclude An array of values that should result in an empty string.\n * @returns The value, or an empty string when falsy or included in the exclude argument.\n */\nfunction valueOrEmptyString(value: string, exclude: string[] = []) {\n  return value && !exclude.includes(value) ? value : '';\n}\n", "import { AuthenticationResult, PopupConfigOptions } from './global';\n\nimport {\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  CLEANUP_IFRAME_TIMEOUT_IN_SECONDS\n} from './constants';\n\nimport {\n  PopupTimeoutError,\n  TimeoutError,\n  GenericError,\n  PopupCancelledError\n} from './errors';\n\nexport const parseAuthenticationResult = (\n  queryString: string\n): AuthenticationResult => {\n  if (queryString.indexOf('#') > -1) {\n    queryString = queryString.substring(0, queryString.indexOf('#'));\n  }\n\n  const searchParams = new URLSearchParams(queryString);\n\n  return {\n    state: searchParams.get('state')!,\n    code: searchParams.get('code') || undefined,\n    error: searchParams.get('error') || undefined,\n    error_description: searchParams.get('error_description') || undefined\n  };\n};\n\nexport const runIframe = (\n  authorizeUrl: string,\n  eventOrigin: string,\n  timeoutInSeconds: number = DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n) => {\n  return new Promise<AuthenticationResult>((res, rej) => {\n    const iframe = window.document.createElement('iframe');\n\n    iframe.setAttribute('width', '0');\n    iframe.setAttribute('height', '0');\n    iframe.style.display = 'none';\n\n    const removeIframe = () => {\n      if (window.document.body.contains(iframe)) {\n        window.document.body.removeChild(iframe);\n        window.removeEventListener('message', iframeEventHandler, false);\n      }\n    };\n\n    let iframeEventHandler: (e: MessageEvent) => void;\n\n    const timeoutSetTimeoutId = setTimeout(() => {\n      rej(new TimeoutError());\n      removeIframe();\n    }, timeoutInSeconds * 1000);\n\n    iframeEventHandler = function (e: MessageEvent) {\n      if (e.origin != eventOrigin) return;\n      if (!e.data || e.data.type !== 'authorization_response') return;\n\n      const eventSource = e.source;\n\n      if (eventSource) {\n        (eventSource as any).close();\n      }\n\n      e.data.response.error\n        ? rej(GenericError.fromPayload(e.data.response))\n        : res(e.data.response);\n\n      clearTimeout(timeoutSetTimeoutId);\n      window.removeEventListener('message', iframeEventHandler, false);\n\n      // Delay the removal of the iframe to prevent hanging loading status\n      // in Chrome: https://github.com/auth0/auth0-spa-js/issues/240\n      setTimeout(removeIframe, CLEANUP_IFRAME_TIMEOUT_IN_SECONDS * 1000);\n    };\n\n    window.addEventListener('message', iframeEventHandler, false);\n    window.document.body.appendChild(iframe);\n    iframe.setAttribute('src', authorizeUrl);\n  });\n};\n\nexport const openPopup = (url: string) => {\n  const width = 400;\n  const height = 600;\n  const left = window.screenX + (window.innerWidth - width) / 2;\n  const top = window.screenY + (window.innerHeight - height) / 2;\n\n  return window.open(\n    url,\n    'auth0:authorize:popup',\n    `left=${left},top=${top},width=${width},height=${height},resizable,scrollbars=yes,status=1`\n  );\n};\n\nexport const runPopup = (config: PopupConfigOptions) => {\n  return new Promise<AuthenticationResult>((resolve, reject) => {\n    let popupEventListener: (e: MessageEvent) => void;\n\n    // Check each second if the popup is closed triggering a PopupCancelledError\n    const popupTimer = setInterval(() => {\n      if (config.popup && config.popup.closed) {\n        clearInterval(popupTimer);\n        clearTimeout(timeoutId);\n        window.removeEventListener('message', popupEventListener, false);\n        reject(new PopupCancelledError(config.popup));\n      }\n    }, 1000);\n\n    const timeoutId = setTimeout(() => {\n      clearInterval(popupTimer);\n      reject(new PopupTimeoutError(config.popup));\n      window.removeEventListener('message', popupEventListener, false);\n    }, (config.timeoutInSeconds || DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS) * 1000);\n\n    popupEventListener = function (e: MessageEvent) {\n      if (!e.data || e.data.type !== 'authorization_response') {\n        return;\n      }\n\n      clearTimeout(timeoutId);\n      clearInterval(popupTimer);\n      window.removeEventListener('message', popupEventListener, false);\n      config.popup.close();\n\n      if (e.data.response.error) {\n        return reject(GenericError.fromPayload(e.data.response));\n      }\n\n      resolve(e.data.response);\n    };\n\n    window.addEventListener('message', popupEventListener);\n  });\n};\n\nexport const getCrypto = () => {\n  return window.crypto;\n};\n\nexport const createRandomString = () => {\n  const charset =\n    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.';\n  let random = '';\n  const randomValues = Array.from(\n    getCrypto().getRandomValues(new Uint8Array(43))\n  );\n  randomValues.forEach(v => (random += charset[v % charset.length]));\n  return random;\n};\n\nexport const encode = (value: string) => btoa(value);\nexport const decode = (value: string) => atob(value);\n\nconst stripUndefined = (params: any) => {\n  return Object.keys(params)\n    .filter(k => typeof params[k] !== 'undefined')\n    .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {});\n};\n\nexport const createQueryParams = ({ clientId: client_id, ...params }: any) => {\n  return new URLSearchParams(\n    stripUndefined({ client_id, ...params })\n  ).toString();\n};\n\nexport const sha256 = async (s: string) => {\n  const digestOp: any = getCrypto().subtle.digest(\n    { name: 'SHA-256' },\n    new TextEncoder().encode(s)\n  );\n\n  return await digestOp;\n};\n\nconst urlEncodeB64 = (input: string) => {\n  const b64Chars: { [index: string]: string } = { '+': '-', '/': '_', '=': '' };\n  return input.replace(/[+/=]/g, (m: string) => b64Chars[m]);\n};\n\n// https://stackoverflow.com/questions/30106476/\nconst decodeB64 = (input: string) =>\n  decodeURIComponent(\n    atob(input)\n      .split('')\n      .map(c => {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n      })\n      .join('')\n  );\n\nexport const urlDecodeB64 = (input: string) =>\n  decodeB64(input.replace(/_/g, '/').replace(/-/g, '+'));\n\nexport const bufferToBase64UrlEncoded = (input: number[] | Uint8Array) => {\n  const ie11SafeInput = new Uint8Array(input);\n  return urlEncodeB64(\n    window.btoa(String.fromCharCode(...Array.from(ie11SafeInput)))\n  );\n};\n\nexport const validateCrypto = () => {\n  if (!getCrypto()) {\n    throw new Error(\n      'For security reasons, `window.crypto` is required to run `auth0-spa-js`.'\n    );\n  }\n  if (typeof getCrypto().subtle === 'undefined') {\n    throw new Error(`\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    `);\n  }\n};\n\n/**\n * @ignore\n */\nexport const getDomain = (domainUrl: string) => {\n  if (!/^https?:\\/\\//.test(domainUrl)) {\n    return `https://${domainUrl}`;\n  }\n\n  return domainUrl;\n};\n\n/**\n * @ignore\n */\nexport const getTokenIssuer = (\n  issuer: string | undefined,\n  domainUrl: string\n) => {\n  if (issuer) {\n    return issuer.startsWith('https://') ? issuer : `https://${issuer}/`;\n  }\n\n  return `${domainUrl}/`;\n};\n\nexport const parseNumber = (value: any): number | undefined => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  return parseInt(value, 10) || undefined;\n};\n", "import { WorkerRefreshTokenMessage } from './worker.types';\n\n/**\n * Sends the specified message to the web worker\n * @param message The message to send\n * @param to The worker to send the message to\n */\nexport const sendMessage = (message: WorkerRefreshTokenMessage, to: Worker) =>\n  new Promise(function (resolve, reject) {\n    const messageChannel = new MessageChannel();\n\n    messageChannel.port1.onmessage = function (event) {\n      // Only for fetch errors, as these get retried\n      if (event.data.error) {\n        reject(new Error(event.data.error));\n      } else {\n        resolve(event.data);\n      }\n      messageChannel.port1.close();\n    };\n\n    to.postMessage(message, [messageChannel.port2]);\n  });\n", "import {\n  DEFAULT_FETCH_TIMEOUT_MS,\n  DEFAULT_SILENT_TOKEN_RETRY_COUNT\n} from './constants';\n\nimport { sendMessage } from './worker/worker.utils';\nimport { FetchOptions } from './global';\nimport {\n  GenericError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport const createAbortController = () => new AbortController();\n\nconst dofetch = async (fetchUrl: string, fetchOptions: FetchOptions) => {\n  const response = await fetch(fetchUrl, fetchOptions);\n\n  return {\n    ok: response.ok,\n    json: await response.json()\n  };\n};\n\nconst fetchWithoutWorker = async (\n  fetchUrl: string,\n  fetchOptions: FetchOptions,\n  timeout: number\n) => {\n  const controller = createAbortController();\n  fetchOptions.signal = controller.signal;\n\n  let timeoutId: NodeJS.Timeout;\n\n  // The promise will resolve with one of these two promises (the fetch or the timeout), whichever completes first.\n  return Promise.race([\n    dofetch(fetchUrl, fetchOptions),\n\n    new Promise((_, reject) => {\n      timeoutId = setTimeout(() => {\n        controller.abort();\n        reject(new Error(\"Timeout when executing 'fetch'\"));\n      }, timeout);\n    })\n  ]).finally(() => {\n    clearTimeout(timeoutId);\n  });\n};\n\nconst fetchWithWorker = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  timeout: number,\n  worker: Worker,\n  useFormData?: boolean\n) => {\n  return sendMessage(\n    {\n      auth: {\n        audience,\n        scope\n      },\n      timeout,\n      fetchUrl,\n      fetchOptions,\n      useFormData\n    },\n    worker\n  );\n};\n\nexport const switchFetch = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean,\n  timeout = DEFAULT_FETCH_TIMEOUT_MS\n): Promise<any> => {\n  if (worker) {\n    return fetchWithWorker(\n      fetchUrl,\n      audience,\n      scope,\n      fetchOptions,\n      timeout,\n      worker,\n      useFormData\n    );\n  } else {\n    return fetchWithoutWorker(fetchUrl, fetchOptions, timeout);\n  }\n};\n\nexport async function getJSON<T>(\n  url: string,\n  timeout: number | undefined,\n  audience: string,\n  scope: string,\n  options: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean\n): Promise<T> {\n  let fetchError: null | Error = null;\n  let response: any;\n\n  for (let i = 0; i < DEFAULT_SILENT_TOKEN_RETRY_COUNT; i++) {\n    try {\n      response = await switchFetch(\n        url,\n        audience,\n        scope,\n        options,\n        worker,\n        useFormData,\n        timeout\n      );\n      fetchError = null;\n      break;\n    } catch (e) {\n      // Fetch only fails in the case of a network issue, so should be\n      // retried here. Failure status (4xx, 5xx, etc) return a resolved Promise\n      // with the failure in the body.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\n      fetchError = e;\n    }\n  }\n\n  if (fetchError) {\n    throw fetchError;\n  }\n\n  const {\n    json: { error, error_description, ...data },\n    ok\n  } = response;\n\n  if (!ok) {\n    const errorMessage =\n      error_description || `HTTP error. Unable to fetch ${url}`;\n\n    if (error === 'mfa_required') {\n      throw new MfaRequiredError(error, errorMessage, data.mfa_token);\n    }\n\n    if (error === 'missing_refresh_token') {\n      throw new MissingRefreshTokenError(audience, scope);\n    }\n\n    throw new GenericError(error || 'request_error', errorMessage);\n  }\n\n  return data;\n}\n", "import { TokenEndpointOptions, TokenEndpointResponse } from './global';\nimport { DEFAULT_AUTH0_CLIENT } from './constants';\nimport { getJSON } from './http';\nimport { createQueryParams } from './utils';\n\nexport async function oauthToken(\n  {\n    baseUrl,\n    timeout,\n    audience,\n    scope,\n    auth0Client,\n    useFormData,\n    ...options\n  }: TokenEndpointOptions,\n  worker?: Worker\n) {\n  const body = useFormData\n    ? createQueryParams(options)\n    : JSON.stringify(options);\n\n  return await getJSON<TokenEndpointResponse>(\n    `${baseUrl}/oauth/token`,\n    timeout,\n    audience || 'default',\n    scope,\n    {\n      method: 'POST',\n      body,\n      headers: {\n        'Content-Type': useFormData\n          ? 'application/x-www-form-urlencoded'\n          : 'application/json',\n        'Auth0-Client': btoa(\n          JSON.stringify(auth0Client || DEFAULT_AUTH0_CLIENT)\n        )\n      }\n    },\n    worker,\n    useFormData\n  );\n}\n", "/**\n * @ignore\n */\nconst dedupe = (arr: string[]) => Array.from(new Set(arr));\n\n/**\n * @ignore\n */\n/**\n * Returns a string of unique scopes by removing duplicates and unnecessary whitespace.\n *\n * @param {...(string | undefined)[]} scopes - A list of scope strings or undefined values.\n * @returns {string} A string containing unique scopes separated by a single space.\n */\nexport const getUniqueScopes = (...scopes: (string | undefined)[]) => {\n  return dedupe(scopes.filter(Boolean).join(' ').trim().split(/\\s+/)).join(' ');\n};\n", "import { IdToken, User } from '../global';\n\nexport const CACHE_KEY_PREFIX = '@@auth0spajs@@';\nexport const CACHE_KEY_ID_TOKEN_SUFFIX = '@@user@@';\n\nexport type CacheKeyData = {\n  audience?: string;\n  scope?: string;\n  clientId: string;\n};\n\nexport class C<PERSON><PERSON>ey {\n  public clientId: string;\n  public scope?: string;\n  public audience?: string;\n\n  constructor(\n    data: CacheKeyData,\n    public prefix: string = CACHE_KEY_PREFIX,\n    public suffix?: string\n  ) {\n    this.clientId = data.clientId;\n    this.scope = data.scope;\n    this.audience = data.audience;\n  }\n\n  /**\n   * Converts this `<PERSON><PERSON><PERSON><PERSON>` instance into a string for use in a cache\n   * @returns A string representation of the key\n   */\n  toKey(): string {\n    return [this.prefix, this.clientId, this.audience, this.scope, this.suffix]\n      .filter(Boolean)\n      .join('::');\n  }\n\n  /**\n   * Converts a cache key string into a `CacheKey` instance.\n   * @param key The key to convert\n   * @returns An instance of `<PERSON><PERSON><PERSON><PERSON>`\n   */\n  static from<PERSON><PERSON>(key: string): <PERSON>ache<PERSON><PERSON> {\n    const [prefix, clientId, audience, scope] = key.split('::');\n\n    return new CacheKey({ clientId, scope, audience }, prefix);\n  }\n\n  /**\n   * Utility function to build a `CacheKey` instance from a cache entry\n   * @param entry The entry\n   * @returns An instance of `CacheKey`\n   */\n  static fromCacheEntry(entry: CacheEntry): CacheKey {\n    const { scope, audience, client_id: clientId } = entry;\n\n    return new CacheKey({\n      scope,\n      audience,\n      clientId\n    });\n  }\n}\n\nexport interface DecodedToken {\n  claims: IdToken;\n  user: User;\n}\n\nexport interface IdTokenEntry {\n  id_token: string;\n  decodedToken: DecodedToken;\n}\n\nexport type CacheEntry = {\n  id_token?: string;\n  access_token: string;\n  expires_in: number;\n  decodedToken?: DecodedToken;\n  audience: string;\n  scope: string;\n  client_id: string;\n  refresh_token?: string;\n  oauthTokenScope?: string;\n};\n\nexport type WrappedCacheEntry = {\n  body: Partial<CacheEntry>;\n  expiresAt: number;\n};\n\nexport type KeyManifestEntry = {\n  keys: string[];\n};\n\nexport type Cacheable = WrappedCacheEntry | KeyManifestEntry;\n\nexport type MaybePromise<T> = Promise<T> | T;\n\nexport interface ICache {\n  set<T = Cacheable>(key: string, entry: T): MaybePromise<void>;\n  get<T = Cacheable>(key: string): MaybePromise<T | undefined>;\n  remove(key: string): MaybePromise<void>;\n  allKeys?(): MaybePromise<string[]>;\n}\n", "import { ICache, Cacheable, CACHE_KEY_PREFIX, Maybe<PERSON>rom<PERSON> } from './shared';\n\nexport class LocalStorageCache implements ICache {\n  public set<T = Cacheable>(key: string, entry: T) {\n    localStorage.setItem(key, JSON.stringify(entry));\n  }\n\n  public get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n    const json = window.localStorage.getItem(key);\n\n    if (!json) return;\n\n    try {\n      const payload = JSON.parse(json) as T;\n      return payload;\n      /* c8 ignore next 3 */\n    } catch (e) {\n      return;\n    }\n  }\n\n  public remove(key: string) {\n    localStorage.removeItem(key);\n  }\n\n  public allKeys() {\n    return Object.keys(window.localStorage).filter(key =>\n      key.startsWith(CACHE_KEY_PREFIX)\n    );\n  }\n}\n", "import { Cacheable, ICache, MaybePromise } from './shared';\n\nexport class InMemoryCache {\n  public enclosedCache: ICache = (function () {\n    let cache: Record<string, unknown> = {};\n\n    return {\n      set<T = Cacheable>(key: string, entry: T) {\n        cache[key] = entry;\n      },\n\n      get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n        const cacheEntry = cache[key] as T;\n\n        if (!cacheEntry) {\n          return;\n        }\n\n        return cacheEntry;\n      },\n\n      remove(key: string) {\n        delete cache[key];\n      },\n\n      allKeys(): string[] {\n        return Object.keys(cache);\n      }\n    };\n  })();\n}\n", "import { DEFAULT_NOW_PROVIDER } from '../constants';\nimport { CacheKeyManifest } from './key-manifest';\n\nimport {\n  CacheEntry,\n  ICache,\n  CacheKey,\n  CACHE_KEY_PREFIX,\n  WrappedCacheEntry,\n  DecodedToken,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  IdTokenEntry\n} from './shared';\n\nconst DEFAULT_EXPIRY_ADJUSTMENT_SECONDS = 0;\n\nexport class CacheManager {\n  private nowProvider: () => number | Promise<number>;\n\n  constructor(\n    private cache: ICache,\n    private keyManifest?: CacheKeyManifest,\n    nowProvider?: () => number | Promise<number>\n  ) {\n    this.nowProvider = nowProvider || DEFAULT_NOW_PROVIDER;\n  }\n\n  async setIdToken(\n    clientId: string,\n    idToken: string,\n    decodedToken: DecodedToken\n  ): Promise<void> {\n    const cacheKey = this.getIdTokenCacheKey(clientId);\n    await this.cache.set(cacheKey, {\n      id_token: idToken,\n      decodedToken\n    });\n    await this.keyManifest?.add(cacheKey);\n  }\n\n  async getIdToken(cacheKey: <PERSON>acheK<PERSON>): Promise<IdTokenEntry | undefined> {\n    const entry = await this.cache.get<IdTokenEntry>(\n      this.getIdTokenCacheKey(cacheKey.clientId)\n    );\n\n    if (!entry && cacheKey.scope && cacheKey.audience) {\n      const entryByScope = await this.get(cacheKey);\n\n      if (!entryByScope) {\n        return;\n      }\n\n      if (!entryByScope.id_token || !entryByScope.decodedToken) {\n        return;\n      }\n\n      return {\n        id_token: entryByScope.id_token,\n        decodedToken: entryByScope.decodedToken\n      };\n    }\n\n    if (!entry) {\n      return;\n    }\n\n    return { id_token: entry.id_token, decodedToken: entry.decodedToken };\n  }\n\n  async get(\n    cacheKey: CacheKey,\n    expiryAdjustmentSeconds = DEFAULT_EXPIRY_ADJUSTMENT_SECONDS\n  ): Promise<Partial<CacheEntry> | undefined> {\n    let wrappedEntry = await this.cache.get<WrappedCacheEntry>(\n      cacheKey.toKey()\n    );\n\n    if (!wrappedEntry) {\n      const keys = await this.getCacheKeys();\n\n      if (!keys) return;\n\n      const matchedKey = this.matchExistingCacheKey(cacheKey, keys);\n\n      if (matchedKey) {\n        wrappedEntry = await this.cache.get<WrappedCacheEntry>(matchedKey);\n      }\n    }\n\n    // If we still don't have an entry, exit.\n    if (!wrappedEntry) {\n      return;\n    }\n\n    const now = await this.nowProvider();\n    const nowSeconds = Math.floor(now / 1000);\n\n    if (wrappedEntry.expiresAt - expiryAdjustmentSeconds < nowSeconds) {\n      if (wrappedEntry.body.refresh_token) {\n        wrappedEntry.body = {\n          refresh_token: wrappedEntry.body.refresh_token\n        };\n\n        await this.cache.set(cacheKey.toKey(), wrappedEntry);\n        return wrappedEntry.body;\n      }\n\n      await this.cache.remove(cacheKey.toKey());\n      await this.keyManifest?.remove(cacheKey.toKey());\n\n      return;\n    }\n\n    return wrappedEntry.body;\n  }\n\n  async set(entry: CacheEntry): Promise<void> {\n    const cacheKey = new CacheKey({\n      clientId: entry.client_id,\n      scope: entry.scope,\n      audience: entry.audience\n    });\n\n    const wrappedEntry = await this.wrapCacheEntry(entry);\n\n    await this.cache.set(cacheKey.toKey(), wrappedEntry);\n    await this.keyManifest?.add(cacheKey.toKey());\n  }\n\n  async clear(clientId?: string): Promise<void> {\n    const keys = await this.getCacheKeys();\n\n    /* c8 ignore next */\n    if (!keys) return;\n\n    await keys\n      .filter(key => (clientId ? key.includes(clientId) : true))\n      .reduce(async (memo, key) => {\n        await memo;\n        await this.cache.remove(key);\n      }, Promise.resolve());\n\n    await this.keyManifest?.clear();\n  }\n\n  private async wrapCacheEntry(entry: CacheEntry): Promise<WrappedCacheEntry> {\n    const now = await this.nowProvider();\n    const expiresInTime = Math.floor(now / 1000) + entry.expires_in;\n\n    return {\n      body: entry,\n      expiresAt: expiresInTime\n    };\n  }\n\n  private async getCacheKeys(): Promise<string[] | undefined> {\n    if (this.keyManifest) {\n      return (await this.keyManifest.get())?.keys;\n    } else if (this.cache.allKeys) {\n      return this.cache.allKeys();\n    }\n  }\n\n  /**\n   * Returns the cache key to be used to store the id token\n   * @param clientId The client id used to link to the id token\n   * @returns The constructed cache key, as a string, to store the id token\n   */\n  private getIdTokenCacheKey(clientId: string) {\n    return new CacheKey(\n      { clientId },\n      CACHE_KEY_PREFIX,\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ).toKey();\n  }\n\n  /**\n   * Finds the corresponding key in the cache based on the provided cache key.\n   * The keys inside the cache are in the format {prefix}::{clientId}::{audience}::{scope}.\n   * The first key in the cache that satisfies the following conditions is returned\n   *  - `prefix` is strict equal to Auth0's internally configured `keyPrefix`\n   *  - `clientId` is strict equal to the `cacheKey.clientId`\n   *  - `audience` is strict equal to the `cacheKey.audience`\n   *  - `scope` contains at least all the `cacheKey.scope` values\n   *  *\n   * @param keyToMatch The provided cache key\n   * @param allKeys A list of existing cache keys\n   */\n  private matchExistingCacheKey(keyToMatch: CacheKey, allKeys: Array<string>) {\n    return allKeys.filter(key => {\n      const cacheKey = CacheKey.fromKey(key);\n      const scopeSet = new Set(cacheKey.scope && cacheKey.scope.split(' '));\n      const scopesToMatch = keyToMatch.scope?.split(' ') || [];\n\n      const hasAllScopes =\n        cacheKey.scope &&\n        scopesToMatch.reduce(\n          (acc, current) => acc && scopeSet.has(current),\n          true\n        );\n\n      return (\n        cacheKey.prefix === CACHE_KEY_PREFIX &&\n        cacheKey.clientId === keyToMatch.clientId &&\n        cacheKey.audience === keyToMatch.audience &&\n        hasAllScopes\n      );\n    })[0];\n  }\n}\n", "import { ClientStorage } from './storage';\n\nconst TRANSACTION_STORAGE_KEY_PREFIX = 'a0.spajs.txs';\n\ninterface Transaction {\n  nonce: string;\n  scope: string;\n  audience: string;\n  appState?: any;\n  code_verifier: string;\n  redirect_uri?: string;\n  organization?: string;\n  state?: string;\n}\n\nexport class TransactionManager {\n  private storageKey: string;\n\n  constructor(\n    private storage: ClientStorage,\n    private clientId: string,\n    private cookieDomain?: string\n  ) {\n    this.storageKey = `${TRANSACTION_STORAGE_KEY_PREFIX}.${this.clientId}`;\n  }\n\n  public create(transaction: Transaction) {\n    this.storage.save(this.storageKey, transaction, {\n      daysUntilExpire: 1,\n      cookieDomain: this.cookieDomain\n    });\n  }\n\n  public get(): Transaction | undefined {\n    return this.storage.get(this.storageKey);\n  }\n\n  public remove() {\n    this.storage.remove(this.storageKey, {\n      cookieDomain: this.cookieDomain\n    });\n  }\n}\n", "import { urlDecodeB64 } from './utils';\nimport { IdToken, JWTVerifyOptions } from './global';\n\nconst isNumber = (n: any) => typeof n === 'number';\n\nconst idTokendecoded = [\n  'iss',\n  'aud',\n  'exp',\n  'nbf',\n  'iat',\n  'jti',\n  'azp',\n  'nonce',\n  'auth_time',\n  'at_hash',\n  'c_hash',\n  'acr',\n  'amr',\n  'sub_jwk',\n  'cnf',\n  'sip_from_tag',\n  'sip_date',\n  'sip_callid',\n  'sip_cseq_num',\n  'sip_via_branch',\n  'orig',\n  'dest',\n  'mky',\n  'events',\n  'toe',\n  'txn',\n  'rph',\n  'sid',\n  'vot',\n  'vtm'\n];\n\nexport const decode = (token: string) => {\n  const parts = token.split('.');\n  const [header, payload, signature] = parts;\n\n  if (parts.length !== 3 || !header || !payload || !signature) {\n    throw new Error('ID token could not be decoded');\n  }\n  const payloadJSON = JSON.parse(urlDecodeB64(payload));\n  const claims: IdToken = { __raw: token };\n  const user: any = {};\n  Object.keys(payloadJSON).forEach(k => {\n    claims[k] = payloadJSON[k];\n    if (!idTokendecoded.includes(k)) {\n      user[k] = payloadJSON[k];\n    }\n  });\n  return {\n    encoded: { header, payload, signature },\n    header: JSON.parse(urlDecodeB64(header)),\n    claims,\n    user\n  };\n};\n\nexport const verify = (options: JWTVerifyOptions) => {\n  if (!options.id_token) {\n    throw new Error('ID token is required but missing');\n  }\n\n  const decoded = decode(options.id_token);\n\n  if (!decoded.claims.iss) {\n    throw new Error(\n      'Issuer (iss) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.claims.iss !== options.iss) {\n    throw new Error(\n      `Issuer (iss) claim mismatch in the ID token; expected \"${options.iss}\", found \"${decoded.claims.iss}\"`\n    );\n  }\n\n  if (!decoded.user.sub) {\n    throw new Error(\n      'Subject (sub) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.header.alg !== 'RS256') {\n    throw new Error(\n      `Signature algorithm of \"${decoded.header.alg}\" is not supported. Expected the ID token to be signed with \"RS256\".`\n    );\n  }\n\n  if (\n    !decoded.claims.aud ||\n    !(\n      typeof decoded.claims.aud === 'string' ||\n      Array.isArray(decoded.claims.aud)\n    )\n  ) {\n    throw new Error(\n      'Audience (aud) claim must be a string or array of strings present in the ID token'\n    );\n  }\n  if (Array.isArray(decoded.claims.aud)) {\n    if (!decoded.claims.aud.includes(options.aud)) {\n      throw new Error(\n        `Audience (aud) claim mismatch in the ID token; expected \"${\n          options.aud\n        }\" but was not one of \"${decoded.claims.aud.join(', ')}\"`\n      );\n    }\n    if (decoded.claims.aud.length > 1) {\n      if (!decoded.claims.azp) {\n        throw new Error(\n          'Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values'\n        );\n      }\n      if (decoded.claims.azp !== options.aud) {\n        throw new Error(\n          `Authorized Party (azp) claim mismatch in the ID token; expected \"${options.aud}\", found \"${decoded.claims.azp}\"`\n        );\n      }\n    }\n  } else if (decoded.claims.aud !== options.aud) {\n    throw new Error(\n      `Audience (aud) claim mismatch in the ID token; expected \"${options.aud}\" but found \"${decoded.claims.aud}\"`\n    );\n  }\n  if (options.nonce) {\n    if (!decoded.claims.nonce) {\n      throw new Error(\n        'Nonce (nonce) claim must be a string present in the ID token'\n      );\n    }\n    if (decoded.claims.nonce !== options.nonce) {\n      throw new Error(\n        `Nonce (nonce) claim mismatch in the ID token; expected \"${options.nonce}\", found \"${decoded.claims.nonce}\"`\n      );\n    }\n  }\n\n  if (options.max_age && !isNumber(decoded.claims.auth_time)) {\n    throw new Error(\n      'Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified'\n    );\n  }\n\n  /* c8 ignore next 5 */\n  if (decoded.claims.exp == null || !isNumber(decoded.claims.exp)) {\n    throw new Error(\n      'Expiration Time (exp) claim must be a number present in the ID token'\n    );\n  }\n  if (!isNumber(decoded.claims.iat)) {\n    throw new Error(\n      'Issued At (iat) claim must be a number present in the ID token'\n    );\n  }\n\n  const leeway = options.leeway || 60;\n  const now = new Date(options.now || Date.now());\n  const expDate = new Date(0);\n\n  expDate.setUTCSeconds(decoded.claims.exp + leeway);\n\n  if (now > expDate) {\n    throw new Error(\n      `Expiration Time (exp) claim error in the ID token; current time (${now}) is after expiration time (${expDate})`\n    );\n  }\n\n  if (decoded.claims.nbf != null && isNumber(decoded.claims.nbf)) {\n    const nbfDate = new Date(0);\n    nbfDate.setUTCSeconds(decoded.claims.nbf - leeway);\n    if (now < nbfDate) {\n      throw new Error(\n        `Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${now}) is before ${nbfDate}`\n      );\n    }\n  }\n\n  if (decoded.claims.auth_time != null && isNumber(decoded.claims.auth_time)) {\n    const authTimeDate = new Date(0);\n    authTimeDate.setUTCSeconds(\n      parseInt(decoded.claims.auth_time) + (options.max_age as number) + leeway\n    );\n\n    if (now > authTimeDate) {\n      throw new Error(\n        `Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${now}) is after last auth at ${authTimeDate}`\n      );\n    }\n  }\n\n  if (options.organization) {\n    const org = options.organization.trim();\n    if (org.startsWith('org_')) {\n      const orgId = org;\n      if (!decoded.claims.org_id) {\n        throw new Error(\n          'Organization ID (org_id) claim must be a string present in the ID token'\n        );\n      } else if (orgId !== decoded.claims.org_id) {\n        throw new Error(\n          `Organization ID (org_id) claim mismatch in the ID token; expected \"${orgId}\", found \"${decoded.claims.org_id}\"`\n        );\n      }\n    } else {\n      const orgName = org.toLowerCase();\n      // TODO should we verify if there is an `org_id` claim?\n      if (!decoded.claims.org_name) {\n        throw new Error(\n          'Organization Name (org_name) claim must be a string present in the ID token'\n        );\n      } else if (orgName !== decoded.claims.org_name) {\n        throw new Error(\n          `Organization Name (org_name) claim mismatch in the ID token; expected \"${orgName}\", found \"${decoded.claims.org_name}\"`\n        );\n      }\n    }\n  }\n\n  return decoded;\n};\n", "\"use strict\";\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nexports.__esModule = true;\r\nfunction stringifyAttribute(name, value) {\r\n    if (!value) {\r\n        return '';\r\n    }\r\n    var stringified = '; ' + name;\r\n    if (value === true) {\r\n        return stringified; // boolean attributes shouldn't have a value\r\n    }\r\n    return stringified + '=' + value;\r\n}\r\nfunction stringifyAttributes(attributes) {\r\n    if (typeof attributes.expires === 'number') {\r\n        var expires = new Date();\r\n        expires.setMilliseconds(expires.getMilliseconds() + attributes.expires * 864e+5);\r\n        attributes.expires = expires;\r\n    }\r\n    return stringifyAttribute('Expires', attributes.expires ? attributes.expires.toUTCString() : '')\r\n        + stringifyAttribute('Domain', attributes.domain)\r\n        + stringifyAttribute('Path', attributes.path)\r\n        + stringifyAttribute('Secure', attributes.secure)\r\n        + stringifyAttribute('SameSite', attributes.sameSite);\r\n}\r\nfunction encode(name, value, attributes) {\r\n    return encodeURIComponent(name)\r\n        .replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent) // allowed special characters\r\n        .replace(/\\(/g, '%28').replace(/\\)/g, '%29') // replace opening and closing parens\r\n        + '=' + encodeURIComponent(value)\r\n        // allowed special characters\r\n        .replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent)\r\n        + stringifyAttributes(attributes);\r\n}\r\nexports.encode = encode;\r\nfunction parse(cookieString) {\r\n    var result = {};\r\n    var cookies = cookieString ? cookieString.split('; ') : [];\r\n    var rdecode = /(%[\\dA-F]{2})+/gi;\r\n    for (var i = 0; i < cookies.length; i++) {\r\n        var parts = cookies[i].split('=');\r\n        var cookie = parts.slice(1).join('=');\r\n        if (cookie.charAt(0) === '\"') {\r\n            cookie = cookie.slice(1, -1);\r\n        }\r\n        try {\r\n            var name_1 = parts[0].replace(rdecode, decodeURIComponent);\r\n            result[name_1] = cookie.replace(rdecode, decodeURIComponent);\r\n        }\r\n        catch (e) {\r\n            // ignore cookies with invalid name/value encoding\r\n        }\r\n    }\r\n    return result;\r\n}\r\nexports.parse = parse;\r\nfunction getAll() {\r\n    return parse(document.cookie);\r\n}\r\nexports.getAll = getAll;\r\nfunction get(name) {\r\n    return getAll()[name];\r\n}\r\nexports.get = get;\r\nfunction set(name, value, attributes) {\r\n    document.cookie = encode(name, value, __assign({ path: '/' }, attributes));\r\n}\r\nexports.set = set;\r\nfunction remove(name, attributes) {\r\n    set(name, '', __assign(__assign({}, attributes), { expires: -1 }));\r\n}\r\nexports.remove = remove;\r\n", "import * as Cookies from 'es-cookie';\n\ninterface ClientStorageOptions {\n  daysUntilExpire?: number;\n  cookieDomain?: string;\n}\n\n/**\n * Defines a type that handles storage to/from a storage location\n */\nexport type ClientStorage = {\n  get<T extends Object>(key: string): T | undefined;\n  save(key: string, value: any, options?: ClientStorageOptions): void;\n  remove(key: string, options?: ClientStorageOptions): void;\n};\n\n/**\n * A storage protocol for marshalling data to/from cookies\n */\nexport const CookieStorage = {\n  get<T extends Object>(key: string) {\n    const value = Cookies.get(key);\n\n    if (typeof value === 'undefined') {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = {\n        secure: true,\n        sameSite: 'none'\n      };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(key, JSON.stringify(value), cookieAttributes);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n  }\n} as ClientStorage;\n\n/**\n * @ignore\n */\nconst LEGACY_PREFIX = '_legacy_';\n\n/**\n * Cookie storage that creates a cookie for modern and legacy browsers.\n * See: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\n */\nexport const CookieStorageWithLegacySameSite = {\n  get<T extends Object>(key: string) {\n    const value = CookieStorage.get<T>(key);\n\n    if (value) {\n      return value;\n    }\n\n    return CookieStorage.get<T>(`${LEGACY_PREFIX}${key}`);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = { secure: true };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(\n      `${LEGACY_PREFIX}${key}`,\n      JSON.stringify(value),\n      cookieAttributes\n    );\n    CookieStorage.save(key, value, options);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n    CookieStorage.remove(key, options);\n    CookieStorage.remove(`${LEGACY_PREFIX}${key}`, options);\n  }\n} as ClientStorage;\n\n/**\n * A storage protocol for marshalling data to/from session storage\n */\nexport const SessionStorage = {\n  get<T extends Object>(key: string) {\n    /* c8 ignore next 3 */\n    if (typeof sessionStorage === 'undefined') {\n      return;\n    }\n\n    const value = sessionStorage.getItem(key);\n\n    if (value == null) {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any): void {\n    sessionStorage.setItem(key, JSON.stringify(value));\n  },\n\n  remove(key: string) {\n    sessionStorage.removeItem(key);\n  }\n} as ClientStorage;\n", "const singlePromiseMap: Record<string, Promise<any>> = {};\n\nexport const singlePromise = <T>(\n  cb: () => Promise<T>,\n  key: string\n): Promise<T> => {\n  let promise: null | Promise<T> = singlePromiseMap[key];\n  if (!promise) {\n    promise = cb().finally(() => {\n      delete singlePromiseMap[key];\n      promise = null;\n    });\n    singlePromiseMap[key] = promise;\n  }\n  return promise;\n};\n\nexport const retryPromise = async (\n  cb: () => Promise<boolean>,\n  maxNumberOfRetries = 3\n) => {\n  for (let i = 0; i < maxNumberOfRetries; i++) {\n    if (await cb()) {\n      return true;\n    }\n  }\n\n  return false;\n};\n", "import {\n  CACHE_KEY_PREFIX,\n  ICache,\n  KeyManifestEntry,\n  MaybePromise\n} from './shared';\n\nexport class CacheKeyManifest {\n  private readonly manifestKey: string;\n\n  constructor(private cache: ICache, private clientId: string) {\n    this.manifestKey = this.createManifestKeyFrom(this.clientId);\n  }\n\n  async add(key: string): Promise<void> {\n    const keys = new Set(\n      (await this.cache.get<KeyManifestEntry>(this.manifestKey))?.keys || []\n    );\n\n    keys.add(key);\n\n    await this.cache.set<KeyManifestEntry>(this.manifestKey, {\n      keys: [...keys]\n    });\n  }\n\n  async remove(key: string): Promise<void> {\n    const entry = await this.cache.get<KeyManifestEntry>(this.manifestKey);\n\n    if (entry) {\n      const keys = new Set(entry.keys);\n      keys.delete(key);\n\n      if (keys.size > 0) {\n        return await this.cache.set(this.manifestKey, { keys: [...keys] });\n      }\n\n      return await this.cache.remove(this.manifestKey);\n    }\n  }\n\n  get(): MaybePromise<KeyManifestEntry | undefined> {\n    return this.cache.get<KeyManifestEntry>(this.manifestKey);\n  }\n\n  clear(): MaybePromise<void> {\n    return this.cache.remove(this.manifestKey);\n  }\n\n  private createManifestKeyFrom(clientId: string): string {\n    return `${CACHE_KEY_PREFIX}::${clientId}`;\n  }\n}\n", "import { ICache, InMemoryCache, LocalStorageCache } from './cache';\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  LogoutOptions\n} from './global';\nimport { getUniqueScopes } from './scope';\n\n/**\n * @ignore\n */\nexport const GET_TOKEN_SILENTLY_LOCK_KEY = 'auth0.lock.getTokenSilently';\n\n/**\n * @ignore\n */\nexport const buildOrganizationHintCookieName = (clientId: string) =>\n  `auth0.${clientId}.organization_hint`;\n\n/**\n * @ignore\n */\nexport const OLD_IS_AUTHENTICATED_COOKIE_NAME = 'auth0.is.authenticated';\n\n/**\n * @ignore\n */\nexport const buildIsAuthenticatedCookieName = (clientId: string) =>\n  `auth0.${clientId}.is.authenticated`;\n\n/**\n * @ignore\n */\nconst cacheLocationBuilders: Record<string, () => ICache> = {\n  memory: () => new InMemoryCache().enclosedCache,\n  localstorage: () => new LocalStorageCache()\n};\n\n/**\n * @ignore\n */\nexport const cacheFactory = (location: string) => {\n  return cacheLocationBuilders[location];\n};\n\n/**\n * @ignore\n */\nexport const getAuthorizeParams = (\n  clientOptions: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  },\n  scope: string,\n  authorizationParams: AuthorizationParams,\n  state: string,\n  nonce: string,\n  code_challenge: string,\n  redirect_uri: string | undefined,\n  response_mode: string | undefined\n): AuthorizeOptions => {\n  return {\n    client_id: clientOptions.clientId,\n    ...clientOptions.authorizationParams,\n    ...authorizationParams,\n    scope: getUniqueScopes(scope, authorizationParams.scope),\n    response_type: 'code',\n    response_mode: response_mode || 'query',\n    state,\n    nonce,\n    redirect_uri:\n      redirect_uri || clientOptions.authorizationParams.redirect_uri,\n    code_challenge,\n    code_challenge_method: 'S256'\n  };\n};\n\n/**\n * @ignore\n *\n * Function used to provide support for the deprecated onRedirect through openUrl.\n */\nexport const patchOpenUrlWithOnRedirect = <\n  T extends Pick<LogoutOptions, 'openUrl' | 'onRedirect'>\n>(\n  options: T\n) => {\n  const { openUrl, onRedirect, ...originalOptions } = options;\n\n  const result = {\n    ...originalOptions,\n    openUrl: openUrl === false || openUrl ? openUrl : onRedirect\n  };\n\n  return result as T;\n};\n", "import Lock from 'browser-tabs-lock';\n\nimport {\n  createQuery<PERSON>ara<PERSON>,\n  runPopup,\n  parseAuthenticationResult,\n  encode,\n  createRandomString,\n  runIframe,\n  sha256,\n  bufferToBase64UrlEncoded,\n  validateCrypto,\n  openPopup,\n  getDomain,\n  getTokenIssuer,\n  parseNumber\n} from './utils';\n\nimport { oauthToken } from './api';\n\nimport { getUniqueScopes } from './scope';\n\nimport {\n  InMemoryCache,\n  ICache,\n  <PERSON>ache<PERSON>ey,\n  CacheManager,\n  CacheEntry,\n  IdTokenEntry,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  DecodedToken\n} from './cache';\n\nimport { TransactionManager } from './transaction-manager';\nimport { verify as verifyIdToken } from './jwt';\nimport {\n  AuthenticationError,\n  GenericError,\n  MissingRefreshTokenError,\n  TimeoutError\n} from './errors';\n\nimport {\n  ClientStorage,\n  CookieStorage,\n  CookieStorageWithLegacySameSite,\n  SessionStorage\n} from './storage';\n\nimport {\n  CACHE_LOCATION_MEMORY,\n  DEFAULT_POPUP_CONFIG_OPTIONS,\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  MISSING_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_SCOPE,\n  DEFAULT_SESSION_CHECK_EXPIRY_DAYS,\n  DEFAULT_AUTH0_CLIENT,\n  INVALID_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_NOW_PROVIDER,\n  DEFAULT_FETCH_TIMEOUT_MS\n} from './constants';\n\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  RedirectLoginOptions,\n  PopupLoginOptions,\n  PopupConfigOptions,\n  RedirectLoginResult,\n  GetTokenSilentlyOptions,\n  GetTokenWithPopupOptions,\n  LogoutOptions,\n  CacheLocation,\n  LogoutUrlOptions,\n  User,\n  IdToken,\n  GetTokenSilentlyVerboseResponse,\n  TokenEndpointResponse\n} from './global';\n\n// @ts-ignore\nimport TokenWorker from './worker/token.worker.ts';\nimport { singlePromise, retryPromise } from './promise-utils';\nimport { CacheKeyManifest } from './cache/key-manifest';\nimport {\n  buildIsAuthenticatedCookieName,\n  buildOrganizationHintCookieName,\n  cacheFactory,\n  getAuthorizeParams,\n  GET_TOKEN_SILENTLY_LOCK_KEY,\n  OLD_IS_AUTHENTICATED_COOKIE_NAME,\n  patchOpenUrlWithOnRedirect\n} from './Auth0Client.utils';\nimport { CustomTokenExchangeOptions } from './TokenExchange';\n\n/**\n * @ignore\n */\ntype GetTokenSilentlyResult = TokenEndpointResponse & {\n  decodedToken: ReturnType<typeof verifyIdToken>;\n  scope: string;\n  oauthTokenScope?: string;\n  audience: string;\n};\n\n/**\n * @ignore\n */\nconst lock = new Lock();\n\n/**\n * Auth0 SDK for Single Page Applications using [Authorization Code Grant Flow with PKCE](https://auth0.com/docs/api-auth/tutorials/authorization-code-grant-pkce).\n */\nexport class Auth0Client {\n  private readonly transactionManager: TransactionManager;\n  private readonly cacheManager: CacheManager;\n  private readonly domainUrl: string;\n  private readonly tokenIssuer: string;\n  private readonly scope: string;\n  private readonly cookieStorage: ClientStorage;\n  private readonly sessionCheckExpiryDays: number;\n  private readonly orgHintCookieName: string;\n  private readonly isAuthenticatedCookieName: string;\n  private readonly nowProvider: () => number | Promise<number>;\n  private readonly httpTimeoutMs: number;\n  private readonly options: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  };\n  private readonly userCache: ICache = new InMemoryCache().enclosedCache;\n\n  private worker?: Worker;\n\n  private readonly defaultOptions: Partial<Auth0ClientOptions> = {\n    authorizationParams: {\n      scope: DEFAULT_SCOPE\n    },\n    useRefreshTokensFallback: false,\n    useFormData: true\n  };\n\n  constructor(options: Auth0ClientOptions) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options,\n      authorizationParams: {\n        ...this.defaultOptions.authorizationParams,\n        ...options.authorizationParams\n      }\n    };\n\n    typeof window !== 'undefined' && validateCrypto();\n\n    if (options.cache && options.cacheLocation) {\n      console.warn(\n        'Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`.'\n      );\n    }\n\n    let cacheLocation: CacheLocation | undefined;\n    let cache: ICache;\n\n    if (options.cache) {\n      cache = options.cache;\n    } else {\n      cacheLocation = options.cacheLocation || CACHE_LOCATION_MEMORY;\n\n      if (!cacheFactory(cacheLocation)) {\n        throw new Error(`Invalid cache location \"${cacheLocation}\"`);\n      }\n\n      cache = cacheFactory(cacheLocation)();\n    }\n\n    this.httpTimeoutMs = options.httpTimeoutInSeconds\n      ? options.httpTimeoutInSeconds * 1000\n      : DEFAULT_FETCH_TIMEOUT_MS;\n\n    this.cookieStorage =\n      options.legacySameSiteCookie === false\n        ? CookieStorage\n        : CookieStorageWithLegacySameSite;\n\n    this.orgHintCookieName = buildOrganizationHintCookieName(\n      this.options.clientId\n    );\n\n    this.isAuthenticatedCookieName = buildIsAuthenticatedCookieName(\n      this.options.clientId\n    );\n\n    this.sessionCheckExpiryDays =\n      options.sessionCheckExpiryDays || DEFAULT_SESSION_CHECK_EXPIRY_DAYS;\n\n    const transactionStorage = options.useCookiesForTransactions\n      ? this.cookieStorage\n      : SessionStorage;\n\n    // Construct the scopes based on the following:\n    // 1. Always include `openid`\n    // 2. Include the scopes provided in `authorizationParams. This defaults to `profile email`\n    // 3. Add `offline_access` if `useRefreshTokens` is enabled\n    this.scope = getUniqueScopes(\n      'openid',\n      this.options.authorizationParams.scope,\n      this.options.useRefreshTokens ? 'offline_access' : ''\n    );\n\n    this.transactionManager = new TransactionManager(\n      transactionStorage,\n      this.options.clientId,\n      this.options.cookieDomain\n    );\n\n    this.nowProvider = this.options.nowProvider || DEFAULT_NOW_PROVIDER;\n\n    this.cacheManager = new CacheManager(\n      cache,\n      !cache.allKeys\n        ? new CacheKeyManifest(cache, this.options.clientId)\n        : undefined,\n      this.nowProvider\n    );\n\n    this.domainUrl = getDomain(this.options.domain);\n    this.tokenIssuer = getTokenIssuer(this.options.issuer, this.domainUrl);\n\n    // Don't use web workers unless using refresh tokens in memory\n    if (\n      typeof window !== 'undefined' &&\n      window.Worker &&\n      this.options.useRefreshTokens &&\n      cacheLocation === CACHE_LOCATION_MEMORY\n    ) {\n      if (this.options.workerUrl) {\n        this.worker = new Worker(this.options.workerUrl);\n      } else {\n        this.worker = new TokenWorker();\n      }\n    }\n  }\n\n  private _url(path: string) {\n    const auth0Client = encodeURIComponent(\n      btoa(JSON.stringify(this.options.auth0Client || DEFAULT_AUTH0_CLIENT))\n    );\n    return `${this.domainUrl}${path}&auth0Client=${auth0Client}`;\n  }\n\n  private _authorizeUrl(authorizeOptions: AuthorizeOptions) {\n    return this._url(`/authorize?${createQueryParams(authorizeOptions)}`);\n  }\n\n  private async _verifyIdToken(\n    id_token: string,\n    nonce?: string,\n    organization?: string\n  ) {\n    const now = await this.nowProvider();\n\n    return verifyIdToken({\n      iss: this.tokenIssuer,\n      aud: this.options.clientId,\n      id_token,\n      nonce,\n      organization,\n      leeway: this.options.leeway,\n      max_age: parseNumber(this.options.authorizationParams.max_age),\n      now\n    });\n  }\n\n  private _processOrgHint(organization?: string) {\n    if (organization) {\n      this.cookieStorage.save(this.orgHintCookieName, organization, {\n        daysUntilExpire: this.sessionCheckExpiryDays,\n        cookieDomain: this.options.cookieDomain\n      });\n    } else {\n      this.cookieStorage.remove(this.orgHintCookieName, {\n        cookieDomain: this.options.cookieDomain\n      });\n    }\n  }\n\n  private async _prepareAuthorizeUrl(\n    authorizationParams: AuthorizationParams,\n    authorizeOptions?: Partial<AuthorizeOptions>,\n    fallbackRedirectUri?: string\n  ): Promise<{\n    scope: string;\n    audience: string;\n    redirect_uri?: string;\n    nonce: string;\n    code_verifier: string;\n    state: string;\n    url: string;\n  }> {\n    const state = encode(createRandomString());\n    const nonce = encode(createRandomString());\n    const code_verifier = createRandomString();\n    const code_challengeBuffer = await sha256(code_verifier);\n    const code_challenge = bufferToBase64UrlEncoded(code_challengeBuffer);\n\n    const params = getAuthorizeParams(\n      this.options,\n      this.scope,\n      authorizationParams,\n      state,\n      nonce,\n      code_challenge,\n      authorizationParams.redirect_uri ||\n        this.options.authorizationParams.redirect_uri ||\n        fallbackRedirectUri,\n      authorizeOptions?.response_mode\n    );\n\n    const url = this._authorizeUrl(params);\n\n    return {\n      nonce,\n      code_verifier,\n      scope: params.scope,\n      audience: params.audience || 'default',\n      redirect_uri: params.redirect_uri,\n      state,\n      url\n    };\n  }\n\n  /**\n   * ```js\n   * try {\n   *  await auth0.loginWithPopup(options);\n   * } catch(e) {\n   *  if (e instanceof PopupCancelledError) {\n   *    // Popup was closed before login completed\n   *  }\n   * }\n   * ```\n   *\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * IMPORTANT: This method has to be called from an event handler\n   * that was started by the user like a button click, for example,\n   * otherwise the popup will be blocked in most browsers.\n   *\n   * @param options\n   * @param config\n   */\n  public async loginWithPopup(\n    options?: PopupLoginOptions,\n    config?: PopupConfigOptions\n  ) {\n    options = options || {};\n    config = config || {};\n\n    if (!config.popup) {\n      config.popup = openPopup('');\n\n      if (!config.popup) {\n        throw new Error(\n          'Unable to open a popup for loginWithPopup - window.open returned `null`'\n        );\n      }\n    }\n\n    const params = await this._prepareAuthorizeUrl(\n      options.authorizationParams || {},\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    config.popup.location.href = params.url;\n\n    const codeResult = await runPopup({\n      ...config,\n      timeoutInSeconds:\n        config.timeoutInSeconds ||\n        this.options.authorizeTimeoutInSeconds ||\n        DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n    });\n\n    if (params.state !== codeResult.state) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization =\n      options.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    await this._requestToken(\n      {\n        audience: params.audience,\n        scope: params.scope,\n        code_verifier: params.code_verifier,\n        grant_type: 'authorization_code',\n        code: codeResult.code as string,\n        redirect_uri: params.redirect_uri\n      },\n      {\n        nonceIn: params.nonce,\n        organization\n      }\n    );\n  }\n\n  /**\n   * ```js\n   * const user = await auth0.getUser();\n   * ```\n   *\n   * Returns the user information if available (decoded\n   * from the `id_token`).\n   *\n   * @typeparam TUser The type to return, has to extend {@link User}.\n   */\n  public async getUser<TUser extends User>(): Promise<TUser | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.user as TUser;\n  }\n\n  /**\n   * ```js\n   * const claims = await auth0.getIdTokenClaims();\n   * ```\n   *\n   * Returns all claims from the id_token if available.\n   */\n  public async getIdTokenClaims(): Promise<IdToken | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.claims;\n  }\n\n  /**\n   * ```js\n   * await auth0.loginWithRedirect(options);\n   * ```\n   *\n   * Performs a redirect to `/authorize` using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated.\n   *\n   * @param options\n   */\n  public async loginWithRedirect<TAppState = any>(\n    options: RedirectLoginOptions<TAppState> = {}\n  ) {\n    const { openUrl, fragment, appState, ...urlOptions } =\n      patchOpenUrlWithOnRedirect(options);\n\n    const organization =\n      urlOptions.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    const { url, ...transaction } = await this._prepareAuthorizeUrl(\n      urlOptions.authorizationParams || {}\n    );\n\n    this.transactionManager.create({\n      ...transaction,\n      appState,\n      ...(organization && { organization })\n    });\n\n    const urlWithFragment = fragment ? `${url}#${fragment}` : url;\n\n    if (openUrl) {\n      await openUrl(urlWithFragment);\n    } else {\n      window.location.assign(urlWithFragment);\n    }\n  }\n\n  /**\n   * After the browser redirects back to the callback page,\n   * call `handleRedirectCallback` to handle success and error\n   * responses from Auth0. If the response is successful, results\n   * will be valid according to their expiration times.\n   */\n  public async handleRedirectCallback<TAppState = any>(\n    url: string = window.location.href\n  ): Promise<RedirectLoginResult<TAppState>> {\n    const queryStringFragments = url.split('?').slice(1);\n\n    if (queryStringFragments.length === 0) {\n      throw new Error('There are no query params available for parsing.');\n    }\n\n    const { state, code, error, error_description } = parseAuthenticationResult(\n      queryStringFragments.join('')\n    );\n\n    const transaction = this.transactionManager.get();\n\n    if (!transaction) {\n      throw new GenericError('missing_transaction', 'Invalid state');\n    }\n\n    this.transactionManager.remove();\n\n    if (error) {\n      throw new AuthenticationError(\n        error,\n        error_description || error,\n        state,\n        transaction.appState\n      );\n    }\n\n    // Transaction should have a `code_verifier` to do PKCE for CSRF protection\n    if (\n      !transaction.code_verifier ||\n      (transaction.state && transaction.state !== state)\n    ) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization = transaction.organization;\n    const nonceIn = transaction.nonce;\n    const redirect_uri = transaction.redirect_uri;\n\n    await this._requestToken(\n      {\n        audience: transaction.audience,\n        scope: transaction.scope,\n        code_verifier: transaction.code_verifier,\n        grant_type: 'authorization_code',\n        code: code as string,\n        ...(redirect_uri ? { redirect_uri } : {})\n      },\n      { nonceIn, organization }\n    );\n\n    return {\n      appState: transaction.appState\n    };\n  }\n\n  /**\n   * ```js\n   * await auth0.checkSession();\n   * ```\n   *\n   * Check if the user is logged in using `getTokenSilently`. The difference\n   * with `getTokenSilently` is that this doesn't return a token, but it will\n   * pre-fill the token cache.\n   *\n   * This method also heeds the `auth0.{clientId}.is.authenticated` cookie, as an optimization\n   *  to prevent calling Auth0 unnecessarily. If the cookie is not present because\n   * there was no previous login (or it has expired) then tokens will not be refreshed.\n   *\n   * It should be used for silently logging in the user when you instantiate the\n   * `Auth0Client` constructor. You should not need this if you are using the\n   * `createAuth0Client` factory.\n   *\n   * **Note:** the cookie **may not** be present if running an app using a private tab, as some\n   * browsers clear JS cookie data and local storage when the tab or page is closed, or on page reload. This effectively\n   * means that `checkSession` could silently return without authenticating the user on page refresh when\n   * using a private tab, despite having previously logged in. As a workaround, use `getTokenSilently` instead\n   * and handle the possible `login_required` error [as shown in the readme](https://github.com/auth0/auth0-spa-js#creating-the-client).\n   *\n   * @param options\n   */\n  public async checkSession(options?: GetTokenSilentlyOptions) {\n    if (!this.cookieStorage.get(this.isAuthenticatedCookieName)) {\n      if (!this.cookieStorage.get(OLD_IS_AUTHENTICATED_COOKIE_NAME)) {\n        return;\n      } else {\n        // Migrate the existing cookie to the new name scoped by client ID\n        this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n          daysUntilExpire: this.sessionCheckExpiryDays,\n          cookieDomain: this.options.cookieDomain\n        });\n\n        this.cookieStorage.remove(OLD_IS_AUTHENTICATED_COOKIE_NAME);\n      }\n    }\n\n    try {\n      await this.getTokenSilently(options);\n    } catch (_) {}\n  }\n\n  /**\n   * Fetches a new access token and returns the response from the /oauth/token endpoint, omitting the refresh token.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions & { detailedResponse: true }\n  ): Promise<GetTokenSilentlyVerboseResponse>;\n\n  /**\n   * Fetches a new access token and returns it.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options?: GetTokenSilentlyOptions\n  ): Promise<string>;\n\n  /**\n   * Fetches a new access token, and either returns just the access token (the default) or the response from the /oauth/token endpoint, depending on the `detailedResponse` option.\n   *\n   * ```js\n   * const token = await auth0.getTokenSilently(options);\n   * ```\n   *\n   * If there's a valid token stored and it has more than 60 seconds\n   * remaining before expiration, return the token. Otherwise, attempt\n   * to obtain a new token.\n   *\n   * A new token will be obtained either by opening an iframe or a\n   * refresh token (if `useRefreshTokens` is `true`).\n\n   * If iframes are used, opens an iframe with the `/authorize` URL\n   * using the parameters provided as arguments. Random and secure `state`\n   * and `nonce` parameters will be auto-generated. If the response is successful,\n   * results will be validated according to their expiration times.\n   *\n   * If refresh tokens are used, the token endpoint is called directly with the\n   * 'refresh_token' grant. If no refresh token is available to make this call,\n   * the SDK will only fall back to using an iframe to the '/authorize' URL if \n   * the `useRefreshTokensFallback` setting has been set to `true`. By default this\n   * setting is `false`.\n   *\n   * This method may use a web worker to perform the token call if the in-memory\n   * cache is used.\n   *\n   * If an `audience` value is given to this function, the SDK always falls\n   * back to using an iframe to make the token exchange.\n   *\n   * Note that in all cases, falling back to an iframe requires access to\n   * the `auth0` cookie.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions = {}\n  ): Promise<undefined | string | GetTokenSilentlyVerboseResponse> {\n    const localOptions: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    } = {\n      cacheMode: 'on',\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    const result = await singlePromise(\n      () => this._getTokenSilently(localOptions),\n      `${this.options.clientId}::${localOptions.authorizationParams.audience}::${localOptions.authorizationParams.scope}`\n    );\n\n    return options.detailedResponse ? result : result?.access_token;\n  }\n\n  private async _getTokenSilently(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const { cacheMode, ...getTokenOptions } = options;\n\n    // Check the cache before acquiring the lock to avoid the latency of\n    // `lock.acquireLock` when the cache is populated.\n    if (cacheMode !== 'off') {\n      const entry = await this._getEntryFromCache({\n        scope: getTokenOptions.authorizationParams.scope,\n        audience: getTokenOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      });\n\n      if (entry) {\n        return entry;\n      }\n    }\n\n    if (cacheMode === 'cache-only') {\n      return;\n    }\n\n    if (\n      await retryPromise(\n        () => lock.acquireLock(GET_TOKEN_SILENTLY_LOCK_KEY, 5000),\n        10\n      )\n    ) {\n      try {\n        window.addEventListener('pagehide', this._releaseLockOnPageHide);\n\n        // Check the cache a second time, because it may have been populated\n        // by a previous call while this call was waiting to acquire the lock.\n        if (cacheMode !== 'off') {\n          const entry = await this._getEntryFromCache({\n            scope: getTokenOptions.authorizationParams.scope,\n            audience: getTokenOptions.authorizationParams.audience || 'default',\n            clientId: this.options.clientId\n          });\n\n          if (entry) {\n            return entry;\n          }\n        }\n\n        const authResult = this.options.useRefreshTokens\n          ? await this._getTokenUsingRefreshToken(getTokenOptions)\n          : await this._getTokenFromIFrame(getTokenOptions);\n\n        const { id_token, access_token, oauthTokenScope, expires_in } =\n          authResult;\n\n        return {\n          id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        };\n      } finally {\n        await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n        window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n      }\n    } else {\n      throw new TimeoutError();\n    }\n  }\n\n  /**\n   * ```js\n   * const token = await auth0.getTokenWithPopup(options);\n   * ```\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * @param options\n   * @param config\n   */\n  public async getTokenWithPopup(\n    options: GetTokenWithPopupOptions = {},\n    config: PopupConfigOptions = {}\n  ) {\n    const localOptions = {\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    config = {\n      ...DEFAULT_POPUP_CONFIG_OPTIONS,\n      ...config\n    };\n\n    await this.loginWithPopup(localOptions, config);\n\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: localOptions.authorizationParams.scope,\n        audience: localOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    return cache!.access_token;\n  }\n\n  /**\n   * ```js\n   * const isAuthenticated = await auth0.isAuthenticated();\n   * ```\n   *\n   * Returns `true` if there's valid information stored,\n   * otherwise returns `false`.\n   *\n   */\n  public async isAuthenticated() {\n    const user = await this.getUser();\n    return !!user;\n  }\n\n  /**\n   * ```js\n   * await auth0.buildLogoutUrl(options);\n   * ```\n   *\n   * Builds a URL to the logout endpoint using the parameters provided as arguments.\n   * @param options\n   */\n  private _buildLogoutUrl(options: LogoutUrlOptions): string {\n    if (options.clientId !== null) {\n      options.clientId = options.clientId || this.options.clientId;\n    } else {\n      delete options.clientId;\n    }\n\n    const { federated, ...logoutOptions } = options.logoutParams || {};\n    const federatedQuery = federated ? `&federated` : '';\n    const url = this._url(\n      `/v2/logout?${createQueryParams({\n        clientId: options.clientId,\n        ...logoutOptions\n      })}`\n    );\n\n    return url + federatedQuery;\n  }\n\n  /**\n   * ```js\n   * await auth0.logout(options);\n   * ```\n   *\n   * Clears the application session and performs a redirect to `/v2/logout`, using\n   * the parameters provided as arguments, to clear the Auth0 session.\n   *\n   * If the `federated` option is specified it also clears the Identity Provider session.\n   * [Read more about how Logout works at Auth0](https://auth0.com/docs/logout).\n   *\n   * @param options\n   */\n  public async logout(options: LogoutOptions = {}): Promise<void> {\n    const { openUrl, ...logoutOptions } = patchOpenUrlWithOnRedirect(options);\n\n    if (options.clientId === null) {\n      await this.cacheManager.clear();\n    } else {\n      await this.cacheManager.clear(options.clientId || this.options.clientId);\n    }\n\n    this.cookieStorage.remove(this.orgHintCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.cookieStorage.remove(this.isAuthenticatedCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.userCache.remove(CACHE_KEY_ID_TOKEN_SUFFIX);\n\n    const url = this._buildLogoutUrl(logoutOptions);\n\n    if (openUrl) {\n      await openUrl(url);\n    } else if (openUrl !== false) {\n      window.location.assign(url);\n    }\n  }\n\n  private async _getTokenFromIFrame(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const params: AuthorizationParams & { scope: string } = {\n      ...options.authorizationParams,\n      prompt: 'none'\n    };\n\n    const orgHint = this.cookieStorage.get<string>(this.orgHintCookieName);\n\n    if (orgHint && !params.organization) {\n      params.organization = orgHint;\n    }\n\n    const {\n      url,\n      state: stateIn,\n      nonce: nonceIn,\n      code_verifier,\n      redirect_uri,\n      scope,\n      audience\n    } = await this._prepareAuthorizeUrl(\n      params,\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    try {\n      // When a browser is running in a Cross-Origin Isolated context, using iframes is not possible.\n      // It doesn't throw an error but times out instead, so we should exit early and inform the user about the reason.\n      // https://developer.mozilla.org/en-US/docs/Web/API/crossOriginIsolated\n      if ((window as any).crossOriginIsolated) {\n        throw new GenericError(\n          'login_required',\n          'The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.'\n        );\n      }\n\n      const authorizeTimeout =\n        options.timeoutInSeconds || this.options.authorizeTimeoutInSeconds;\n\n      const codeResult = await runIframe(url, this.domainUrl, authorizeTimeout);\n\n      if (stateIn !== codeResult.state) {\n        throw new GenericError('state_mismatch', 'Invalid state');\n      }\n\n      const tokenResult = await this._requestToken(\n        {\n          ...options.authorizationParams,\n          code_verifier,\n          code: codeResult.code as string,\n          grant_type: 'authorization_code',\n          redirect_uri,\n          timeout: options.authorizationParams.timeout || this.httpTimeoutMs\n        },\n        {\n          nonceIn,\n          organization: params.organization\n        }\n      );\n\n      return {\n        ...tokenResult,\n        scope: scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: audience\n      };\n    } catch (e) {\n      if (e.error === 'login_required') {\n        this.logout({\n          openUrl: false\n        });\n      }\n      throw e;\n    }\n  }\n\n  private async _getTokenUsingRefreshToken(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: options.authorizationParams.scope,\n        audience: options.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    // If you don't have a refresh token in memory\n    // and you don't have a refresh token in web worker memory\n    // and useRefreshTokensFallback was explicitly enabled\n    // fallback to an iframe\n    if ((!cache || !cache.refresh_token) && !this.worker) {\n      if (this.options.useRefreshTokensFallback) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw new MissingRefreshTokenError(\n        options.authorizationParams.audience || 'default',\n        options.authorizationParams.scope\n      );\n    }\n\n    const redirect_uri =\n      options.authorizationParams.redirect_uri ||\n      this.options.authorizationParams.redirect_uri ||\n      window.location.origin;\n\n    const timeout =\n      typeof options.timeoutInSeconds === 'number'\n        ? options.timeoutInSeconds * 1000\n        : null;\n\n    try {\n      const tokenResult = await this._requestToken({\n        ...options.authorizationParams,\n        grant_type: 'refresh_token',\n        refresh_token: cache && cache.refresh_token,\n        redirect_uri,\n        ...(timeout && { timeout })\n      });\n\n      return {\n        ...tokenResult,\n        scope: options.authorizationParams.scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: options.authorizationParams.audience || 'default'\n      };\n    } catch (e) {\n      if (\n        // The web worker didn't have a refresh token in memory so\n        // fallback to an iframe.\n        (e.message.indexOf(MISSING_REFRESH_TOKEN_ERROR_MESSAGE) > -1 ||\n          // A refresh token was found, but is it no longer valid\n          // and useRefreshTokensFallback is explicitly enabled. Fallback to an iframe.\n          (e.message &&\n            e.message.indexOf(INVALID_REFRESH_TOKEN_ERROR_MESSAGE) > -1)) &&\n        this.options.useRefreshTokensFallback\n      ) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw e;\n    }\n  }\n\n  private async _saveEntryInCache(\n    entry: CacheEntry & { id_token: string; decodedToken: DecodedToken }\n  ) {\n    const { id_token, decodedToken, ...entryWithoutIdToken } = entry;\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, {\n      id_token,\n      decodedToken\n    });\n\n    await this.cacheManager.setIdToken(\n      this.options.clientId,\n      entry.id_token,\n      entry.decodedToken\n    );\n\n    await this.cacheManager.set(entryWithoutIdToken);\n  }\n\n  private async _getIdTokenFromCache() {\n    const audience = this.options.authorizationParams.audience || 'default';\n\n    const cache = await this.cacheManager.getIdToken(\n      new CacheKey({\n        clientId: this.options.clientId,\n        audience,\n        scope: this.scope\n      })\n    );\n\n    const currentCache = this.userCache.get<IdTokenEntry>(\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ) as IdTokenEntry;\n\n    // If the id_token in the cache matches the value we previously cached in memory return the in-memory\n    // value so that object comparison will work\n    if (cache && cache.id_token === currentCache?.id_token) {\n      return currentCache;\n    }\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, cache);\n    return cache;\n  }\n\n  private async _getEntryFromCache({\n    scope,\n    audience,\n    clientId\n  }: {\n    scope: string;\n    audience: string;\n    clientId: string;\n  }): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const entry = await this.cacheManager.get(\n      new CacheKey({\n        scope,\n        audience,\n        clientId\n      }),\n      60 // get a new token if within 60 seconds of expiring\n    );\n\n    if (entry && entry.access_token) {\n      const { access_token, oauthTokenScope, expires_in } = entry as CacheEntry;\n      const cache = await this._getIdTokenFromCache();\n      return (\n        cache && {\n          id_token: cache.id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        }\n      );\n    }\n  }\n\n  /**\n   * Releases any lock acquired by the current page that's not released yet\n   *\n   * Get's called on the `pagehide` event.\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/pagehide_event\n   */\n  private _releaseLockOnPageHide = async () => {\n    await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n\n    window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n  };\n\n  private async _requestToken(\n    options:\n      | PKCERequestTokenOptions\n      | RefreshTokenRequestTokenOptions\n      | TokenExchangeRequestOptions,\n    additionalParameters?: RequestTokenAdditionalParameters\n  ) {\n    const { nonceIn, organization } = additionalParameters || {};\n    const authResult = await oauthToken(\n      {\n        baseUrl: this.domainUrl,\n        client_id: this.options.clientId,\n        auth0Client: this.options.auth0Client,\n        useFormData: this.options.useFormData,\n        timeout: this.httpTimeoutMs,\n        ...options\n      },\n      this.worker\n    );\n\n    const decodedToken = await this._verifyIdToken(\n      authResult.id_token,\n      nonceIn,\n      organization\n    );\n\n    await this._saveEntryInCache({\n      ...authResult,\n      decodedToken,\n      scope: options.scope,\n      audience: options.audience || 'default',\n      ...(authResult.scope ? { oauthTokenScope: authResult.scope } : null),\n      client_id: this.options.clientId\n    });\n\n    this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n      daysUntilExpire: this.sessionCheckExpiryDays,\n      cookieDomain: this.options.cookieDomain\n    });\n\n    this._processOrgHint(organization || decodedToken.claims.org_id);\n\n    return { ...authResult, decodedToken };\n  }\n\n  /*\n  Custom Token Exchange\n  * **Implementation Notes:**\n  * - Ensure that the `subject_token` provided has been securely obtained and is valid according\n  *   to your external identity provider's policies before invoking this function.\n  * - The function leverages internal helper methods:\n  *   - `validateTokenType` confirms that the `subject_token_type` is supported.\n  *   - `getUniqueScopes` merges and de-duplicates scopes between the provided options and\n  *     the instance's default scopes.\n  *   - `_requestToken` performs the actual HTTP request to the token endpoint.\n  */\n\n  /**\n   * Exchanges an external subject token for an Auth0 token via a token exchange request.\n   *\n   * @param {CustomTokenExchangeOptions} options - The options required to perform the token exchange.\n   *\n   * @returns {Promise<TokenEndpointResponse>} A promise that resolves to the token endpoint response,\n   * which contains the issued Auth0 tokens.\n   *\n   * This method implements the token exchange grant as specified in RFC 8693 by first validating\n   * the provided subject token type and then constructing a token request to the /oauth/token endpoint.\n   * The request includes the following parameters:\n   *\n   * - `grant_type`: Hard-coded to \"urn:ietf:params:oauth:grant-type:token-exchange\".\n   * - `subject_token`: The external token provided via the options.\n   * - `subject_token_type`: The type of the external token (validated by this function).\n   * - `scope`: A unique set of scopes, generated by merging the scopes supplied in the options\n   *            with the SDK’s default scopes.\n   * - `audience`: The target audience, as determined by the SDK's authorization configuration.\n   *\n   * **Example Usage:**\n   *\n   * ```\n   * // Define the token exchange options\n   * const options: CustomTokenExchangeOptions = {\n   *   subject_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6Ikp...',\n   *   subject_token_type: 'urn:acme:legacy-system-token',\n   *   scope: ['openid', 'profile']\n   * };\n   *\n   * // Exchange the external token for Auth0 tokens\n   * try {\n   *   const tokenResponse = await instance.exchangeToken(options);\n   *   console.log('Token response:', tokenResponse);\n   * } catch (error) {\n   *   console.error('Token exchange failed:', error);\n   * }\n   * ```\n   */\n  async exchangeToken(\n    options: CustomTokenExchangeOptions\n  ): Promise<TokenEndpointResponse> {\n    return this._requestToken({\n      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',\n      subject_token: options.subject_token,\n      subject_token_type: options.subject_token_type,\n      scope: getUniqueScopes(options.scope, this.scope),\n      audience: this.options.authorizationParams.audience\n    });\n  }\n}\n\ninterface BaseRequestTokenOptions {\n  audience?: string;\n  scope: string;\n  timeout?: number;\n  redirect_uri?: string;\n}\n\ninterface PKCERequestTokenOptions extends BaseRequestTokenOptions {\n  code: string;\n  grant_type: 'authorization_code';\n  code_verifier: string;\n}\n\ninterface RefreshTokenRequestTokenOptions extends BaseRequestTokenOptions {\n  grant_type: 'refresh_token';\n  refresh_token?: string;\n}\n\ninterface TokenExchangeRequestOptions extends BaseRequestTokenOptions {\n  grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange';\n  subject_token: string;\n  subject_token_type: string;\n  actor_token?: string;\n  actor_token_type?: string;\n}\n\ninterface RequestTokenAdditionalParameters {\n  nonceIn?: string;\n  organization?: string;\n}\n", "import { ICache } from './cache';\n\nexport interface AuthorizationParams {\n  /**\n   * - `'page'`: displays the UI with a full page view\n   * - `'popup'`: displays the UI with a popup window\n   * - `'touch'`: displays the UI in a way that leverages a touch interface\n   * - `'wap'`: displays the UI with a \"feature phone\" type interface\n   */\n  display?: 'page' | 'popup' | 'touch' | 'wap';\n\n  /**\n   * - `'none'`: do not prompt user for login or consent on reauthentication\n   * - `'login'`: prompt user for reauthentication\n   * - `'consent'`: prompt user for consent before processing request\n   * - `'select_account'`: prompt user to select an account\n   */\n  prompt?: 'none' | 'login' | 'consent' | 'select_account';\n\n  /**\n   * Maximum allowable elapsed time (in seconds) since authentication.\n   * If the last time the user authenticated is greater than this value,\n   * the user must be reauthenticated.\n   */\n  max_age?: string | number;\n\n  /**\n   * The space-separated list of language tags, ordered by preference.\n   * For example: `'fr-CA fr en'`.\n   */\n  ui_locales?: string;\n\n  /**\n   * Previously issued ID Token.\n   */\n  id_token_hint?: string;\n\n  /**\n   * Provides a hint to Auth0 as to what flow should be displayed.\n   * The default behavior is to show a login page but you can override\n   * this by passing 'signup' to show the signup page instead.\n   *\n   * This only affects the New Universal Login Experience.\n   */\n  screen_hint?: 'signup' | 'login' | string;\n\n  /**\n   * The user's email address or other identifier. When your app knows\n   * which user is trying to authenticate, you can provide this parameter\n   * to pre-fill the email box or select the right session for sign-in.\n   *\n   * This currently only affects the classic Lock experience.\n   */\n  login_hint?: string;\n\n  acr_values?: string;\n\n  /**\n   * The default scope to be used on authentication requests.\n   *\n   * This defaults to `profile email` if not set. If you are setting extra scopes and require\n   * `profile` and `email` to be included then you must include them in the provided scope.\n   *\n   * Note: The `openid` scope is **always applied** regardless of this setting.\n   */\n  scope?: string;\n\n  /**\n   * The default audience to be used for requesting API access.\n   */\n  audience?: string;\n\n  /**\n   * The name of the connection configured for your application.\n   * If null, it will redirect to the Auth0 Login Page and show\n   * the Login Widget.\n   */\n  connection?: string;\n\n  /**\n   * The organization to log in to.\n   *\n   * This will specify an `organization` parameter in your user's login request.\n   *\n   * - If you provide an Organization ID (a string with the prefix `org_`), it will be validated against the `org_id` claim of your user's ID Token. The validation is case-sensitive.\n   * - If you provide an Organization Name (a string *without* the prefix `org_`), it will be validated against the `org_name` claim of your user's ID Token. The validation is case-insensitive.\n   *\n   */\n  organization?: string;\n\n  /**\n   * The Id of an invitation to accept. This is available from the user invitation URL that is given when participating in a user invitation flow.\n   */\n  invitation?: string;\n\n  /**\n   * The default URL where Auth0 will redirect your browser to with\n   * the authentication result. It must be whitelisted in\n   * the \"Allowed Callback URLs\" field in your Auth0 Application's\n   * settings. If not provided here, it should be provided in the other\n   * methods that provide authentication.\n   */\n  redirect_uri?: string;\n\n  /**\n   * If you need to send custom parameters to the Authorization Server,\n   * make sure to use the original parameter name.\n   */\n  [key: string]: any;\n}\n\ninterface BaseLoginOptions {\n  /**\n   * URL parameters that will be sent back to the Authorization Server. This can be known parameters\n   * defined by Auth0 or custom parameters that you define.\n   */\n  authorizationParams?: AuthorizationParams;\n}\n\nexport interface Auth0ClientOptions extends BaseLoginOptions {\n  /**\n   * Your Auth0 account domain such as `'example.auth0.com'`,\n   * `'example.eu.auth0.com'` or , `'example.mycompany.com'`\n   * (when using [custom domains](https://auth0.com/docs/custom-domains))\n   */\n  domain: string;\n  /**\n   * The issuer to be used for validation of JWTs, optionally defaults to the domain above\n   */\n  issuer?: string;\n  /**\n   * The Client ID found on your Application settings page\n   */\n  clientId: string;\n  /**\n   * The value in seconds used to account for clock skew in JWT expirations.\n   * Typically, this value is no more than a minute or two at maximum.\n   * Defaults to 60s.\n   */\n  leeway?: number;\n\n  /**\n   * The location to use when storing cache data. Valid values are `memory` or `localstorage`.\n   * The default setting is `memory`.\n   *\n   * Read more about [changing storage options in the Auth0 docs](https://auth0.com/docs/libraries/auth0-single-page-app-sdk#change-storage-options)\n   */\n  cacheLocation?: CacheLocation;\n\n  /**\n   * Specify a custom cache implementation to use for token storage and retrieval. This setting takes precedence over `cacheLocation` if they are both specified.\n   */\n  cache?: ICache;\n\n  /**\n   * If true, refresh tokens are used to fetch new access tokens from the Auth0 server. If false, the legacy technique of using a hidden iframe and the `authorization_code` grant with `prompt=none` is used.\n   * The default setting is `false`.\n   *\n   * **Note**: Use of refresh tokens must be enabled by an administrator on your Auth0 client application.\n   */\n  useRefreshTokens?: boolean;\n\n  /**\n   * If true, fallback to the technique of using a hidden iframe and the `authorization_code` grant with `prompt=none` when unable to use refresh tokens. If false, the iframe fallback is not used and\n   * errors relating to a failed `refresh_token` grant should be handled appropriately. The default setting is `false`.\n   *\n   * **Note**: There might be situations where doing silent auth with a Web Message response from an iframe is not possible,\n   * like when you're serving your application from the file system or a custom protocol (like in a Desktop or Native app).\n   * In situations like this you can disable the iframe fallback and handle the failed `refresh_token` grant and prompt the user to login interactively with `loginWithRedirect` or `loginWithPopup`.\"\n   *\n   * E.g. Using the `file:` protocol in an Electron application does not support that legacy technique.\n   *\n   * @example\n   * let token: string;\n   * try {\n   *   token = await auth0.getTokenSilently();\n   * } catch (e) {\n   *   if (e.error === 'missing_refresh_token' || e.error === 'invalid_grant') {\n   *     auth0.loginWithRedirect();\n   *   }\n   * }\n   */\n  useRefreshTokensFallback?: boolean;\n\n  /**\n   * A maximum number of seconds to wait before declaring background calls to /authorize as failed for timeout\n   * Defaults to 60s.\n   */\n  authorizeTimeoutInSeconds?: number;\n\n  /**\n   * Specify the timeout for HTTP calls using `fetch`. The default is 10 seconds.\n   */\n  httpTimeoutInSeconds?: number;\n\n  /**\n   * Internal property to send information about the client to the authorization server.\n   * @internal\n   */\n  auth0Client?: {\n    name: string;\n    version: string;\n    env?: { [key: string]: string };\n  };\n\n  /**\n   * Sets an additional cookie with no SameSite attribute to support legacy browsers\n   * that are not compatible with the latest SameSite changes.\n   * This will log a warning on modern browsers, you can disable the warning by setting\n   * this to false but be aware that some older useragents will not work,\n   * See https://www.chromium.org/updates/same-site/incompatible-clients\n   * Defaults to true\n   */\n  legacySameSiteCookie?: boolean;\n\n  /**\n   * If `true`, the SDK will use a cookie when storing information about the auth transaction while\n   * the user is going through the authentication flow on the authorization server.\n   *\n   * The default is `false`, in which case the SDK will use session storage.\n   *\n   * @notes\n   *\n   * You might want to enable this if you rely on your users being able to authenticate using flows that\n   * may end up spanning across multiple tabs (e.g. magic links) or you cannot otherwise rely on session storage being available.\n   */\n  useCookiesForTransactions?: boolean;\n\n  /**\n   * Number of days until the cookie `auth0.is.authenticated` will expire\n   * Defaults to 1.\n   */\n  sessionCheckExpiryDays?: number;\n\n  /**\n   * The domain the cookie is accessible from. If not set, the cookie is scoped to\n   * the current domain, including the subdomain.\n   *\n   * Note: setting this incorrectly may cause silent authentication to stop working\n   * on page load.\n   *\n   *\n   * To keep a user logged in across multiple subdomains set this to your\n   * top-level domain and prefixed with a `.` (eg: `.example.com`).\n   */\n  cookieDomain?: string;\n\n  /**\n   * If true, data to the token endpoint is transmitted as x-www-form-urlencoded data, if false it will be transmitted as JSON. The default setting is `true`.\n   *\n   * **Note:** Setting this to `false` may affect you if you use Auth0 Rules and are sending custom, non-primitive data. If you disable this,\n   * please verify that your Auth0 Rules continue to work as intended.\n   */\n  useFormData?: boolean;\n\n  /**\n   * Modify the value used as the current time during the token validation.\n   *\n   * **Note**: Using this improperly can potentially compromise the token validation.\n   */\n  nowProvider?: () => Promise<number> | number;\n\n  /**\n   * If provided, the SDK will load the token worker from this URL instead of the integrated `blob`. An example of when this is useful is if you have strict\n   * Content-Security-Policy (CSP) and wish to avoid needing to set `worker-src: blob:`. We recommend either serving the worker, which you can find in the module \n   * at `<module_path>/dist/auth0-spa-js.worker.production.js`, from the same host as your application or using the Auth0 CDN \n   * `https://cdn.auth0.com/js/auth0-spa-js/<version>/auth0-spa-js.worker.production.js`.\n   * \n   * **Note**: The worker is only used when `useRefreshTokens: true`, `cacheLocation: 'memory'`, and the `cache` is not custom.\n   */\n  workerUrl?: string;\n}\n\n/**\n * The possible locations where tokens can be stored\n */\nexport type CacheLocation = 'memory' | 'localstorage';\n\n/**\n * @ignore\n */\nexport interface AuthorizeOptions extends AuthorizationParams {\n  response_type: string;\n  response_mode: string;\n  redirect_uri?: string;\n  nonce: string;\n  state: string;\n  scope: string;\n  code_challenge: string;\n  code_challenge_method: string;\n}\n\nexport interface RedirectLoginOptions<TAppState = any>\n  extends BaseLoginOptions {\n  /**\n   * Used to store state before doing the redirect\n   */\n  appState?: TAppState;\n  /**\n   * Used to add to the URL fragment before redirecting\n   */\n  fragment?: string;\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * const client = new Auth0Client({\n   *   async onRedirect(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   * @deprecated since v2.0.1, use `openUrl` instead.\n   */\n  onRedirect?: (url: string) => Promise<void>;\n\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * const client = new Auth0Client({\n   *   openUrl(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   *\n   * @example\n   * import { Browser } from '@capacitor/browser';\n   *\n   * const client = new Auth0Client({\n   *   async openUrl(url) {\n   *     await Browser.open({ url });\n   *   }\n   * });\n   */\n  openUrl?: (url: string) => Promise<void> | void;\n}\n\nexport interface RedirectLoginResult<TAppState = any> {\n  /**\n   * State stored when the redirect request was made\n   */\n  appState?: TAppState;\n}\n\nexport interface PopupLoginOptions extends BaseLoginOptions {}\n\nexport interface PopupConfigOptions {\n  /**\n   * The number of seconds to wait for a popup response before\n   * throwing a timeout error. Defaults to 60s\n   */\n  timeoutInSeconds?: number;\n\n  /**\n   * Accepts an already-created popup window to use. If not specified, the SDK\n   * will create its own. This may be useful for platforms like iOS that have\n   * security restrictions around when popups can be invoked (e.g. from a user click event)\n   */\n  popup?: any;\n}\n\nexport interface GetTokenSilentlyOptions {\n  /**\n   * When `off`, ignores the cache and always sends a\n   * request to Auth0.\n   * When `cache-only`, only reads from the cache and never sends a request to Auth0.\n   * Defaults to `on`, where it both reads from the cache and sends a request to Auth0 as needed.\n   */\n  cacheMode?: 'on' | 'off' | 'cache-only';\n\n  /**\n   * Parameters that will be sent back to Auth0 as part of a request.\n   */\n  authorizationParams?: {\n    /**\n     * There's no actual redirect when getting a token silently,\n     * but, according to the spec, a `redirect_uri` param is required.\n     * Auth0 uses this parameter to validate that the current `origin`\n     * matches the `redirect_uri` `origin` when sending the response.\n     * It must be whitelisted in the \"Allowed Web Origins\" in your\n     * Auth0 Application's settings.\n     */\n    redirect_uri?: string;\n\n    /**\n     * The scope that was used in the authentication request\n     */\n    scope?: string;\n\n    /**\n     * The audience that was used in the authentication request\n     */\n    audience?: string;\n\n    /**\n     * If you need to send custom parameters to the Authorization Server,\n     * make sure to use the original parameter name.\n     */\n    [key: string]: any;\n  };\n\n  /** A maximum number of seconds to wait before declaring the background /authorize call as failed for timeout\n   * Defaults to 60s.\n   */\n  timeoutInSeconds?: number;\n\n  /**\n   * If true, the full response from the /oauth/token endpoint (or the cache, if the cache was used) is returned\n   * (minus `refresh_token` if one was issued). Otherwise, just the access token is returned.\n   *\n   * The default is `false`.\n   */\n  detailedResponse?: boolean;\n}\n\nexport interface GetTokenWithPopupOptions extends PopupLoginOptions {\n  /**\n   * When `off`, ignores the cache and always sends a request to Auth0.\n   * When `cache-only`, only reads from the cache and never sends a request to Auth0.\n   * Defaults to `on`, where it both reads from the cache and sends a request to Auth0 as needed.\n   */\n  cacheMode?: 'on' | 'off' | 'cache-only';\n}\n\nexport interface LogoutUrlOptions {\n  /**\n   * The `clientId` of your application.\n   *\n   * If this property is not set, then the `clientId` that was used during initialization of the SDK is sent to the logout endpoint.\n   *\n   * If this property is set to `null`, then no client ID value is sent to the logout endpoint.\n   *\n   * [Read more about how redirecting after logout works](https://auth0.com/docs/logout/guides/redirect-users-after-logout)\n   */\n  clientId?: string | null;\n\n  /**\n   * Parameters to pass to the logout endpoint. This can be known parameters defined by Auth0 or custom parameters\n   * you wish to provide.\n   */\n  logoutParams?: {\n    /**\n     * When supported by the upstream identity provider,\n     * forces the user to logout of their identity provider\n     * and from Auth0.\n     * [Read more about how federated logout works at Auth0](https://auth0.com/docs/logout/guides/logout-idps)\n     */\n    federated?: boolean;\n    /**\n     * The URL where Auth0 will redirect your browser to after the logout.\n     *\n     * **Note**: If the `client_id` parameter is included, the\n     * `returnTo` URL that is provided must be listed in the\n     * Application's \"Allowed Logout URLs\" in the Auth0 dashboard.\n     * However, if the `client_id` parameter is not included, the\n     * `returnTo` URL must be listed in the \"Allowed Logout URLs\" at\n     * the account level in the Auth0 dashboard.\n     *\n     * [Read more about how redirecting after logout works](https://auth0.com/docs/logout/guides/redirect-users-after-logout)\n     */\n    returnTo?: string;\n\n    /**\n     * If you need to send custom parameters to the logout endpoint, make sure to use the original parameter name.\n     */\n    [key: string]: any;\n  };\n}\n\nexport interface LogoutOptions extends LogoutUrlOptions {\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * await auth0.logout({\n   *   async onRedirect(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   * @deprecated since v2.0.1, use `openUrl` instead.\n   */\n  onRedirect?: (url: string) => Promise<void>;\n\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * Set to `false` to disable the redirect, or provide a function to handle the actual redirect yourself.\n   *\n   * @example\n   * await auth0.logout({\n   *   openUrl(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   *\n   * @example\n   * import { Browser } from '@capacitor/browser';\n   *\n   * await auth0.logout({\n   *   async openUrl(url) {\n   *     await Browser.open({ url });\n   *   }\n   * });\n   */\n  openUrl?: false | ((url: string) => Promise<void> | void);\n}\n\n/**\n * @ignore\n */\nexport interface AuthenticationResult {\n  state: string;\n  code?: string;\n  error?: string;\n  error_description?: string;\n}\n\n/**\n * @ignore\n */\nexport interface TokenEndpointOptions {\n  baseUrl: string;\n  client_id: string;\n  grant_type: string;\n  timeout?: number;\n  auth0Client: any;\n  useFormData?: boolean;\n  [key: string]: any;\n}\n\nexport type TokenEndpointResponse = {\n  id_token: string;\n  access_token: string;\n  refresh_token?: string;\n  expires_in: number;\n  scope?: string;\n};\n\n/**\n * @ignore\n */\nexport interface OAuthTokenOptions extends TokenEndpointOptions {\n  code_verifier: string;\n  code: string;\n  redirect_uri: string;\n  audience: string;\n  scope: string;\n}\n\n/**\n * @ignore\n */\nexport interface RefreshTokenOptions extends TokenEndpointOptions {\n  refresh_token: string;\n}\n\n/**\n * @ignore\n */\nexport interface JWTVerifyOptions {\n  iss: string;\n  aud: string;\n  id_token: string;\n  nonce?: string;\n  leeway?: number;\n  max_age?: number;\n  organization?: string;\n  now?: number;\n}\n\nexport interface IdToken {\n  __raw: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  middle_name?: string;\n  nickname?: string;\n  preferred_username?: string;\n  profile?: string;\n  picture?: string;\n  website?: string;\n  email?: string;\n  email_verified?: boolean;\n  gender?: string;\n  birthdate?: string;\n  zoneinfo?: string;\n  locale?: string;\n  phone_number?: string;\n  phone_number_verified?: boolean;\n  address?: string;\n  updated_at?: string;\n  iss?: string;\n  aud?: string;\n  exp?: number;\n  nbf?: number;\n  iat?: number;\n  jti?: string;\n  azp?: string;\n  nonce?: string;\n  auth_time?: string;\n  at_hash?: string;\n  c_hash?: string;\n  acr?: string;\n  amr?: string[];\n  sub_jwk?: string;\n  cnf?: string;\n  sid?: string;\n  org_id?: string;\n  org_name?: string;\n  [key: string]: any;\n}\n\nexport class User {\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  middle_name?: string;\n  nickname?: string;\n  preferred_username?: string;\n  profile?: string;\n  picture?: string;\n  website?: string;\n  email?: string;\n  email_verified?: boolean;\n  gender?: string;\n  birthdate?: string;\n  zoneinfo?: string;\n  locale?: string;\n  phone_number?: string;\n  phone_number_verified?: boolean;\n  address?: string;\n  updated_at?: string;\n  sub?: string;\n  [key: string]: any;\n}\n\n/**\n * @ignore\n */\nexport type FetchOptions = {\n  method?: string;\n  headers?: Record<string, string>;\n  credentials?: 'include' | 'omit';\n  body?: string;\n  signal?: AbortSignal;\n};\n\nexport type GetTokenSilentlyVerboseResponse = Omit<\n  TokenEndpointResponse,\n  'refresh_token'\n>;\n", "import { Auth0Client } from './Auth0Client';\nimport { Auth0ClientOptions } from './global';\n\nimport './global';\n\nexport * from './global';\n\n/**\n * Asynchronously creates the Auth0Client instance and calls `checkSession`.\n *\n * **Note:** There are caveats to using this in a private browser tab, which may not silently authenticae\n * a user on page refresh. Please see [the checkSession docs](https://auth0.github.io/auth0-spa-js/classes/Auth0Client.html#checksession) for more info.\n *\n * @param options The client options\n * @returns An instance of Auth0Client\n */\nexport async function createAuth0Client(options: Auth0ClientOptions) {\n  const auth0 = new Auth0Client(options);\n  await auth0.checkSession();\n  return auth0;\n}\n\nexport { Auth0Client };\n\nexport {\n  GenericError,\n  AuthenticationError,\n  TimeoutError,\n  PopupTimeoutError,\n  PopupCancelledError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport {\n  ICache,\n  LocalStorageCache,\n  InMemoryCache,\n  Cacheable,\n  DecodedToken,\n  CacheEntry,\n  WrappedCacheEntry,\n  KeyManifestEntry,\n  MaybePromise,\n  CacheKey,\n  CacheKeyData\n} from './cache';\n"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "SuppressedError", "error", "suppressed", "message", "Error", "name", "defineProperty", "exports", "value", "ProcessLocking", "_this", "this", "locked", "Map", "addToLocked", "key", "toAdd", "callbacks", "get", "undefined", "set", "unshift", "isLocked", "has", "lock", "Promise", "resolve", "reject", "unlock", "delete", "toCall", "pop", "setTimeout", "getInstance", "instance", "getLock", "default", "__awaiter", "thisArg", "_arguments", "P", "generator", "fulfilled", "step", "next", "rejected", "result", "done", "then", "apply", "__generator", "body", "_", "label", "sent", "trys", "ops", "f", "y", "g", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "TypeError", "push", "LOCK_STORAGE_KEY", "DEFAULT_STORAGE_HANDLER", "index", "_a", "getItem", "clear", "window", "localStorage", "removeItem", "setItem", "keySync", "getItemSync", "clearSync", "removeItemSync", "setItemSync", "delay", "milliseconds", "generateRandomString", "CHARS", "randomstring", "INDEX", "Math", "floor", "random", "getLockId", "Date", "now", "toString", "SuperTokensLock", "storageHandler", "acquiredIatSet", "Set", "id", "acquireLock", "bind", "releaseLock", "releaseLock__private__", "waitForSomethingToChange", "refreshLockWhileAcquired", "waiters", "<PERSON><PERSON><PERSON>", "timeout", "iat", "MAX_TIME", "STORAGE_KEY", "STORAGE", "lock<PERSON>bj", "TIMEOUT_KEY", "lockObjPostDelay", "parsedLockObjPostDelay", "JSON", "stringify", "timeout<PERSON><PERSON>", "timeAcquired", "timeRefreshed", "parse", "add", "lock<PERSON><PERSON><PERSON><PERSON>", "storageKey", "parsedLockObj", "processLock_1", "resolvedCalled", "startedAt", "MIN_TIME_TO_WAIT", "removedListeners", "stopWaiting", "removeEventListener", "removeFromWaiting", "clearTimeout", "timeOutId", "timeToWait", "addEventListener", "addToWaiting", "max", "func", "filter", "notify<PERSON><PERSON><PERSON>", "slice", "for<PERSON>ach", "parsedlockObj", "MIN_ALLOWED_TIME", "KEYS", "currIndex", "LOCK_KEY", "includes", "version", "DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS", "DEFAULT_POPUP_CONFIG_OPTIONS", "timeoutInSeconds", "DEFAULT_SILENT_TOKEN_RETRY_COUNT", "CLEANUP_IFRAME_TIMEOUT_IN_SECONDS", "DEFAULT_FETCH_TIMEOUT_MS", "CACHE_LOCATION_MEMORY", "MISSING_REFRESH_TOKEN_ERROR_MESSAGE", "INVALID_REFRESH_TOKEN_ERROR_MESSAGE", "DEFAULT_SCOPE", "DEFAULT_SESSION_CHECK_EXPIRY_DAYS", "DEFAULT_AUTH0_CLIENT", "DEFAULT_NOW_PROVIDER", "GenericError", "constructor", "error_description", "super", "setPrototypeOf", "static", "AuthenticationError", "state", "appState", "TimeoutError", "PopupTimeoutError", "popup", "PopupCancelledError", "MfaRequiredError", "mfa_token", "MissingRefreshTokenError", "audience", "scope", "valueOrEmptyString", "exclude", "parseAuthenticationResult", "queryString", "substring", "searchParams", "URLSearchParams", "code", "runIframe", "authorizeUrl", "<PERSON><PERSON><PERSON><PERSON>", "res", "rej", "iframe", "document", "createElement", "setAttribute", "style", "display", "removeIframe", "contains", "<PERSON><PERSON><PERSON><PERSON>", "iframeEventHandler", "timeoutSetTimeoutId", "origin", "data", "type", "eventSource", "source", "close", "response", "fromPayload", "append<PERSON><PERSON><PERSON>", "openPopup", "url", "width", "height", "left", "screenX", "innerWidth", "top", "screenY", "innerHeight", "open", "runPopup", "config", "popupEventListener", "popupTimer", "setInterval", "closed", "clearInterval", "timeoutId", "getCrypto", "crypto", "createRandomString", "charset", "randomValues", "Array", "from", "getRandomValues", "Uint8Array", "encode", "btoa", "stripUndefined", "params", "keys", "k", "reduce", "acc", "assign", "createQueryParams", "clientId", "client_id", "sha256", "async", "digestOp", "subtle", "digest", "TextEncoder", "urlEncodeB64", "input", "b64Chars", "replace", "m", "decodeB64", "decodeURIComponent", "atob", "split", "map", "c", "charCodeAt", "join", "urlDecodeB64", "bufferToBase64UrlEncoded", "ie11SafeInput", "String", "fromCharCode", "validateCrypto", "getDomain", "domainUrl", "test", "getT<PERSON><PERSON><PERSON><PERSON>", "issuer", "startsWith", "parseNumber", "parseInt", "sendMessage", "to", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "postMessage", "port2", "createAbortController", "AbortController", "dofetch", "fetchUrl", "fetchOptions", "fetch", "ok", "json", "fetchWithoutWorker", "controller", "signal", "race", "abort", "finally", "fetchWithWorker", "worker", "useFormData", "auth", "switchFetch", "getJSON", "options", "fetchError", "errorMessage", "oauthToken", "baseUrl", "auth0Client", "method", "headers", "dedupe", "arr", "getUniqueScopes", "scopes", "Boolean", "trim", "CACHE_KEY_PREFIX", "CACHE_KEY_ID_TOKEN_SUFFIX", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "suffix", "to<PERSON><PERSON>", "entry", "LocalStorageCache", "payload", "remove", "allKeys", "InMemoryCache", "enclosedCache", "cache", "cacheEntry", "DEFAULT_EXPIRY_ADJUSTMENT_SECONDS", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyManifest", "nowProvider", "idToken", "decodedToken", "cache<PERSON>ey", "getIdTokenCache<PERSON>ey", "id_token", "entryByScope", "expiryAdjustmentSeconds", "wrappedEntry", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "matchExisting<PERSON>ache<PERSON>ey", "nowSeconds", "expiresAt", "refresh_token", "wrapCacheEntry", "memo", "expiresInTime", "expires_in", "keyToMatch", "fromKey", "scopeSet", "scopesToMatch", "hasAllScopes", "current", "TRANSACTION_STORAGE_KEY_PREFIX", "TransactionManager", "storage", "cookieDomain", "create", "transaction", "save", "daysUntilExpire", "isNumber", "idTokendecoded", "decode", "token", "parts", "header", "signature", "payloadJSON", "claims", "__raw", "user", "encoded", "verify", "decoded", "iss", "sub", "alg", "aud", "isArray", "azp", "nonce", "max_age", "auth_time", "exp", "leeway", "expDate", "setUTCSeconds", "nbf", "nbfDate", "authTimeDate", "organization", "org", "orgId", "org_id", "orgName", "toLowerCase", "org_name", "__assign", "arguments", "__esModule", "stringifyAttribute", "stringified", "stringifyAttributes", "attributes", "expires", "setMilliseconds", "getMilliseconds", "toUTCString", "domain", "path", "secure", "sameSite", "encodeURIComponent", "cookieString", "cookies", "rdecode", "cookie", "char<PERSON>t", "name_1", "getAll", "Cookie<PERSON>torage", "Cookies.get", "cookieAttributes", "location", "protocol", "Cookies.set", "Cookies.remove", "LEGACY_PREFIX", "CookieStorageWithLegacySameSite", "SessionStorage", "sessionStorage", "singlePromiseMap", "singlePromise", "cb", "promise", "retryPromise", "maxNumberOfRetries", "CacheKeyManifest", "manifest<PERSON>ey", "createManifestKeyFrom", "size", "GET_TOKEN_SILENTLY_LOCK_KEY", "buildOrganizationHintCookieName", "OLD_IS_AUTHENTICATED_COOKIE_NAME", "buildIsAuthenticatedCookieName", "cacheLocationBuilders", "memory", "localstorage", "cacheFactory", "getAuthorizeParams", "clientOptions", "authorizationParams", "code_challenge", "redirect_uri", "response_mode", "response_type", "code_challenge_method", "patchOpenUrlWithOnRedirect", "openUrl", "onRedirect", "originalOptions", "Lock", "Auth0Client", "userCache", "defaultOptions", "useRefreshTokensFallback", "_releaseLockOnPageHide", "cacheLocation", "console", "warn", "httpTimeoutMs", "httpTimeoutInSeconds", "cookieStorage", "legacySameSiteCookie", "orgHintCookieName", "isAuthenticatedCookieName", "sessionCheckExpiryDays", "transactionStorage", "useCookiesForTransactions", "useRefreshTokens", "transactionManager", "cacheManager", "token<PERSON>ssuer", "Worker", "workerUrl", "TokenWorker", "_url", "_authorizeUrl", "authorizeOptions", "verifyIdToken", "_processOrgHint", "fallbackRedirectUri", "code_verifier", "code_challengeBuffer", "_prepareAuthorizeUrl", "href", "codeResult", "authorizeTimeoutInSeconds", "_requestToken", "grant_type", "nonceIn", "_getIdTokenFromCache", "_b", "fragment", "urlOptions", "_c", "urlWithFragment", "queryStringFragments", "getTokenSilently", "localOptions", "cacheMode", "_getTokenSilently", "detailedResponse", "access_token", "getTokenOptions", "_getEntryFromCache", "authResult", "_getTokenUsingRefreshToken", "_getTokenFromIFrame", "oauthTokenScope", "loginWithPopup", "getUser", "_buildLogoutUrl", "logoutParams", "federated", "logoutOptions", "federatedQuery", "prompt", "orgHint", "stateIn", "crossOriginIsolated", "authorizeTimeout", "tokenResult", "logout", "entryWithoutIdToken", "setIdToken", "getIdToken", "currentCache", "additionalParameters", "_verifyIdToken", "_saveEntryInCache", "subject_token", "subject_token_type", "User", "createAuth0Client", "auth0", "checkSession"], "mappings": ";;;;;IA0CO,SAASA,OAAOC,GAAGC;QACtB,IAAIC,IAAI,CAAA;QACR,KAAK,IAAIC,KAAKH,GAAG,IAAII,OAAOC,UAAUC,eAAeC,KAAKP,GAAGG,MAAMF,EAAEO,QAAQL,KAAK,GAC9ED,EAAEC,KAAKH,EAAEG;QACb,IAAIH,KAAK,eAAeI,OAAOK,0BAA0B,YACrD,KAAK,IAAIC,IAAI,GAAGP,IAAIC,OAAOK,sBAAsBT,IAAIU,IAAIP,EAAEQ,QAAQD,KAAK;YACpE,IAAIT,EAAEO,QAAQL,EAAEO,MAAM,KAAKN,OAAOC,UAAUO,qBAAqBL,KAAKP,GAAGG,EAAEO,KACvER,EAAEC,EAAEO,MAAMV,EAAEG,EAAEO;AACrB;QACL,OAAOR;AACX;WAuQ8BW,oBAAoB,aAAaA,kBAAkB,SAAUC,OAAOC,YAAYC;QAC1G,IAAIf,IAAI,IAAIgB,MAAMD;QAClB,OAAOf,EAAEiB,OAAO,mBAAmBjB,EAAEa,QAAQA,OAAOb,EAAEc,aAAaA,YAAYd;AACnF;;;;;;;;;;;QC7TAG,OAAOe,eAAeC,SAAS,cAAc;YAAEC,OAAO;;QACtD,IAAIC,iBAAgC;YAChC,SAASA;gBACL,IAAIC,QAAQC;gBACZA,KAAKC,SAAS,IAAIC;gBAClBF,KAAKG,cAAc,SAAUC,KAAKC;oBAC9B,IAAIC,YAAYP,MAAME,OAAOM,IAAIH;oBACjC,IAAIE,cAAcE,WAAW;wBACzB,IAAIH,UAAUG,WAAW;4BACrBT,MAAME,OAAOQ,IAAIL,KAAK;AACzB,+BACI;4BACDL,MAAME,OAAOQ,IAAIL,KAAK,EAACC;AAC1B;AACJ,2BACI;wBACD,IAAIA,UAAUG,WAAW;4BACrBF,UAAUI,QAAQL;4BAClBN,MAAME,OAAOQ,IAAIL,KAAKE;AACzB;AACJ;AACb;gBACQN,KAAKW,WAAW,SAAUP;oBACtB,OAAOL,MAAME,OAAOW,IAAIR;AACpC;gBACQJ,KAAKa,OAAO,SAAUT;oBAClB,OAAO,IAAIU,SAAQ,SAAUC,SAASC;wBAClC,IAAIjB,MAAMY,SAASP,MAAM;4BACrBL,MAAMI,YAAYC,KAAKW;AAC1B,+BACI;4BACDhB,MAAMI,YAAYC;4BAClBW;AACH;AACjB;AACA;gBACQf,KAAKiB,SAAS,SAAUb;oBACpB,IAAIE,YAAYP,MAAME,OAAOM,IAAIH;oBACjC,IAAIE,cAAcE,aAAaF,UAAUnB,WAAW,GAAG;wBACnDY,MAAME,OAAOiB,OAAOd;wBACpB;AACH;oBACD,IAAIe,SAASb,UAAUc;oBACvBrB,MAAME,OAAOQ,IAAIL,KAAKE;oBACtB,IAAIa,WAAWX,WAAW;wBACtBa,WAAWF,QAAQ;AACtB;AACb;AACK;YACDrB,eAAewB,cAAc;gBACzB,IAAIxB,eAAeyB,aAAaf,WAAW;oBACvCV,eAAeyB,WAAW,IAAIzB;AACjC;gBACD,OAAOA,eAAeyB;AAC9B;YACI,OAAOzB;AACX;QACA,SAAS0B;YACL,OAAO1B,eAAewB;AAC1B;QACA1B,QAAA6B,UAAkBD;;;;QC5DlB,IAAIE,YAAa1B,kBAAQA,eAAK0B,aAAc,SAAUC,SAASC,YAAYC,GAAGC;YAC1E,OAAO,KAAKD,MAAMA,IAAIf,WAAU,SAAUC,SAASC;gBAC/C,SAASe,UAAUlC;oBAAS;wBAAMmC,KAAKF,UAAUG,KAAKpC;sBAAW,OAAOpB;wBAAKuC,OAAOvC;;AAAO;gBAC3F,SAASyD,SAASrC;oBAAS;wBAAMmC,KAAKF,UAAU,SAASjC;sBAAW,OAAOpB;wBAAKuC,OAAOvC;;AAAO;gBAC9F,SAASuD,KAAKG;oBAAUA,OAAOC,OAAOrB,QAAQoB,OAAOtC,SAAS,IAAIgC,GAAE,SAAUd;wBAAWA,QAAQoB,OAAOtC;wBAAWwC,KAAKN,WAAWG;AAAY;gBAC/IF,MAAMF,YAAYA,UAAUQ,MAAMX,SAASC,cAAc,KAAKK;AACtE;AACA;QACA,IAAIM,cAAevC,kBAAQA,eAAKuC,eAAgB,SAAUZ,SAASa;YAC/D,IAAIC,IAAI;gBAAEC,OAAO;gBAAGC,MAAM;oBAAa,IAAIjE,EAAE,KAAK,GAAG,MAAMA,EAAE;oBAAI,OAAOA,EAAE;AAAK;gBAAEkE,MAAM;gBAAIC,KAAK;eAAMC,GAAGC,GAAGrE,GAAGsE;YAC/G,OAAOA,IAAI;gBAAEf,MAAMgB,KAAK;gBAAIC,OAASD,KAAK;gBAAIE,QAAUF,KAAK;sBAAaG,WAAW,eAAeJ,EAAEI,OAAOC,YAAY;gBAAa,OAAOrD;AAAO,gBAAGgD;YACvJ,SAASC,KAAKK;gBAAK,OAAO,SAAUC;oBAAK,OAAOvB,KAAK,EAACsB,GAAGC;AAAM;AAAG;YAClE,SAASvB,KAAKwB;gBACV,IAAIV,GAAG,MAAM,IAAIW,UAAU;gBAC3B,OAAOhB;oBACH,IAAIK,IAAI,GAAGC,MAAMrE,IAAI8E,GAAG,KAAK,IAAIT,EAAE,YAAYS,GAAG,KAAKT,EAAE,cAAcrE,IAAIqE,EAAE,cAAcrE,EAAEK,KAAKgE;oBAAI,KAAKA,EAAEd,WAAWvD,IAAIA,EAAEK,KAAKgE,GAAGS,GAAG,KAAKpB,MAAM,OAAO1D;oBAC3J,IAAIqE,IAAI,GAAGrE,GAAG8E,KAAK,EAACA,GAAG,KAAK,GAAG9E,EAAEmB;oBACjC,QAAQ2D,GAAG;sBACP,KAAK;sBAAG,KAAK;wBAAG9E,IAAI8E;wBAAI;;sBACxB,KAAK;wBAAGf,EAAEC;wBAAS,OAAO;4BAAE7C,OAAO2D,GAAG;4BAAIpB,MAAM;;;sBAChD,KAAK;wBAAGK,EAAEC;wBAASK,IAAIS,GAAG;wBAAIA,KAAK,EAAC;wBAAI;;sBACxC,KAAK;wBAAGA,KAAKf,EAAEI,IAAIzB;wBAAOqB,EAAEG,KAAKxB;wBAAO;;sBACxC;wBACI,MAAM1C,IAAI+D,EAAEG,MAAMlE,IAAIA,EAAES,SAAS,KAAKT,EAAEA,EAAES,SAAS,QAAQqE,GAAG,OAAO,KAAKA,GAAG,OAAO,IAAI;4BAAEf,IAAI;4BAAG;AAAW;wBAC5G,IAAIe,GAAG,OAAO,OAAO9E,KAAM8E,GAAG,KAAK9E,EAAE,MAAM8E,GAAG,KAAK9E,EAAE,KAAM;4BAAE+D,EAAEC,QAAQc,GAAG;4BAAI;AAAQ;wBACtF,IAAIA,GAAG,OAAO,KAAKf,EAAEC,QAAQhE,EAAE,IAAI;4BAAE+D,EAAEC,QAAQhE,EAAE;4BAAIA,IAAI8E;4BAAI;AAAQ;wBACrE,IAAI9E,KAAK+D,EAAEC,QAAQhE,EAAE,IAAI;4BAAE+D,EAAEC,QAAQhE,EAAE;4BAAI+D,EAAEI,IAAIa,KAAKF;4BAAK;AAAQ;wBACnE,IAAI9E,EAAE,IAAI+D,EAAEI,IAAIzB;wBAChBqB,EAAEG,KAAKxB;wBAAO;;oBAEtBoC,KAAKhB,KAAKzD,KAAK4C,SAASc;kBAC1B,OAAOhE;oBAAK+E,KAAK,EAAC,GAAG/E;oBAAIsE,IAAI;AAAE,kBAAW;oBAAED,IAAIpE,IAAI;AAAI;gBAC1D,IAAI8E,GAAG,KAAK,GAAG,MAAMA,GAAG;gBAAI,OAAO;oBAAE3D,OAAO2D,GAAG,KAAKA,GAAG,UAAU;oBAAGpB,MAAM;;AAC7E;AACL;QACA,IAAIrC,QAAQC;QACZpB,OAAOe,eAAeC,SAAS,cAAc;YAAEC,OAAO;;QAkBtD,IAAI8D,mBAAmB;QACvB,IAAIC,0BAA0B;YAC1BxD,KAAK,SAAUyD;gBAAS,OAAOnC,UAAU3B,YAAY,QAAQ,IAAG;oBAC5D,OAAOwC,YAAYvC,OAAM,SAAU8D;wBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;YACLsE,SAAS,SAAU3D;gBAAO,OAAOsB,UAAU3B,YAAY,QAAQ,IAAG;oBAC9D,OAAOwC,YAAYvC,OAAM,SAAU8D;wBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;YACLuE,OAAO;gBAAc,OAAOtC,UAAU3B,YAAY,QAAQ,IAAG;oBACzD,OAAOwC,YAAYvC,OAAM,SAAU8D;wBAC/B,OAAO,EAAC,GAAcG,OAAOC,aAAaF;AACtD;AACK;AAAI;YACLG,YAAY,SAAU/D;gBAAO,OAAOsB,UAAU3B,YAAY,QAAQ,IAAG;oBACjE,OAAOwC,YAAYvC,OAAM,SAAU8D;wBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;YACL2E,SAAS,SAAUhE,KAAKP;gBAAS,OAAO6B,UAAU3B,YAAY,QAAQ,IAAG;oBACrE,OAAOwC,YAAYvC,OAAM,SAAU8D;wBAC/B,MAAM,IAAIrE,MAAM;AAC5B;AACK;AAAI;YACL4E,SAAS,SAAUR;gBACf,OAAOI,OAAOC,aAAa9D,IAAIyD;AAClC;YACDS,aAAa,SAAUlE;gBACnB,OAAO6D,OAAOC,aAAaH,QAAQ3D;AACtC;YACDmE,WAAW;gBACP,OAAON,OAAOC,aAAaF;AAC9B;YACDQ,gBAAgB,SAAUpE;gBACtB,OAAO6D,OAAOC,aAAaC,WAAW/D;AACzC;YACDqE,aAAa,SAAUrE,KAAKP;gBACxB,OAAOoE,OAAOC,aAAaE,QAAQhE,KAAKP;AAC3C;;QAOL,SAAS6E,MAAMC;YACX,OAAO,IAAI7D,SAAQ,SAAUC;gBAAW,OAAOM,WAAWN,SAAS4D;AAAc;AACrF;QAOA,SAASC,qBAAqBzF;YAC1B,IAAI0F,QAAQ;YACZ,IAAIC,eAAe;YACnB,KAAK,IAAI5F,IAAI,GAAGA,IAAIC,QAAQD,KAAK;gBAC7B,IAAI6F,QAAQC,KAAKC,MAAMD,KAAKE,WAAWL,MAAM1F;gBAC7C2F,gBAAgBD,MAAME;AACzB;YACD,OAAOD;AACX;QAMA,SAASK;YACL,OAAOC,KAAKC,MAAMC,aAAaV,qBAAqB;AACxD;QACA,IAAIW,kBAAiC;YACjC,SAASA,gBAAgBC;gBACrBxF,KAAKyF,iBAAiB,IAAIC;gBAC1B1F,KAAKwF,iBAAiBhF;gBACtBR,KAAK2F,KAAKR;gBACVnF,KAAK4F,cAAc5F,KAAK4F,YAAYC,KAAK7F;gBACzCA,KAAK8F,cAAc9F,KAAK8F,YAAYD,KAAK7F;gBACzCA,KAAK+F,yBAAyB/F,KAAK+F,uBAAuBF,KAAK7F;gBAC/DA,KAAKgG,2BAA2BhG,KAAKgG,yBAAyBH,KAAK7F;gBACnEA,KAAKiG,2BAA2BjG,KAAKiG,yBAAyBJ,KAAK7F;gBACnEA,KAAKwF,iBAAiBA;gBACtB,IAAID,gBAAgBW,YAAY1F,WAAW;oBACvC+E,gBAAgBW,UAAU;AAC7B;AACJ;YAWDX,gBAAgB1G,UAAU+G,cAAc,SAAUO,SAASC;gBACvD,IAAIA,iBAAiB,GAAG;oBAAEA,UAAU;AAAO;gBAC3C,OAAO1E,UAAU1B,WAAW,QAAQ,IAAG;oBACnC,IAAIqG,KAAKC,UAAUC,aAAaC,SAASC,SAASC,aAAaC,kBAAkBC;oBACjF,OAAOrE,YAAYvC,OAAM,SAAU8D;wBAC/B,QAAQA,GAAGpB;0BACP,KAAK;4BACD2D,MAAMjB,KAAKC,QAAQT,qBAAqB;4BACxC0B,WAAWlB,KAAKC,QAAQe;4BACxBG,cAAc5C,mBAAmB,MAAMwC;4BACvCK,UAAUxG,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;4BAC7E1B,GAAGpB,QAAQ;;0BACf,KAAK;4BACD,MAAM0C,KAAKC,QAAQiB,WAAW,OAAO,EAAC,GAAa;4BACnD,OAAO,EAAC,GAAa5B,MAAM;;0BAC/B,KAAK;4BACDZ,GAAGnB;4BACH8D,UAAUD,QAAQlC,YAAYiC;4BAC9B,MAAME,YAAY,OAAO,OAAO,EAAC,GAAa;4BAC9CC,cAAc1G,KAAK2F,KAAK,MAAMQ,UAAU,MAAME;4BAE9C,OAAO,EAAC,GAAa3B,MAAMM,KAAKC,MAAMD,KAAKE,WAAW;;0BAC1D,KAAK;4BAEDpB,GAAGnB;4BACH6D,QAAQ/B,YAAY8B,aAAaM,KAAKC,UAAU;gCAC5CnB,IAAI3F,KAAK2F;gCACTU,KAAKA;gCACLU,YAAYL;gCACZM,cAAc5B,KAAKC;gCACnB4B,eAAe7B,KAAKC;;4BAExB,OAAO,EAAC,GAAaX,MAAM;;0BAC/B,KAAK;4BACDZ,GAAGnB;4BACHgE,mBAAmBH,QAAQlC,YAAYiC;4BACvC,IAAII,qBAAqB,MAAM;gCAC3BC,yBAAyBC,KAAKK,MAAMP;gCACpC,IAAIC,uBAAuBjB,OAAO3F,KAAK2F,MAAMiB,uBAAuBP,QAAQA,KAAK;oCAC7ErG,KAAKyF,eAAe0B,IAAId;oCACxBrG,KAAKiG,yBAAyBM,aAAaF;oCAC3C,OAAO,EAAC,GAAc;AACzB;AACJ;4BACD,OAAO,EAAC,GAAa;;0BACzB,KAAK;4BACDd,gBAAgB6B,cAAcpH,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;4BACjG,OAAO,EAAC,GAAaxF,KAAKgG,yBAAyBM;;0BACvD,KAAK;4BACDxC,GAAGnB;4BACHmB,GAAGpB,QAAQ;;0BACf,KAAK;4BACD2D,MAAMjB,KAAKC,QAAQT,qBAAqB;4BACxC,OAAO,EAAC,GAAa;;0BACzB,KAAK;4BAAG,OAAO,EAAC,GAAc;;AAElD;AACA;AACA;YACIW,gBAAgB1G,UAAUoH,2BAA2B,SAAUoB,YAAYhB;gBACvE,OAAO3E,UAAU1B,WAAW,QAAQ,IAAG;oBACnC,IAAID,QAAQC;oBACZ,OAAOuC,YAAYvC,OAAM,SAAU8D;wBAC/BzC,YAAW;4BAAc,OAAOK,UAAU3B,YAAY,QAAQ,IAAG;gCAC7D,IAAIyG,SAASC,SAASa;gCACtB,OAAO/E,YAAYvC,OAAM,SAAU8D;oCAC/B,QAAQA,GAAGpB;sCACP,KAAK;wCAAG,OAAO,EAAC,GAAa6E,YAAc9F,UAAUZ,KAAKwF;;sCAC1D,KAAK;wCACDvC,GAAGnB;wCACH,KAAK3C,KAAKyF,eAAe7E,IAAIyF,MAAM;4CAC/BkB,YAAc9F,UAAUR,OAAOoF;4CAC/B,OAAO,EAAC;AACX;wCACDG,UAAUxG,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;wCAC7EiB,UAAUD,QAAQlC,YAAY+C;wCAC9B,IAAIZ,YAAY,MAAM;4CAClBa,gBAAgBT,KAAKK,MAAMT;4CAC3Ba,cAAcL,gBAAgB7B,KAAKC;4CACnCmB,QAAQ/B,YAAY4C,YAAYR,KAAKC,UAAUQ;4CAC/CC,YAAc9F,UAAUR,OAAOoF;AAClC,+CACI;4CACDkB,YAAc9F,UAAUR,OAAOoF;4CAC/B,OAAO,EAAC;AACX;wCACDrG,KAAKiG,yBAAyBoB,YAAYhB;wCAC1C,OAAO,EAAC;;AAExC;AACA;AAAmB,4BAAI;wBACP,OAAO,EAAC;AACxB;AACA;AACA;YACId,gBAAgB1G,UAAUmH,2BAA2B,SAAUM;gBAC3D,OAAO5E,UAAU1B,WAAW,QAAQ,IAAG;oBACnC,OAAOuC,YAAYvC,OAAM,SAAU8D;wBAC/B,QAAQA,GAAGpB;0BACP,KAAK;4BAAG,OAAO,EAAC,GAAa,IAAI5B,SAAQ,SAAUC;gCAC3C,IAAIyG,iBAAiB;gCACrB,IAAIC,YAAYrC,KAAKC;gCACrB,IAAIqC,mBAAmB;gCACvB,IAAIC,mBAAmB;gCACvB,SAASC;oCACL,KAAKD,kBAAkB;wCACnB1D,OAAO4D,oBAAoB,WAAWD;wCACtCrC,gBAAgBuC,kBAAkBF;wCAClCG,aAAaC;wCACbL,mBAAmB;AACtB;oCACD,KAAKH,gBAAgB;wCACjBA,iBAAiB;wCACjB,IAAIS,aAAaP,oBAAoBtC,KAAKC,QAAQoC;wCAClD,IAAIQ,aAAa,GAAG;4CAChB5G,WAAWN,SAASkH;AACvB,+CACI;4CACDlH,QAAQ;AACX;AACJ;AACJ;gCACDkD,OAAOiE,iBAAiB,WAAWN;gCACnCrC,gBAAgB4C,aAAaP;gCAC7B,IAAII,YAAY3G,WAAWuG,aAAa5C,KAAKoD,IAAI,GAAG9B,WAAWlB,KAAKC;AACvE;;0BACL,KAAK;4BACDvB,GAAGnB;4BACH,OAAO,EAAC;;AAEhC;AACA;AACA;YACI4C,gBAAgB4C,eAAe,SAAUE;gBACrCrI,KAAK8H,kBAAkBO;gBACvB,IAAI9C,gBAAgBW,YAAY1F,WAAW;oBACvC;AACH;gBACD+E,gBAAgBW,QAAQxC,KAAK2E;AACrC;YACI9C,gBAAgBuC,oBAAoB,SAAUO;gBAC1C,IAAI9C,gBAAgBW,YAAY1F,WAAW;oBACvC;AACH;gBACD+E,gBAAgBW,UAAUX,gBAAgBW,QAAQoC,QAAO,SAAUpJ;oBAAK,OAAOA,MAAMmJ;AAAO;AACpG;YACI9C,gBAAgBgD,gBAAgB;gBAC5B,IAAIhD,gBAAgBW,YAAY1F,WAAW;oBACvC;AACH;gBACD,IAAI0F,UAAUX,gBAAgBW,QAAQsC;gBACtCtC,QAAQuC,SAAQ,SAAUvJ;oBAAK,OAAOA;AAAI;AAClD;YAQIqG,gBAAgB1G,UAAUiH,cAAc,SAAUK;gBAC9C,OAAOzE,UAAU1B,WAAW,QAAQ,IAAG;oBACnC,OAAOuC,YAAYvC,OAAM,SAAU8D;wBAC/B,QAAQA,GAAGpB;0BACP,KAAK;4BAAG,OAAO,EAAC,GAAa1C,KAAK+F,uBAAuBI;;0BACzD,KAAK;4BAAG,OAAO,EAAC,GAAcrC,GAAGnB;;AAErD;AACA;AACA;YAQI4C,gBAAgB1G,UAAUkH,yBAAyB,SAAUI;gBACzD,OAAOzE,UAAU1B,WAAW,QAAQ,IAAG;oBACnC,IAAIwG,SAASD,aAAaE,SAASiC;oBACnC,OAAOnG,YAAYvC,OAAM,SAAU8D;wBAC/B,QAAQA,GAAGpB;0BACP,KAAK;4BACD8D,UAAUxG,KAAKwF,mBAAmBhF,YAAYoD,0BAA0B5D,KAAKwF;4BAC7Ee,cAAc5C,mBAAmB,MAAMwC;4BACvCM,UAAUD,QAAQlC,YAAYiC;4BAC9B,IAAIE,YAAY,MAAM;gCAClB,OAAO,EAAC;AACX;4BACDiC,gBAAgB7B,KAAKK,MAAMT;4BAC3B,MAAMiC,cAAc/C,OAAO3F,KAAK2F,KAAK,OAAO,EAAC,GAAa;4BAC1D,OAAO,EAAC,GAAa4B,YAAc9F,UAAUZ,KAAK6H,cAAcrC;;0BACpE,KAAK;4BACDvC,GAAGnB;4BACH3C,KAAKyF,eAAevE,OAAOwH,cAAcrC;4BACzCG,QAAQhC,eAAe+B;4BACvBgB,YAAc9F,UAAUR,OAAOyH,cAAcrC;4BAC7Cd,gBAAgBgD;4BAChBzE,GAAGpB,QAAQ;;0BACf,KAAK;4BAAG,OAAO,EAAC;;AAEpC;AACA;AACA;YAOI6C,gBAAgB6B,gBAAgB,SAAU5B;gBACtC,IAAImD,mBAAmBvD,KAAKC,QAAQ;gBACpC,IAAImB,UAAUhB;gBACd,IAAIoD,OAAO;gBACX,IAAIC,YAAY;gBAChB,OAAO,MAAM;oBACT,IAAIzI,MAAMoG,QAAQnC,QAAQwE;oBAC1B,IAAIzI,QAAQ,MAAM;wBACd;AACH;oBACDwI,KAAKlF,KAAKtD;oBACVyI;AACH;gBACD,IAAIN,gBAAgB;gBACpB,KAAK,IAAIrJ,IAAI,GAAGA,IAAI0J,KAAKzJ,QAAQD,KAAK;oBAClC,IAAI4J,WAAWF,KAAK1J;oBACpB,IAAI4J,SAASC,SAASpF,mBAAmB;wBACrC,IAAI8C,UAAUD,QAAQlC,YAAYwE;wBAClC,IAAIrC,YAAY,MAAM;4BAClB,IAAIiC,gBAAgB7B,KAAKK,MAAMT;4BAC/B,IAAKiC,cAAczB,kBAAkBzG,aAAakI,cAAc1B,eAAe2B,oBAC1ED,cAAczB,kBAAkBzG,aAAakI,cAAczB,gBAAgB0B,kBAAmB;gCAC/FnC,QAAQhC,eAAesE;gCACvBP,gBAAgB;AACnB;AACJ;AACJ;AACJ;gBACD,IAAIA,eAAe;oBACfhD,gBAAgBgD;AACnB;AACT;YACIhD,gBAAgBW,UAAU1F;YAC1B,OAAO+E;AACX;QACA3F,QAAA6B,UAAkB8D;;;IC/YlB,IAAAyD,UAAe;ICMR,MAAMC,uCAAuC;IAK7C,MAAMC,+BAAmD;QAC9DC,kBAAkBF;;IAMb,MAAMG,mCAAmC;IAKzC,MAAMC,oCAAoC;IAK1C,MAAMC,2BAA2B;IAEjC,MAAMC,wBAAwB;IAM9B,MAAMC,sCAAsC;IAK5C,MAAMC,sCAAsC;IAK5C,MAAMC,gBAAgB;IAKtB,MAAMC,oCAAoC;IAK1C,MAAMC,uBAAuB;QAClClK,MAAM;QACNsJ,SAASA;;IAGJ,MAAMa,uBAAuB,MAAMzE,KAAKC;IC1DzC,MAAOyE,qBAAqBrK;QAChCsK,YAAmBzK,OAAsB0K;YACvCC,MAAMD;YADWhK,KAAKV,QAALA;YAAsBU,KAAiBgK,oBAAjBA;YAEvCpL,OAAOsL,eAAelK,MAAM8J,aAAajL;AAC1C;QAEDsL,oBAAmB7K,OACjBA,OAAK0K,mBACLA;YAKA,OAAO,IAAIF,aAAaxK,OAAO0K;AAChC;;IAOG,MAAOI,4BAA4BN;QACvCC,YACEzK,OACA0K,mBACOK,OACAC,WAAgB;YAEvBL,MAAM3K,OAAO0K;YAHNhK,KAAKqK,QAALA;YACArK,KAAQsK,WAARA;YAIP1L,OAAOsL,eAAelK,MAAMoK,oBAAoBvL;AACjD;;IAOG,MAAO0L,qBAAqBT;QAChCC;YACEE,MAAM,WAAW;YAEjBrL,OAAOsL,eAAelK,MAAMuK,aAAa1L;AAC1C;;IAMG,MAAO2L,0BAA0BD;QACrCR,YAAmBU;YACjBR;YADiBjK,KAAKyK,QAALA;YAGjB7L,OAAOsL,eAAelK,MAAMwK,kBAAkB3L;AAC/C;;IAGG,MAAO6L,4BAA4BZ;QACvCC,YAAmBU;YACjBR,MAAM,aAAa;YADFjK,KAAKyK,QAALA;YAGjB7L,OAAOsL,eAAelK,MAAM0K,oBAAoB7L;AACjD;;IAMG,MAAO8L,yBAAyBb;QACpCC,YACEzK,OACA0K,mBACOY;YAEPX,MAAM3K,OAAO0K;YAFNhK,KAAS4K,YAATA;YAIPhM,OAAOsL,eAAelK,MAAM2K,iBAAiB9L;AAC9C;;IAMG,MAAOgM,iCAAiCf;QAC5CC,YAAmBe,UAAyBC;YAC1Cd,MACE,yBACA,qCAAqCe,mBAAmBF,UAAU,EAChE,0BACcE,mBAAmBD;YALpB/K,KAAQ8K,WAARA;YAAyB9K,KAAK+K,QAALA;YAO1CnM,OAAOsL,eAAelK,MAAM6K,yBAAyBhM;AACtD;;IASH,SAASmM,mBAAmBnL,OAAeoL,UAAoB;QAC7D,OAAOpL,UAAUoL,QAAQlC,SAASlJ,SAASA,QAAQ;AACrD;IC5FO,MAAMqL,4BACXC;QAEA,IAAIA,YAAYnM,QAAQ,QAAQ,GAAG;YACjCmM,cAAcA,YAAYC,UAAU,GAAGD,YAAYnM,QAAQ;AAC5D;QAED,MAAMqM,eAAe,IAAIC,gBAAgBH;QAEzC,OAAO;YACLd,OAAOgB,aAAa9K,IAAI;YACxBgL,MAAMF,aAAa9K,IAAI,WAAWC;YAClClB,OAAO+L,aAAa9K,IAAI,YAAYC;YACpCwJ,mBAAmBqB,aAAa9K,IAAI,wBAAwBC;;AAC7D;IAGI,MAAMgL,YAAY,CACvBC,cACAC,aACAvC,mBAA2BF,yCAEpB,IAAInI,SAA8B,CAAC6K,KAAKC;QAC7C,MAAMC,SAAS5H,OAAO6H,SAASC,cAAc;QAE7CF,OAAOG,aAAa,SAAS;QAC7BH,OAAOG,aAAa,UAAU;QAC9BH,OAAOI,MAAMC,UAAU;QAEvB,MAAMC,eAAe;YACnB,IAAIlI,OAAO6H,SAAStJ,KAAK4J,SAASP,SAAS;gBACzC5H,OAAO6H,SAAStJ,KAAK6J,YAAYR;gBACjC5H,OAAO4D,oBAAoB,WAAWyE,oBAAoB;AAC3D;AAAA;QAGH,IAAIA;QAEJ,MAAMC,sBAAsBlL,YAAW;YACrCuK,IAAI,IAAIrB;YACR4B;AAAc,YACbhD,mBAAmB;QAEtBmD,qBAAqB,SAAU7N;YAC7B,IAAIA,EAAE+N,UAAUd,aAAa;YAC7B,KAAKjN,EAAEgO,QAAQhO,EAAEgO,KAAKC,SAAS,0BAA0B;YAEzD,MAAMC,cAAclO,EAAEmO;YAEtB,IAAID,aAAa;gBACdA,YAAoBE;AACtB;YAEDpO,EAAEgO,KAAKK,SAASxN,QACZsM,IAAI9B,aAAaiD,YAAYtO,EAAEgO,KAAKK,aACpCnB,IAAIlN,EAAEgO,KAAKK;YAEf/E,aAAawE;YACbtI,OAAO4D,oBAAoB,WAAWyE,oBAAoB;YAI1DjL,WAAW8K,cAAc9C,oCAAoC;AAC/D;QAEApF,OAAOiE,iBAAiB,WAAWoE,oBAAoB;QACvDrI,OAAO6H,SAAStJ,KAAKwK,YAAYnB;QACjCA,OAAOG,aAAa,OAAOP;AAAa;IAIrC,MAAMwB,YAAaC;QACxB,MAAMC,QAAQ;QACd,MAAMC,SAAS;QACf,MAAMC,OAAOpJ,OAAOqJ,WAAWrJ,OAAOsJ,aAAaJ,SAAS;QAC5D,MAAMK,MAAMvJ,OAAOwJ,WAAWxJ,OAAOyJ,cAAcN,UAAU;QAE7D,OAAOnJ,OAAO0J,KACZT,KACA,yBACA,QAAQG,YAAYG,aAAaL,gBAAgBC;AAClD;IAGI,MAAMQ,WAAYC,UAChB,IAAI/M,SAA8B,CAACC,SAASC;QACjD,IAAI8M;QAGJ,MAAMC,aAAaC,aAAY;YAC7B,IAAIH,OAAOpD,SAASoD,OAAOpD,MAAMwD,QAAQ;gBACvCC,cAAcH;gBACdhG,aAAaoG;gBACblK,OAAO4D,oBAAoB,WAAWiG,oBAAoB;gBAC1D9M,OAAO,IAAI0J,oBAAoBmD,OAAOpD;AACvC;AAAA,YACA;QAEH,MAAM0D,YAAY9M,YAAW;YAC3B6M,cAAcH;YACd/M,OAAO,IAAIwJ,kBAAkBqD,OAAOpD;YACpCxG,OAAO4D,oBAAoB,WAAWiG,oBAAoB;AAAM,aAC9DD,OAAO1E,oBAAoBF,wCAAwC;QAEvE6E,qBAAqB,SAAUrP;YAC7B,KAAKA,EAAEgO,QAAQhO,EAAEgO,KAAKC,SAAS,0BAA0B;gBACvD;AACD;YAED3E,aAAaoG;YACbD,cAAcH;YACd9J,OAAO4D,oBAAoB,WAAWiG,oBAAoB;YAC1DD,OAAOpD,MAAMoC;YAEb,IAAIpO,EAAEgO,KAAKK,SAASxN,OAAO;gBACzB,OAAO0B,OAAO8I,aAAaiD,YAAYtO,EAAEgO,KAAKK;AAC/C;YAED/L,QAAQtC,EAAEgO,KAAKK;AACjB;QAEA7I,OAAOiE,iBAAiB,WAAW4F;AAAmB;IAInD,MAAMM,YAAY,MAChBnK,OAAOoK;IAGT,MAAMC,qBAAqB;QAChC,MAAMC,UACJ;QACF,IAAIrJ,SAAS;QACb,MAAMsJ,eAAeC,MAAMC,KACzBN,YAAYO,gBAAgB,IAAIC,WAAW;QAE7CJ,aAAa/F,SAAQlF,KAAM2B,UAAUqJ,QAAQhL,IAAIgL,QAAQpP;QACzD,OAAO+F;AAAM;IAGR,MAAM2J,SAAUhP,SAAkBiP,KAAKjP;IAG9C,MAAMkP,iBAAkBC,UACfpQ,OAAOqQ,KAAKD,QAChB1G,QAAO4G,YAAYF,OAAOE,OAAO,cACjCC,QAAO,CAACC,KAAKhP,QAAQxB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAMD,MAAG;QAAEhP,CAACA,MAAM4O,OAAO5O;SAAS,CAAA;IAGrD,MAAMkP,oBAAqBxL;aAAEyL,UAAUC,aAAS1L,IAAKkL,SAAMzQ,OAAAuF,IAAhC;QAChC,OAAO,IAAIwH,gBACTyD,eAAiBnQ,OAAAyQ,OAAA;YAAAG;WAAcR,UAC/B1J;AAAU;IAGP,MAAMmK,SAASC,MAAOlR;QAC3B,MAAMmR,WAAgBvB,YAAYwB,OAAOC,OACvC;YAAEnQ,MAAM;YACR,IAAIoQ,aAAcjB,OAAOrQ;QAG3B,aAAamR;AAAQ;IAGvB,MAAMI,eAAgBC;QACpB,MAAMC,WAAwC;YAAE,KAAK;YAAK,KAAK;YAAK,KAAK;;QACzE,OAAOD,MAAME,QAAQ,WAAWC,KAAcF,SAASE;AAAG;IAI5D,MAAMC,YAAaJ,SACjBK,mBACEC,KAAKN,OACFO,MAAM,IACNC,KAAIC,KACI,OAAO,OAAOA,EAAEC,WAAW,GAAGpL,SAAS,KAAKkD,OAAO,KAE3DmI,KAAK;IAGL,MAAMC,eAAgBZ,SAC3BI,UAAUJ,MAAME,QAAQ,MAAM,KAAKA,QAAQ,MAAM;IAE5C,MAAMW,2BAA4Bb;QACvC,MAAMc,gBAAgB,IAAIlC,WAAWoB;QACrC,OAAOD,aACL9L,OAAO6K,KAAKiC,OAAOC,gBAAgBvC,MAAMC,KAAKoC;AAC/C;IAGI,MAAMG,iBAAiB;QAC5B,KAAK7C,aAAa;YAChB,MAAM,IAAI3O,MACR;AAEH;QACD,WAAW2O,YAAYwB,WAAW,aAAa;YAC7C,MAAM,IAAInQ,MAAM;AAGjB;AAAA;IAMI,MAAMyR,YAAaC;QACxB,KAAK,eAAeC,KAAKD,YAAY;YACnC,OAAO,WAAWA;AACnB;QAED,OAAOA;AAAS;IAMX,MAAME,iBAAiB,CAC5BC,QACAH;QAEA,IAAIG,QAAQ;YACV,OAAOA,OAAOC,WAAW,cAAcD,SAAS,WAAWA;AAC5D;QAED,OAAO,GAAGH;AAAY;IAGjB,MAAMK,cAAe3R;QAC1B,WAAWA,UAAU,UAAU;YAC7B,OAAOA;AACR;QACD,OAAO4R,SAAS5R,OAAO,OAAOW;AAAS;IC/OlC,MAAMkR,cAAc,CAAClS,SAAoCmS,OAC9D,IAAI7Q,SAAQ,SAAUC,SAASC;QAC7B,MAAM4Q,iBAAiB,IAAIC;QAE3BD,eAAeE,MAAMC,YAAY,SAAUC;YAEzC,IAAIA,MAAMvF,KAAKnN,OAAO;gBACpB0B,OAAO,IAAIvB,MAAMuS,MAAMvF,KAAKnN;AAC7B,mBAAM;gBACLyB,QAAQiR,MAAMvF;AACf;YACDmF,eAAeE,MAAMjF;AACvB;QAEA8E,GAAGM,YAAYzS,SAAS,EAACoS,eAAeM;AAC1C;ICTK,MAAMC,wBAAwB,MAAM,IAAIC;IAE/C,MAAMC,UAAU3C,OAAO4C,UAAkBC;QACvC,MAAMzF,iBAAiB0F,MAAMF,UAAUC;QAEvC,OAAO;YACLE,IAAI3F,SAAS2F;YACbC,YAAY5F,SAAS4F;;AACtB;IAGH,MAAMC,qBAAqBjD,OACzB4C,UACAC,cACAnM;QAEA,MAAMwM,aAAaT;QACnBI,aAAaM,SAASD,WAAWC;QAEjC,IAAI1E;QAGJ,OAAOrN,QAAQgS,KAAK,EAClBT,QAAQC,UAAUC,eAElB,IAAIzR,SAAQ,CAAC2B,GAAGzB;YACdmN,YAAY9M,YAAW;gBACrBuR,WAAWG;gBACX/R,OAAO,IAAIvB,MAAM;AAAkC,gBAClD2G;AAAQ,eAEZ4M,SAAQ;YACTjL,aAAaoG;AAAU;AACvB;IAGJ,MAAM8E,kBAAkBvD,OACtB4C,UACAxH,UACAC,OACAwH,cACAnM,SACA8M,QACAC,gBAEOzB,YACL;QACE0B,MAAM;YACJtI;YACAC;;QAEF3E;QACAkM;QACAC;QACAY;OAEFD;IAIG,MAAMG,cAAc3D,OACzB4C,UACAxH,UACAC,OACAwH,cACAW,QACAC,aACA/M,UAAUkD;QAEV,IAAI4J,QAAQ;YACV,OAAOD,gBACLX,UACAxH,UACAC,OACAwH,cACAnM,SACA8M,QACAC;AAEH,eAAM;YACL,OAAOR,mBAAmBL,UAAUC,cAAcnM;AACnD;AAAA;IAGIsJ,eAAe4D,QACpBpG,KACA9G,SACA0E,UACAC,OACAwI,SACAL,QACAC;QAEA,IAAIK,aAA2B;QAC/B,IAAI1G;QAEJ,KAAK,IAAI5N,IAAI,GAAGA,IAAIkK,kCAAkClK,KAAK;YACzD;gBACE4N,iBAAiBuG,YACfnG,KACApC,UACAC,OACAwI,SACAL,QACAC,aACA/M;gBAEFoN,aAAa;gBACb;AAOD,cANC,OAAO/U;gBAKP+U,aAAa/U;AACd;AACF;QAED,IAAI+U,YAAY;YACd,MAAMA;AACP;QAED,MACE1P,KAEEgJ,SAAQ4F,OAFVpT,OAAQA,OAAK0K,mBAAEA,qBAAiBlG,IAAK2I,OAAIlO,OAAAuF,IAAnC,oCADF2O,IAEJA,MACE3F;QAEJ,KAAK2F,IAAI;YACP,MAAMgB,eACJzJ,qBAAqB,+BAA+BkD;YAEtD,IAAI5N,UAAU,gBAAgB;gBAC5B,MAAM,IAAIqL,iBAAiBrL,OAAOmU,cAAchH,KAAK7B;AACtD;YAED,IAAItL,UAAU,yBAAyB;gBACrC,MAAM,IAAIuL,yBAAyBC,UAAUC;AAC9C;YAED,MAAM,IAAIjB,aAAaxK,SAAS,iBAAiBmU;AAClD;QAED,OAAOhH;AACT;ICvJOiD,eAAegE,WACpB5P,IASAoP;QATA,KAAAS,SACEA,SAAOvN,SACPA,SAAO0E,UACPA,UAAQC,OACRA,OAAK6I,aACLA,aAAWT,aACXA,eAEqBrP,IADlByP,UAAOhV,OAAAuF,IAPZ;QAWA,MAAMtB,OAAO2Q,cACT7D,kBAAkBiE,WAClB1M,KAAKC,UAAUyM;QAEnB,aAAaD,QACX,GAAGK,uBACHvN,SACA0E,YAAY,WACZC,OACA;YACE8I,QAAQ;YACRrR;YACAsR,SAAS;gBACP,gBAAgBX,cACZ,sCACA;gBACJ,gBAAgBrE,KACdjI,KAAKC,UAAU8M,eAAehK;;WAIpCsJ,QACAC;AAEJ;ICtCA,MAAMY,SAAUC,OAAkBvF,MAAMC,KAAK,IAAIhJ,IAAIsO;IAW9C,MAAMC,kBAAkB,IAAIC,WAC1BH,OAAOG,OAAO5L,OAAO6L,SAASxD,KAAK,KAAKyD,OAAO7D,MAAM,QAAQI,KAAK;ICbpE,MAAM0D,mBAAmB;IACzB,MAAMC,4BAA4B;UAQ5BC;QAKXxK,YACE0C,MACO+H,SAAiBH,kBACjBI;YADAzU,KAAMwU,SAANA;YACAxU,KAAMyU,SAANA;YAEPzU,KAAKuP,WAAW9C,KAAK8C;YACrBvP,KAAK+K,QAAQ0B,KAAK1B;YAClB/K,KAAK8K,WAAW2B,KAAK3B;AACtB;QAMD4J;YACE,OAAO,EAAC1U,KAAKwU,QAAQxU,KAAKuP,UAAUvP,KAAK8K,UAAU9K,KAAK+K,OAAO/K,KAAKyU,SACjEnM,OAAO6L,SACPxD,KAAK;AACT;QAODxG,eAAe/J;YACb,OAAOoU,QAAQjF,UAAUzE,UAAUC,SAAS3K,IAAImQ,MAAM;YAEtD,OAAO,IAAIgE,SAAS;gBAAEhF;gBAAUxE;gBAAOD;eAAY0J;AACpD;QAODrK,sBAAsBwK;YACpB,OAAM5J,OAAEA,OAAKD,UAAEA,UAAU0E,WAAWD,YAAaoF;YAEjD,OAAO,IAAIJ,SAAS;gBAClBxJ;gBACAD;gBACAyE;;AAEH;;UC1DUqF;QACJnU,IAAmBL,KAAauU;YACrCzQ,aAAaE,QAAQhE,KAAKyG,KAAKC,UAAU6N;AAC1C;QAEMpU,IAAmBH;YACxB,MAAMsS,OAAOzO,OAAOC,aAAaH,QAAQ3D;YAEzC,KAAKsS,MAAM;YAEX;gBACE,MAAMmC,UAAUhO,KAAKK,MAAMwL;gBAC3B,OAAOmC;AAIR,cAFC,OAAOpW;gBACP;AACD;AACF;QAEMqW,OAAO1U;YACZ8D,aAAaC,WAAW/D;AACzB;QAEM2U;YACL,OAAOnW,OAAOqQ,KAAKhL,OAAOC,cAAcoE,QAAOlI,OAC7CA,IAAImR,WAAW8C;AAElB;;UC3BUW;QAAbjL;YACS/J,KAAAiV,gBAAwB;gBAC7B,IAAIC,QAAiC,CAAA;gBAErC,OAAO;oBACLzU,IAAmBL,KAAauU;wBAC9BO,MAAM9U,OAAOuU;AACd;oBAEDpU,IAAmBH;wBACjB,MAAM+U,aAAaD,MAAM9U;wBAEzB,KAAK+U,YAAY;4BACf;AACD;wBAED,OAAOA;AACR;oBAEDL,OAAO1U;+BACE8U,MAAM9U;AACd;oBAED2U;wBACE,OAAOnW,OAAOqQ,KAAKiG;AACpB;;AAEJ,aA1B8B;AA2BhC;;IChBD,MAAME,oCAAoC;UAE7BC;QAGXtL,YACUmL,OACAI,aACRC;YAFQvV,KAAKkV,QAALA;YACAlV,KAAWsV,cAAXA;YAGRtV,KAAKuV,cAAcA,eAAe1L;AACnC;QAED6F,iBACEH,UACAiG,SACAC;;YAEA,MAAMC,WAAW1V,KAAK2V,mBAAmBpG;kBACnCvP,KAAKkV,MAAMzU,IAAIiV,UAAU;gBAC7BE,UAAUJ;gBACVC;;oBAEI3R,KAAA9D,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAqD,IAAIuO;AAC7B;QAEDhG,iBAAiBgG;YACf,MAAMf,cAAc3U,KAAKkV,MAAM3U,IAC7BP,KAAK2V,mBAAmBD,SAASnG;YAGnC,KAAKoF,SAASe,SAAS3K,SAAS2K,SAAS5K,UAAU;gBACjD,MAAM+K,qBAAqB7V,KAAKO,IAAImV;gBAEpC,KAAKG,cAAc;oBACjB;AACD;gBAED,KAAKA,aAAaD,aAAaC,aAAaJ,cAAc;oBACxD;AACD;gBAED,OAAO;oBACLG,UAAUC,aAAaD;oBACvBH,cAAcI,aAAaJ;;AAE9B;YAED,KAAKd,OAAO;gBACV;AACD;YAED,OAAO;gBAAEiB,UAAUjB,MAAMiB;gBAAUH,cAAcd,MAAMc;;AACxD;QAED/F,UACEgG,UACAI,0BAA0BV;;YAE1B,IAAIW,qBAAqB/V,KAAKkV,MAAM3U,IAClCmV,SAAShB;YAGX,KAAKqB,cAAc;gBACjB,MAAM9G,aAAajP,KAAKgW;gBAExB,KAAK/G,MAAM;gBAEX,MAAMgH,aAAajW,KAAKkW,sBAAsBR,UAAUzG;gBAExD,IAAIgH,YAAY;oBACdF,qBAAqB/V,KAAKkV,MAAM3U,IAAuB0V;AACxD;AACF;YAGD,KAAKF,cAAc;gBACjB;AACD;YAED,MAAM1Q,YAAYrF,KAAKuV;YACvB,MAAMY,aAAanR,KAAKC,MAAMI,MAAM;YAEpC,IAAI0Q,aAAaK,YAAYN,0BAA0BK,YAAY;gBACjE,IAAIJ,aAAavT,KAAK6T,eAAe;oBACnCN,aAAavT,OAAO;wBAClB6T,eAAeN,aAAavT,KAAK6T;;0BAG7BrW,KAAKkV,MAAMzU,IAAIiV,SAAShB,SAASqB;oBACvC,OAAOA,aAAavT;AACrB;sBAEKxC,KAAKkV,MAAMJ,OAAOY,SAAShB;wBAC3B5Q,KAAA9D,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAgR,OAAOY,SAAShB;gBAExC;AACD;YAED,OAAOqB,aAAavT;AACrB;QAEDkN,UAAUiF;;YACR,MAAMe,WAAW,IAAInB,SAAS;gBAC5BhF,UAAUoF,MAAMnF;gBAChBzE,OAAO4J,MAAM5J;gBACbD,UAAU6J,MAAM7J;;YAGlB,MAAMiL,qBAAqB/V,KAAKsW,eAAe3B;kBAEzC3U,KAAKkV,MAAMzU,IAAIiV,SAAShB,SAASqB;oBACjCjS,KAAA9D,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAqD,IAAIuO,SAAShB;AACtC;QAEDhF,YAAYH;;YACV,MAAMN,aAAajP,KAAKgW;YAGxB,KAAK/G,MAAM;kBAELA,KACH3G,QAAOlI,OAAQmP,WAAWnP,IAAI2I,SAASwG,YAAY,OACnDJ,QAAOO,OAAO6G,MAAMnW;sBACbmW;sBACAvW,KAAKkV,MAAMJ,OAAO1U;AAAI,gBAC3BU,QAAQC;yBAEPf,KAAKsV,iBAAa,QAAAxR,YAAA,SAAA,IAAAA,GAAAE;AACzB;QAEO0L,qBAAqBiF;YAC3B,MAAMtP,YAAYrF,KAAKuV;YACvB,MAAMiB,gBAAgBxR,KAAKC,MAAMI,MAAM,OAAQsP,MAAM8B;YAErD,OAAO;gBACLjU,MAAMmS;gBACNyB,WAAWI;;AAEd;QAEO9G;;YACN,IAAI1P,KAAKsV,aAAa;gBACpB,QAAOxR,WAAO9D,KAAKsV,YAAY/U,WAAQ,QAAAuD,YAAA,SAAA,IAAAA,GAAAmL;AACxC,mBAAM,IAAIjP,KAAKkV,MAAMH,SAAS;gBAC7B,OAAO/U,KAAKkV,MAAMH;AACnB;AACF;QAOOY,mBAAmBpG;YACzB,OAAO,IAAIgF,SACT;gBAAEhF;eACF8E,kBACAC,2BACAI;AACH;QAcOwB,sBAAsBQ,YAAsB3B;YAClD,OAAOA,QAAQzM,QAAOlI;;gBACpB,MAAMsV,WAAWnB,SAASoC,QAAQvW;gBAClC,MAAMwW,WAAW,IAAIlR,IAAIgQ,SAAS3K,SAAS2K,SAAS3K,MAAMwF,MAAM;gBAChE,MAAMsG,kBAAgB/S,KAAA4S,WAAW3L,WAAO,QAAAjH,YAAA,SAAA,IAAAA,GAAAyM,MAAM,SAAQ;gBAEtD,MAAMuG,eACJpB,SAAS3K,SACT8L,cAAc1H,QACZ,CAACC,KAAK2H,YAAY3H,OAAOwH,SAAShW,IAAImW,WACtC;gBAGJ,OACErB,SAASlB,WAAWH,oBACpBqB,SAASnG,aAAamH,WAAWnH,YACjCmG,SAAS5K,aAAa4L,WAAW5L,YACjCgM;AACA,gBACD;AACJ;;IC9MH,MAAME,iCAAiC;UAa1BC;QAGXlN,YACUmN,SACA3H,UACA4H;YAFAnX,KAAOkX,UAAPA;YACAlX,KAAQuP,WAARA;YACAvP,KAAYmX,eAAZA;YAERnX,KAAKqH,aAAa,GAAG2P,kCAAkChX,KAAKuP;AAC7D;QAEM6H,OAAOC;YACZrX,KAAKkX,QAAQI,KAAKtX,KAAKqH,YAAYgQ,aAAa;gBAC9CE,iBAAiB;gBACjBJ,cAAcnX,KAAKmX;;AAEtB;QAEM5W;YACL,OAAOP,KAAKkX,QAAQ3W,IAAIP,KAAKqH;AAC9B;QAEMyN;YACL9U,KAAKkX,QAAQpC,OAAO9U,KAAKqH,YAAY;gBACnC8P,cAAcnX,KAAKmX;;AAEtB;;ICtCH,MAAMK,WAAYlU,YAAkBA,MAAM;IAE1C,MAAMmU,iBAAiB,EACrB,OACA,OACA,OACA,OACA,OACA,OACA,OACA,SACA,aACA,WACA,UACA,OACA,OACA,WACA,OACA,gBACA,YACA,cACA,gBACA,kBACA,QACA,QACA,OACA,UACA,OACA,OACA,OACA,OACA,OACA;IAGK,MAAMC,SAAUC;QACrB,MAAMC,QAAQD,MAAMpH,MAAM;QAC1B,OAAOsH,QAAQhD,SAASiD,aAAaF;QAErC,IAAIA,MAAMzY,WAAW,MAAM0Y,WAAWhD,YAAYiD,WAAW;YAC3D,MAAM,IAAIrY,MAAM;AACjB;QACD,MAAMsY,cAAclR,KAAKK,MAAM0J,aAAaiE;QAC5C,MAAMmD,SAAkB;YAAEC,OAAON;;QACjC,MAAMO,OAAY,CAAA;QAClBtZ,OAAOqQ,KAAK8I,aAAatP,SAAQyG;YAC/B8I,OAAO9I,KAAK6I,YAAY7I;YACxB,KAAKuI,eAAe1O,SAASmG,IAAI;gBAC/BgJ,KAAKhJ,KAAK6I,YAAY7I;AACvB;AAAA;QAEH,OAAO;YACLiJ,SAAS;gBAAEN;gBAAQhD;gBAASiD;;YAC5BD,QAAQhR,KAAKK,MAAM0J,aAAaiH;YAChCG;YACAE;;AACD;IAGI,MAAME,SAAU7E;QACrB,KAAKA,QAAQqC,UAAU;YACrB,MAAM,IAAInW,MAAM;AACjB;QAED,MAAM4Y,UAAUX,OAAOnE,QAAQqC;QAE/B,KAAKyC,QAAQL,OAAOM,KAAK;YACvB,MAAM,IAAI7Y,MACR;AAEH;QAED,IAAI4Y,QAAQL,OAAOM,QAAQ/E,QAAQ+E,KAAK;YACtC,MAAM,IAAI7Y,MACR,0DAA0D8T,QAAQ+E,gBAAgBD,QAAQL,OAAOM;AAEpG;QAED,KAAKD,QAAQH,KAAKK,KAAK;YACrB,MAAM,IAAI9Y,MACR;AAEH;QAED,IAAI4Y,QAAQR,OAAOW,QAAQ,SAAS;YAClC,MAAM,IAAI/Y,MACR,2BAA2B4Y,QAAQR,OAAOW;AAE7C;QAED,KACGH,QAAQL,OAAOS,gBAEPJ,QAAQL,OAAOS,QAAQ,YAC9BhK,MAAMiK,QAAQL,QAAQL,OAAOS,OAE/B;YACA,MAAM,IAAIhZ,MACR;AAEH;QACD,IAAIgP,MAAMiK,QAAQL,QAAQL,OAAOS,MAAM;YACrC,KAAKJ,QAAQL,OAAOS,IAAI1P,SAASwK,QAAQkF,MAAM;gBAC7C,MAAM,IAAIhZ,MACR,4DACE8T,QAAQkF,4BACeJ,QAAQL,OAAOS,IAAI9H,KAAK;AAEpD;YACD,IAAI0H,QAAQL,OAAOS,IAAItZ,SAAS,GAAG;gBACjC,KAAKkZ,QAAQL,OAAOW,KAAK;oBACvB,MAAM,IAAIlZ,MACR;AAEH;gBACD,IAAI4Y,QAAQL,OAAOW,QAAQpF,QAAQkF,KAAK;oBACtC,MAAM,IAAIhZ,MACR,oEAAoE8T,QAAQkF,gBAAgBJ,QAAQL,OAAOW;AAE9G;AACF;AACF,eAAM,IAAIN,QAAQL,OAAOS,QAAQlF,QAAQkF,KAAK;YAC7C,MAAM,IAAIhZ,MACR,4DAA4D8T,QAAQkF,mBAAmBJ,QAAQL,OAAOS;AAEzG;QACD,IAAIlF,QAAQqF,OAAO;YACjB,KAAKP,QAAQL,OAAOY,OAAO;gBACzB,MAAM,IAAInZ,MACR;AAEH;YACD,IAAI4Y,QAAQL,OAAOY,UAAUrF,QAAQqF,OAAO;gBAC1C,MAAM,IAAInZ,MACR,2DAA2D8T,QAAQqF,kBAAkBP,QAAQL,OAAOY;AAEvG;AACF;QAED,IAAIrF,QAAQsF,YAAYrB,SAASa,QAAQL,OAAOc,YAAY;YAC1D,MAAM,IAAIrZ,MACR;AAEH;QAGD,IAAI4Y,QAAQL,OAAOe,OAAO,SAASvB,SAASa,QAAQL,OAAOe,MAAM;YAC/D,MAAM,IAAItZ,MACR;AAEH;QACD,KAAK+X,SAASa,QAAQL,OAAO3R,MAAM;YACjC,MAAM,IAAI5G,MACR;AAEH;QAED,MAAMuZ,SAASzF,QAAQyF,UAAU;QACjC,MAAM3T,MAAM,IAAID,KAAKmO,QAAQlO,OAAOD,KAAKC;QACzC,MAAM4T,UAAU,IAAI7T,KAAK;QAEzB6T,QAAQC,cAAcb,QAAQL,OAAOe,MAAMC;QAE3C,IAAI3T,MAAM4T,SAAS;YACjB,MAAM,IAAIxZ,MACR,oEAAoE4F,kCAAkC4T;AAEzG;QAED,IAAIZ,QAAQL,OAAOmB,OAAO,QAAQ3B,SAASa,QAAQL,OAAOmB,MAAM;YAC9D,MAAMC,UAAU,IAAIhU,KAAK;YACzBgU,QAAQF,cAAcb,QAAQL,OAAOmB,MAAMH;YAC3C,IAAI3T,MAAM+T,SAAS;gBACjB,MAAM,IAAI3Z,MACR,+GAA+G4F,kBAAkB+T;AAEpI;AACF;QAED,IAAIf,QAAQL,OAAOc,aAAa,QAAQtB,SAASa,QAAQL,OAAOc,YAAY;YAC1E,MAAMO,eAAe,IAAIjU,KAAK;YAC9BiU,aAAaH,cACXzH,SAAS4G,QAAQL,OAAOc,aAAcvF,QAAQsF,UAAqBG;YAGrE,IAAI3T,MAAMgU,cAAc;gBACtB,MAAM,IAAI5Z,MACR,uJAAuJ4F,8BAA8BgU;AAExL;AACF;QAED,IAAI9F,QAAQ+F,cAAc;YACxB,MAAMC,MAAMhG,QAAQ+F,aAAalF;YACjC,IAAImF,IAAIhI,WAAW,SAAS;gBAC1B,MAAMiI,QAAQD;gBACd,KAAKlB,QAAQL,OAAOyB,QAAQ;oBAC1B,MAAM,IAAIha,MACR;AAEH,uBAAM,IAAI+Z,UAAUnB,QAAQL,OAAOyB,QAAQ;oBAC1C,MAAM,IAAIha,MACR,sEAAsE+Z,kBAAkBnB,QAAQL,OAAOyB;AAE1G;AACF,mBAAM;gBACL,MAAMC,UAAUH,IAAII;gBAEpB,KAAKtB,QAAQL,OAAO4B,UAAU;oBAC5B,MAAM,IAAIna,MACR;AAEH,uBAAM,IAAIia,YAAYrB,QAAQL,OAAO4B,UAAU;oBAC9C,MAAM,IAAIna,MACR,0EAA0Eia,oBAAoBrB,QAAQL,OAAO4B;AAEhH;AACF;AACF;QAED,OAAOvB;AAAO;;QC9NhB,IAAIwB,WAAY7Z,kBAAQA,eAAK6Z,YAAa;YACtCA,WAAWjb,OAAOyQ,UAAU,SAAS3Q;gBACjC,KAAK,IAAIF,GAAGU,IAAI,GAAGoE,IAAIwW,UAAU3a,QAAQD,IAAIoE,GAAGpE,KAAK;oBACjDV,IAAIsb,UAAU5a;oBACd,KAAK,IAAIP,KAAKH,GAAG,IAAII,OAAOC,UAAUC,eAAeC,KAAKP,GAAGG,IACzDD,EAAEC,KAAKH,EAAEG;AAChB;gBACD,OAAOD;AACf;YACI,OAAOmb,SAASvX,MAAMtC,MAAM8Z;AAChC;QACAla,QAAkBma,aAAG;QACrB,SAASC,mBAAmBta,MAAMG;YAC9B,KAAKA,OAAO;gBACR,OAAO;AACV;YACD,IAAIoa,cAAc,OAAOva;YACzB,IAAIG,UAAU,MAAM;gBAChB,OAAOoa;AACV;YACD,OAAOA,cAAc,MAAMpa;AAC/B;QACA,SAASqa,oBAAoBC;YACzB,WAAWA,WAAWC,YAAY,UAAU;gBACxC,IAAIA,UAAU,IAAIhV;gBAClBgV,QAAQC,gBAAgBD,QAAQE,oBAAoBH,WAAWC,UAAU;gBACzED,WAAWC,UAAUA;AACxB;YACD,OAAOJ,mBAAmB,WAAWG,WAAWC,UAAUD,WAAWC,QAAQG,gBAAgB,MACvFP,mBAAmB,UAAUG,WAAWK,UACxCR,mBAAmB,QAAQG,WAAWM,QACtCT,mBAAmB,UAAUG,WAAWO,UACxCV,mBAAmB,YAAYG,WAAWQ;AACpD;QACA,SAAS9L,OAAOnP,MAAMG,OAAOsa;YACzB,OAAOS,mBAAmBlb,MACrBwQ,QAAQ,4BAA4BG,oBACpCH,QAAQ,OAAO,OAAOA,QAAQ,OAAO,SACpC,MAAM0K,mBAAmB/a,OAE1BqQ,QAAQ,6DAA6DG,sBACpE6J,oBAAoBC;AAC9B;QACAva,QAAciP,SAAGA;QACjB,SAAS3H,MAAM2T;YACX,IAAI1Y,SAAS,CAAA;YACb,IAAI2Y,UAAUD,eAAeA,aAAatK,MAAM,QAAQ;YACxD,IAAIwK,UAAU;YACd,KAAK,IAAI7b,IAAI,GAAGA,IAAI4b,QAAQ3b,QAAQD,KAAK;gBACrC,IAAI0Y,QAAQkD,QAAQ5b,GAAGqR,MAAM;gBAC7B,IAAIyK,SAASpD,MAAMpP,MAAM,GAAGmI,KAAK;gBACjC,IAAIqK,OAAOC,OAAO,OAAO,KAAK;oBAC1BD,SAASA,OAAOxS,MAAM,IAAI;AAC7B;gBACD;oBACI,IAAI0S,SAAStD,MAAM,GAAG1H,QAAQ6K,SAAS1K;oBACvClO,OAAO+Y,UAAUF,OAAO9K,QAAQ6K,SAAS1K;AAI5C,kBAFD,OAAO5R,IAEN;AACJ;YACD,OAAO0D;AACX;QACAvC,QAAasH,QAAGA;QAChB,SAASiU;YACL,OAAOjU,MAAM4E,SAASkP;AAC1B;QACApb,QAAcub,SAAGA;QACjB,SAAS5a,IAAIb;YACT,OAAOyb,SAASzb;AACpB;QACAE,QAAWW,MAAGA;QACd,SAASE,IAAIf,MAAMG,OAAOsa;YACtBrO,SAASkP,SAASnM,OAAOnP,MAAMG,OAAOga,SAAS;gBAAEY,MAAM;eAAON;AAClE;QACAva,QAAWa,MAAGA;QACd,SAASqU,OAAOpV,MAAMya;YAClB1Z,IAAIf,MAAM,IAAIma,SAASA,SAAS,CAAA,GAAIM,aAAa;gBAAEC,UAAU;;AACjE;QACAxa,QAAAkV,SAAiBA;;;;;;;;;IC9DV,MAAMsG,gBAAgB;QAC3B7a,IAAsBH;YACpB,MAAMP,QAAQwb,WAAYjb;YAE1B,WAAWP,UAAU,aAAa;gBAChC;AACD;YAED,OAAUgH,KAAKK,MAAMrH;AACtB;QAEDyX,KAAKlX,KAAaP,OAAY0T;YAC5B,IAAI+H,mBAA6C,CAAA;YAEjD,IAAI,aAAarX,OAAOsX,SAASC,UAAU;gBACzCF,mBAAmB;oBACjBZ,QAAQ;oBACRC,UAAU;;AAEb;YAED,IAAIpH,YAAA,QAAAA,8BAAAA,QAASgE,iBAAiB;gBAC5B+D,iBAAiBlB,UAAU7G,QAAQgE;AACpC;YAED,IAAIhE,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;gBACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;YAEDsE,WAAYrb,KAAKyG,KAAKC,UAAUjH,QAAQyb;AACzC;QAEDxG,OAAO1U,KAAamT;YAClB,IAAI+H,mBAA6C,CAAA;YAEjD,IAAI/H,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;gBACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;YAEDuE,WAAetb,KAAKkb;AACrB;;IAMH,MAAMK,gBAAgB;IAMf,MAAMC,kCAAkC;QAC7Crb,IAAsBH;YACpB,MAAMP,QAAQub,cAAc7a,IAAOH;YAEnC,IAAIP,OAAO;gBACT,OAAOA;AACR;YAED,OAAOub,cAAc7a,IAAO,GAAGob,gBAAgBvb;AAChD;QAEDkX,KAAKlX,KAAaP,OAAY0T;YAC5B,IAAI+H,mBAA6C,CAAA;YAEjD,IAAI,aAAarX,OAAOsX,SAASC,UAAU;gBACzCF,mBAAmB;oBAAEZ,QAAQ;;AAC9B;YAED,IAAInH,YAAA,QAAAA,8BAAAA,QAASgE,iBAAiB;gBAC5B+D,iBAAiBlB,UAAU7G,QAAQgE;AACpC;YAED,IAAIhE,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;gBACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;YAEDsE,WACE,GAAGE,gBAAgBvb,OACnByG,KAAKC,UAAUjH,QACfyb;YAEFF,cAAc9D,KAAKlX,KAAKP,OAAO0T;AAChC;QAEDuB,OAAO1U,KAAamT;YAClB,IAAI+H,mBAA6C,CAAA;YAEjD,IAAI/H,YAAA,QAAAA,8BAAAA,QAAS4D,cAAc;gBACzBmE,iBAAiBd,SAASjH,QAAQ4D;AACnC;YAEDuE,WAAetb,KAAKkb;YACpBF,cAActG,OAAO1U,KAAKmT;YAC1B6H,cAActG,OAAO,GAAG6G,gBAAgBvb,OAAOmT;AAChD;;IAMI,MAAMsI,iBAAiB;QAC5Btb,IAAsBH;YAEpB,WAAW0b,mBAAmB,aAAa;gBACzC;AACD;YAED,MAAMjc,QAAQic,eAAe/X,QAAQ3D;YAErC,IAAIP,SAAS,MAAM;gBACjB;AACD;YAED,OAAUgH,KAAKK,MAAMrH;AACtB;QAEDyX,KAAKlX,KAAaP;YAChBic,eAAe1X,QAAQhE,KAAKyG,KAAKC,UAAUjH;AAC5C;QAEDiV,OAAO1U;YACL0b,eAAe3X,WAAW/D;AAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC/IH,MAAM2b,mBAAiD,CAAA;IAEhD,MAAMC,gBAAgB,CAC3BC,IACA7b;QAEA,IAAI8b,UAA6BH,iBAAiB3b;QAClD,KAAK8b,SAAS;YACZA,UAAUD,KAAKjJ,SAAQ;uBACd+I,iBAAiB3b;gBACxB8b,UAAU;AAAI;YAEhBH,iBAAiB3b,OAAO8b;AACzB;QACD,OAAOA;AAAO;IAGT,MAAMC,eAAezM,OAC1BuM,IACAG,qBAAqB;QAErB,KAAK,IAAIld,IAAI,GAAGA,IAAIkd,oBAAoBld,KAAK;YAC3C,UAAU+c,MAAM;gBACd,OAAO;AACR;AACF;QAED,OAAO;AAAK;UCpBDI;QAGXtS,YAAoBmL,OAAuB3F;YAAvBvP,KAAKkV,QAALA;YAAuBlV,KAAQuP,WAARA;YACzCvP,KAAKsc,cAActc,KAAKuc,sBAAsBvc,KAAKuP;AACpD;QAEDG,UAAUtP;;YACR,MAAM6O,OAAO,IAAIvJ,MACf5B,WAAO9D,KAAKkV,MAAM3U,IAAsBP,KAAKsc,kBAAa,QAAAxY,YAAA,SAAA,IAAAA,GAAEmL,SAAQ;YAGtEA,KAAK9H,IAAI/G;kBAEHJ,KAAKkV,MAAMzU,IAAsBT,KAAKsc,aAAa;gBACvDrN,MAAM,KAAIA;;AAEb;QAEDS,aAAatP;YACX,MAAMuU,cAAc3U,KAAKkV,MAAM3U,IAAsBP,KAAKsc;YAE1D,IAAI3H,OAAO;gBACT,MAAM1F,OAAO,IAAIvJ,IAAIiP,MAAM1F;gBAC3BA,KAAK/N,OAAOd;gBAEZ,IAAI6O,KAAKuN,OAAO,GAAG;oBACjB,aAAaxc,KAAKkV,MAAMzU,IAAIT,KAAKsc,aAAa;wBAAErN,MAAM,KAAIA;;AAC3D;gBAED,aAAajP,KAAKkV,MAAMJ,OAAO9U,KAAKsc;AACrC;AACF;QAED/b;YACE,OAAOP,KAAKkV,MAAM3U,IAAsBP,KAAKsc;AAC9C;QAEDtY;YACE,OAAOhE,KAAKkV,MAAMJ,OAAO9U,KAAKsc;AAC/B;QAEOC,sBAAsBhN;YAC5B,OAAO,GAAG8E,qBAAqB9E;AAChC;;ICvCI,MAAMkN,8BAA8B;IAKpC,MAAMC,kCAAmCnN,YAC9C,SAASA;IAKJ,MAAMoN,mCAAmC;IAKzC,MAAMC,iCAAkCrN,YAC7C,SAASA;IAKX,MAAMsN,wBAAsD;QAC1DC,QAAQ,OAAM,IAAI9H,eAAgBC;QAClC8H,cAAc,MAAM,IAAInI;;IAMnB,MAAMoI,eAAgBzB,YACpBsB,sBAAsBtB;IAMxB,MAAM0B,qBAAqB,CAChCC,eAGAnS,OACAoS,qBACA9S,OACAuO,OACAwE,gBACAC,cACAC,kBAEA1e,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;QACEG,WAAW0N,cAAc3N;OACtB2N,cAAcC,sBACdA,sBACH;QAAApS,OAAOkJ,gBAAgBlJ,OAAOoS,oBAAoBpS;QAClDwS,eAAe;QACfD,eAAeA,iBAAiB;QAChCjT;QACAuO;QACAyE,cACEA,gBAAgBH,cAAcC,oBAAoBE;QACpDD;QACAI,uBAAuB;;IASpB,MAAMC,6BAGXlK;QAEA,OAAMmK,SAAEA,SAAOC,YAAEA,cAAmCpK,SAApBqK,kBAAoBrf,OAAAgV,SAA9C,EAAA,WAAA;QAEN,MAAMpR,yCACDyb,kBAAe;YAClBF,SAASA,YAAY,SAASA,UAAUA,UAAUC;;QAGpD,OAAOxb;AAAW;ICepB,MAAMtB,OAAO,IAAIgd;UAKJC;QA2BX/T,YAAYwJ;YAZKvT,KAAA+d,aAAoB,IAAI/I,eAAgBC;YAIxCjV,KAAAge,iBAA8C;gBAC7Db,qBAAqB;oBACnBpS,OAAOrB;;gBAETuU,0BAA0B;gBAC1B9K,aAAa;;YA27BPnT,KAAsBke,yBAAGxO;sBACzB7O,KAAKiF,YAAY2W;gBAEvBxY,OAAO4D,oBAAoB,YAAY7H,KAAKke;AAAuB;YA17BnEle,KAAKuT,UACA3U,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAArP,KAAKge,iBACLzK,UACH;gBAAA4J,qDACKnd,KAAKge,eAAeb,sBACpB5J,QAAQ4J;;mBAIRlZ,WAAW,eAAegN;YAEjC,IAAIsC,QAAQ2B,SAAS3B,QAAQ4K,eAAe;gBAC1CC,QAAQC,KACN;AAEH;YAED,IAAIF;YACJ,IAAIjJ;YAEJ,IAAI3B,QAAQ2B,OAAO;gBACjBA,QAAQ3B,QAAQ2B;AACjB,mBAAM;gBACLiJ,gBAAgB5K,QAAQ4K,iBAAiB5U;gBAEzC,KAAKyT,aAAamB,gBAAgB;oBAChC,MAAM,IAAI1e,MAAM,2BAA2B0e;AAC5C;gBAEDjJ,QAAQ8H,aAAamB,cAAbnB;AACT;YAEDhd,KAAKse,gBAAgB/K,QAAQgL,uBACzBhL,QAAQgL,uBAAuB,MAC/BjV;YAEJtJ,KAAKwe,gBACHjL,QAAQkL,yBAAyB,QAC7BrD,gBACAQ;YAEN5b,KAAK0e,oBAAoBhC,gCACvB1c,KAAKuT,QAAQhE;YAGfvP,KAAK2e,4BAA4B/B,+BAC/B5c,KAAKuT,QAAQhE;YAGfvP,KAAK4e,yBACHrL,QAAQqL,0BAA0BjV;YAEpC,MAAMkV,qBAAqBtL,QAAQuL,4BAC/B9e,KAAKwe,gBACL3C;YAMJ7b,KAAK+K,QAAQkJ,gBACX,UACAjU,KAAKuT,QAAQ4J,oBAAoBpS,OACjC/K,KAAKuT,QAAQwL,mBAAmB,mBAAmB;YAGrD/e,KAAKgf,qBAAqB,IAAI/H,mBAC5B4H,oBACA7e,KAAKuT,QAAQhE,UACbvP,KAAKuT,QAAQ4D;YAGfnX,KAAKuV,cAAcvV,KAAKuT,QAAQgC,eAAe1L;YAE/C7J,KAAKif,eAAe,IAAI5J,aACtBH,QACCA,MAAMH,UACH,IAAIsH,iBAAiBnH,OAAOlV,KAAKuT,QAAQhE,YACzC/O,WACJR,KAAKuV;YAGPvV,KAAKmR,YAAYD,UAAUlR,KAAKuT,QAAQiH;YACxCxa,KAAKkf,cAAc7N,eAAerR,KAAKuT,QAAQjC,QAAQtR,KAAKmR;YAG5D,WACSlN,WAAW,eAClBA,OAAOkb,UACPnf,KAAKuT,QAAQwL,oBACbZ,kBAAkB5U,uBAClB;gBACA,IAAIvJ,KAAKuT,QAAQ6L,WAAW;oBAC1Bpf,KAAKkT,SAAS,IAAIiM,OAAOnf,KAAKuT,QAAQ6L;AACvC,uBAAM;oBACLpf,KAAKkT,SAAS,IAAImM;AACnB;AACF;AACF;QAEOC,KAAK7E;YACX,MAAM7G,cAAcgH,mBAClB9L,KAAKjI,KAAKC,UAAU9G,KAAKuT,QAAQK,eAAehK;YAElD,OAAO,GAAG5J,KAAKmR,YAAYsJ,oBAAoB7G;AAChD;QAEO2L,cAAcC;YACpB,OAAOxf,KAAKsf,KAAK,cAAchQ,kBAAkBkQ;AAClD;QAEO9P,qBACNkG,UACAgD,OACAU;YAEA,MAAMjU,YAAYrF,KAAKuV;YAEvB,OAAOkK,OAAc;gBACnBnH,KAAKtY,KAAKkf;gBACVzG,KAAKzY,KAAKuT,QAAQhE;gBAClBqG;gBACAgD;gBACAU;gBACAN,QAAQhZ,KAAKuT,QAAQyF;gBACrBH,SAASrH,YAAYxR,KAAKuT,QAAQ4J,oBAAoBtE;gBACtDxT;;AAEH;QAEOqa,gBAAgBpG;YACtB,IAAIA,cAAc;gBAChBtZ,KAAKwe,cAAclH,KAAKtX,KAAK0e,mBAAmBpF,cAAc;oBAC5D/B,iBAAiBvX,KAAK4e;oBACtBzH,cAAcnX,KAAKuT,QAAQ4D;;AAE9B,mBAAM;gBACLnX,KAAKwe,cAAc1J,OAAO9U,KAAK0e,mBAAmB;oBAChDvH,cAAcnX,KAAKuT,QAAQ4D;;AAE9B;AACF;QAEOzH,2BACNyN,qBACAqC,kBACAG;YAUA,MAAMtV,QAAQwE,OAAOP;YACrB,MAAMsK,QAAQ/J,OAAOP;YACrB,MAAMsR,gBAAgBtR;YACtB,MAAMuR,6BAA6BpQ,OAAOmQ;YAC1C,MAAMxC,iBAAiBvM,yBAAyBgP;YAEhD,MAAM7Q,SAASiO,mBACbjd,KAAKuT,SACLvT,KAAK+K,OACLoS,qBACA9S,OACAuO,OACAwE,gBACAD,oBAAoBE,gBAClBrd,KAAKuT,QAAQ4J,oBAAoBE,gBACjCsC,qBACFH,qBAAA,QAAAA,uCAAAA,iBAAkBlC;YAGpB,MAAMpQ,MAAMlN,KAAKuf,cAAcvQ;YAE/B,OAAO;gBACL4J;gBACAgH;gBACA7U,OAAOiE,OAAOjE;gBACdD,UAAUkE,OAAOlE,YAAY;gBAC7BuS,cAAcrO,OAAOqO;gBACrBhT;gBACA6C;;AAEH;QAyBMwC,qBACL6D,SACA1F;;YAEA0F,UAAUA,WAAW;YACrB1F,SAASA,UAAU;YAEnB,KAAKA,OAAOpD,OAAO;gBACjBoD,OAAOpD,QAAQwC,UAAU;gBAEzB,KAAKY,OAAOpD,OAAO;oBACjB,MAAM,IAAIhL,MACR;AAEH;AACF;YAED,MAAMuP,eAAehP,KAAK8f,qBACxBvM,QAAQ4J,uBAAuB,CAAA,GAC/B;gBAAEG,eAAe;eACjBrZ,OAAOsX,SAAS/O;YAGlBqB,OAAOpD,MAAM8Q,SAASwE,OAAO/Q,OAAO9B;YAEpC,MAAM8S,mBAAmBpS,SAAQhP,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAC5BxB,SAAM;gBACT1E,kBACE0E,OAAO1E,oBACPnJ,KAAKuT,QAAQ0M,6BACbhX;;YAGJ,IAAI+F,OAAO3E,UAAU2V,WAAW3V,OAAO;gBACrC,MAAM,IAAIP,aAAa,kBAAkB;AAC1C;YAED,MAAMwP,iBACJxV,KAAAyP,QAAQ4J,6DAAqB7D,iBAC7BtZ,KAAKuT,QAAQ4J,oBAAoB7D;kBAE7BtZ,KAAKkgB,cACT;gBACEpV,UAAUkE,OAAOlE;gBACjBC,OAAOiE,OAAOjE;gBACd6U,eAAe5Q,OAAO4Q;gBACtBO,YAAY;gBACZ5U,MAAMyU,WAAWzU;gBACjB8R,cAAcrO,OAAOqO;eAEvB;gBACE+C,SAASpR,OAAO4J;gBAChBU;;AAGL;QAYM5J;;YACL,MAAMwF,cAAclV,KAAKqgB;YAEzB,QAAOvc,KAAAoR,UAAK,QAALA,eAAK,SAAA,IAALA,MAAOO,kBAAc,QAAA3R,YAAA,SAAA,IAAAA,GAAAoU;AAC7B;QASMxI;;YACL,MAAMwF,cAAclV,KAAKqgB;YAEzB,QAAOvc,KAAAoR,UAAK,QAALA,eAAK,SAAA,IAALA,MAAOO,kBAAc,QAAA3R,YAAA,SAAA,IAAAA,GAAAkU;AAC7B;QAaMtI,wBACL6D,UAA2C;;YAE3C,MAAM+M,KACJ7C,2BAA2BlK,WADvBmK,SAAEA,SAAO6C,UAAEA,UAAQjW,UAAEA,YACUgW,IADGE,aAAlCjiB,OAAA+hB,IAAA,EAAA,WAAA,YAAA;YAGN,MAAMhH,iBACJxV,KAAA0c,WAAWrD,6DAAqB7D,iBAChCtZ,KAAKuT,QAAQ4J,oBAAoB7D;YAEnC,MAAMmH,WAAgCzgB,KAAK8f,qBACzCU,WAAWrD,uBAAuB,MAD9BjQ,KAAEA,OAAGuT,IAAKpJ,cAAW9Y,OAAAkiB,IAArB,EAAuB;YAI7BzgB,KAAKgf,mBAAmB5H,OAAMxY,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACzBgI,cACH;gBAAA/M;gBACIgP,gBAAgB;gBAAEA;;YAGxB,MAAMoH,kBAAkBH,WAAW,GAAGrT,OAAOqT,aAAarT;YAE1D,IAAIwQ,SAAS;sBACLA,QAAQgD;AACf,mBAAM;gBACLzc,OAAOsX,SAASlM,OAAOqR;AACxB;AACF;QAQMhR,6BACLxC,MAAcjJ,OAAOsX,SAASwE;YAE9B,MAAMY,uBAAuBzT,IAAIqD,MAAM,KAAK/H,MAAM;YAElD,IAAImY,qBAAqBxhB,WAAW,GAAG;gBACrC,MAAM,IAAIM,MAAM;AACjB;YAED,OAAM4K,OAAEA,OAAKkB,MAAEA,MAAIjM,OAAEA,OAAK0K,mBAAEA,qBAAsBkB,0BAChDyV,qBAAqBhQ,KAAK;YAG5B,MAAM0G,cAAcrX,KAAKgf,mBAAmBze;YAE5C,KAAK8W,aAAa;gBAChB,MAAM,IAAIvN,aAAa,uBAAuB;AAC/C;YAED9J,KAAKgf,mBAAmBlK;YAExB,IAAIxV,OAAO;gBACT,MAAM,IAAI8K,oBACR9K,OACA0K,qBAAqB1K,OACrB+K,OACAgN,YAAY/M;AAEf;YAGD,KACG+M,YAAYuI,iBACZvI,YAAYhN,SAASgN,YAAYhN,UAAUA,OAC5C;gBACA,MAAM,IAAIP,aAAa,kBAAkB;AAC1C;YAED,MAAMwP,eAAejC,YAAYiC;YACjC,MAAM8G,UAAU/I,YAAYuB;YAC5B,MAAMyE,eAAehG,YAAYgG;kBAE3Brd,KAAKkgB,cAAathB,OAAAyQ,OAAA;gBAEpBvE,UAAUuM,YAAYvM;gBACtBC,OAAOsM,YAAYtM;gBACnB6U,eAAevI,YAAYuI;gBAC3BO,YAAY;gBACZ5U,MAAMA;eACF8R,eAAe;gBAAEA;gBAAiB,CAAE,IAE1C;gBAAE+C;gBAAS9G;;YAGb,OAAO;gBACLhP,UAAU+M,YAAY/M;;AAEzB;QA2BMoF,mBAAmB6D;YACxB,KAAKvT,KAAKwe,cAAcje,IAAIP,KAAK2e,4BAA4B;gBAC3D,KAAK3e,KAAKwe,cAAcje,IAAIoc,mCAAmC;oBAC7D;AACD,uBAAM;oBAEL3c,KAAKwe,cAAclH,KAAKtX,KAAK2e,2BAA2B,MAAM;wBAC5DpH,iBAAiBvX,KAAK4e;wBACtBzH,cAAcnX,KAAKuT,QAAQ4D;;oBAG7BnX,KAAKwe,cAAc1J,OAAO6H;AAC3B;AACF;YAED;sBACQ3c,KAAK4gB,iBAAiBrN;AAChB,cAAZ,OAAO9Q,IAAK;AACf;QAwDMiN,uBACL6D,UAAmC;;YAEnC,MAAMsN,eAGJjiB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;gBAAAyR,WAAW;eACRvN,UAAO;gBACV4J,qBAAmBve,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,IACdrP,KAAKuT,QAAQ4J,sBACb5J,QAAQ4J,sBAAmB;oBAC9BpS,OAAOkJ,gBAAgBjU,KAAK+K,QAAOjH,KAAAyP,QAAQ4J,yBAAmB,QAAArZ,YAAA,SAAA,IAAAA,GAAEiH;;;YAIpE,MAAM5I,eAAe6Z,eACnB,MAAMhc,KAAK+gB,kBAAkBF,gBAC7B,GAAG7gB,KAAKuT,QAAQhE,aAAasR,aAAa1D,oBAAoBrS,aAAa+V,aAAa1D,oBAAoBpS;YAG9G,OAAOwI,QAAQyN,mBAAmB7e,SAASA,WAAA,QAAAA,6BAAAA,OAAQ8e;AACpD;QAEOvR,wBACN6D;YAIA,OAAMuN,WAAEA,aAAkCvN,SAApB2N,kBAAe3iB,OAAKgV,SAApC,EAAiC;YAIvC,IAAIuN,cAAc,OAAO;gBACvB,MAAMnM,cAAc3U,KAAKmhB,mBAAmB;oBAC1CpW,OAAOmW,gBAAgB/D,oBAAoBpS;oBAC3CD,UAAUoW,gBAAgB/D,oBAAoBrS,YAAY;oBAC1DyE,UAAUvP,KAAKuT,QAAQhE;;gBAGzB,IAAIoF,OAAO;oBACT,OAAOA;AACR;AACF;YAED,IAAImM,cAAc,cAAc;gBAC9B;AACD;YAED,UACQ3E,cACJ,MAAMtb,KAAK+E,YAAY6W,6BAA6B,OACpD,KAEF;gBACA;oBACExY,OAAOiE,iBAAiB,YAAYlI,KAAKke;oBAIzC,IAAI4C,cAAc,OAAO;wBACvB,MAAMnM,cAAc3U,KAAKmhB,mBAAmB;4BAC1CpW,OAAOmW,gBAAgB/D,oBAAoBpS;4BAC3CD,UAAUoW,gBAAgB/D,oBAAoBrS,YAAY;4BAC1DyE,UAAUvP,KAAKuT,QAAQhE;;wBAGzB,IAAIoF,OAAO;4BACT,OAAOA;AACR;AACF;oBAED,MAAMyM,aAAaphB,KAAKuT,QAAQwL,yBACtB/e,KAAKqhB,2BAA2BH,yBAChClhB,KAAKshB,oBAAoBJ;oBAEnC,OAAMtL,UAAEA,UAAQqL,cAAEA,cAAYM,iBAAEA,iBAAe9K,YAAEA,cAC/C2K;oBAEF,OAAAxiB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;wBACEuG;wBACAqL;uBACIM,kBAAkB;wBAAExW,OAAOwW;wBAAoB,OAAK;wBACxD9K;;AAKH,kBAHS;0BACF5V,KAAKiF,YAAY2W;oBACvBxY,OAAO4D,oBAAoB,YAAY7H,KAAKke;AAC7C;AACF,mBAAM;gBACL,MAAM,IAAI3T;AACX;AACF;QAcMmF,wBACL6D,UAAoC,IACpC1F,SAA6B,CAAA;;YAE7B,MAAMgT,eAAYjiB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACbkE,UAAO;gBACV4J,qBACKve,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAArP,KAAKuT,QAAQ4J,sBACb5J,QAAQ4J,sBAAmB;oBAC9BpS,OAAOkJ,gBAAgBjU,KAAK+K,QAAOjH,KAAAyP,QAAQ4J,yBAAmB,QAAArZ,YAAA,SAAA,IAAAA,GAAEiH;;;YAIpE8C,SACKjP,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,IAAAnG,+BACA2E;kBAGC7N,KAAKwhB,eAAeX,cAAchT;YAExC,MAAMqH,cAAclV,KAAKif,aAAa1e,IACpC,IAAIgU,SAAS;gBACXxJ,OAAO8V,aAAa1D,oBAAoBpS;gBACxCD,UAAU+V,aAAa1D,oBAAoBrS,YAAY;gBACvDyE,UAAUvP,KAAKuT,QAAQhE;;YAI3B,OAAO2F,MAAO+L;AACf;QAWMvR;YACL,MAAMwI,aAAalY,KAAKyhB;YACxB,SAASvJ;AACV;QAUOwJ,gBAAgBnO;YACtB,IAAIA,QAAQhE,aAAa,MAAM;gBAC7BgE,QAAQhE,WAAWgE,QAAQhE,YAAYvP,KAAKuT,QAAQhE;AACrD,mBAAM;uBACEgE,QAAQhE;AAChB;YAED,MAAMzL,KAAkCyP,QAAQoO,gBAAgB,CAAA,IAA1DC,WAAEA,iBAAcC,gBAAhBtjB,OAAAuF,IAAA,EAAA;YACN,MAAMge,iBAAiBF,YAAY,eAAe;YAClD,MAAM1U,MAAMlN,KAAKsf,KACf,cAAchQ,kBAAiB1Q,OAAAyQ,OAAA;gBAC7BE,UAAUgE,QAAQhE;eACfsS;YAIP,OAAO3U,MAAM4U;AACd;QAeMpS,aAAa6D,UAAyB;YAC3C,MAAMzP,KAAgC2Z,2BAA2BlK,WAA3DmK,SAAEA,WAAO5Z,IAAK+d,gBAAdtjB,OAAAuF,IAAA,EAAA;YAEN,IAAIyP,QAAQhE,aAAa,MAAM;sBACvBvP,KAAKif,aAAajb;AACzB,mBAAM;sBACChE,KAAKif,aAAajb,MAAMuP,QAAQhE,YAAYvP,KAAKuT,QAAQhE;AAChE;YAEDvP,KAAKwe,cAAc1J,OAAO9U,KAAK0e,mBAAmB;gBAChDvH,cAAcnX,KAAKuT,QAAQ4D;;YAE7BnX,KAAKwe,cAAc1J,OAAO9U,KAAK2e,2BAA2B;gBACxDxH,cAAcnX,KAAKuT,QAAQ4D;;YAE7BnX,KAAK+d,UAAUjJ,OAAOR;YAEtB,MAAMpH,MAAMlN,KAAK0hB,gBAAgBG;YAEjC,IAAInE,SAAS;sBACLA,QAAQxQ;AACf,mBAAM,IAAIwQ,YAAY,OAAO;gBAC5BzZ,OAAOsX,SAASlM,OAAOnC;AACxB;AACF;QAEOwC,0BACN6D;YAIA,MAAMvE,SACDpQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAAkE,QAAQ4J;gBACX4E,QAAQ;;YAGV,MAAMC,UAAUhiB,KAAKwe,cAAcje,IAAYP,KAAK0e;YAEpD,IAAIsD,YAAYhT,OAAOsK,cAAc;gBACnCtK,OAAOsK,eAAe0I;AACvB;YAED,OAAM9U,KACJA,KACA7C,OAAO4X,SACPrJ,OAAOwH,SAAOR,eACdA,eAAavC,cACbA,cAAYtS,OACZA,OAAKD,UACLA,kBACQ9K,KAAK8f,qBACb9Q,QACA;gBAAEsO,eAAe;eACjBrZ,OAAOsX,SAAS/O;YAGlB;gBAIE,IAAKvI,OAAeie,qBAAqB;oBACvC,MAAM,IAAIpY,aACR,kBACA;AAEH;gBAED,MAAMqY,mBACJ5O,QAAQpK,oBAAoBnJ,KAAKuT,QAAQ0M;gBAE3C,MAAMD,mBAAmBxU,UAAU0B,KAAKlN,KAAKmR,WAAWgR;gBAExD,IAAIF,YAAYjC,WAAW3V,OAAO;oBAChC,MAAM,IAAIP,aAAa,kBAAkB;AAC1C;gBAED,MAAMsY,oBAAoBpiB,KAAKkgB,cAExBthB,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAAkE,QAAQ4J;oBACXyC;oBACArU,MAAMyU,WAAWzU;oBACjB4U,YAAY;oBACZ9C;oBACAjX,SAASmN,QAAQ4J,oBAAoB/W,WAAWpG,KAAKse;oBAEvD;oBACE8B;oBACA9G,cAActK,OAAOsK;;gBAIzB,OAAA1a,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACK+S,cAAW;oBACdrX,OAAOA;oBACPwW,iBAAiBa,YAAYrX;oBAC7BD,UAAUA;;AASb,cAPC,OAAOrM;gBACP,IAAIA,EAAEa,UAAU,kBAAkB;oBAChCU,KAAKqiB,OAAO;wBACV3E,SAAS;;AAEZ;gBACD,MAAMjf;AACP;AACF;QAEOiR,iCACN6D;YAIA,MAAM2B,cAAclV,KAAKif,aAAa1e,IACpC,IAAIgU,SAAS;gBACXxJ,OAAOwI,QAAQ4J,oBAAoBpS;gBACnCD,UAAUyI,QAAQ4J,oBAAoBrS,YAAY;gBAClDyE,UAAUvP,KAAKuT,QAAQhE;;YAQ3B,MAAM2F,UAAUA,MAAMmB,mBAAmBrW,KAAKkT,QAAQ;gBACpD,IAAIlT,KAAKuT,QAAQ0K,0BAA0B;oBACzC,aAAaje,KAAKshB,oBAAoB/N;AACvC;gBAED,MAAM,IAAI1I,yBACR0I,QAAQ4J,oBAAoBrS,YAAY,WACxCyI,QAAQ4J,oBAAoBpS;AAE/B;YAED,MAAMsS,eACJ9J,QAAQ4J,oBAAoBE,gBAC5Brd,KAAKuT,QAAQ4J,oBAAoBE,gBACjCpZ,OAAOsX,SAAS/O;YAElB,MAAMpG,iBACGmN,QAAQpK,qBAAqB,WAChCoK,QAAQpK,mBAAmB,MAC3B;YAEN;gBACE,MAAMiZ,oBAAoBpiB,KAAKkgB,cAAathB,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACvCkE,QAAQ4J,sBAAmB;oBAC9BgD,YAAY;oBACZ9J,eAAenB,SAASA,MAAMmB;oBAC9BgH;oBACIjX,WAAW;oBAAEA;;gBAGnB,OACKxH,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAA+S,cACH;oBAAArX,OAAOwI,QAAQ4J,oBAAoBpS;oBACnCwW,iBAAiBa,YAAYrX;oBAC7BD,UAAUyI,QAAQ4J,oBAAoBrS,YAAY;;AAiBrD,cAfC,OAAOrM;gBACP,KAGGA,EAAEe,QAAQR,QAAQwK,wCAAwC,KAGxD/K,EAAEe,WACDf,EAAEe,QAAQR,QAAQyK,wCAAwC,MAC9DzJ,KAAKuT,QAAQ0K,0BACb;oBACA,aAAaje,KAAKshB,oBAAoB/N;AACvC;gBAED,MAAM9U;AACP;AACF;QAEOiR,wBACNiF;YAEA,OAAMiB,UAAEA,UAAQH,cAAEA,gBAAyCd,OAAxB2N,sBAAwB/jB,OAAAoW,OAArD,EAAA,YAAA;YAEN3U,KAAK+d,UAAUtd,IAAI6T,2BAA2B;gBAC5CsB;gBACAH;;kBAGIzV,KAAKif,aAAasD,WACtBviB,KAAKuT,QAAQhE,UACboF,MAAMiB,UACNjB,MAAMc;kBAGFzV,KAAKif,aAAaxe,IAAI6hB;AAC7B;QAEO5S;YACN,MAAM5E,WAAW9K,KAAKuT,QAAQ4J,oBAAoBrS,YAAY;YAE9D,MAAMoK,cAAclV,KAAKif,aAAauD,WACpC,IAAIjO,SAAS;gBACXhF,UAAUvP,KAAKuT,QAAQhE;gBACvBzE;gBACAC,OAAO/K,KAAK+K;;YAIhB,MAAM0X,eAAeziB,KAAK+d,UAAUxd,IAClC+T;YAKF,IAAIY,SAASA,MAAMU,cAAa6M,iBAAA,QAAAA,sBAAA,SAAA,IAAAA,aAAc7M,WAAU;gBACtD,OAAO6M;AACR;YAEDziB,KAAK+d,UAAUtd,IAAI6T,2BAA2BY;YAC9C,OAAOA;AACR;QAEOxF,0BAAyB3E,OAC/BA,OAAKD,UACLA,UAAQyE,UACRA;YAMA,MAAMoF,cAAc3U,KAAKif,aAAa1e,IACpC,IAAIgU,SAAS;gBACXxJ;gBACAD;gBACAyE;gBAEF;YAGF,IAAIoF,SAASA,MAAMsM,cAAc;gBAC/B,OAAMA,cAAEA,cAAYM,iBAAEA,iBAAe9K,YAAEA,cAAe9B;gBACtD,MAAMO,cAAclV,KAAKqgB;gBACzB,OACEnL,SACEtW,OAAAyQ,OAAAzQ,OAAAyQ,OAAA;oBAAAuG,UAAUV,MAAMU;oBAChBqL;mBACIM,kBAAkB;oBAAExW,OAAOwW;oBAAoB,OAAK;oBACxD9K;;AAGL;AACF;QAcO/G,oBACN6D,SAIAmP;YAEA,OAAMtC,SAAEA,SAAO9G,cAAEA,gBAAiBoJ,wBAAwB,CAAA;YAC1D,MAAMtB,mBAAmB1N;gBAErBC,SAAS3T,KAAKmR;gBACd3B,WAAWxP,KAAKuT,QAAQhE;gBACxBqE,aAAa5T,KAAKuT,QAAQK;gBAC1BT,aAAanT,KAAKuT,QAAQJ;gBAC1B/M,SAASpG,KAAKse;eACX/K,UAELvT,KAAKkT;YAGP,MAAMuC,qBAAqBzV,KAAK2iB,eAC9BvB,WAAWxL,UACXwK,SACA9G;kBAGItZ,KAAK4iB,kBAAiBhkB,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GACvB+R,aACH;gBAAA3L;gBACA1K,OAAOwI,QAAQxI;gBACfD,UAAUyI,QAAQzI,YAAY;gBAC1BsW,WAAWrW,QAAQ;gBAAEwW,iBAAiBH,WAAWrW;gBAAU,OAC/D;gBAAAyE,WAAWxP,KAAKuT,QAAQhE;;YAG1BvP,KAAKwe,cAAclH,KAAKtX,KAAK2e,2BAA2B,MAAM;gBAC5DpH,iBAAiBvX,KAAK4e;gBACtBzH,cAAcnX,KAAKuT,QAAQ4D;;YAG7BnX,KAAK0f,gBAAgBpG,gBAAgB7D,aAAauC,OAAOyB;YAEzD,OAAY7a,OAAAyQ,OAAAzQ,OAAAyQ,OAAA,CAAA,GAAA+R,aAAY;gBAAA3L;;AACzB;QAoDD/F,oBACE6D;YAEA,OAAOvT,KAAKkgB,cAAc;gBACxBC,YAAY;gBACZ0C,eAAetP,QAAQsP;gBACvBC,oBAAoBvP,QAAQuP;gBAC5B/X,OAAOkJ,gBAAgBV,QAAQxI,OAAO/K,KAAK+K;gBAC3CD,UAAU9K,KAAKuT,QAAQ4J,oBAAoBrS;;AAE9C;;UChlBUiY;ICplBNrT,eAAesT,kBAAkBzP;QACtC,MAAM0P,QAAQ,IAAInF,YAAYvK;cACxB0P,MAAMC;QACZ,OAAOD;AACT;;;;;;;;;;;;;;;;;"}