/**
 * Routes du chatbot Nouri - Proxy vers le service chatbot
 */

import { Router, Request, Response } from 'express';
import axios from 'axios';
import { config } from '../config/config';
import { logger } from '../utils/logger';

const router = Router();

const proxyToChatbotService = async (req: Request, res: Response, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') => {
  try {
    const url = `${config.services.chatbotService}${endpoint}`;
    
    const axiosConfig = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': req.user!.id,
        'X-User-Email': req.user!.email,
        'X-User-Language': req.user!.metadata?.language || 'fr',
        'Authorization': req.headers.authorization
      },
      ...(method !== 'GET' && { data: req.body })
    };

    const response = await axios(axiosConfig);
    res.status(response.status).json(response.data);

  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      logger.error('Erreur lors du proxy vers le service chatbot:', error);
      res.status(500).json({
        error: 'Erreur de communication avec le service chatbot',
        code: 'CHATBOT_SERVICE_ERROR'
      });
    }
  }
};

// Routes du chat
router.post('/chat', async (req: Request, res: Response) => {
  await proxyToChatbotService(req, res, '/chat', 'POST');
});

router.get('/sessions', async (req: Request, res: Response) => {
  await proxyToChatbotService(req, res, '/sessions');
});

router.get('/sessions/:sessionId', async (req: Request, res: Response) => {
  await proxyToChatbotService(req, res, `/sessions/${req.params.sessionId}`);
});

router.delete('/sessions/:sessionId', async (req: Request, res: Response) => {
  await proxyToChatbotService(req, res, `/sessions/${req.params.sessionId}`, 'DELETE');
});

export default router;
