/**
 * Complete Education Page
 * Financial education modules and learning resources
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  LinearProgress,
  Chip,
  CardActions,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material'
import {
  PlayArrow as PlayIcon,
  Home as HomeIcon,
  Logout as LogoutIcon,
  School as SchoolIcon,
  CheckCircle as CheckIcon,
  Lock as LockIcon,
  Quiz as QuizIcon,
  Article as ArticleIcon,
  VideoLibrary as VideoIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock education modules
const educationModules = [
  {
    id: '1',
    title: 'Les bases de la finance personnelle',
    description: 'Apprenez les fondamentaux de la gestion financière personnelle',
    duration: '30 min',
    difficulty: 'Débutant',
    progress: 100,
    completed: true,
    type: 'video',
    category: 'Bases',
    lessons: 5,
    image: '💰',
    color: '#4caf50'
  },
  {
    id: '2',
    title: '<PERSON><PERSON><PERSON> et gérer un budget',
    description: 'Maîtrisez l\'art de la budgétisation pour contrôler vos finances',
    duration: '45 min',
    difficulty: 'Débutant',
    progress: 75,
    completed: false,
    type: 'interactive',
    category: 'Budget',
    lessons: 7,
    image: '📊',
    color: '#2196f3'
  },
  {
    id: '3',
    title: 'Stratégies d\'épargne efficaces',
    description: 'Découvrez comment épargner intelligemment pour vos objectifs',
    duration: '35 min',
    difficulty: 'Intermédiaire',
    progress: 40,
    completed: false,
    type: 'article',
    category: 'Épargne',
    lessons: 6,
    image: '🏦',
    color: '#ff9800'
  },
  {
    id: '4',
    title: 'Introduction aux investissements',
    description: 'Les bases pour commencer à investir en toute sécurité',
    duration: '60 min',
    difficulty: 'Intermédiaire',
    progress: 0,
    completed: false,
    type: 'video',
    category: 'Investissement',
    lessons: 8,
    image: '📈',
    color: '#9c27b0'
  },
  {
    id: '5',
    title: 'Gestion des dettes',
    description: 'Apprenez à gérer et rembourser vos dettes efficacement',
    duration: '40 min',
    difficulty: 'Intermédiaire',
    progress: 0,
    completed: false,
    type: 'quiz',
    category: 'Dettes',
    lessons: 5,
    image: '💳',
    color: '#f44336'
  },
  {
    id: '6',
    title: 'Planification de la retraite',
    description: 'Préparez votre avenir financier dès maintenant',
    duration: '50 min',
    difficulty: 'Avancé',
    progress: 0,
    completed: false,
    type: 'video',
    category: 'Retraite',
    lessons: 9,
    image: '🏖️',
    color: '#607d8b',
    locked: true
  }
]

const categories = ['Tous', 'Bases', 'Budget', 'Épargne', 'Investissement', 'Dettes', 'Retraite']
const difficulties = ['Tous', 'Débutant', 'Intermédiaire', 'Avancé']

interface ModuleCardProps {
  module: typeof educationModules[0]
  onStart: (module: typeof educationModules[0]) => void
}

const ModuleCard: React.FC<ModuleCardProps> = ({ module, onStart }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <VideoIcon />
      case 'article': return <ArticleIcon />
      case 'quiz': return <QuizIcon />
      case 'interactive': return <SchoolIcon />
      default: return <SchoolIcon />
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Débutant': return 'success'
      case 'Intermédiaire': return 'warning'
      case 'Avancé': return 'error'
      default: return 'default'
    }
  }

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Box sx={{ position: 'relative' }}>
        <Box
          sx={{
            height: 120,
            background: `linear-gradient(135deg, ${module.color}20 0%, ${module.color}40 100%)`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Typography variant="h2">{module.image}</Typography>
        </Box>
        {module.locked && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              bgcolor: 'rgba(0,0,0,0.7)',
              borderRadius: 1,
              p: 0.5
            }}
          >
            <LockIcon sx={{ color: 'white', fontSize: 20 }} />
          </Box>
        )}
        {module.completed && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              bgcolor: 'success.main',
              borderRadius: 1,
              p: 0.5
            }}
          >
            <CheckIcon sx={{ color: 'white', fontSize: 20 }} />
          </Box>
        )}
      </Box>

      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
          <Chip
            label={module.difficulty}
            size="small"
            color={getDifficultyColor(module.difficulty) as any}
          />
          <Chip
            label={module.category}
            size="small"
            variant="outlined"
          />
        </Box>

        <Typography variant="h6" gutterBottom fontWeight="bold">
          {module.title}
        </Typography>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {module.description}
        </Typography>

        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {getTypeIcon(module.type)}
            <Typography variant="caption" color="text.secondary">
              {module.lessons} leçons
            </Typography>
          </Box>
          <Typography variant="caption" color="text.secondary">
            {module.duration}
          </Typography>
        </Box>

        {module.progress > 0 && (
          <Box sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
              <Typography variant="caption">Progression</Typography>
              <Typography variant="caption">{module.progress}%</Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={module.progress}
              sx={{ height: 6, borderRadius: 3 }}
            />
          </Box>
        )}
      </CardContent>

      <CardActions>
        <Button
          fullWidth
          variant={module.completed ? "outlined" : "contained"}
          onClick={() => onStart(module)}
          disabled={!!module.locked}
          startIcon={module.completed ? <CheckIcon /> : <PlayIcon />}
        >
          {module.locked ? 'Verrouillé' : module.completed ? 'Revoir' : module.progress > 0 ? 'Continuer' : 'Commencer'}
        </Button>
      </CardActions>
    </Card>
  )
}

export const EducationPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [selectedCategory, setSelectedCategory] = useState('Tous')
  const [selectedDifficulty, setSelectedDifficulty] = useState('Tous')
  const [selectedModule, setSelectedModule] = useState<typeof educationModules[0] | null>(null)
  const [openDialog, setOpenDialog] = useState(false)

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const handleStartModule = (module: typeof educationModules[0]) => {
    setSelectedModule(module)
    setOpenDialog(true)
  }

  const filteredModules = educationModules.filter(module => {
    const categoryMatch = selectedCategory === 'Tous' || module.category === selectedCategory
    const difficultyMatch = selectedDifficulty === 'Tous' || module.difficulty === selectedDifficulty
    return categoryMatch && difficultyMatch
  })

  const completedModules = educationModules.filter(m => m.completed).length
  const totalProgress = educationModules.reduce((sum, m) => sum + m.progress, 0) / educationModules.length

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Éducation Financière
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Éducation Financière
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Développez vos connaissances financières avec nos modules d'apprentissage interactifs
          </Typography>
        </Box>

        {/* Progress Overview */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" fontWeight="bold">
                  {completedModules}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Modules Terminés
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {totalProgress.toFixed(0)}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Progression Globale
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main" fontWeight="bold">
                  {educationModules.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Modules Disponibles
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              <Typography variant="body2" fontWeight="bold">Filtres:</Typography>

              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Typography variant="body2" color="text.secondary">Catégorie:</Typography>
                {categories.map(category => (
                  <Chip
                    key={category}
                    label={category}
                    onClick={() => setSelectedCategory(category)}
                    color={selectedCategory === category ? 'primary' : 'default'}
                    variant={selectedCategory === category ? 'filled' : 'outlined'}
                    size="small"
                  />
                ))}
              </Box>

              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Typography variant="body2" color="text.secondary">Difficulté:</Typography>
                {difficulties.map(difficulty => (
                  <Chip
                    key={difficulty}
                    label={difficulty}
                    onClick={() => setSelectedDifficulty(difficulty)}
                    color={selectedDifficulty === difficulty ? 'secondary' : 'default'}
                    variant={selectedDifficulty === difficulty ? 'filled' : 'outlined'}
                    size="small"
                  />
                ))}
              </Box>
            </Box>
          </CardContent>
        </Card>

        {/* Modules Grid */}
        <Grid container spacing={3}>
          {filteredModules.map((module) => (
            <Grid item xs={12} sm={6} md={4} key={module.id}>
              <ModuleCard
                module={module}
                onStart={handleStartModule}
              />
            </Grid>
          ))}
        </Grid>

        {/* Module Details Dialog */}
        <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
          {selectedModule && (
            <>
              <DialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Typography variant="h2">{selectedModule.image}</Typography>
                  <Box>
                    <Typography variant="h6">{selectedModule.title}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {selectedModule.duration} • {selectedModule.lessons} leçons
                    </Typography>
                  </Box>
                </Box>
              </DialogTitle>
              <DialogContent>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {selectedModule.description}
                </Typography>

                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" fontWeight="bold" gutterBottom>
                    Ce que vous allez apprendre:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Concepts fondamentaux de {selectedModule.category.toLowerCase()}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Stratégies pratiques et applicables
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Exercices interactifs et quiz
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    • Certificat de completion
                  </Typography>
                </Box>

                {selectedModule.progress > 0 && (
                  <Box>
                    <Typography variant="body2" fontWeight="bold" gutterBottom>
                      Votre progression:
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={selectedModule.progress}
                      sx={{ height: 8, borderRadius: 4, mb: 1 }}
                    />
                    <Typography variant="caption" color="text.secondary">
                      {selectedModule.progress}% terminé
                    </Typography>
                  </Box>
                )}
              </DialogContent>
              <DialogActions>
                <Button onClick={() => setOpenDialog(false)}>Fermer</Button>
                <Button
                  variant="contained"
                  onClick={() => {
                    setOpenDialog(false)
                    // Here you would navigate to the actual module content
                  }}
                  disabled={!!selectedModule.locked}
                >
                  {selectedModule.completed ? 'Revoir' : selectedModule.progress > 0 ? 'Continuer' : 'Commencer'}
                </Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </Container>
    </Box>
  )
}

export default EducationPage
