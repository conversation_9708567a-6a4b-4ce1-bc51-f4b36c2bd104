import{j as e,B as s,o as n,p as t,b as i,I as o,H as r,q as a,d as c,s as l,t as u,u as d,G as p,g as x,h as m,ap as h,e as j,aq as v,ar as g,as as b,at as f,ae as q,af as y,ag as z,ah as A,f as C,au as w,av as I,aw as B,ax as N,aa as D}from"./mui-vendor-b761306f.js";import{u as R,r as L}from"./react-vendor-2f216e43.js";import{A as O,R as P}from"./index-bd55ea72.js";const S=[{category:"Général",questions:[{question:"Comment créer mon premier budget ?",answer:'Pour créer votre premier budget, allez dans la section "Budgets" et cliquez sur "Nouveau Budget". Choisissez une catégorie, définissez un montant mensuel et suivez vos dépenses.'},{question:"Comment connecter mes comptes bancaires ?",answer:"Actuellement, Nouri fonctionne en mode démo. Dans la version complète, vous pourrez connecter vos comptes via notre partenariat sécurisé avec les banques tunisiennes."},{question:"Mes données sont-elles sécurisées ?",answer:"Absolument ! Nouri utilise un chiffrement de niveau bancaire pour protéger toutes vos données financières. Nous ne partageons jamais vos informations avec des tiers."}]},{category:"Budgets",questions:[{question:"Comment modifier un budget existant ?",answer:'Dans la page Budgets, cliquez sur l\'icône "Modifier" sur la carte du budget que vous souhaitez changer. Vous pouvez ajuster le montant, la catégorie ou supprimer le budget.'},{question:"Que faire si je dépasse mon budget ?",answer:"Si vous dépassez un budget, Nouri vous enverra une alerte. Analysez vos dépenses, ajustez le budget si nécessaire, ou réduisez les dépenses dans cette catégorie."},{question:"Puis-je créer des budgets personnalisés ?",answer:'Oui ! Vous pouvez créer des budgets pour n\'importe quelle catégorie. Utilisez "Autre" comme catégorie et personnalisez le nom selon vos besoins.'}]},{category:"Épargne",questions:[{question:"Comment définir un objectif d'épargne ?",answer:'Allez dans "Épargne", cliquez sur "Nouvel Objectif", définissez le montant cible, la date d\'échéance et la priorité. Nouri vous aidera à suivre vos progrès.'},{question:"Comment ajouter de l'argent à mes objectifs ?",answer:"Sur chaque carte d'objectif, cliquez sur \"Ajouter de l'argent\" et saisissez le montant. Vos progrès seront automatiquement mis à jour."},{question:"Puis-je modifier mes objectifs d'épargne ?",answer:'Oui, vous pouvez modifier le montant cible, la date d\'échéance et la priorité de vos objectifs à tout moment via le bouton "Modifier".'}]},{category:"Assistant IA",questions:[{question:"Comment utiliser l'assistant IA Nouri ?",answer:'L\'assistant IA est disponible dans la section "Assistant IA". Posez vos questions en français ou en arabe, et il vous donnera des conseils personnalisés basés sur vos données financières.'},{question:"Quels types de conseils peut donner l'IA ?",answer:"L'IA peut analyser vos dépenses, suggérer des optimisations de budget, recommander des stratégies d'épargne et vous aider à atteindre vos objectifs financiers."},{question:"L'IA a-t-elle accès à mes données bancaires ?",answer:"L'IA analyse uniquement les données que vous saisissez dans Nouri. Elle ne peut pas accéder directement à vos comptes bancaires."}]}],k=[{title:"Chat en direct",description:"Discutez avec notre équipe support",icon:e.jsx(w,{}),action:"Démarrer le chat",available:!0},{title:"Email",description:"<EMAIL>",icon:e.jsx(I,{}),action:"Envoyer un email",available:!0},{title:"Téléphone",description:"+216 70 123 456",icon:e.jsx(B,{}),action:"Appeler",available:!0}],G=[{title:"Guide de démarrage",description:"Apprenez les bases de Nouri",icon:e.jsx(N,{}),type:"guide"},{title:"Tutoriels vidéo",description:"Regardez nos tutoriels",icon:e.jsx(D,{}),type:"video"},{title:"Formation financière",description:"Modules d'apprentissage",icon:e.jsx(N,{}),type:"education"}],T=()=>{const I=R(),{logout:B}=L.useContext(O),[N,D]=L.useState(""),[T,W]=L.useState("Général"),E=S.map((e=>({...e,questions:e.questions.filter((e=>e.question.toLowerCase().includes(N.toLowerCase())||e.answer.toLowerCase().includes(N.toLowerCase())))}))).filter((e=>e.questions.length>0));return e.jsxs(s,{sx:{flexGrow:1},children:[e.jsx(n,{position:"static",elevation:1,children:e.jsxs(t,{children:[e.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Centre d'Aide"}),e.jsx(o,{color:"inherit",onClick:()=>I(P.DASHBOARD),children:e.jsx(r,{})}),e.jsx(o,{color:"inherit",onClick:()=>{B(),I(P.HOME)},children:e.jsx(a,{})})]})}),e.jsxs(c,{maxWidth:"lg",sx:{py:4},children:[e.jsxs(s,{sx:{textAlign:"center",mb:4},children:[e.jsx(i,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Comment pouvons-nous vous aider ?"}),e.jsx(i,{variant:"body1",color:"text.secondary",sx:{mb:3},children:"Trouvez des réponses à vos questions ou contactez notre équipe support"}),e.jsx(l,{placeholder:"Rechercher dans l'aide...",value:N,onChange:e=>D(e.target.value),InputProps:{startAdornment:e.jsx(u,{position:"start",children:e.jsx(d,{})})},sx:{maxWidth:500,width:"100%"}})]}),e.jsxs(p,{container:!0,spacing:3,children:[e.jsx(p,{item:!0,xs:12,md:8,children:e.jsx(x,{children:e.jsxs(m,{children:[e.jsxs(i,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(h,{}),"Questions Fréquentes"]}),0===E.length?e.jsxs(i,{color:"text.secondary",children:['Aucune question trouvée pour "',N,'"']}):E.map((n=>e.jsxs(s,{sx:{mb:2},children:[e.jsx(j,{label:n.category,color:"primary",sx:{mb:2}}),n.questions.map(((s,t)=>{return e.jsxs(v,{expanded:T===`${n.category}-${t}`,onChange:(o=`${n.category}-${t}`,(e,s)=>{W(!!s&&o)}),children:[e.jsx(g,{expandIcon:e.jsx(b,{}),children:e.jsx(i,{fontWeight:"medium",children:s.question})}),e.jsx(f,{children:e.jsx(i,{color:"text.secondary",children:s.answer})})]},t);var o}))]},n.category)))]})})}),e.jsxs(p,{item:!0,xs:12,md:4,children:[e.jsx(x,{sx:{mb:3},children:e.jsxs(m,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"💬 Contacter le Support"}),e.jsx(q,{children:k.map(((s,n)=>e.jsxs(y,{sx:{px:0},children:[e.jsx(z,{children:s.icon}),e.jsx(A,{primary:s.title,secondary:s.description}),e.jsx(C,{size:"small",variant:"outlined",disabled:!s.available,children:s.action})]},n)))})]})}),e.jsx(x,{children:e.jsxs(m,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"📚 Ressources Utiles"}),e.jsx(q,{children:G.map(((s,n)=>e.jsxs(y,{sx:{px:0,cursor:"pointer"},onClick:()=>{"education"===s.type&&I(P.EDUCATION)},children:[e.jsx(z,{children:s.icon}),e.jsx(A,{primary:s.title,secondary:s.description})]},n)))})]})}),e.jsx(x,{sx:{mt:3},children:e.jsxs(m,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"💡 Conseils Rapides"}),e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:[e.jsxs(s,{sx:{p:2,bgcolor:"info.50",borderRadius:2},children:[e.jsx(i,{variant:"body2",fontWeight:"bold",color:"info.dark",children:"Astuce du jour"}),e.jsx(i,{variant:"body2",sx:{mt:1},children:"Utilisez l'assistant IA pour obtenir des conseils personnalisés sur vos finances !"})]}),e.jsxs(s,{sx:{p:2,bgcolor:"success.50",borderRadius:2},children:[e.jsx(i,{variant:"body2",fontWeight:"bold",color:"success.dark",children:"Saviez-vous ?"}),e.jsx(i,{variant:"body2",sx:{mt:1},children:"Vous pouvez exporter vos rapports financiers en PDF depuis la page Rapports."})]})]})]})})]})]}),e.jsx(x,{sx:{mt:4,textAlign:"center"},children:e.jsxs(m,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Vous ne trouvez pas ce que vous cherchez ?"}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Notre équipe est là pour vous aider 24h/24 et 7j/7"}),e.jsx(C,{variant:"contained",startIcon:e.jsx(w,{}),size:"large",onClick:()=>I(P.CHATBOT),children:"Parler à l'Assistant IA"})]})})]})]})};export{T as HelpPage,T as default};
