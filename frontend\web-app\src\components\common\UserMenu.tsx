/**
 * Menu utilisateur
 */

import React from 'react'
import { Menu, MenuItem, Typography } from '@mui/material'
import { useAuth0 } from '@auth0/auth0-react'

interface UserMenuProps {
  anchorEl: HTMLElement | null
  open: boolean
  onClose: () => void
}

export const UserMenu: React.FC<UserMenuProps> = ({ anchorEl, open, onClose }) => {
  const { logout } = useAuth0()

  const handleLogout = () => {
    logout({ logoutParams: { returnTo: window.location.origin } })
    onClose()
  }

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <MenuItem onClick={onClose}>
        <Typography>Profil</Typography>
      </MenuItem>
      <MenuItem onClick={onClose}>
        <Typography>Paramètres</Typography>
      </MenuItem>
      <MenuItem onClick={handleLogout}>
        <Typography>Déconnexion</Typography>
      </MenuItem>
    </Menu>
  )
}

export default UserMenu
