/**
 * Complete Chatbot Page
 * AI Financial Assistant with chat interface
 */

import React, { useState, useContext, useRef, useEffect } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  TextField,
  IconButton,
  AppBar,
  Toolbar,
  Avatar,
  Paper,
  Chip,
  Button,
  Grid,
  Divider
} from '@mui/material'
import {
  Send as SendIcon,
  Home as HomeIcon,
  Logout as LogoutIcon,
  SmartToy as BotIcon,
  Person as PersonIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

interface Message {
  id: string
  text: string
  sender: 'user' | 'bot'
  timestamp: Date
  type?: 'text' | 'suggestion' | 'chart'
}

// Mock AI responses
const mockResponses = [
  "Bonjour ! Je suis Nouri, votre assistant financier IA. Comment puis-je vous aider aujourd'hui ?",
  "D'après vos données, vous avez bien géré votre budget ce mois-ci. Félicitations !",
  "Je recommande d'augmenter votre épargne de 50 TND ce mois pour atteindre votre objectif vacances.",
  "Votre dépense en restaurants a augmenté de 15% ce mois. Voulez-vous que je vous aide à optimiser ce budget ?",
  "Excellente question ! Basé sur vos habitudes, je suggère de créer un budget de 200 TND pour les loisirs.",
  "Votre fonds d'urgence représente actuellement 3 mois de dépenses. L'objectif recommandé est de 6 mois.",
  "Je peux vous aider à analyser vos dépenses par catégorie. Quelle période vous intéresse ?",
  "Votre score financier s'améliore ! Continuez sur cette voie pour atteindre vos objectifs."
]

const quickSuggestions = [
  "Analyser mes dépenses",
  "Conseils d'épargne",
  "Optimiser mon budget",
  "Objectifs financiers",
  "Rapport mensuel",
  "Conseils d'investissement"
]

export const ChatbotPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Bonjour ! Je suis Nouri, votre assistant financier IA. Comment puis-je vous aider aujourd'hui ?",
      sender: 'bot',
      timestamp: new Date(),
      type: 'text'
    }
  ])
  const [inputText, setInputText] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const handleSendMessage = (text?: string) => {
    const messageText = text || inputText.trim()
    if (!messageText) return

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      sender: 'user',
      timestamp: new Date(),
      type: 'text'
    }

    setMessages(prev => [...prev, userMessage])
    setInputText('')
    setIsTyping(true)

    // Simulate AI response
    setTimeout(() => {
      const randomResponse = mockResponses[Math.floor(Math.random() * mockResponses.length)]
      const botMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: randomResponse,
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      }
      setMessages(prev => [...prev, botMessage])
      setIsTyping(false)
    }, 1000 + Math.random() * 2000) // Random delay between 1-3 seconds
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const handleClearChat = () => {
    setMessages([
      {
        id: '1',
        text: "Bonjour ! Je suis Nouri, votre assistant financier IA. Comment puis-je vous aider aujourd'hui ?",
        sender: 'bot',
        timestamp: new Date(),
        type: 'text'
      }
    ])
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Box sx={{ flexGrow: 1, height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Assistant IA
          </Typography>
          <IconButton color="inherit" onClick={handleClearChat}>
            <RefreshIcon />
          </IconButton>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', py: 2 }}>
        <Grid container spacing={2} sx={{ flexGrow: 1 }}>
          {/* Chat Area */}
          <Grid item xs={12} md={8}>
            <Card sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
              {/* Messages Area */}
              <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
                {messages.map((message) => (
                  <Box
                    key={message.id}
                    sx={{
                      display: 'flex',
                      justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
                      mb: 2
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: 1,
                        maxWidth: '70%',
                        flexDirection: message.sender === 'user' ? 'row-reverse' : 'row'
                      }}
                    >
                      <Avatar
                        sx={{
                          bgcolor: message.sender === 'user' ? 'primary.main' : 'secondary.main',
                          width: 32,
                          height: 32
                        }}
                      >
                        {message.sender === 'user' ? <PersonIcon /> : <BotIcon />}
                      </Avatar>
                      <Paper
                        sx={{
                          p: 2,
                          bgcolor: message.sender === 'user' ? 'primary.main' : 'grey.100',
                          color: message.sender === 'user' ? 'white' : 'text.primary',
                          borderRadius: 2
                        }}
                      >
                        <Typography variant="body2">
                          {message.text}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            opacity: 0.7,
                            display: 'block',
                            mt: 0.5,
                            textAlign: message.sender === 'user' ? 'right' : 'left'
                          }}
                        >
                          {formatTime(message.timestamp)}
                        </Typography>
                      </Paper>
                    </Box>
                  </Box>
                ))}

                {/* Typing Indicator */}
                {isTyping && (
                  <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
                        <BotIcon />
                      </Avatar>
                      <Paper sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          Nouri est en train d'écrire...
                        </Typography>
                      </Paper>
                    </Box>
                  </Box>
                )}

                <div ref={messagesEndRef} />
              </Box>

              <Divider />

              {/* Input Area */}
              <Box sx={{ p: 2 }}>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <TextField
                    fullWidth
                    multiline
                    maxRows={3}
                    placeholder="Tapez votre message..."
                    value={inputText}
                    onChange={(e) => setInputText(e.target.value)}
                    onKeyPress={handleKeyPress}
                    disabled={isTyping}
                  />
                  <IconButton
                    color="primary"
                    onClick={() => handleSendMessage()}
                    disabled={!inputText.trim() || isTyping}
                    sx={{ alignSelf: 'flex-end' }}
                  >
                    <SendIcon />
                  </IconButton>
                </Box>
              </Box>
            </Card>
          </Grid>

          {/* Sidebar */}
          <Grid item xs={12} md={4}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* Quick Suggestions */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Suggestions rapides
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    {quickSuggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outlined"
                        size="small"
                        onClick={() => handleSendMessage(suggestion)}
                        disabled={isTyping}
                        sx={{ justifyContent: 'flex-start' }}
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </Box>
                </CardContent>
              </Card>

              {/* AI Capabilities */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Mes capacités
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Chip label="📊 Analyse des dépenses" variant="outlined" size="small" />
                    <Chip label="💰 Conseils d'épargne" variant="outlined" size="small" />
                    <Chip label="📈 Planification budgétaire" variant="outlined" size="small" />
                    <Chip label="🎯 Objectifs financiers" variant="outlined" size="small" />
                    <Chip label="📋 Rapports personnalisés" variant="outlined" size="small" />
                    <Chip label="⚠️ Alertes intelligentes" variant="outlined" size="small" />
                  </Box>
                </CardContent>
              </Card>

              {/* Tips */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    💡 Conseil du jour
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Saviez-vous que mettre de côté seulement 10 TND par jour peut vous faire économiser
                    plus de 3600 TND par an ? Commencez petit et augmentez progressivement !
                  </Typography>
                </CardContent>
              </Card>

              {/* Stats */}
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    📈 Vos statistiques
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Messages échangés</Typography>
                      <Typography variant="body2" fontWeight="bold">{messages.length}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Conseils reçus</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {messages.filter(m => m.sender === 'bot').length}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Session active</Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {Math.floor((Date.now() - messages[0].timestamp.getTime()) / 60000)} min
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  )
}

export default ChatbotPage
