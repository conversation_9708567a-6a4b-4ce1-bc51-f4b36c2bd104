/**
 * Configuration Swagger/OpenAPI pour l'API Gateway Nouri
 * Documentation automatique des endpoints
 */

import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { Express } from 'express';
import { config } from './config';

// Configuration de base Swagger
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Nouri - Coach Financier IA API',
      version: config.app.version,
      description: `
        API REST pour Nouri, le coach financier intelligent pour le marché tunisien.
        
        ## Fonctionnalités principales
        - 🔐 Authentification sécurisée avec Auth0
        - 🏦 Intégration bancaire et synchronisation des transactions
        - 💰 Gestion intelligente des budgets et objectifs d'épargne
        - 🤖 Chatbot IA multilingue (arabe/français)
        - 📚 Module éducatif gamifié
        - 📊 Rapports et analyses financières
        - 🔔 Notifications intelligentes
        
        ## Sécurité
        Cette API utilise une architecture "Zero Trust" avec chiffrement AES-256 et TLS 1.3.
        Toutes les routes protégées nécessitent un token JWT valide.
        
        ## Support
        - Email: <EMAIL>
        - Documentation: https://docs.nouri.tn
      `,
      contact: {
        name: 'Équipe Nouri',
        email: '<EMAIL>',
        url: 'https://nouri.tn'
      },
      license: {
        name: 'Propriétaire',
        url: 'https://nouri.tn/license'
      }
    },
    servers: [
      {
        url: config.app.environment === 'development' 
          ? `http://localhost:${config.server.port}`
          : 'https://api.nouri.tn',
        description: config.app.environment === 'development' 
          ? 'Serveur de développement'
          : 'Serveur de production'
      }
    ],
    components: {
      securitySchemes: {
        BearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'Token JWT obtenu via Auth0 ou l\'endpoint /api/auth/login'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Message d\'erreur'
            },
            code: {
              type: 'string',
              description: 'Code d\'erreur'
            },
            timestamp: {
              type: 'string',
              format: 'date-time',
              description: 'Timestamp de l\'erreur'
            },
            path: {
              type: 'string',
              description: 'Chemin de la requête'
            },
            details: {
              type: 'array',
              items: {
                type: 'object'
              },
              description: 'Détails additionnels (validation, etc.)'
            }
          }
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Identifiant unique de l\'utilisateur'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Adresse email'
            },
            firstName: {
              type: 'string',
              description: 'Prénom'
            },
            lastName: {
              type: 'string',
              description: 'Nom de famille'
            },
            phoneNumber: {
              type: 'string',
              description: 'Numéro de téléphone'
            },
            language: {
              type: 'string',
              enum: ['ar', 'fr', 'en'],
              description: 'Langue préférée'
            },
            currency: {
              type: 'string',
              enum: ['TND', 'EUR', 'USD'],
              description: 'Devise préférée'
            },
            onboardingCompleted: {
              type: 'boolean',
              description: 'Onboarding terminé'
            },
            kycStatus: {
              type: 'string',
              enum: ['pending', 'verified', 'rejected'],
              description: 'Statut KYC'
            }
          }
        },
        Account: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Identifiant unique du compte'
            },
            accountName: {
              type: 'string',
              description: 'Nom du compte'
            },
            accountType: {
              type: 'string',
              enum: ['checking', 'savings', 'credit'],
              description: 'Type de compte'
            },
            balance: {
              type: 'number',
              format: 'decimal',
              description: 'Solde du compte'
            },
            currency: {
              type: 'string',
              description: 'Devise du compte'
            },
            bankName: {
              type: 'string',
              description: 'Nom de la banque'
            },
            isActive: {
              type: 'boolean',
              description: 'Compte actif'
            }
          }
        },
        Transaction: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Identifiant unique de la transaction'
            },
            amount: {
              type: 'number',
              format: 'decimal',
              description: 'Montant de la transaction'
            },
            currency: {
              type: 'string',
              description: 'Devise de la transaction'
            },
            description: {
              type: 'string',
              description: 'Description de la transaction'
            },
            category: {
              type: 'string',
              description: 'Catégorie de la transaction'
            },
            transactionDate: {
              type: 'string',
              format: 'date-time',
              description: 'Date de la transaction'
            },
            transactionType: {
              type: 'string',
              enum: ['debit', 'credit'],
              description: 'Type de transaction'
            }
          }
        },
        Budget: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Identifiant unique du budget'
            },
            name: {
              type: 'string',
              description: 'Nom du budget'
            },
            totalAmount: {
              type: 'number',
              format: 'decimal',
              description: 'Montant total du budget'
            },
            spentAmount: {
              type: 'number',
              format: 'decimal',
              description: 'Montant dépensé'
            },
            currency: {
              type: 'string',
              description: 'Devise du budget'
            },
            startDate: {
              type: 'string',
              format: 'date',
              description: 'Date de début'
            },
            endDate: {
              type: 'string',
              format: 'date',
              description: 'Date de fin'
            }
          }
        },
        SavingsGoal: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Identifiant unique de l\'objectif'
            },
            name: {
              type: 'string',
              description: 'Nom de l\'objectif'
            },
            targetAmount: {
              type: 'number',
              format: 'decimal',
              description: 'Montant cible'
            },
            currentAmount: {
              type: 'number',
              format: 'decimal',
              description: 'Montant actuel'
            },
            currency: {
              type: 'string',
              description: 'Devise'
            },
            targetDate: {
              type: 'string',
              format: 'date',
              description: 'Date cible'
            },
            category: {
              type: 'string',
              description: 'Catégorie de l\'objectif'
            }
          }
        }
      },
      responses: {
        UnauthorizedError: {
          description: 'Token d\'authentification manquant ou invalide',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ForbiddenError: {
          description: 'Permissions insuffisantes',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        NotFoundError: {
          description: 'Ressource non trouvée',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        ValidationError: {
          description: 'Erreur de validation des données',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        InternalServerError: {
          description: 'Erreur interne du serveur',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      }
    },
    security: [
      {
        BearerAuth: []
      }
    ],
    tags: [
      {
        name: 'Authentification',
        description: 'Gestion de l\'authentification et des sessions'
      },
      {
        name: 'Utilisateurs',
        description: 'Gestion des profils utilisateurs'
      },
      {
        name: 'Bancaire',
        description: 'Comptes bancaires et transactions'
      },
      {
        name: 'Budgets',
        description: 'Gestion des budgets et objectifs d\'épargne'
      },
      {
        name: 'Chatbot',
        description: 'Assistant virtuel Nouri'
      },
      {
        name: 'Notifications',
        description: 'Système de notifications'
      },
      {
        name: 'Éducation',
        description: 'Modules éducatifs et quiz'
      },
      {
        name: 'Rapports',
        description: 'Génération de rapports financiers'
      },
      {
        name: 'Santé',
        description: 'Monitoring et diagnostics système'
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/index.ts'
  ]
};

// Génération de la spécification Swagger
const specs = swaggerJsdoc(swaggerOptions);

/**
 * Configuration de Swagger UI
 */
export const setupSwagger = (app: Express): void => {
  // Options personnalisées pour Swagger UI
  const swaggerUiOptions = {
    customCss: `
      .swagger-ui .topbar { display: none }
      .swagger-ui .info .title { color: #1976d2 }
      .swagger-ui .scheme-container { background: #fafafa; padding: 15px; border-radius: 4px; }
    `,
    customSiteTitle: 'Nouri API Documentation',
    customfavIcon: '/favicon.ico',
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
      filter: true,
      showExtensions: true,
      showCommonExtensions: true,
      docExpansion: 'none',
      defaultModelsExpandDepth: 2,
      defaultModelExpandDepth: 2
    }
  };

  // Route pour la documentation Swagger
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerUiOptions));

  // Route pour obtenir la spécification JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(specs);
  });

  console.log(`📚 Documentation Swagger disponible sur: http://localhost:${config.server.port}/api-docs`);
};

export { specs };
