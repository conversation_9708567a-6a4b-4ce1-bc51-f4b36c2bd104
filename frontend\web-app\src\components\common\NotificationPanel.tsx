/**
 * Panel de notifications
 */

import React from 'react'
import { Drawer, Box, Typography } from '@mui/material'

interface NotificationPanelProps {
  open: boolean
  onClose: () => void
}

export const NotificationPanel: React.FC<NotificationPanelProps> = ({ open, onClose }) => {
  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{ '& .MuiDrawer-paper': { width: 320 } }}
    >
      <Box sx={{ p: 2 }}>
        <Typography variant="h6">Notifications</Typography>
        <Typography variant="body2" color="text.secondary">
          Aucune notification pour le moment
        </Typography>
      </Box>
    </Drawer>
  )
}

export default NotificationPanel
