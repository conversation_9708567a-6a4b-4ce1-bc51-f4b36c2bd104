/**
 * Configuration i18next pour Nouri
 * Support multilingue français, arabe et anglais
 */

import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'

// Import des traductions
import fr from './locales/fr.json'
import ar from './locales/ar.json'
import en from './locales/en.json'

// Configuration
const resources = {
  fr: { translation: fr },
  ar: { translation: ar },
  en: { translation: en }
}

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    
    // Langue par défaut
    fallbackLng: 'fr',
    
    // Langues supportées
    supportedLngs: ['fr', 'ar', 'en'],
    
    // Détection de la langue
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'nouri_language'
    },

    // Configuration
    interpolation: {
      escapeValue: false // React échappe déjà les valeurs
    },

    // Namespace par défaut
    defaultNS: 'translation',
    
    // Debug en développement
    debug: import.meta.env.DEV,

    // Configuration RTL pour l'arabe
    react: {
      useSuspense: false
    }
  })

// Fonction pour changer la direction du document
i18n.on('languageChanged', (lng) => {
  const isRTL = lng === 'ar'
  document.documentElement.dir = isRTL ? 'rtl' : 'ltr'
  document.documentElement.lang = lng
})

export default i18n
