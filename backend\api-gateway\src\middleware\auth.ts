/**
 * Middleware d'authentification et d'autorisation pour Nouri
 * Intégration avec Auth0 et gestion des permissions
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import axios from 'axios';
import { config } from '../config/config';
import { logger, logSecurityEvent } from '../utils/logger';
import { redisClient } from '../config/redis';

// Extension de l'interface Request pour inclure l'utilisateur
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        sub: string;
        permissions: string[];
        roles: string[];
        metadata?: any;
      };
    }
  }
}

interface Auth0User {
  sub: string;
  email: string;
  email_verified: boolean;
  name: string;
  picture: string;
  permissions?: string[];
  'https://nouri.tn/roles'?: string[];
  'https://nouri.tn/metadata'?: any;
}

/**
 * Vérifie et décode le token JWT Auth0
 */
const verifyAuth0Token = async (token: string): Promise<Auth0User> => {
  try {
    // Récupérer les clés publiques Auth0 (avec cache)
    const cacheKey = `auth0:jwks:${config.auth.auth0Domain}`;
    let jwks = await redisClient.get(cacheKey);
    
    if (!jwks) {
      const response = await axios.get(`https://${config.auth.auth0Domain}/.well-known/jwks.json`);
      jwks = JSON.stringify(response.data);
      await redisClient.setex(cacheKey, 3600, jwks); // Cache 1 heure
    }

    // Décoder le token sans vérification pour obtenir le header
    const decoded = jwt.decode(token, { complete: true });
    if (!decoded || typeof decoded === 'string') {
      throw new Error('Token invalide');
    }

    // Trouver la clé correspondante
    const jwksData = JSON.parse(jwks);
    const key = jwksData.keys.find((k: any) => k.kid === decoded.header.kid);
    if (!key) {
      throw new Error('Clé de signature non trouvée');
    }

    // Construire la clé publique
    const publicKey = `-----BEGIN CERTIFICATE-----\n${key.x5c[0]}\n-----END CERTIFICATE-----`;

    // Vérifier le token
    const payload = jwt.verify(token, publicKey, {
      audience: config.auth.auth0Audience,
      issuer: `https://${config.auth.auth0Domain}/`,
      algorithms: ['RS256']
    }) as Auth0User;

    return payload;
  } catch (error) {
    logger.error('Erreur lors de la vérification du token Auth0:', error);
    throw new Error('Token invalide ou expiré');
  }
};

/**
 * Récupère les informations utilisateur depuis Auth0
 */
const getUserInfo = async (sub: string): Promise<any> => {
  try {
    const cacheKey = `user:auth0:${sub}`;
    let userInfo = await redisClient.get(cacheKey);
    
    if (!userInfo) {
      // Obtenir un token de management Auth0
      const tokenResponse = await axios.post(`https://${config.auth.auth0Domain}/oauth/token`, {
        client_id: config.auth.auth0ClientId,
        client_secret: config.auth.auth0ClientSecret,
        audience: `https://${config.auth.auth0Domain}/api/v2/`,
        grant_type: 'client_credentials'
      });

      const managementToken = tokenResponse.data.access_token;

      // Récupérer les informations utilisateur
      const userResponse = await axios.get(
        `https://${config.auth.auth0Domain}/api/v2/users/${encodeURIComponent(sub)}`,
        {
          headers: {
            Authorization: `Bearer ${managementToken}`
          }
        }
      );

      userInfo = JSON.stringify(userResponse.data);
      await redisClient.setex(cacheKey, 300, userInfo); // Cache 5 minutes
    }

    return JSON.parse(userInfo);
  } catch (error) {
    logger.error('Erreur lors de la récupération des infos utilisateur:', error);
    return null;
  }
};

/**
 * Middleware principal d'authentification
 */
export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logSecurityEvent('missing_auth_header', { url: req.originalUrl }, req);
      return res.status(401).json({
        error: 'Token d\'authentification manquant',
        code: 'MISSING_AUTH_TOKEN'
      });
    }

    const token = authHeader.substring(7); // Enlever "Bearer "
    
    // Vérifier si le token est en blacklist
    const isBlacklisted = await redisClient.get(`blacklist:${token}`);
    if (isBlacklisted) {
      logSecurityEvent('blacklisted_token_used', { token: token.substring(0, 20) + '...' }, req);
      return res.status(401).json({
        error: 'Token révoqué',
        code: 'TOKEN_REVOKED'
      });
    }

    // Vérifier le token Auth0
    const auth0User = await verifyAuth0Token(token);
    
    // Récupérer les informations complètes de l'utilisateur
    const userInfo = await getUserInfo(auth0User.sub);
    
    // Construire l'objet utilisateur pour la requête
    req.user = {
      id: auth0User.sub,
      email: auth0User.email,
      sub: auth0User.sub,
      permissions: auth0User.permissions || [],
      roles: auth0User['https://nouri.tn/roles'] || [],
      metadata: {
        ...auth0User['https://nouri.tn/metadata'],
        ...userInfo?.user_metadata,
        emailVerified: auth0User.email_verified,
        name: auth0User.name,
        picture: auth0User.picture
      }
    };

    // Log de l'accès réussi
    logger.info('Authentification réussie', {
      userId: req.user.id,
      email: req.user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });

    next();
  } catch (error) {
    logSecurityEvent('auth_failure', { 
      error: error instanceof Error ? error.message : 'Erreur inconnue',
      url: req.originalUrl 
    }, req);
    
    return res.status(401).json({
      error: 'Authentification échouée',
      message: error instanceof Error ? error.message : 'Erreur inconnue',
      code: 'AUTH_FAILED'
    });
  }
};

/**
 * Middleware de vérification des permissions
 */
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Utilisateur non authentifié',
        code: 'NOT_AUTHENTICATED'
      });
    }

    if (!req.user.permissions.includes(permission)) {
      logSecurityEvent('permission_denied', {
        userId: req.user.id,
        requiredPermission: permission,
        userPermissions: req.user.permissions,
        url: req.originalUrl
      }, req);

      return res.status(403).json({
        error: 'Permission insuffisante',
        message: `La permission '${permission}' est requise`,
        code: 'INSUFFICIENT_PERMISSION'
      });
    }

    next();
  };
};

/**
 * Middleware de vérification des rôles
 */
export const requireRole = (role: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Utilisateur non authentifié',
        code: 'NOT_AUTHENTICATED'
      });
    }

    if (!req.user.roles.includes(role)) {
      logSecurityEvent('role_denied', {
        userId: req.user.id,
        requiredRole: role,
        userRoles: req.user.roles,
        url: req.originalUrl
      }, req);

      return res.status(403).json({
        error: 'Rôle insuffisant',
        message: `Le rôle '${role}' est requis`,
        code: 'INSUFFICIENT_ROLE'
      });
    }

    next();
  };
};

/**
 * Middleware optionnel d'authentification (pour les routes publiques avec infos utilisateur optionnelles)
 */
export const optionalAuth = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      const auth0User = await verifyAuth0Token(token);
      const userInfo = await getUserInfo(auth0User.sub);
      
      req.user = {
        id: auth0User.sub,
        email: auth0User.email,
        sub: auth0User.sub,
        permissions: auth0User.permissions || [],
        roles: auth0User['https://nouri.tn/roles'] || [],
        metadata: {
          ...auth0User['https://nouri.tn/metadata'],
          ...userInfo?.user_metadata,
          emailVerified: auth0User.email_verified,
          name: auth0User.name,
          picture: auth0User.picture
        }
      };
    }
    
    next();
  } catch (error) {
    // En cas d'erreur, continuer sans utilisateur (auth optionnelle)
    next();
  }
};

/**
 * Fonction pour révoquer un token (blacklist)
 */
export const revokeToken = async (token: string): Promise<void> => {
  try {
    // Décoder le token pour obtenir l'expiration
    const decoded = jwt.decode(token) as any;
    const expiresIn = decoded.exp ? decoded.exp - Math.floor(Date.now() / 1000) : 3600;
    
    // Ajouter à la blacklist jusqu'à expiration
    await redisClient.setex(`blacklist:${token}`, Math.max(expiresIn, 0), 'revoked');
    
    logger.info('Token révoqué avec succès');
  } catch (error) {
    logger.error('Erreur lors de la révocation du token:', error);
    throw error;
  }
};
