/**
 * Page de connexion Nouri
 */

import React from 'react'
import { Box, Typography, Button } from '@mui/material'
import { useAuth0 } from '@auth0/auth0-react'

export const LoginPage: React.FC = () => {
  const { loginWithRedirect } = useAuth0()

  return (
    <Box sx={{ textAlign: 'center', p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Connexion à Nouri
      </Typography>
      <Button
        variant="contained"
        onClick={() => loginWithRedirect()}
        sx={{ mt: 2 }}
      >
        Se connecter avec Auth0
      </Button>
    </Box>
  )
}

export default LoginPage
