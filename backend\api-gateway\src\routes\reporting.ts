/**
 * Routes de rapports pour Nouri - Proxy vers le service reporting
 */

import { Router, Request, Response } from 'express';
import axios from 'axios';
import { config } from '../config/config';
import { logger } from '../utils/logger';

const router = Router();

const proxyToReportingService = async (req: Request, res: Response, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') => {
  try {
    const url = `${config.services.reportingService}${endpoint}`;
    
    const axiosConfig = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': req.user!.id,
        'X-User-Email': req.user!.email,
        'Authorization': req.headers.authorization
      },
      ...(method !== 'GET' && { data: req.body })
    };

    const response = await axios(axiosConfig);
    res.status(response.status).json(response.data);

  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      logger.error('Erreur lors du proxy vers le service reporting:', error);
      res.status(500).json({
        error: 'Erreur de communication avec le service reporting',
        code: 'REPORTING_SERVICE_ERROR'
      });
    }
  }
};

// Routes des rapports
router.get('/monthly', async (req: Request, res: Response) => {
  await proxyToReportingService(req, res, '/reports/monthly');
});

router.get('/yearly', async (req: Request, res: Response) => {
  await proxyToReportingService(req, res, '/reports/yearly');
});

router.get('/spending-analysis', async (req: Request, res: Response) => {
  await proxyToReportingService(req, res, '/reports/spending-analysis');
});

router.get('/savings-progress', async (req: Request, res: Response) => {
  await proxyToReportingService(req, res, '/reports/savings-progress');
});

router.post('/generate-pdf', async (req: Request, res: Response) => {
  await proxyToReportingService(req, res, '/reports/generate-pdf', 'POST');
});

export default router;
