/**
 * Routes éducatives pour Nouri - Proxy vers le service éducation
 */

import { Router, Request, Response } from 'express';
import axios from 'axios';
import { config } from '../config/config';
import { logger } from '../utils/logger';

const router = Router();

const proxyToEducationService = async (req: Request, res: Response, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') => {
  try {
    const url = `${config.services.educationService}${endpoint}`;
    
    const axiosConfig = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': req.user!.id,
        'X-User-Email': req.user!.email,
        'X-User-Language': req.user!.metadata?.language || 'fr',
        'Authorization': req.headers.authorization
      },
      ...(method !== 'GET' && { data: req.body })
    };

    const response = await axios(axiosConfig);
    res.status(response.status).json(response.data);

  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      logger.error('Erreur lors du proxy vers le service éducation:', error);
      res.status(500).json({
        error: 'Erreur de communication avec le service éducation',
        code: 'EDUCATION_SERVICE_ERROR'
      });
    }
  }
};

// Routes des modules éducatifs
router.get('/modules', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, '/modules');
});

router.get('/modules/:moduleId', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, `/modules/${req.params.moduleId}`);
});

router.post('/modules/:moduleId/start', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, `/modules/${req.params.moduleId}/start`, 'POST');
});

router.put('/modules/:moduleId/progress', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, `/modules/${req.params.moduleId}/progress`, 'PUT');
});

// Routes des quiz
router.get('/quizzes/:quizId', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, `/quizzes/${req.params.quizId}`);
});

router.post('/quizzes/:quizId/submit', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, `/quizzes/${req.params.quizId}/submit`, 'POST');
});

// Routes de progression
router.get('/progress', async (req: Request, res: Response) => {
  await proxyToEducationService(req, res, '/progress');
});

export default router;
