{"version": 3, "file": "auth0-react.min.js", "sources": ["../node_modules/@auth0/auth0-spa-js/dist/auth0-spa-js.production.esm.js", "../src/auth-state.tsx", "../src/auth0-context.tsx", "../src/errors.tsx", "../src/utils.tsx", "../src/reducer.tsx", "../src/auth0-provider.tsx", "../src/use-auth0.tsx", "../src/with-authentication-required.tsx", "../src/with-auth0.tsx"], "sourcesContent": ["function e(e,t){var i={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(i[o]=e[o]);if(null!=e&&\"function\"==typeof Object.getOwnPropertySymbols){var n=0;for(o=Object.getOwnPropertySymbols(e);n<o.length;n++)t.indexOf(o[n])<0&&Object.prototype.propertyIsEnumerable.call(e,o[n])&&(i[o[n]]=e[o[n]])}return i}\"function\"==typeof SuppressedError&&SuppressedError;var t=\"undefined\"!=typeof globalThis?globalThis:\"undefined\"!=typeof window?window:\"undefined\"!=typeof global?global:\"undefined\"!=typeof self?self:{};function i(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,\"default\")?e.default:e}function o(e,t){return e(t={exports:{}},t.exports),t.exports}var n=o((function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0});var i=function(){function e(){var e=this;this.locked=new Map,this.addToLocked=function(t,i){var o=e.locked.get(t);void 0===o?void 0===i?e.locked.set(t,[]):e.locked.set(t,[i]):void 0!==i&&(o.unshift(i),e.locked.set(t,o))},this.isLocked=function(t){return e.locked.has(t)},this.lock=function(t){return new Promise((function(i,o){e.isLocked(t)?e.addToLocked(t,i):(e.addToLocked(t),i())}))},this.unlock=function(t){var i=e.locked.get(t);if(void 0!==i&&0!==i.length){var o=i.pop();e.locked.set(t,i),void 0!==o&&setTimeout(o,0)}else e.locked.delete(t)}}return e.getInstance=function(){return void 0===e.instance&&(e.instance=new e),e.instance},e}();t.default=function(){return i.getInstance()}}));i(n);var a=i(o((function(e,i){var o=t&&t.__awaiter||function(e,t,i,o){return new(i||(i=Promise))((function(n,a){function r(e){try{c(o.next(e))}catch(e){a(e)}}function s(e){try{c(o.throw(e))}catch(e){a(e)}}function c(e){e.done?n(e.value):new i((function(t){t(e.value)})).then(r,s)}c((o=o.apply(e,t||[])).next())}))},a=t&&t.__generator||function(e,t){var i,o,n,a,r={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},\"function\"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(i)throw new TypeError(\"Generator is already executing.\");for(;r;)try{if(i=1,o&&(n=2&a[0]?o.return:a[0]?o.throw||((n=o.return)&&n.call(o),0):o.next)&&!(n=n.call(o,a[1])).done)return n;switch(o=0,n&&(a=[2&a[0],n.value]),a[0]){case 0:case 1:n=a;break;case 4:return r.label++,{value:a[1],done:!1};case 5:r.label++,o=a[1],a=[0];continue;case 7:a=r.ops.pop(),r.trys.pop();continue;default:if(!(n=r.trys,(n=n.length>0&&n[n.length-1])||6!==a[0]&&2!==a[0])){r=0;continue}if(3===a[0]&&(!n||a[1]>n[0]&&a[1]<n[3])){r.label=a[1];break}if(6===a[0]&&r.label<n[1]){r.label=n[1],n=a;break}if(n&&r.label<n[2]){r.label=n[2],r.ops.push(a);break}n[2]&&r.ops.pop(),r.trys.pop();continue}a=t.call(e,r)}catch(e){a=[6,e],o=0}finally{i=n=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},r=t;Object.defineProperty(i,\"__esModule\",{value:!0});var s=\"browser-tabs-lock-key\",c={key:function(e){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},getItem:function(e){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},clear:function(){return o(r,void 0,void 0,(function(){return a(this,(function(e){return[2,window.localStorage.clear()]}))}))},removeItem:function(e){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},setItem:function(e,t){return o(r,void 0,void 0,(function(){return a(this,(function(e){throw new Error(\"Unsupported\")}))}))},keySync:function(e){return window.localStorage.key(e)},getItemSync:function(e){return window.localStorage.getItem(e)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(e){return window.localStorage.removeItem(e)},setItemSync:function(e,t){return window.localStorage.setItem(e,t)}};function d(e){return new Promise((function(t){return setTimeout(t,e)}))}function u(e){for(var t=\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz\",i=\"\",o=0;o<e;o++){i+=t[Math.floor(Math.random()*t.length)]}return i}var l=function(){function e(t){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+u(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=t,void 0===e.waiters&&(e.waiters=[])}return e.prototype.acquireLock=function(t,i){return void 0===i&&(i=5e3),o(this,void 0,void 0,(function(){var o,n,r,l,h,p,m;return a(this,(function(a){switch(a.label){case 0:o=Date.now()+u(4),n=Date.now()+i,r=s+\"-\"+t,l=void 0===this.storageHandler?c:this.storageHandler,a.label=1;case 1:return Date.now()<n?[4,d(30)]:[3,8];case 2:return a.sent(),null!==l.getItemSync(r)?[3,5]:(h=this.id+\"-\"+t+\"-\"+o,[4,d(Math.floor(25*Math.random()))]);case 3:return a.sent(),l.setItemSync(r,JSON.stringify({id:this.id,iat:o,timeoutKey:h,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,d(30)];case 4:return a.sent(),null!==(p=l.getItemSync(r))&&(m=JSON.parse(p)).id===this.id&&m.iat===o?(this.acquiredIatSet.add(o),this.refreshLockWhileAcquired(r,o),[2,!0]):[3,7];case 5:return e.lockCorrector(void 0===this.storageHandler?c:this.storageHandler),[4,this.waitForSomethingToChange(n)];case 6:a.sent(),a.label=7;case 7:return o=Date.now()+u(4),[3,1];case 8:return[2,!1]}}))}))},e.prototype.refreshLockWhileAcquired=function(e,t){return o(this,void 0,void 0,(function(){var i=this;return a(this,(function(r){return setTimeout((function(){return o(i,void 0,void 0,(function(){var i,o,r;return a(this,(function(a){switch(a.label){case 0:return[4,n.default().lock(t)];case 1:return a.sent(),this.acquiredIatSet.has(t)?(i=void 0===this.storageHandler?c:this.storageHandler,null===(o=i.getItemSync(e))?(n.default().unlock(t),[2]):((r=JSON.parse(o)).timeRefreshed=Date.now(),i.setItemSync(e,JSON.stringify(r)),n.default().unlock(t),this.refreshLockWhileAcquired(e,t),[2])):(n.default().unlock(t),[2])}}))}))}),1e3),[2]}))}))},e.prototype.waitForSomethingToChange=function(t){return o(this,void 0,void 0,(function(){return a(this,(function(i){switch(i.label){case 0:return[4,new Promise((function(i){var o=!1,n=Date.now(),a=!1;function r(){if(a||(window.removeEventListener(\"storage\",r),e.removeFromWaiting(r),clearTimeout(s),a=!0),!o){o=!0;var t=50-(Date.now()-n);t>0?setTimeout(i,t):i(null)}}window.addEventListener(\"storage\",r),e.addToWaiting(r);var s=setTimeout(r,Math.max(0,t-Date.now()))}))];case 1:return i.sent(),[2]}}))}))},e.addToWaiting=function(t){this.removeFromWaiting(t),void 0!==e.waiters&&e.waiters.push(t)},e.removeFromWaiting=function(t){void 0!==e.waiters&&(e.waiters=e.waiters.filter((function(e){return e!==t})))},e.notifyWaiters=function(){void 0!==e.waiters&&e.waiters.slice().forEach((function(e){return e()}))},e.prototype.releaseLock=function(e){return o(this,void 0,void 0,(function(){return a(this,(function(t){switch(t.label){case 0:return[4,this.releaseLock__private__(e)];case 1:return[2,t.sent()]}}))}))},e.prototype.releaseLock__private__=function(t){return o(this,void 0,void 0,(function(){var i,o,r,d;return a(this,(function(a){switch(a.label){case 0:return i=void 0===this.storageHandler?c:this.storageHandler,o=s+\"-\"+t,null===(r=i.getItemSync(o))?[2]:(d=JSON.parse(r)).id!==this.id?[3,2]:[4,n.default().lock(d.iat)];case 1:a.sent(),this.acquiredIatSet.delete(d.iat),i.removeItemSync(o),n.default().unlock(d.iat),e.notifyWaiters(),a.label=2;case 2:return[2]}}))}))},e.lockCorrector=function(t){for(var i=Date.now()-5e3,o=t,n=[],a=0;;){var r=o.keySync(a);if(null===r)break;n.push(r),a++}for(var c=!1,d=0;d<n.length;d++){var u=n[d];if(u.includes(s)){var l=o.getItemSync(u);if(null!==l){var h=JSON.parse(l);(void 0===h.timeRefreshed&&h.timeAcquired<i||void 0!==h.timeRefreshed&&h.timeRefreshed<i)&&(o.removeItemSync(u),c=!0)}}}c&&e.notifyWaiters()},e.waiters=void 0,e}();i.default=l})));const r={timeoutInSeconds:60},s={name:\"auth0-spa-js\",version:\"2.1.3\"},c=()=>Date.now();class d extends Error{constructor(e,t){super(t),this.error=e,this.error_description=t,Object.setPrototypeOf(this,d.prototype)}static fromPayload({error:e,error_description:t}){return new d(e,t)}}class u extends d{constructor(e,t,i,o=null){super(e,t),this.state=i,this.appState=o,Object.setPrototypeOf(this,u.prototype)}}class l extends d{constructor(){super(\"timeout\",\"Timeout\"),Object.setPrototypeOf(this,l.prototype)}}class h extends l{constructor(e){super(),this.popup=e,Object.setPrototypeOf(this,h.prototype)}}class p extends d{constructor(e){super(\"cancelled\",\"Popup closed\"),this.popup=e,Object.setPrototypeOf(this,p.prototype)}}class m extends d{constructor(e,t,i){super(e,t),this.mfa_token=i,Object.setPrototypeOf(this,m.prototype)}}class f extends d{constructor(e,t){super(\"missing_refresh_token\",`Missing Refresh Token (audience: '${g(e,[\"default\"])}', scope: '${g(t)}')`),this.audience=e,this.scope=t,Object.setPrototypeOf(this,f.prototype)}}function g(e,t=[]){return e&&!t.includes(e)?e:\"\"}const w=()=>window.crypto,y=()=>{const e=\"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.\";let t=\"\";return Array.from(w().getRandomValues(new Uint8Array(43))).forEach((i=>t+=e[i%e.length])),t},k=e=>btoa(e),v=t=>{var{clientId:i}=t,o=e(t,[\"clientId\"]);return new URLSearchParams((e=>Object.keys(e).filter((t=>void 0!==e[t])).reduce(((t,i)=>Object.assign(Object.assign({},t),{[i]:e[i]})),{}))(Object.assign({client_id:i},o))).toString()},b=e=>(e=>decodeURIComponent(atob(e).split(\"\").map((e=>\"%\"+(\"00\"+e.charCodeAt(0).toString(16)).slice(-2))).join(\"\")))(e.replace(/_/g,\"/\").replace(/-/g,\"+\")),_=async(e,t)=>{const i=await fetch(e,t);return{ok:i.ok,json:await i.json()}},I=async(e,t,i)=>{const o=new AbortController;let n;return t.signal=o.signal,Promise.race([_(e,t),new Promise(((e,t)=>{n=setTimeout((()=>{o.abort(),t(new Error(\"Timeout when executing 'fetch'\"))}),i)}))]).finally((()=>{clearTimeout(n)}))},S=async(e,t,i,o,n,a,r)=>{return s={auth:{audience:t,scope:i},timeout:n,fetchUrl:e,fetchOptions:o,useFormData:r},c=a,new Promise((function(e,t){const i=new MessageChannel;i.port1.onmessage=function(o){o.data.error?t(new Error(o.data.error)):e(o.data),i.port1.close()},c.postMessage(s,[i.port2])}));var s,c},O=async(e,t,i,o,n,a,r=1e4)=>n?S(e,t,i,o,r,n,a):I(e,o,r);async function T(t,i){var{baseUrl:o,timeout:n,audience:a,scope:r,auth0Client:c,useFormData:u}=t,l=e(t,[\"baseUrl\",\"timeout\",\"audience\",\"scope\",\"auth0Client\",\"useFormData\"]);const h=u?v(l):JSON.stringify(l);return await async function(t,i,o,n,a,r,s){let c,u=null;for(let e=0;e<3;e++)try{c=await O(t,o,n,a,r,s,i),u=null;break}catch(e){u=e}if(u)throw u;const l=c.json,{error:h,error_description:p}=l,g=e(l,[\"error\",\"error_description\"]),{ok:w}=c;if(!w){const e=p||`HTTP error. Unable to fetch ${t}`;if(\"mfa_required\"===h)throw new m(h,e,g.mfa_token);if(\"missing_refresh_token\"===h)throw new f(o,n);throw new d(h||\"request_error\",e)}return g}(`${o}/oauth/token`,n,a||\"default\",r,{method:\"POST\",body:h,headers:{\"Content-Type\":u?\"application/x-www-form-urlencoded\":\"application/json\",\"Auth0-Client\":btoa(JSON.stringify(c||s))}},i,u)}const j=(...e)=>{return(t=e.filter(Boolean).join(\" \").trim().split(/\\s+/),Array.from(new Set(t))).join(\" \");var t};class C{constructor(e,t=\"@@auth0spajs@@\",i){this.prefix=t,this.suffix=i,this.clientId=e.clientId,this.scope=e.scope,this.audience=e.audience}toKey(){return[this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join(\"::\")}static fromKey(e){const[t,i,o,n]=e.split(\"::\");return new C({clientId:i,scope:n,audience:o},t)}static fromCacheEntry(e){const{scope:t,audience:i,client_id:o}=e;return new C({scope:t,audience:i,clientId:o})}}class z{set(e,t){localStorage.setItem(e,JSON.stringify(t))}get(e){const t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return}}remove(e){localStorage.removeItem(e)}allKeys(){return Object.keys(window.localStorage).filter((e=>e.startsWith(\"@@auth0spajs@@\")))}}class P{constructor(){this.enclosedCache=function(){let e={};return{set(t,i){e[t]=i},get(t){const i=e[t];if(i)return i},remove(t){delete e[t]},allKeys:()=>Object.keys(e)}}()}}class x{constructor(e,t,i){this.cache=e,this.keyManifest=t,this.nowProvider=i||c}async setIdToken(e,t,i){var o;const n=this.getIdTokenCacheKey(e);await this.cache.set(n,{id_token:t,decodedToken:i}),await(null===(o=this.keyManifest)||void 0===o?void 0:o.add(n))}async getIdToken(e){const t=await this.cache.get(this.getIdTokenCacheKey(e.clientId));if(!t&&e.scope&&e.audience){const t=await this.get(e);if(!t)return;if(!t.id_token||!t.decodedToken)return;return{id_token:t.id_token,decodedToken:t.decodedToken}}if(t)return{id_token:t.id_token,decodedToken:t.decodedToken}}async get(e,t=0){var i;let o=await this.cache.get(e.toKey());if(!o){const t=await this.getCacheKeys();if(!t)return;const i=this.matchExistingCacheKey(e,t);i&&(o=await this.cache.get(i))}if(!o)return;const n=await this.nowProvider(),a=Math.floor(n/1e3);return o.expiresAt-t<a?o.body.refresh_token?(o.body={refresh_token:o.body.refresh_token},await this.cache.set(e.toKey(),o),o.body):(await this.cache.remove(e.toKey()),void await(null===(i=this.keyManifest)||void 0===i?void 0:i.remove(e.toKey()))):o.body}async set(e){var t;const i=new C({clientId:e.client_id,scope:e.scope,audience:e.audience}),o=await this.wrapCacheEntry(e);await this.cache.set(i.toKey(),o),await(null===(t=this.keyManifest)||void 0===t?void 0:t.add(i.toKey()))}async clear(e){var t;const i=await this.getCacheKeys();i&&(await i.filter((t=>!e||t.includes(e))).reduce((async(e,t)=>{await e,await this.cache.remove(t)}),Promise.resolve()),await(null===(t=this.keyManifest)||void 0===t?void 0:t.clear()))}async wrapCacheEntry(e){const t=await this.nowProvider();return{body:e,expiresAt:Math.floor(t/1e3)+e.expires_in}}async getCacheKeys(){var e;return this.keyManifest?null===(e=await this.keyManifest.get())||void 0===e?void 0:e.keys:this.cache.allKeys?this.cache.allKeys():void 0}getIdTokenCacheKey(e){return new C({clientId:e},\"@@auth0spajs@@\",\"@@user@@\").toKey()}matchExistingCacheKey(e,t){return t.filter((t=>{var i;const o=C.fromKey(t),n=new Set(o.scope&&o.scope.split(\" \")),a=(null===(i=e.scope)||void 0===i?void 0:i.split(\" \"))||[],r=o.scope&&a.reduce(((e,t)=>e&&n.has(t)),!0);return\"@@auth0spajs@@\"===o.prefix&&o.clientId===e.clientId&&o.audience===e.audience&&r}))[0]}}class Z{constructor(e,t,i){this.storage=e,this.clientId=t,this.cookieDomain=i,this.storageKey=`a0.spajs.txs.${this.clientId}`}create(e){this.storage.save(this.storageKey,e,{daysUntilExpire:1,cookieDomain:this.cookieDomain})}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain})}}const K=e=>\"number\"==typeof e,W=[\"iss\",\"aud\",\"exp\",\"nbf\",\"iat\",\"jti\",\"azp\",\"nonce\",\"auth_time\",\"at_hash\",\"c_hash\",\"acr\",\"amr\",\"sub_jwk\",\"cnf\",\"sip_from_tag\",\"sip_date\",\"sip_callid\",\"sip_cseq_num\",\"sip_via_branch\",\"orig\",\"dest\",\"mky\",\"events\",\"toe\",\"txn\",\"rph\",\"sid\",\"vot\",\"vtm\"],E=e=>{if(!e.id_token)throw new Error(\"ID token is required but missing\");const t=(e=>{const t=e.split(\".\"),[i,o,n]=t;if(3!==t.length||!i||!o||!n)throw new Error(\"ID token could not be decoded\");const a=JSON.parse(b(o)),r={__raw:e},s={};return Object.keys(a).forEach((e=>{r[e]=a[e],W.includes(e)||(s[e]=a[e])})),{encoded:{header:i,payload:o,signature:n},header:JSON.parse(b(i)),claims:r,user:s}})(e.id_token);if(!t.claims.iss)throw new Error(\"Issuer (iss) claim must be a string present in the ID token\");if(t.claims.iss!==e.iss)throw new Error(`Issuer (iss) claim mismatch in the ID token; expected \"${e.iss}\", found \"${t.claims.iss}\"`);if(!t.user.sub)throw new Error(\"Subject (sub) claim must be a string present in the ID token\");if(\"RS256\"!==t.header.alg)throw new Error(`Signature algorithm of \"${t.header.alg}\" is not supported. Expected the ID token to be signed with \"RS256\".`);if(!t.claims.aud||\"string\"!=typeof t.claims.aud&&!Array.isArray(t.claims.aud))throw new Error(\"Audience (aud) claim must be a string or array of strings present in the ID token\");if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e.aud))throw new Error(`Audience (aud) claim mismatch in the ID token; expected \"${e.aud}\" but was not one of \"${t.claims.aud.join(\", \")}\"`);if(t.claims.aud.length>1){if(!t.claims.azp)throw new Error(\"Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values\");if(t.claims.azp!==e.aud)throw new Error(`Authorized Party (azp) claim mismatch in the ID token; expected \"${e.aud}\", found \"${t.claims.azp}\"`)}}else if(t.claims.aud!==e.aud)throw new Error(`Audience (aud) claim mismatch in the ID token; expected \"${e.aud}\" but found \"${t.claims.aud}\"`);if(e.nonce){if(!t.claims.nonce)throw new Error(\"Nonce (nonce) claim must be a string present in the ID token\");if(t.claims.nonce!==e.nonce)throw new Error(`Nonce (nonce) claim mismatch in the ID token; expected \"${e.nonce}\", found \"${t.claims.nonce}\"`)}if(e.max_age&&!K(t.claims.auth_time))throw new Error(\"Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified\");if(null==t.claims.exp||!K(t.claims.exp))throw new Error(\"Expiration Time (exp) claim must be a number present in the ID token\");if(!K(t.claims.iat))throw new Error(\"Issued At (iat) claim must be a number present in the ID token\");const i=e.leeway||60,o=new Date(e.now||Date.now()),n=new Date(0);if(n.setUTCSeconds(t.claims.exp+i),o>n)throw new Error(`Expiration Time (exp) claim error in the ID token; current time (${o}) is after expiration time (${n})`);if(null!=t.claims.nbf&&K(t.claims.nbf)){const e=new Date(0);if(e.setUTCSeconds(t.claims.nbf-i),o<e)throw new Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${o}) is before ${e}`)}if(null!=t.claims.auth_time&&K(t.claims.auth_time)){const n=new Date(0);if(n.setUTCSeconds(parseInt(t.claims.auth_time)+e.max_age+i),o>n)throw new Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${o}) is after last auth at ${n}`)}if(e.organization){const i=e.organization.trim();if(i.startsWith(\"org_\")){const e=i;if(!t.claims.org_id)throw new Error(\"Organization ID (org_id) claim must be a string present in the ID token\");if(e!==t.claims.org_id)throw new Error(`Organization ID (org_id) claim mismatch in the ID token; expected \"${e}\", found \"${t.claims.org_id}\"`)}else{const e=i.toLowerCase();if(!t.claims.org_name)throw new Error(\"Organization Name (org_name) claim must be a string present in the ID token\");if(e!==t.claims.org_name)throw new Error(`Organization Name (org_name) claim mismatch in the ID token; expected \"${e}\", found \"${t.claims.org_name}\"`)}}return t};var R=o((function(e,i){var o=t&&t.__assign||function(){return o=Object.assign||function(e){for(var t,i=1,o=arguments.length;i<o;i++)for(var n in t=arguments[i])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},o.apply(this,arguments)};function n(e,t){if(!t)return\"\";var i=\"; \"+e;return!0===t?i:i+\"=\"+t}function a(e,t,i){return encodeURIComponent(e).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\\(/g,\"%28\").replace(/\\)/g,\"%29\")+\"=\"+encodeURIComponent(t).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(e){if(\"number\"==typeof e.expires){var t=new Date;t.setMilliseconds(t.getMilliseconds()+864e5*e.expires),e.expires=t}return n(\"Expires\",e.expires?e.expires.toUTCString():\"\")+n(\"Domain\",e.domain)+n(\"Path\",e.path)+n(\"Secure\",e.secure)+n(\"SameSite\",e.sameSite)}(i)}function r(e){for(var t={},i=e?e.split(\"; \"):[],o=/(%[\\dA-F]{2})+/gi,n=0;n<i.length;n++){var a=i[n].split(\"=\"),r=a.slice(1).join(\"=\");'\"'===r.charAt(0)&&(r=r.slice(1,-1));try{t[a[0].replace(o,decodeURIComponent)]=r.replace(o,decodeURIComponent)}catch(e){}}return t}function s(){return r(document.cookie)}function c(e,t,i){document.cookie=a(e,t,o({path:\"/\"},i))}i.__esModule=!0,i.encode=a,i.parse=r,i.getAll=s,i.get=function(e){return s()[e]},i.set=c,i.remove=function(e,t){c(e,\"\",o(o({},t),{expires:-1}))}}));i(R),R.encode,R.parse,R.getAll;var U=R.get,L=R.set,D=R.remove;const X={get(e){const t=U(e);if(void 0!==t)return JSON.parse(t)},save(e,t,i){let o={};\"https:\"===window.location.protocol&&(o={secure:!0,sameSite:\"none\"}),(null==i?void 0:i.daysUntilExpire)&&(o.expires=i.daysUntilExpire),(null==i?void 0:i.cookieDomain)&&(o.domain=i.cookieDomain),L(e,JSON.stringify(t),o)},remove(e,t){let i={};(null==t?void 0:t.cookieDomain)&&(i.domain=t.cookieDomain),D(e,i)}},N={get(e){const t=X.get(e);return t||X.get(`_legacy_${e}`)},save(e,t,i){let o={};\"https:\"===window.location.protocol&&(o={secure:!0}),(null==i?void 0:i.daysUntilExpire)&&(o.expires=i.daysUntilExpire),(null==i?void 0:i.cookieDomain)&&(o.domain=i.cookieDomain),L(`_legacy_${e}`,JSON.stringify(t),o),X.save(e,t,i)},remove(e,t){let i={};(null==t?void 0:t.cookieDomain)&&(i.domain=t.cookieDomain),D(e,i),X.remove(e,t),X.remove(`_legacy_${e}`,t)}},J={get(e){if(\"undefined\"==typeof sessionStorage)return;const t=sessionStorage.getItem(e);return null!=t?JSON.parse(t):void 0},save(e,t){sessionStorage.setItem(e,JSON.stringify(t))},remove(e){sessionStorage.removeItem(e)}};function F(e,t,i){var o=void 0===t?null:t,n=function(e,t){var i=atob(e);if(t){for(var o=new Uint8Array(i.length),n=0,a=i.length;n<a;++n)o[n]=i.charCodeAt(n);return String.fromCharCode.apply(null,new Uint16Array(o.buffer))}return i}(e,void 0!==i&&i),a=n.indexOf(\"\\n\",10)+1,r=n.substring(a)+(o?\"//# sourceMappingURL=\"+o:\"\"),s=new Blob([r],{type:\"application/javascript\"});return URL.createObjectURL(s)}var H,Y,G,V,M=(H=\"Lyogcm9sbHVwLXBsdWdpbi13ZWItd29ya2VyLWxvYWRlciAqLwohZnVuY3Rpb24oKXsidXNlIHN0cmljdCI7Y2xhc3MgZSBleHRlbmRzIEVycm9ye2NvbnN0cnVjdG9yKHQscil7c3VwZXIociksdGhpcy5lcnJvcj10LHRoaXMuZXJyb3JfZGVzY3JpcHRpb249cixPYmplY3Quc2V0UHJvdG90eXBlT2YodGhpcyxlLnByb3RvdHlwZSl9c3RhdGljIGZyb21QYXlsb2FkKHtlcnJvcjp0LGVycm9yX2Rlc2NyaXB0aW9uOnJ9KXtyZXR1cm4gbmV3IGUodCxyKX19Y2xhc3MgdCBleHRlbmRzIGV7Y29uc3RydWN0b3IoZSxzKXtzdXBlcigibWlzc2luZ19yZWZyZXNoX3Rva2VuIixgTWlzc2luZyBSZWZyZXNoIFRva2VuIChhdWRpZW5jZTogJyR7cihlLFsiZGVmYXVsdCJdKX0nLCBzY29wZTogJyR7cihzKX0nKWApLHRoaXMuYXVkaWVuY2U9ZSx0aGlzLnNjb3BlPXMsT2JqZWN0LnNldFByb3RvdHlwZU9mKHRoaXMsdC5wcm90b3R5cGUpfX1mdW5jdGlvbiByKGUsdD1bXSl7cmV0dXJuIGUmJiF0LmluY2x1ZGVzKGUpP2U6IiJ9ImZ1bmN0aW9uIj09dHlwZW9mIFN1cHByZXNzZWRFcnJvciYmU3VwcHJlc3NlZEVycm9yO2NvbnN0IHM9ZT0+e3ZhcntjbGllbnRJZDp0fT1lLHI9ZnVuY3Rpb24oZSx0KXt2YXIgcj17fTtmb3IodmFyIHMgaW4gZSlPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZSxzKSYmdC5pbmRleE9mKHMpPDAmJihyW3NdPWVbc10pO2lmKG51bGwhPWUmJiJmdW5jdGlvbiI9PXR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzKXt2YXIgbz0wO2ZvcihzPU9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMoZSk7bzxzLmxlbmd0aDtvKyspdC5pbmRleE9mKHNbb10pPDAmJk9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChlLHNbb10pJiYocltzW29dXT1lW3Nbb11dKX1yZXR1cm4gcn0oZSxbImNsaWVudElkIl0pO3JldHVybiBuZXcgVVJMU2VhcmNoUGFyYW1zKChlPT5PYmplY3Qua2V5cyhlKS5maWx0ZXIoKHQ9PnZvaWQgMCE9PWVbdF0pKS5yZWR1Y2UoKCh0LHIpPT5PYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sdCkse1tyXTplW3JdfSkpLHt9KSkoT2JqZWN0LmFzc2lnbih7Y2xpZW50X2lkOnR9LHIpKSkudG9TdHJpbmcoKX07bGV0IG89e307Y29uc3Qgbj0oZSx0KT0+YCR7ZX18JHt0fWA7YWRkRXZlbnRMaXN0ZW5lcigibWVzc2FnZSIsKGFzeW5jKHtkYXRhOnt0aW1lb3V0OmUsYXV0aDpyLGZldGNoVXJsOmksZmV0Y2hPcHRpb25zOmMsdXNlRm9ybURhdGE6YX0scG9ydHM6W3BdfSk9PntsZXQgZjtjb25zdHthdWRpZW5jZTp1LHNjb3BlOmx9PXJ8fHt9O3RyeXtjb25zdCByPWE/KGU9Pntjb25zdCB0PW5ldyBVUkxTZWFyY2hQYXJhbXMoZSkscj17fTtyZXR1cm4gdC5mb3JFYWNoKCgoZSx0KT0+e3JbdF09ZX0pKSxyfSkoYy5ib2R5KTpKU09OLnBhcnNlKGMuYm9keSk7aWYoIXIucmVmcmVzaF90b2tlbiYmInJlZnJlc2hfdG9rZW4iPT09ci5ncmFudF90eXBlKXtjb25zdCBlPSgoZSx0KT0+b1tuKGUsdCldKSh1LGwpO2lmKCFlKXRocm93IG5ldyB0KHUsbCk7Yy5ib2R5PWE/cyhPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30scikse3JlZnJlc2hfdG9rZW46ZX0pKTpKU09OLnN0cmluZ2lmeShPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30scikse3JlZnJlc2hfdG9rZW46ZX0pKX1sZXQgaCxnOyJmdW5jdGlvbiI9PXR5cGVvZiBBYm9ydENvbnRyb2xsZXImJihoPW5ldyBBYm9ydENvbnRyb2xsZXIsYy5zaWduYWw9aC5zaWduYWwpO3RyeXtnPWF3YWl0IFByb21pc2UucmFjZShbKGQ9ZSxuZXcgUHJvbWlzZSgoZT0+c2V0VGltZW91dChlLGQpKSkpLGZldGNoKGksT2JqZWN0LmFzc2lnbih7fSxjKSldKX1jYXRjaChlKXtyZXR1cm4gdm9pZCBwLnBvc3RNZXNzYWdlKHtlcnJvcjplLm1lc3NhZ2V9KX1pZighZylyZXR1cm4gaCYmaC5hYm9ydCgpLHZvaWQgcC5wb3N0TWVzc2FnZSh7ZXJyb3I6IlRpbWVvdXQgd2hlbiBleGVjdXRpbmcgJ2ZldGNoJyJ9KTtmPWF3YWl0IGcuanNvbigpLGYucmVmcmVzaF90b2tlbj8oKChlLHQscik9PntvW24odCxyKV09ZX0pKGYucmVmcmVzaF90b2tlbix1LGwpLGRlbGV0ZSBmLnJlZnJlc2hfdG9rZW4pOigoZSx0KT0+e2RlbGV0ZSBvW24oZSx0KV19KSh1LGwpLHAucG9zdE1lc3NhZ2Uoe29rOmcub2ssanNvbjpmfSl9Y2F0Y2goZSl7cC5wb3N0TWVzc2FnZSh7b2s6ITEsanNvbjp7ZXJyb3I6ZS5lcnJvcixlcnJvcl9kZXNjcmlwdGlvbjplLm1lc3NhZ2V9fSl9dmFyIGR9KSl9KCk7Cgo=\",Y=null,G=!1,function(e){return V=V||F(H,Y,G),new Worker(V,e)});const A={};class B{constructor(e,t){this.cache=e,this.clientId=t,this.manifestKey=this.createManifestKeyFrom(this.clientId)}async add(e){var t;const i=new Set((null===(t=await this.cache.get(this.manifestKey))||void 0===t?void 0:t.keys)||[]);i.add(e),await this.cache.set(this.manifestKey,{keys:[...i]})}async remove(e){const t=await this.cache.get(this.manifestKey);if(t){const i=new Set(t.keys);return i.delete(e),i.size>0?await this.cache.set(this.manifestKey,{keys:[...i]}):await this.cache.remove(this.manifestKey)}}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(e){return`@@auth0spajs@@::${e}`}}const $={memory:()=>(new P).enclosedCache,localstorage:()=>new z},q=e=>$[e],Q=t=>{const{openUrl:i,onRedirect:o}=t,n=e(t,[\"openUrl\",\"onRedirect\"]);return Object.assign(Object.assign({},n),{openUrl:!1===i||i?i:o})},ee=new a;class te{constructor(e){let t,i;if(this.userCache=(new P).enclosedCache,this.defaultOptions={authorizationParams:{scope:\"openid profile email\"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=async()=>{await ee.releaseLock(\"auth0.lock.getTokenSilently\"),window.removeEventListener(\"pagehide\",this._releaseLockOnPageHide)},this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),e),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),e.authorizationParams)}),\"undefined\"!=typeof window&&(()=>{if(!w())throw new Error(\"For security reasons, `window.crypto` is required to run `auth0-spa-js`.\");if(void 0===w().subtle)throw new Error(\"\\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\\n    \")})(),e.cache&&e.cacheLocation&&console.warn(\"Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`.\"),e.cache)i=e.cache;else{if(t=e.cacheLocation||\"memory\",!q(t))throw new Error(`Invalid cache location \"${t}\"`);i=q(t)()}this.httpTimeoutMs=e.httpTimeoutInSeconds?1e3*e.httpTimeoutInSeconds:1e4,this.cookieStorage=!1===e.legacySameSiteCookie?X:N,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(e=>`auth0.${e}.is.authenticated`)(this.options.clientId),this.sessionCheckExpiryDays=e.sessionCheckExpiryDays||1;const o=e.useCookiesForTransactions?this.cookieStorage:J;var n;this.scope=j(\"openid\",this.options.authorizationParams.scope,this.options.useRefreshTokens?\"offline_access\":\"\"),this.transactionManager=new Z(o,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||c,this.cacheManager=new x(i,i.allKeys?void 0:new B(i,this.options.clientId),this.nowProvider),this.domainUrl=(n=this.options.domain,/^https?:\\/\\//.test(n)?n:`https://${n}`),this.tokenIssuer=((e,t)=>e?e.startsWith(\"https://\")?e:`https://${e}/`:`${t}/`)(this.options.issuer,this.domainUrl),\"undefined\"!=typeof window&&window.Worker&&this.options.useRefreshTokens&&\"memory\"===t&&(this.options.workerUrl?this.worker=new Worker(this.options.workerUrl):this.worker=new M)}_url(e){const t=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||s)));return`${this.domainUrl}${e}&auth0Client=${t}`}_authorizeUrl(e){return this._url(`/authorize?${v(e)}`)}async _verifyIdToken(e,t,i){const o=await this.nowProvider();return E({iss:this.tokenIssuer,aud:this.options.clientId,id_token:e,nonce:t,organization:i,leeway:this.options.leeway,max_age:(n=this.options.authorizationParams.max_age,\"string\"!=typeof n?n:parseInt(n,10)||void 0),now:o});var n}_processOrgHint(e){e?this.cookieStorage.save(this.orgHintCookieName,e,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain})}async _prepareAuthorizeUrl(e,t,i){const o=k(y()),n=k(y()),a=y(),r=(e=>{const t=new Uint8Array(e);return(e=>{const t={\"+\":\"-\",\"/\":\"_\",\"=\":\"\"};return e.replace(/[+/=]/g,(e=>t[e]))})(window.btoa(String.fromCharCode(...Array.from(t))))})(await(async e=>{const t=w().subtle.digest({name:\"SHA-256\"},(new TextEncoder).encode(e));return await t})(a)),s=((e,t,i,o,n,a,r,s)=>Object.assign(Object.assign(Object.assign({client_id:e.clientId},e.authorizationParams),i),{scope:j(t,i.scope),response_type:\"code\",response_mode:s||\"query\",state:o,nonce:n,redirect_uri:r||e.authorizationParams.redirect_uri,code_challenge:a,code_challenge_method:\"S256\"}))(this.options,this.scope,e,o,n,r,e.redirect_uri||this.options.authorizationParams.redirect_uri||i,null==t?void 0:t.response_mode),c=this._authorizeUrl(s);return{nonce:n,code_verifier:a,scope:s.scope,audience:s.audience||\"default\",redirect_uri:s.redirect_uri,state:o,url:c}}async loginWithPopup(e,t){var i;if(e=e||{},!(t=t||{}).popup&&(t.popup=(e=>{const t=window.screenX+(window.innerWidth-400)/2,i=window.screenY+(window.innerHeight-600)/2;return window.open(e,\"auth0:authorize:popup\",`left=${t},top=${i},width=400,height=600,resizable,scrollbars=yes,status=1`)})(\"\"),!t.popup))throw new Error(\"Unable to open a popup for loginWithPopup - window.open returned `null`\");const o=await this._prepareAuthorizeUrl(e.authorizationParams||{},{response_mode:\"web_message\"},window.location.origin);t.popup.location.href=o.url;const n=await(e=>new Promise(((t,i)=>{let o;const n=setInterval((()=>{e.popup&&e.popup.closed&&(clearInterval(n),clearTimeout(a),window.removeEventListener(\"message\",o,!1),i(new p(e.popup)))}),1e3),a=setTimeout((()=>{clearInterval(n),i(new h(e.popup)),window.removeEventListener(\"message\",o,!1)}),1e3*(e.timeoutInSeconds||60));o=function(r){if(r.data&&\"authorization_response\"===r.data.type){if(clearTimeout(a),clearInterval(n),window.removeEventListener(\"message\",o,!1),e.popup.close(),r.data.response.error)return i(d.fromPayload(r.data.response));t(r.data.response)}},window.addEventListener(\"message\",o)})))(Object.assign(Object.assign({},t),{timeoutInSeconds:t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}));if(o.state!==n.state)throw new d(\"state_mismatch\",\"Invalid state\");const a=(null===(i=e.authorizationParams)||void 0===i?void 0:i.organization)||this.options.authorizationParams.organization;await this._requestToken({audience:o.audience,scope:o.scope,code_verifier:o.code_verifier,grant_type:\"authorization_code\",code:n.code,redirect_uri:o.redirect_uri},{nonceIn:o.nonce,organization:a})}async getUser(){var e;const t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.user}async getIdTokenClaims(){var e;const t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.claims}async loginWithRedirect(t={}){var i;const o=Q(t),{openUrl:n,fragment:a,appState:r}=o,s=e(o,[\"openUrl\",\"fragment\",\"appState\"]),c=(null===(i=s.authorizationParams)||void 0===i?void 0:i.organization)||this.options.authorizationParams.organization,d=await this._prepareAuthorizeUrl(s.authorizationParams||{}),{url:u}=d,l=e(d,[\"url\"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},l),{appState:r}),c&&{organization:c}));const h=a?`${u}#${a}`:u;n?await n(h):window.location.assign(h)}async handleRedirectCallback(e=window.location.href){const t=e.split(\"?\").slice(1);if(0===t.length)throw new Error(\"There are no query params available for parsing.\");const{state:i,code:o,error:n,error_description:a}=(e=>{e.indexOf(\"#\")>-1&&(e=e.substring(0,e.indexOf(\"#\")));const t=new URLSearchParams(e);return{state:t.get(\"state\"),code:t.get(\"code\")||void 0,error:t.get(\"error\")||void 0,error_description:t.get(\"error_description\")||void 0}})(t.join(\"\")),r=this.transactionManager.get();if(!r)throw new d(\"missing_transaction\",\"Invalid state\");if(this.transactionManager.remove(),n)throw new u(n,a||n,i,r.appState);if(!r.code_verifier||r.state&&r.state!==i)throw new d(\"state_mismatch\",\"Invalid state\");const s=r.organization,c=r.nonce,l=r.redirect_uri;return await this._requestToken(Object.assign({audience:r.audience,scope:r.scope,code_verifier:r.code_verifier,grant_type:\"authorization_code\",code:o},l?{redirect_uri:l}:{}),{nonceIn:c,organization:s}),{appState:r.appState}}async checkSession(e){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get(\"auth0.is.authenticated\"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(\"auth0.is.authenticated\")}try{await this.getTokenSilently(e)}catch(e){}}async getTokenSilently(e={}){var t;const i=Object.assign(Object.assign({cacheMode:\"on\"},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:j(this.scope,null===(t=e.authorizationParams)||void 0===t?void 0:t.scope)})}),o=await((e,t)=>{let i=A[t];return i||(i=e().finally((()=>{delete A[t],i=null})),A[t]=i),i})((()=>this._getTokenSilently(i)),`${this.options.clientId}::${i.authorizationParams.audience}::${i.authorizationParams.scope}`);return e.detailedResponse?o:null==o?void 0:o.access_token}async _getTokenSilently(t){const{cacheMode:i}=t,o=e(t,[\"cacheMode\"]);if(\"off\"!==i){const e=await this._getEntryFromCache({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||\"default\",clientId:this.options.clientId});if(e)return e}if(\"cache-only\"!==i){if(!await(async(e,t=3)=>{for(let i=0;i<t;i++)if(await e())return!0;return!1})((()=>ee.acquireLock(\"auth0.lock.getTokenSilently\",5e3)),10))throw new l;try{if(window.addEventListener(\"pagehide\",this._releaseLockOnPageHide),\"off\"!==i){const e=await this._getEntryFromCache({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||\"default\",clientId:this.options.clientId});if(e)return e}const e=this.options.useRefreshTokens?await this._getTokenUsingRefreshToken(o):await this._getTokenFromIFrame(o),{id_token:t,access_token:n,oauthTokenScope:a,expires_in:r}=e;return Object.assign(Object.assign({id_token:t,access_token:n},a?{scope:a}:null),{expires_in:r})}finally{await ee.releaseLock(\"auth0.lock.getTokenSilently\"),window.removeEventListener(\"pagehide\",this._releaseLockOnPageHide)}}}async getTokenWithPopup(e={},t={}){var i;const o=Object.assign(Object.assign({},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:j(this.scope,null===(i=e.authorizationParams)||void 0===i?void 0:i.scope)})});t=Object.assign(Object.assign({},r),t),await this.loginWithPopup(o,t);return(await this.cacheManager.get(new C({scope:o.authorizationParams.scope,audience:o.authorizationParams.audience||\"default\",clientId:this.options.clientId}))).access_token}async isAuthenticated(){return!!await this.getUser()}_buildLogoutUrl(t){null!==t.clientId?t.clientId=t.clientId||this.options.clientId:delete t.clientId;const i=t.logoutParams||{},{federated:o}=i,n=e(i,[\"federated\"]),a=o?\"&federated\":\"\";return this._url(`/v2/logout?${v(Object.assign({clientId:t.clientId},n))}`)+a}async logout(t={}){const i=Q(t),{openUrl:o}=i,n=e(i,[\"openUrl\"]);null===t.clientId?await this.cacheManager.clear():await this.cacheManager.clear(t.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove(\"@@user@@\");const a=this._buildLogoutUrl(n);o?await o(a):!1!==o&&window.location.assign(a)}async _getTokenFromIFrame(e){const t=Object.assign(Object.assign({},e.authorizationParams),{prompt:\"none\"}),i=this.cookieStorage.get(this.orgHintCookieName);i&&!t.organization&&(t.organization=i);const{url:o,state:n,nonce:a,code_verifier:r,redirect_uri:s,scope:c,audience:u}=await this._prepareAuthorizeUrl(t,{response_mode:\"web_message\"},window.location.origin);try{if(window.crossOriginIsolated)throw new d(\"login_required\",\"The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.\");const i=e.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,h=await((e,t,i=60)=>new Promise(((o,n)=>{const a=window.document.createElement(\"iframe\");a.setAttribute(\"width\",\"0\"),a.setAttribute(\"height\",\"0\"),a.style.display=\"none\";const r=()=>{window.document.body.contains(a)&&(window.document.body.removeChild(a),window.removeEventListener(\"message\",s,!1))};let s;const c=setTimeout((()=>{n(new l),r()}),1e3*i);s=function(e){if(e.origin!=t)return;if(!e.data||\"authorization_response\"!==e.data.type)return;const i=e.source;i&&i.close(),e.data.response.error?n(d.fromPayload(e.data.response)):o(e.data.response),clearTimeout(c),window.removeEventListener(\"message\",s,!1),setTimeout(r,2e3)},window.addEventListener(\"message\",s,!1),window.document.body.appendChild(a),a.setAttribute(\"src\",e)})))(o,this.domainUrl,i);if(n!==h.state)throw new d(\"state_mismatch\",\"Invalid state\");const p=await this._requestToken(Object.assign(Object.assign({},e.authorizationParams),{code_verifier:r,code:h.code,grant_type:\"authorization_code\",redirect_uri:s,timeout:e.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:a,organization:t.organization});return Object.assign(Object.assign({},p),{scope:c,oauthTokenScope:p.scope,audience:u})}catch(e){throw\"login_required\"===e.error&&this.logout({openUrl:!1}),e}}async _getTokenUsingRefreshToken(e){const t=await this.cacheManager.get(new C({scope:e.authorizationParams.scope,audience:e.authorizationParams.audience||\"default\",clientId:this.options.clientId}));if(!(t&&t.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw new f(e.authorizationParams.audience||\"default\",e.authorizationParams.scope)}const i=e.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,o=\"number\"==typeof e.timeoutInSeconds?1e3*e.timeoutInSeconds:null;try{const n=await this._requestToken(Object.assign(Object.assign(Object.assign({},e.authorizationParams),{grant_type:\"refresh_token\",refresh_token:t&&t.refresh_token,redirect_uri:i}),o&&{timeout:o}));return Object.assign(Object.assign({},n),{scope:e.authorizationParams.scope,oauthTokenScope:n.scope,audience:e.authorizationParams.audience||\"default\"})}catch(t){if((t.message.indexOf(\"Missing Refresh Token\")>-1||t.message&&t.message.indexOf(\"invalid refresh token\")>-1)&&this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw t}}async _saveEntryInCache(t){const{id_token:i,decodedToken:o}=t,n=e(t,[\"id_token\",\"decodedToken\"]);this.userCache.set(\"@@user@@\",{id_token:i,decodedToken:o}),await this.cacheManager.setIdToken(this.options.clientId,t.id_token,t.decodedToken),await this.cacheManager.set(n)}async _getIdTokenFromCache(){const e=this.options.authorizationParams.audience||\"default\",t=await this.cacheManager.getIdToken(new C({clientId:this.options.clientId,audience:e,scope:this.scope})),i=this.userCache.get(\"@@user@@\");return t&&t.id_token===(null==i?void 0:i.id_token)?i:(this.userCache.set(\"@@user@@\",t),t)}async _getEntryFromCache({scope:e,audience:t,clientId:i}){const o=await this.cacheManager.get(new C({scope:e,audience:t,clientId:i}),60);if(o&&o.access_token){const{access_token:e,oauthTokenScope:t,expires_in:i}=o,n=await this._getIdTokenFromCache();return n&&Object.assign(Object.assign({id_token:n.id_token,access_token:e},t?{scope:t}:null),{expires_in:i})}}async _requestToken(e,t){const{nonceIn:i,organization:o}=t||{},n=await T(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},e),this.worker),a=await this._verifyIdToken(n.id_token,i,o);return await this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},n),{decodedToken:a,scope:e.scope,audience:e.audience||\"default\"}),n.scope?{oauthTokenScope:n.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(o||a.claims.org_id),Object.assign(Object.assign({},n),{decodedToken:a})}}class ie{}async function oe(e){const t=new te(e);return await t.checkSession(),t}export{te as Auth0Client,u as AuthenticationError,C as CacheKey,d as GenericError,P as InMemoryCache,z as LocalStorageCache,m as MfaRequiredError,f as MissingRefreshTokenError,p as PopupCancelledError,h as PopupTimeoutError,l as TimeoutError,ie as User,oe as createAuth0Client};\n//# sourceMappingURL=auth0-spa-js.production.esm.js.map\n", "import { User } from '@auth0/auth0-spa-js';\n\n/**\n * The auth state which, when combined with the auth methods, make up the return object of the `useAuth0` hook.\n */\nexport interface AuthState<TUser extends User = User> {\n  error?: Error;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  user?: TUser;\n}\n\n/**\n * The initial auth state.\n */\nexport const initialAuthState: AuthState = {\n  isAuthenticated: false,\n  isLoading: true,\n};\n", "import {\n  GetTokenSilentlyOptions,\n  GetTokenWithPopupOptions,\n  IdToken,\n  LogoutOptions as SPALogoutOptions,\n  PopupLoginOptions,\n  PopupConfigOptions,\n  RedirectLoginResult,\n  User,\n  GetTokenSilentlyVerboseResponse,\n  RedirectLoginOptions as SPARedirectLoginOptions,\n} from '@auth0/auth0-spa-js';\nimport { createContext } from 'react';\nimport { AuthState, initialAuthState } from './auth-state';\nimport { AppState } from './auth0-provider';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface LogoutOptions extends Omit<SPALogoutOptions, 'onRedirect'> {}\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface RedirectLoginOptions<TAppState = AppState>\n  extends Omit<SPARedirectLoginOptions<TAppState>, 'onRedirect'> {}\n\n/**\n * Contains the authenticated state and authentication methods provided by the `useAuth0` hook.\n */\nexport interface Auth0ContextInterface<TUser extends User = User>\n  extends AuthState<TUser> {\n  /**\n   * ```js\n   * const token = await getAccessTokenSilently(options);\n   * ```\n   *\n   * If there's a valid token stored, return it. Otherwise, opens an\n   * iframe with the `/authorize` URL using the parameters provided\n   * as arguments. Random and secure `state` and `nonce` parameters\n   * will be auto-generated. If the response is successful, results\n   * will be valid according to their expiration times.\n   *\n   * If refresh tokens are used, the token endpoint is called directly with the\n   * 'refresh_token' grant. If no refresh token is available to make this call,\n   * the SDK will only fall back to using an iframe to the '/authorize' URL if \n   * the `useRefreshTokensFallback` setting has been set to `true`. By default this\n   * setting is `false`.\n   *\n   * This method may use a web worker to perform the token call if the in-memory\n   * cache is used.\n   *\n   * If an `audience` value is given to this function, the SDK always falls\n   * back to using an iframe to make the token exchange.\n   *\n   * Note that in all cases, falling back to an iframe requires access to\n   * the `auth0` cookie.\n   */\n  getAccessTokenSilently: {\n    (\n      options: GetTokenSilentlyOptions & { detailedResponse: true }\n    ): Promise<GetTokenSilentlyVerboseResponse>;\n    (options?: GetTokenSilentlyOptions): Promise<string>;\n    (options: GetTokenSilentlyOptions): Promise<\n      GetTokenSilentlyVerboseResponse | string\n    >;\n  };\n\n  /**\n   * ```js\n   * const token = await getTokenWithPopup(options, config);\n   * ```\n   *\n   * Get an access token interactively.\n   *\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   */\n  getAccessTokenWithPopup: (\n    options?: GetTokenWithPopupOptions,\n    config?: PopupConfigOptions\n  ) => Promise<string | undefined>;\n\n  /**\n   * ```js\n   * const claims = await getIdTokenClaims();\n   * ```\n   *\n   * Returns all claims from the id_token if available.\n   */\n  getIdTokenClaims: () => Promise<IdToken | undefined>;\n\n  /**\n   * ```js\n   * await loginWithRedirect(options);\n   * ```\n   *\n   * Performs a redirect to `/authorize` using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated.\n   */\n  loginWithRedirect: (\n    options?: RedirectLoginOptions<AppState>\n  ) => Promise<void>;\n\n  /**\n   * ```js\n   * await loginWithPopup(options, config);\n   * ```\n   *\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * IMPORTANT: This method has to be called from an event handler\n   * that was started by the user like a button click, for example,\n   * otherwise the popup will be blocked in most browsers.\n   */\n  loginWithPopup: (\n    options?: PopupLoginOptions,\n    config?: PopupConfigOptions\n  ) => Promise<void>;\n\n  /**\n   * ```js\n   * auth0.logout({ logoutParams: { returnTo: window.location.origin } });\n   * ```\n   *\n   * Clears the application session and performs a redirect to `/v2/logout`, using\n   * the parameters provided as arguments, to clear the Auth0 session.\n   * If the `logoutParams.federated` option is specified, it also clears the Identity Provider session.\n   * [Read more about how Logout works at Auth0](https://auth0.com/docs/logout).\n   */\n  logout: (options?: LogoutOptions) => Promise<void>;\n\n  /**\n   * After the browser redirects back to the callback page,\n   * call `handleRedirectCallback` to handle success and error\n   * responses from Auth0. If the response is successful, results\n   * will be valid according to their expiration times.\n   *\n   * @param url The URL to that should be used to retrieve the `state` and `code` values. Defaults to `window.location.href` if not given.\n   */\n  handleRedirectCallback: (url?: string) => Promise<RedirectLoginResult>;\n}\n\n/**\n * @ignore\n */\nconst stub = (): never => {\n  throw new Error('You forgot to wrap your component in <Auth0Provider>.');\n};\n\n/**\n * @ignore\n */\nexport const initialContext = {\n  ...initialAuthState,\n  buildAuthorizeUrl: stub,\n  buildLogoutUrl: stub,\n  getAccessTokenSilently: stub,\n  getAccessTokenWithPopup: stub,\n  getIdTokenClaims: stub,\n  loginWithRedirect: stub,\n  loginWithPopup: stub,\n  logout: stub,\n  handleRedirectCallback: stub,\n};\n\n/**\n * The Auth0 Context\n */\nconst Auth0Context = createContext<Auth0ContextInterface>(initialContext);\n\nexport default Auth0Context;\n", "/**\n * An OAuth2 error will come from the authorization server and will have at least an `error` property which will\n * be the error code. And possibly an `error_description` property\n *\n * See: https://openid.net/specs/openid-connect-core-1_0.html#rfc.section.*******\n */\nexport class OAuthError extends Error {\n  constructor(public error: string, public error_description?: string) {\n    super(error_description || error);\n\n    // https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, OAuthError.prototype);\n  }\n}\n", "import { OAuthError } from './errors';\n\nconst CODE_RE = /[?&]code=[^&]+/;\nconst STATE_RE = /[?&]state=[^&]+/;\nconst ERROR_RE = /[?&]error=[^&]+/;\n\nexport const hasAuthParams = (searchParams = window.location.search): boolean =>\n  (CODE_RE.test(searchParams) || ERROR_RE.test(searchParams)) &&\n  STATE_RE.test(searchParams);\n\nconst normalizeErrorFn =\n  (fallbackMessage: string) =>\n  (error: unknown): Error => {\n    if (error instanceof Error) {\n      return error;\n    }\n    // try to check errors of the following form: {error: string; error_description?: string}\n    if (\n      error !== null &&\n      typeof error === 'object' &&\n      'error' in error &&\n      typeof error.error === 'string'\n    ) {\n      if (\n        'error_description' in error &&\n        typeof error.error_description === 'string'\n      ) {\n        return new OAuthError(error.error, error.error_description);\n      }\n      return new OAuthError(error.error);\n    }\n    return new Error(fallbackMessage);\n  };\n\nexport const loginError = normalizeErrorFn('Login failed');\n\nexport const tokenError = normalizeErrorFn('Get access token failed');\n\n/**\n * @ignore\n * Helper function to map the v1 `redirectUri` option to the v2 `authorizationParams.redirect_uri`\n * and log a warning.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const deprecateRedirectUri = (options?: any) => {\n  if (options?.redirectUri) {\n    console.warn(\n      'Using `redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version'\n    );\n    options.authorizationParams = options.authorizationParams || {};\n    options.authorizationParams.redirect_uri = options.redirectUri;\n    delete options.redirectUri;\n  }\n\n  if (options?.authorizationParams?.redirectUri) {\n    console.warn(\n      'Using `authorizationParams.redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `authorizationParams.redirectUri` will be removed in a future version'\n    );\n    options.authorizationParams.redirect_uri =\n      options.authorizationParams.redirectUri;\n    delete options.authorizationParams.redirectUri;\n  }\n};\n", "import { User } from '@auth0/auth0-spa-js';\nimport { AuthState } from './auth-state';\n\ntype Action =\n  | { type: 'LOGIN_POPUP_STARTED' }\n  | {\n      type:\n        | 'INITIALISED'\n        | 'LOGIN_POPUP_COMPLETE'\n        | 'GET_ACCESS_TOKEN_COMPLETE'\n        | 'HANDLE_REDIRECT_COMPLETE';\n      user?: User;\n    }\n  | { type: 'LOGOUT' }\n  | { type: 'ERROR'; error: Error };\n\n/**\n * Handles how that state changes in the `useAuth0` hook.\n */\nexport const reducer = (state: AuthState, action: Action): AuthState => {\n  switch (action.type) {\n    case 'LOGIN_POPUP_STARTED':\n      return {\n        ...state,\n        isLoading: true,\n      };\n    case 'LOGIN_POPUP_COMPLETE':\n    case 'INITIALISED':\n      return {\n        ...state,\n        isAuthenticated: !!action.user,\n        user: action.user,\n        isLoading: false,\n        error: undefined,\n      };\n    case 'HANDLE_REDIRECT_COMPLETE':\n    case 'GET_ACCESS_TOKEN_COMPLETE':\n      if (state.user === action.user) {\n        return state;\n      }\n      return {\n        ...state,\n        isAuthenticated: !!action.user,\n        user: action.user,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        isAuthenticated: false,\n        user: undefined,\n      };\n    case 'ERROR':\n      return {\n        ...state,\n        isLoading: false,\n        error: action.error,\n      };\n  }\n};\n", "import React, {\n  use<PERSON><PERSON>back,\n  useEffect,\n  useMemo,\n  useReducer,\n  useRef,\n  useState,\n} from 'react';\nimport {\n  Auth0Client,\n  Auth0ClientOptions,\n  PopupLoginOptions,\n  PopupConfigOptions,\n  GetTokenWithPopupOptions,\n  RedirectLoginResult,\n  GetTokenSilentlyOptions,\n  User,\n} from '@auth0/auth0-spa-js';\nimport Auth0Context, {\n  Auth0ContextInterface,\n  LogoutOptions,\n  RedirectLoginOptions,\n} from './auth0-context';\nimport {\n  hasAuthParams,\n  loginError,\n  tokenError,\n  deprecateRedirectUri,\n} from './utils';\nimport { reducer } from './reducer';\nimport { initialAuthState } from './auth-state';\n\n/**\n * The state of the application before the user was redirected to the login page.\n */\nexport type AppState = {\n  returnTo?: string;\n  [key: string]: any; // eslint-disable-line @typescript-eslint/no-explicit-any\n};\n\n/**\n * The main configuration to instantiate the `Auth0Provider`.\n */\nexport interface Auth0ProviderOptions extends Auth0ClientOptions {\n  /**\n   * The child nodes your Provider has wrapped\n   */\n  children?: React.ReactNode;\n  /**\n   * By default this removes the code and state parameters from the url when you are redirected from the authorize page.\n   * It uses `window.history` but you might want to overwrite this if you are using a custom router, like `react-router-dom`\n   * See the EXAMPLES.md for more info.\n   */\n  onRedirectCallback?: (appState?: AppState, user?: User) => void;\n  /**\n   * By default, if the page url has code/state params, the SDK will treat them as Auth0's and attempt to exchange the\n   * code for a token. In some cases the code might be for something else (another OAuth SDK perhaps). In these\n   * instances you can instruct the client to ignore them eg\n   *\n   * ```jsx\n   * <Auth0Provider\n   *   clientId={clientId}\n   *   domain={domain}\n   *   skipRedirectCallback={window.location.pathname === '/stripe-oauth-callback'}\n   * >\n   * ```\n   */\n  skipRedirectCallback?: boolean;\n  /**\n   * Context to be used when creating the Auth0Provider, defaults to the internally created context.\n   *\n   * This allows multiple Auth0Providers to be nested within the same application, the context value can then be\n   * passed to useAuth0, withAuth0, or withAuthenticationRequired to use that specific Auth0Provider to access\n   * auth state and methods specifically tied to the provider that the context belongs to.\n   *\n   * When using multiple Auth0Providers in a single application you should do the following to ensure sessions are not\n   * overwritten:\n   *\n   * * Configure a different redirect_uri for each Auth0Provider, and set skipRedirectCallback for each provider to ignore\n   * the others redirect_uri\n   * * If using localstorage for both Auth0Providers, ensure that the audience and scope are different for so that the key\n   * used to store data is different\n   *\n   * For a sample on using multiple Auth0Providers review the [React Account Linking Sample](https://github.com/auth0-samples/auth0-link-accounts-sample/tree/react-variant)\n   */\n  context?: React.Context<Auth0ContextInterface>;\n}\n\n/**\n * Replaced by the package version at build time.\n * @ignore\n */\ndeclare const __VERSION__: string;\n\n/**\n * @ignore\n */\nconst toAuth0ClientOptions = (\n  opts: Auth0ProviderOptions\n): Auth0ClientOptions => {\n  deprecateRedirectUri(opts);\n\n  return {\n    ...opts,\n    auth0Client: {\n      name: 'auth0-react',\n      version: __VERSION__,\n    },\n  };\n};\n\n/**\n * @ignore\n */\nconst defaultOnRedirectCallback = (appState?: AppState): void => {\n  window.history.replaceState(\n    {},\n    document.title,\n    appState?.returnTo || window.location.pathname\n  );\n};\n\n/**\n * ```jsx\n * <Auth0Provider\n *   domain={domain}\n *   clientId={clientId}\n *   authorizationParams={{ redirect_uri: window.location.origin }}>\n *   <MyApp />\n * </Auth0Provider>\n * ```\n *\n * Provides the Auth0Context to its child components.\n */\nconst Auth0Provider = (opts: Auth0ProviderOptions) => {\n  const {\n    children,\n    skipRedirectCallback,\n    onRedirectCallback = defaultOnRedirectCallback,\n    context = Auth0Context,\n    ...clientOpts\n  } = opts;\n  const [client] = useState(\n    () => new Auth0Client(toAuth0ClientOptions(clientOpts))\n  );\n  const [state, dispatch] = useReducer(reducer, initialAuthState);\n  const didInitialise = useRef(false);\n\n  const handleError = useCallback((error: Error) => {\n    dispatch({ type: 'ERROR', error });\n    return error;\n  }, []);\n\n  useEffect(() => {\n    if (didInitialise.current) {\n      return;\n    }\n    didInitialise.current = true;\n    (async (): Promise<void> => {\n      try {\n        let user: User | undefined;\n        if (hasAuthParams() && !skipRedirectCallback) {\n          const { appState } = await client.handleRedirectCallback();\n          user = await client.getUser();\n          onRedirectCallback(appState, user);\n        } else {\n          await client.checkSession();\n          user = await client.getUser();\n        }\n        dispatch({ type: 'INITIALISED', user });\n      } catch (error) {\n        handleError(loginError(error));\n      }\n    })();\n  }, [client, onRedirectCallback, skipRedirectCallback, handleError]);\n\n  const loginWithRedirect = useCallback(\n    (opts?: RedirectLoginOptions): Promise<void> => {\n      deprecateRedirectUri(opts);\n\n      return client.loginWithRedirect(opts);\n    },\n    [client]\n  );\n\n  const loginWithPopup = useCallback(\n    async (\n      options?: PopupLoginOptions,\n      config?: PopupConfigOptions\n    ): Promise<void> => {\n      dispatch({ type: 'LOGIN_POPUP_STARTED' });\n      try {\n        await client.loginWithPopup(options, config);\n      } catch (error) {\n        handleError(loginError(error));\n        return;\n      }\n      const user = await client.getUser();\n      dispatch({ type: 'LOGIN_POPUP_COMPLETE', user });\n    },\n    [client]\n  );\n\n  const logout = useCallback(\n    async (opts: LogoutOptions = {}): Promise<void> => {\n      await client.logout(opts);\n      if (opts.openUrl || opts.openUrl === false) {\n        dispatch({ type: 'LOGOUT' });\n      }\n    },\n    [client]\n  );\n\n  const getAccessTokenSilently = useCallback(\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    async (opts?: GetTokenSilentlyOptions): Promise<any> => {\n      let token;\n      try {\n        token = await client.getTokenSilently(opts);\n      } catch (error) {\n        throw tokenError(error);\n      } finally {\n        dispatch({\n          type: 'GET_ACCESS_TOKEN_COMPLETE',\n          user: await client.getUser(),\n        });\n      }\n      return token;\n    },\n    [client]\n  );\n\n  const getAccessTokenWithPopup = useCallback(\n    async (\n      opts?: GetTokenWithPopupOptions,\n      config?: PopupConfigOptions\n    ): Promise<string | undefined> => {\n      let token;\n      try {\n        token = await client.getTokenWithPopup(opts, config);\n      } catch (error) {\n        throw tokenError(error);\n      } finally {\n        dispatch({\n          type: 'GET_ACCESS_TOKEN_COMPLETE',\n          user: await client.getUser(),\n        });\n      }\n      return token;\n    },\n    [client]\n  );\n\n  const getIdTokenClaims = useCallback(\n    () => client.getIdTokenClaims(),\n    [client]\n  );\n\n  const handleRedirectCallback = useCallback(\n    async (url?: string): Promise<RedirectLoginResult> => {\n      try {\n        return await client.handleRedirectCallback(url);\n      } catch (error) {\n        throw tokenError(error);\n      } finally {\n        dispatch({\n          type: 'HANDLE_REDIRECT_COMPLETE',\n          user: await client.getUser(),\n        });\n      }\n    },\n    [client]\n  );\n\n  const contextValue = useMemo<Auth0ContextInterface<User>>(() => {\n    return {\n      ...state,\n      getAccessTokenSilently,\n      getAccessTokenWithPopup,\n      getIdTokenClaims,\n      loginWithRedirect,\n      loginWithPopup,\n      logout,\n      handleRedirectCallback,\n    };\n  }, [\n    state,\n    getAccessTokenSilently,\n    getAccessTokenWithPopup,\n    getIdTokenClaims,\n    loginWithRedirect,\n    loginWithPopup,\n    logout,\n    handleRedirectCallback,\n  ]);\n\n  return <context.Provider value={contextValue}>{children}</context.Provider>;\n};\n\nexport default Auth0Provider;\n", "import { useContext } from 'react';\nimport { User } from '@auth0/auth0-spa-js';\nimport Auth0Context, { Auth0ContextInterface } from './auth0-context';\n\n/**\n * ```js\n * const {\n *   // Auth state:\n *   error,\n *   isAuthenticated,\n *   isLoading,\n *   user,\n *   // Auth methods:\n *   getAccessTokenSilently,\n *   getAccessTokenWithPopup,\n *   getIdTokenClaims,\n *   loginWithRedirect,\n *   loginWithPopup,\n *   logout,\n * } = useAuth0<TUser>();\n * ```\n *\n * Use the `useAuth0` hook in your components to access the auth state and methods.\n *\n * TUser is an optional type param to provide a type to the `user` field.\n */\nconst useAuth0 = <TUser extends User = User>(\n  context = Auth0Context\n): Auth0ContextInterface<TUser> =>\n  useContext(context) as Auth0ContextInterface<TUser>;\n\nexport default useAuth0;\n", "import React, { ComponentType, useEffect, FC } from 'react';\nimport useAuth0 from './use-auth0';\nimport Auth0Context, {\n  Auth0ContextInterface,\n  RedirectLoginOptions,\n} from './auth0-context';\n\n/**\n * @ignore\n */\nconst defaultOnRedirecting = (): React.JSX.Element => <></>;\n\n/**\n* @ignore\n*/\nconst defaultOnBeforeAuthentication = async (): Promise<void> => {/* noop */ };\n\n/**\n * @ignore\n */\nconst defaultReturnTo = (): string =>\n  `${window.location.pathname}${window.location.search}`;\n\n/**\n * Options for the withAuthenticationRequired Higher Order Component\n */\nexport interface WithAuthenticationRequiredOptions {\n  /**\n   * ```js\n   * withAuthenticationRequired(Profile, {\n   *   returnTo: '/profile'\n   * })\n   * ```\n   *\n   * or\n   *\n   * ```js\n   * withAuthenticationRequired(Profile, {\n   *   returnTo: () => window.location.hash.substr(1)\n   * })\n   * ```\n   *\n   * Add a path for the `onRedirectCallback` handler to return the user to after login.\n   */\n  returnTo?: string | (() => string);\n  /**\n   * ```js\n   * withAuthenticationRequired(Profile, {\n   *   onRedirecting: () => <div>Redirecting you to the login...</div>\n   * })\n   * ```\n   *\n   * Render a message to show that the user is being redirected to the login.\n   */\n  onRedirecting?: () => React.JSX.Element;\n  /**\n   * ```js\n   * withAuthenticationRequired(Profile, {\n   *   onBeforeAuthentication: () => { analyticsLibrary.track('login_triggered'); }\n   * })\n   * ```\n   *\n   * Allows executing logic before the user is redirected to the login page.\n   */\n  onBeforeAuthentication?: () => Promise<void>;\n  /**\n   * ```js\n   * withAuthenticationRequired(Profile, {\n   *   loginOptions: {\n   *     appState: {\n   *       customProp: 'foo'\n   *     }\n   *   }\n   * })\n   * ```\n   *\n   * Pass additional login options, like extra `appState` to the login page.\n   * This will be merged with the `returnTo` option used by the `onRedirectCallback` handler.\n   */\n  loginOptions?: RedirectLoginOptions;\n  /**\n   * The context to be used when calling useAuth0, this should only be provided if you are using multiple Auth0Providers\n   * within your application and you wish to tie a specific component to a Auth0Provider other than the Auth0Provider\n   * associated with the default Auth0Context.\n   */\n  context?: React.Context<Auth0ContextInterface>;\n}\n\n/**\n * ```js\n * const MyProtectedComponent = withAuthenticationRequired(MyComponent);\n * ```\n *\n * When you wrap your components in this Higher Order Component and an anonymous user visits your component\n * they will be redirected to the login page; after login they will be returned to the page they were redirected from.\n */\nconst withAuthenticationRequired = <P extends object>(\n  Component: ComponentType<P>,\n  options: WithAuthenticationRequiredOptions = {}\n): FC<P> => {\n  return function WithAuthenticationRequired(props: P): React.JSX.Element {\n    const {\n      returnTo = defaultReturnTo,\n      onRedirecting = defaultOnRedirecting,\n      onBeforeAuthentication = defaultOnBeforeAuthentication,\n      loginOptions,\n      context = Auth0Context,\n    } = options;\n\n    const { isAuthenticated, isLoading, loginWithRedirect } =\n      useAuth0(context);\n\n    useEffect(() => {\n      if (isLoading || isAuthenticated) {\n        return;\n      }\n      const opts = {\n        ...loginOptions,\n        appState: {\n          ...(loginOptions && loginOptions.appState),\n          returnTo: typeof returnTo === 'function' ? returnTo() : returnTo,\n        },\n      };\n      (async (): Promise<void> => {\n        await onBeforeAuthentication();\n        await loginWithRedirect(opts);\n      })();\n    }, [\n      isLoading,\n      isAuthenticated,\n      loginWithRedirect,\n      onBeforeAuthentication,\n      loginOptions,\n      returnTo,\n    ]);\n\n    return isAuthenticated ? <Component {...props} /> : onRedirecting();\n  };\n};\n\nexport default withAuthenticationRequired;\n", "import React, { ComponentType } from 'react';\nimport Auth0Context, { Auth0ContextInterface } from './auth0-context';\n\n/**\n * Components wrapped in `withAuth0` will have an additional `auth0` prop\n */\nexport interface WithAuth0Props {\n  auth0: Auth0ContextInterface;\n}\n\n/**\n * ```jsx\n * class MyComponent extends Component {\n *   render() {\n *     // Access the auth context from the `auth0` prop\n *     const { user } = this.props.auth0;\n *     return <div>Hello {user.name}!</div>\n *   }\n * }\n * // Wrap your class component in withAuth0\n * export default withAuth0(MyComponent);\n * ```\n *\n * Wrap your class components in this Higher Order Component to give them access to the Auth0Context.\n *\n * Providing a context as the second argument allows you to configure the Auth0Provider the Auth0Context\n * should come from f you have multiple within your application.\n */\nconst withAuth0 = <P extends WithAuth0Props>(\n  Component: ComponentType<P>,\n  context = Auth0Context\n): ComponentType<Omit<P, keyof WithAuth0Props>> => {\n  return function WithAuth(props): React.JSX.Element {\n    return (\n      <context.Consumer>\n        {(auth: Auth0ContextInterface): React.JSX.Element => (\n          <Component {...(props as P)} auth0={auth} />\n        )}\n      </context.Consumer>\n    );\n  };\n};\n\nexport default withAuth0;\n"], "names": ["e", "t", "i", "o", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "n", "length", "propertyIsEnumerable", "SuppressedError", "globalThis", "window", "global", "self", "__esModule", "default", "exports", "defineProperty", "value", "this", "locked", "Map", "addToLocked", "get", "set", "unshift", "isLocked", "has", "lock", "Promise", "unlock", "pop", "setTimeout", "delete", "getInstance", "instance", "a", "__awaiter", "r", "c", "next", "s", "throw", "done", "then", "apply", "__generator", "label", "sent", "trys", "ops", "return", "Symbol", "iterator", "TypeError", "push", "key", "Error", "getItem", "clear", "localStorage", "removeItem", "setItem", "keySync", "getItemSync", "clearSync", "removeItemSync", "setItemSync", "d", "u", "Math", "floor", "random", "l", "acquiredIatSet", "Set", "storageHandler", "id", "Date", "now", "toString", "acquireLock", "bind", "releaseLock", "releaseLock__private__", "waitForSomethingToChange", "refreshLockWhileAcquired", "waiters", "h", "p", "m", "JSON", "stringify", "iat", "timeout<PERSON><PERSON>", "timeAcquired", "timeRefreshed", "parse", "add", "lock<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "removeFromWaiting", "clearTimeout", "addEventListener", "addToWaiting", "max", "filter", "notify<PERSON><PERSON><PERSON>", "slice", "for<PERSON>ach", "includes", "timeoutInSeconds", "name", "version", "constructor", "super", "error", "error_description", "setPrototypeOf", "fromPayload", "state", "appState", "popup", "mfa_token", "f", "g", "audience", "scope", "w", "crypto", "y", "Array", "from", "getRandomValues", "Uint8Array", "k", "btoa", "v", "clientId", "URLSearchParams", "keys", "reduce", "assign", "client_id", "b", "decodeURIComponent", "atob", "split", "map", "charCodeAt", "join", "replace", "_", "async", "fetch", "ok", "json", "O", "auth", "timeout", "fetchUrl", "fetchOptions", "useFormData", "MessageChannel", "port1", "onmessage", "data", "close", "postMessage", "port2", "S", "AbortController", "signal", "race", "abort", "finally", "I", "T", "baseUrl", "auth0Client", "method", "body", "headers", "j", "Boolean", "trim", "C", "prefix", "suffix", "to<PERSON><PERSON>", "fromKey", "fromCacheEntry", "z", "remove", "allKeys", "startsWith", "P", "enclosedCache", "x", "cache", "keyManifest", "nowProvider", "setIdToken", "getIdTokenCache<PERSON>ey", "id_token", "decodedToken", "getIdToken", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchExisting<PERSON>ache<PERSON>ey", "expiresAt", "refresh_token", "wrapCacheEntry", "resolve", "expires_in", "Z", "storage", "cookieDomain", "storageKey", "create", "save", "daysUntilExpire", "K", "W", "R", "__assign", "arguments", "encodeURIComponent", "expires", "setMilliseconds", "getMilliseconds", "toUTCString", "domain", "path", "secure", "sameSite", "char<PERSON>t", "document", "cookie", "encode", "getAll", "U", "L", "D", "X", "location", "protocol", "N", "J", "sessionStorage", "V", "M", "String", "fromCharCode", "Uint16Array", "buffer", "substring", "Blob", "type", "URL", "createObjectURL", "F", "Worker", "A", "B", "manifest<PERSON>ey", "createManifestKeyFrom", "size", "$", "memory", "localstorage", "q", "Q", "openUrl", "onRedirect", "ee", "te", "userCache", "defaultOptions", "authorizationParams", "useRefreshTokensFallback", "_releaseLockOnPageHide", "options", "subtle", "cacheLocation", "console", "warn", "httpTimeoutMs", "httpTimeoutInSeconds", "cookieStorage", "legacySameSiteCookie", "orgHintCookieName", "isAuthenticatedCookieName", "sessionCheckExpiryDays", "useCookiesForTransactions", "useRefreshTokens", "transactionManager", "cacheManager", "domainUrl", "test", "token<PERSON>ssuer", "issuer", "workerUrl", "worker", "_url", "_authorizeUrl", "_verifyIdToken", "__raw", "encoded", "header", "payload", "signature", "claims", "user", "iss", "sub", "alg", "aud", "isArray", "azp", "nonce", "max_age", "auth_time", "exp", "leeway", "setUTCSeconds", "nbf", "parseInt", "organization", "org_id", "toLowerCase", "org_name", "E", "_processOrgHint", "_prepareAuthorizeUrl", "digest", "TextEncoder", "response_type", "response_mode", "redirect_uri", "code_challenge", "code_challenge_method", "code_verifier", "url", "loginWithPopup", "screenX", "innerWidth", "screenY", "innerHeight", "open", "origin", "href", "setInterval", "closed", "clearInterval", "response", "authorizeTimeoutInSeconds", "_requestToken", "grant_type", "code", "nonceIn", "getUser", "_getIdTokenFromCache", "getIdTokenClaims", "loginWithRedirect", "fragment", "handleRedirectCallback", "checkSession", "getTokenSilently", "cacheMode", "_getTokenSilently", "detailedResponse", "access_token", "_getEntryFromCache", "_getTokenUsingRefreshToken", "_getTokenFromIFrame", "oauthTokenScope", "getTokenWithPopup", "isAuthenticated", "_buildLogoutUrl", "logoutParams", "federated", "logout", "prompt", "crossOriginIsolated", "createElement", "setAttribute", "style", "display", "contains", "<PERSON><PERSON><PERSON><PERSON>", "source", "append<PERSON><PERSON><PERSON>", "message", "_saveEntryInCache", "initialAuthState", "isLoading", "stub", "initialContext", "buildAuthorizeUrl", "buildLogoutUrl", "getAccessTokenSilently", "getAccessTokenWithPopup", "Auth0Context", "createContext", "OAuthError", "_super", "_this", "__extends", "CODE_RE", "STATE_RE", "ERROR_RE", "normalizeErrorFn", "fallbackMessage", "loginError", "tokenError", "deprecateRedirectUri", "redirectUri", "_a", "reducer", "action", "undefined", "defaultOnRedirectCallback", "history", "replaceState", "title", "returnTo", "pathname", "useAuth0", "context", "useContext", "defaultOnRedirecting", "React", "Fragment", "defaultOnBeforeAuthentication", "defaultReturnTo", "concat", "search", "opts", "children", "skipRedirectCallback", "onRedirectCallback", "_b", "clientOpts", "__rest", "client", "useState", "Auth0Client", "toAuth0ClientOptions", "_c", "useReducer", "dispatch", "didInitialise", "useRef", "handleError", "useCallback", "useEffect", "current", "searchParams", "error_1", "config", "error_2", "token", "error_3", "error_4", "error_5", "contextValue", "useMemo", "Provider", "Component", "props", "Consumer", "auth0", "onRedirecting", "onBeforeAuthentication", "loginOptions", "_d", "_e"], "mappings": "imEAAA,SAASA,EAAEA,EAAEC,GAAG,IAAIC,EAAE,GAAG,IAAI,IAAIC,KAAKH,EAAEI,OAAOC,UAAUC,eAAeC,KAAKP,EAAEG,IAAIF,EAAEO,QAAQL,GAAG,IAAID,EAAEC,GAAGH,EAAEG,IAAI,GAAG,MAAMH,GAAG,mBAAmBI,OAAOK,sBAAsB,CAAC,IAAIC,EAAE,EAAE,IAAIP,EAAEC,OAAOK,sBAAsBT,GAAGU,EAAEP,EAAEQ,OAAOD,IAAIT,EAAEO,QAAQL,EAAEO,IAAI,GAAGN,OAAOC,UAAUO,qBAAqBL,KAAKP,EAAEG,EAAEO,MAAMR,EAAEC,EAAEO,IAAIV,EAAEG,EAAEO,IAAI,CAAC,OAAOR,CAAC,qDAAC,mBAAmBW,iBAAiBA,gBAAgB,IAAIZ,EAAE,oBAAoBa,WAAWA,WAAW,oBAAoBC,OAAOA,OAAO,oBAAoBC,OAAOA,OAAO,oBAAoBC,KAAKA,KAAK,GAAG,SAASf,EAAEF,GAAG,OAAOA,GAAGA,EAAEkB,YAAYd,OAAOC,UAAUC,eAAeC,KAAKP,EAAE,WAAWA,EAAEmB,QAAQnB,CAAC,CAAC,SAASG,EAAEH,EAAEC,GAAG,OAAOD,EAAEC,EAAE,CAACmB,QAAQ,CAAA,GAAInB,EAAEmB,SAASnB,EAAEmB,OAAO,CAAC,IAAIV,EAAEP,YAAYH,EAAEC,GAAGG,OAAOiB,eAAepB,EAAE,aAAa,CAACqB,OAAM,IAAK,IAAIpB,EAAE,WAAW,SAASF,IAAI,IAAIA,EAAEuB,KAAKA,KAAKC,OAAO,IAAIC,IAAIF,KAAKG,YAAY,SAASzB,EAAEC,GAAG,IAAIC,EAAEH,EAAEwB,OAAOG,IAAI1B,QAAG,IAASE,OAAE,IAASD,EAAEF,EAAEwB,OAAOI,IAAI3B,EAAE,IAAID,EAAEwB,OAAOI,IAAI3B,EAAE,CAACC,SAAI,IAASA,IAAIC,EAAE0B,QAAQ3B,GAAGF,EAAEwB,OAAOI,IAAI3B,EAAEE,GAAG,EAAEoB,KAAKO,SAAS,SAAS7B,GAAG,OAAOD,EAAEwB,OAAOO,IAAI9B,EAAE,EAAEsB,KAAKS,KAAK,SAAS/B,GAAG,OAAO,IAAIgC,SAAO,SAAW/B,EAAEC,GAAGH,EAAE8B,SAAS7B,GAAGD,EAAE0B,YAAYzB,EAAEC,IAAIF,EAAE0B,YAAYzB,GAAGC,IAAK,GAAE,EAAEqB,KAAKW,OAAO,SAASjC,GAAG,IAAIC,EAAEF,EAAEwB,OAAOG,IAAI1B,GAAG,QAAG,IAASC,GAAG,IAAIA,EAAES,OAAO,CAAC,IAAIR,EAAED,EAAEiC,MAAMnC,EAAEwB,OAAOI,IAAI3B,EAAEC,QAAG,IAASC,GAAGiC,WAAWjC,EAAE,EAAE,MAAMH,EAAEwB,OAAOa,OAAOpC,EAAE,CAAC,CAAC,OAAOD,EAAEsC,YAAY,WAAW,YAAO,IAAStC,EAAEuC,WAAWvC,EAAEuC,SAAS,IAAIvC,GAAGA,EAAEuC,QAAQ,EAAEvC,CAAC,CAAznB,GAA6nBC,EAAEkB,QAAQ,WAAW,OAAOjB,EAAEoC,aAAa,CAAE,IAAGpC,EAAEQ,GAAG,IAAI8B,EAAEtC,EAAEC,GAAG,SAASH,EAAEE,GAAG,IAAIC,EAAEF,GAAGA,EAAEwC,WAAW,SAASzC,EAAEC,EAAEC,EAAEC,GAAG,OAAO,IAAID,IAAIA,EAAE+B,oBAAoBvB,EAAE8B,GAAG,SAASE,EAAE1C,GAAG,IAAI2C,EAAExC,EAAEyC,KAAK5C,GAAG,CAAC,MAAMA,GAAGwC,EAAExC,EAAE,CAAC,CAAC,SAAS6C,EAAE7C,GAAG,IAAI2C,EAAExC,EAAE2C,MAAM9C,GAAG,CAAC,MAAMA,GAAGwC,EAAExC,EAAE,CAAC,CAAC,SAAS2C,EAAE3C,GAAGA,EAAE+C,KAAKrC,EAAEV,EAAEsB,OAAO,IAAIpB,YAAYD,GAAGA,EAAED,EAAEsB,MAAO,IAAG0B,KAAKN,EAAEG,EAAE,CAACF,GAAGxC,EAAEA,EAAE8C,MAAMjD,EAAEC,GAAG,KAAK2C,OAAQ,GAAE,EAAEJ,EAAEvC,GAAGA,EAAEiD,aAAa,SAASlD,EAAEC,GAAG,IAAIC,EAAEC,EAAEO,EAAE8B,EAAEE,EAAE,CAACS,MAAM,EAAEC,KAAK,WAAW,GAAG,EAAE1C,EAAE,GAAG,MAAMA,EAAE,GAAG,OAAOA,EAAE,EAAE,EAAE2C,KAAK,GAAGC,IAAI,IAAI,OAAOd,EAAE,CAACI,KAAKC,EAAE,GAAGC,MAAMD,EAAE,GAAGU,OAAOV,EAAE,IAAI,mBAAmBW,SAAShB,EAAEgB,OAAOC,UAAU,WAAW,OAAOlC,IAAI,GAAGiB,EAAE,SAASK,EAAEL,GAAG,OAAO,SAASK,GAAG,OAAO,SAASL,GAAG,GAAGtC,EAAE,MAAM,IAAIwD,UAAU,mCAAmC,KAAKhB,GAAG,IAAI,GAAGxC,EAAE,EAAEC,IAAIO,EAAE,EAAE8B,EAAE,GAAGrC,EAAEoD,OAAOf,EAAE,GAAGrC,EAAE2C,SAASpC,EAAEP,EAAEoD,SAAS7C,EAAEH,KAAKJ,GAAG,GAAGA,EAAEyC,SAASlC,EAAEA,EAAEH,KAAKJ,EAAEqC,EAAE,KAAKO,KAAK,OAAOrC,EAAE,OAAOP,EAAE,EAAEO,IAAI8B,EAAE,CAAC,EAAEA,EAAE,GAAG9B,EAAEY,QAAQkB,EAAE,IAAI,KAAK,EAAE,KAAK,EAAE9B,EAAE8B,EAAE,MAAM,KAAK,EAAE,OAAOE,EAAES,QAAQ,CAAC7B,MAAMkB,EAAE,GAAGO,MAAK,GAAI,KAAK,EAAEL,EAAES,QAAQhD,EAAEqC,EAAE,GAAGA,EAAE,CAAC,GAAG,SAAS,KAAK,EAAEA,EAAEE,EAAEY,IAAInB,MAAMO,EAAEW,KAAKlB,MAAM,SAAS,QAAQ,MAAezB,GAAVA,EAAEgC,EAAEW,MAAU1C,OAAO,GAAGD,EAAEA,EAAEC,OAAO,KAAK,IAAI6B,EAAE,IAAI,IAAIA,EAAE,IAAI,CAACE,EAAE,EAAE,QAAQ,CAAC,GAAG,IAAIF,EAAE,MAAM9B,GAAG8B,EAAE,GAAG9B,EAAE,IAAI8B,EAAE,GAAG9B,EAAE,IAAI,CAACgC,EAAES,MAAMX,EAAE,GAAG,KAAK,CAAC,GAAG,IAAIA,EAAE,IAAIE,EAAES,MAAMzC,EAAE,GAAG,CAACgC,EAAES,MAAMzC,EAAE,GAAGA,EAAE8B,EAAE,KAAK,CAAC,GAAG9B,GAAGgC,EAAES,MAAMzC,EAAE,GAAG,CAACgC,EAAES,MAAMzC,EAAE,GAAGgC,EAAEY,IAAIK,KAAKnB,GAAG,KAAK,CAAC9B,EAAE,IAAIgC,EAAEY,IAAInB,MAAMO,EAAEW,KAAKlB,MAAM,SAASK,EAAEvC,EAAEM,KAAKP,EAAE0C,EAAE,CAAC,MAAM1C,GAAGwC,EAAE,CAAC,EAAExC,GAAGG,EAAE,CAAC,CAAC,QAAQD,EAAEQ,EAAE,CAAC,CAAC,GAAG,EAAE8B,EAAE,GAAG,MAAMA,EAAE,GAAG,MAAM,CAAClB,MAAMkB,EAAE,GAAGA,EAAE,QAAG,EAAOO,MAAK,EAAG,CAApxB,CAAsxB,CAACP,EAAEK,GAAG,CAAC,CAAC,EAAEH,EAAEzC,EAAEG,OAAOiB,eAAenB,EAAE,aAAa,CAACoB,OAAM,IAAK,IAAIuB,EAAE,wBAAwBF,EAAE,CAACiB,IAAI,SAAS5D,GAAG,OAAOG,EAAEuC,OAAE,OAAO,GAAM,WAAa,OAAOF,EAAEjB,MAAM,SAASvB,GAAG,MAAM,IAAI6D,MAAM,cAAe,GAAG,GAAE,EAAEC,QAAQ,SAAS9D,GAAG,OAAOG,EAAEuC,OAAE,OAAO,GAAM,WAAa,OAAOF,EAAEjB,eAAevB,GAAG,MAAM,IAAI6D,MAAM,cAAe,GAAG,GAAE,EAAEE,MAAM,WAAW,OAAO5D,EAAEuC,OAAE,OAAO,GAAM,WAAa,OAAOF,EAAEjB,eAAevB,GAAG,MAAM,CAAC,EAAEe,OAAOiD,aAAaD,QAAS,GAAG,GAAE,EAAEE,WAAW,SAASjE,GAAG,OAAOG,EAAEuC,OAAE,OAAO,cAAmB,OAAOF,EAAEjB,MAAM,SAASvB,GAAG,MAAM,IAAI6D,MAAM,cAAe,GAAG,GAAE,EAAEK,QAAQ,SAASlE,EAAEC,GAAG,OAAOE,EAAEuC,OAAE,OAAO,GAAM,WAAa,OAAOF,EAAEjB,MAAM,SAASvB,GAAG,MAAM,IAAI6D,MAAM,cAAe,GAAG,GAAE,EAAEM,QAAQ,SAASnE,GAAG,OAAOe,OAAOiD,aAAaJ,IAAI5D,EAAE,EAAEoE,YAAY,SAASpE,GAAG,OAAOe,OAAOiD,aAAaF,QAAQ9D,EAAE,EAAEqE,UAAU,WAAW,OAAOtD,OAAOiD,aAAaD,OAAO,EAAEO,eAAe,SAAStE,GAAG,OAAOe,OAAOiD,aAAaC,WAAWjE,EAAE,EAAEuE,YAAY,SAASvE,EAAEC,GAAG,OAAOc,OAAOiD,aAAaE,QAAQlE,EAAEC,EAAE,GAAG,SAASuE,EAAExE,GAAG,OAAO,IAAIiC,SAAO,SAAWhC,GAAG,OAAOmC,WAAWnC,EAAED,EAAG,GAAE,CAAC,SAASyE,EAAEzE,GAAG,IAAI,IAAIC,EAAE,gEAAgEC,EAAE,GAAGC,EAAE,EAAEA,EAAEH,EAAEG,IAAKD,GAAGD,EAAEyE,KAAKC,MAAoB1E,GAAdyE,KAAKE,WAAoB,OAAO1E,CAAC,CAAC,IAAI2E,EAAE,WAAW,SAAS7E,EAAEC,GAAGsB,KAAKuD,eAAe,IAAIC,IAAIxD,KAAKyD,oBAAe,EAAOzD,KAAK0D,GAAGC,KAAKC,MAAMC,WAAWX,EAAE,IAAIlD,KAAK8D,YAAY9D,KAAK8D,YAAYC,KAAK/D,MAAMA,KAAKgE,YAAYhE,KAAKgE,YAAYD,KAAK/D,MAAMA,KAAKiE,uBAAuBjE,KAAKiE,uBAAuBF,KAAK/D,MAAMA,KAAKkE,yBAAyBlE,KAAKkE,yBAAyBH,KAAK/D,MAAMA,KAAKmE,yBAAyBnE,KAAKmE,yBAAyBJ,KAAK/D,MAAMA,KAAKyD,eAAe/E,OAAE,IAASD,EAAE2F,UAAU3F,EAAE2F,QAAQ,GAAG,CAAC,OAAO3F,EAAEK,UAAUgF,YAAY,SAASpF,EAAEC,GAAG,YAAO,IAASA,IAAIA,EAAE,KAAKC,EAAEoB,UAAK,OAAO,cAAmB,IAAIpB,EAAEO,EAAEgC,EAAEmC,EAAEe,EAAEC,EAAEC,EAAE,OAAOtD,EAAEjB,eAAeiB,GAAG,OAAOA,EAAEW,OAAO,KAAK,EAAEhD,EAAE+E,KAAKC,MAAMV,EAAE,GAAG/D,EAAEwE,KAAKC,MAAMjF,EAAEwC,EAAEG,EAAE,IAAI5C,EAAE4E,OAAE,IAAStD,KAAKyD,eAAerC,EAAEpB,KAAKyD,eAAexC,EAAEW,MAAM,EAAE,KAAK,EAAE,OAAO+B,KAAKC,MAAMzE,EAAE,CAAC,EAAE8D,EAAE,KAAK,CAAC,EAAE,GAAG,KAAK,EAAE,OAAOhC,EAAEY,OAAO,OAAOyB,EAAET,YAAY1B,GAAG,CAAC,EAAE,IAAIkD,EAAErE,KAAK0D,GAAG,IAAIhF,EAAE,IAAIE,EAAE,CAAC,EAAEqE,EAAEE,KAAKC,MAAM,GAAGD,KAAKE,aAAa,KAAK,EAAE,OAAOpC,EAAEY,OAAOyB,EAAEN,YAAY7B,EAAEqD,KAAKC,UAAU,CAACf,GAAG1D,KAAK0D,GAAGgB,IAAI9F,EAAE+F,WAAWN,EAAEO,aAAajB,KAAKC,MAAMiB,cAAclB,KAAKC,SAAS,CAAC,EAAEX,EAAE,KAAK,KAAK,EAAE,OAAOhC,EAAEY,OAAO,QAAQyC,EAAEhB,EAAET,YAAY1B,MAAMoD,EAAEC,KAAKM,MAAMR,IAAIZ,KAAK1D,KAAK0D,IAAIa,EAAEG,MAAM9F,GAAGoB,KAAKuD,eAAewB,IAAInG,GAAGoB,KAAKmE,yBAAyBhD,EAAEvC,GAAG,CAAC,GAAE,IAAK,CAAC,EAAE,GAAG,KAAK,EAAE,OAAOH,EAAEuG,mBAAc,IAAShF,KAAKyD,eAAerC,EAAEpB,KAAKyD,gBAAgB,CAAC,EAAEzD,KAAKkE,yBAAyB/E,IAAI,KAAK,EAAE8B,EAAEY,OAAOZ,EAAEW,MAAM,EAAE,KAAK,EAAE,OAAOhD,EAAE+E,KAAKC,MAAMV,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,MAAM,CAAC,GAAE,GAAK,GAAG,GAAE,EAAEzE,EAAEK,UAAUqF,yBAAyB,SAAS1F,EAAEC,GAAG,OAAOE,EAAEoB,UAAK,OAAO,GAAM,WAAa,IAAIrB,EAAEqB,KAAK,OAAOiB,EAAEjB,MAAM,SAASmB,GAAG,OAAON,YAAU,WAAa,OAAOjC,EAAED,OAAE,OAAO,GAAM,WAAa,IAAIA,EAAEC,EAAEuC,EAAE,OAAOF,EAAEjB,eAAeiB,GAAG,OAAOA,EAAEW,OAAO,KAAK,EAAE,MAAM,CAAC,EAAEzC,EAAES,UAAUa,KAAK/B,IAAI,KAAK,EAAE,OAAOuC,EAAEY,OAAO7B,KAAKuD,eAAe/C,IAAI9B,IAAIC,OAAE,IAASqB,KAAKyD,eAAerC,EAAEpB,KAAKyD,eAAe,QAAQ7E,EAAED,EAAEkE,YAAYpE,KAAKU,EAAES,UAAUe,OAAOjC,GAAG,CAAC,MAAMyC,EAAEqD,KAAKM,MAAMlG,IAAIiG,cAAclB,KAAKC,MAAMjF,EAAEqE,YAAYvE,EAAE+F,KAAKC,UAAUtD,IAAIhC,EAAES,UAAUe,OAAOjC,GAAGsB,KAAKmE,yBAAyB1F,EAAEC,GAAG,CAAC,MAAMS,EAAES,UAAUe,OAAOjC,GAAG,CAAC,IAAK,GAAG,GAAG,GAAE,KAAK,CAAC,EAAG,GAAG,GAAE,EAAED,EAAEK,UAAUoF,yBAAyB,SAASxF,GAAG,OAAOE,EAAEoB,UAAK,OAAO,GAAM,WAAa,OAAOiB,EAAEjB,MAAI,SAAWrB,GAAG,OAAOA,EAAEiD,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE,IAAIlB,SAAO,SAAW/B,GAAG,IAAIC,GAAE,EAAGO,EAAEwE,KAAKC,MAAM3C,GAAE,EAAG,SAASE,IAAI,GAAGF,IAAIzB,OAAOyF,oBAAoB,UAAU9D,GAAG1C,EAAEyG,kBAAkB/D,GAAGgE,aAAa7D,GAAGL,GAAE,IAAKrC,EAAE,CAACA,GAAE,EAAG,IAAIF,EAAE,IAAIiF,KAAKC,MAAMzE,GAAGT,EAAE,EAAEmC,WAAWlC,EAAED,GAAGC,EAAE,KAAK,CAAC,CAACa,OAAO4F,iBAAiB,UAAUjE,GAAG1C,EAAE4G,aAAalE,GAAG,IAAIG,EAAET,WAAWM,EAAEgC,KAAKmC,IAAI,EAAE5G,EAAEiF,KAAKC,OAAQ,KAAI,KAAK,EAAE,OAAOjF,EAAEkD,OAAO,CAAC,GAAI,GAAG,GAAE,EAAEpD,EAAE4G,aAAa,SAAS3G,GAAGsB,KAAKkF,kBAAkBxG,QAAG,IAASD,EAAE2F,SAAS3F,EAAE2F,QAAQhC,KAAK1D,EAAE,EAAED,EAAEyG,kBAAkB,SAASxG,QAAG,IAASD,EAAE2F,UAAU3F,EAAE2F,QAAQ3F,EAAE2F,QAAQmB,iBAAiB9G,GAAG,OAAOA,IAAIC,CAAE,IAAG,EAAED,EAAE+G,cAAc,gBAAW,IAAS/G,EAAE2F,SAAS3F,EAAE2F,QAAQqB,QAAQC,SAAO,SAAWjH,GAAG,OAAOA,GAAI,GAAE,EAAEA,EAAEK,UAAUkF,YAAY,SAASvF,GAAG,OAAOG,EAAEoB,UAAK,OAAO,cAAmB,OAAOiB,EAAEjB,eAAetB,GAAG,OAAOA,EAAEkD,OAAO,KAAK,EAAE,MAAM,CAAC,EAAE5B,KAAKiE,uBAAuBxF,IAAI,KAAK,EAAE,MAAM,CAAC,EAAEC,EAAEmD,QAAS,GAAG,GAAE,EAAEpD,EAAEK,UAAUmF,uBAAuB,SAASvF,GAAG,OAAOE,EAAEoB,UAAK,OAAO,GAAQ,WAAW,IAAIrB,EAAEC,EAAEuC,EAAE8B,EAAE,OAAOhC,EAAEjB,MAAI,SAAWiB,GAAG,OAAOA,EAAEW,OAAO,KAAK,EAAE,OAAOjD,OAAE,IAASqB,KAAKyD,eAAerC,EAAEpB,KAAKyD,eAAe7E,EAAE0C,EAAE,IAAI5C,EAAE,QAAQyC,EAAExC,EAAEkE,YAAYjE,IAAI,CAAC,IAAIqE,EAAEuB,KAAKM,MAAM3D,IAAIuC,KAAK1D,KAAK0D,GAAG,CAAC,EAAE,GAAG,CAAC,EAAEvE,EAAES,UAAUa,KAAKwC,EAAEyB,MAAM,KAAK,EAAEzD,EAAEY,OAAO7B,KAAKuD,eAAezC,OAAOmC,EAAEyB,KAAK/F,EAAEoE,eAAenE,GAAGO,EAAES,UAAUe,OAAOsC,EAAEyB,KAAKjG,EAAE+G,gBAAgBvE,EAAEW,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,GAAI,GAAG,GAAE,EAAEnD,EAAEuG,cAAc,SAAStG,GAAG,IAAI,IAAIC,EAAEgF,KAAKC,MAAM,IAAIhF,EAAEF,EAAES,EAAE,GAAG8B,EAAE,IAAI,CAAC,IAAIE,EAAEvC,EAAEgE,QAAQ3B,GAAG,GAAG,OAAOE,EAAE,MAAMhC,EAAEiD,KAAKjB,GAAGF,GAAG,CAAC,IAAI,IAAIG,GAAE,EAAG6B,EAAE,EAAEA,EAAE9D,EAAEC,OAAO6D,IAAI,CAAC,IAAIC,EAAE/D,EAAE8D,GAAG,GAAGC,EAAEyC,SAASrE,GAAG,CAAC,IAAIgC,EAAE1E,EAAEiE,YAAYK,GAAG,GAAG,OAAOI,EAAE,CAAC,IAAIe,EAAEG,KAAKM,MAAMxB,SAAI,IAASe,EAAEQ,eAAeR,EAAEO,aAAajG,QAAG,IAAS0F,EAAEQ,eAAeR,EAAEQ,cAAclG,KAAKC,EAAEmE,eAAeG,GAAG9B,GAAE,EAAG,CAAC,CAAC,CAACA,GAAG3C,EAAE+G,eAAe,EAAE/G,EAAE2F,aAAQ,EAAO3F,CAAC,CAA/2H,GAAm3HE,EAAEiB,QAAQ0D,CAAE,KAAI,MAAMnC,EAAE,CAACyE,iBAAiB,IAAItE,EAAE,CAACuE,KAAK,eAAeC,QAAQ,SAAS1E,EAAE,IAAIuC,KAAKC,MAAM,MAAMX,UAAUX,MAAM,WAAAyD,CAAYtH,EAAEC,GAAGsH,MAAMtH,GAAGsB,KAAKiG,MAAMxH,EAAEuB,KAAKkG,kBAAkBxH,EAAEG,OAAOsH,eAAenG,KAAKiD,EAAEnE,UAAU,CAAC,kBAAOsH,EAAaH,MAAMxH,EAAEyH,kBAAkBxH,IAAI,OAAO,IAAIuE,EAAExE,EAAEC,EAAE,EAAE,MAAMwE,UAAUD,EAAE,WAAA8C,CAAYtH,EAAEC,EAAEC,EAAEC,EAAE,MAAMoH,MAAMvH,EAAEC,GAAGsB,KAAKqG,MAAM1H,EAAEqB,KAAKsG,SAAS1H,EAAEC,OAAOsH,eAAenG,KAAKkD,EAAEpE,UAAU,EAAE,MAAMwE,UAAUL,EAAE,WAAA8C,GAAcC,MAAM,UAAU,WAAWnH,OAAOsH,eAAenG,KAAKsD,EAAExE,UAAU,EAAE,MAAMuF,UAAUf,EAAE,WAAAyC,CAAYtH,GAAGuH,QAAQhG,KAAKuG,MAAM9H,EAAEI,OAAOsH,eAAenG,KAAKqE,EAAEvF,UAAU,EAAE,MAAMwF,UAAUrB,EAAE,WAAA8C,CAAYtH,GAAGuH,MAAM,YAAY,gBAAgBhG,KAAKuG,MAAM9H,EAAEI,OAAOsH,eAAenG,KAAKsE,EAAExF,UAAU,EAAE,MAAMyF,UAAUtB,EAAE,WAAA8C,CAAYtH,EAAEC,EAAEC,GAAGqH,MAAMvH,EAAEC,GAAGsB,KAAKwG,UAAU7H,EAAEE,OAAOsH,eAAenG,KAAKuE,EAAEzF,UAAU,EAAE,MAAM2H,UAAUxD,EAAE,WAAA8C,CAAYtH,EAAEC,GAAGsH,MAAM,wBAAwB,qCAAqCU,EAAEjI,EAAE,CAAC,yBAAyBiI,EAAEhI,QAAQsB,KAAK2G,SAASlI,EAAEuB,KAAK4G,MAAMlI,EAAEG,OAAOsH,eAAenG,KAAKyG,EAAE3H,UAAU,EAAE,SAAS4H,EAAEjI,EAAEC,EAAE,IAAI,OAAOD,IAAIC,EAAEiH,SAASlH,GAAGA,EAAE,EAAE,CAAC,MAAMoI,EAAE,IAAIrH,OAAOsH,OAAOC,EAAE,KAAK,MAAMtI,EAAE,qEAAqE,IAAIC,EAAE,GAAG,OAAOsI,MAAMC,KAAKJ,IAAIK,gBAAgB,IAAIC,WAAW,MAAMzB,SAAS/G,GAAGD,GAAGD,EAAEE,EAAEF,MAAYC,GAAG0I,EAAE3I,GAAG4I,KAAK5I,GAAG6I,EAAE5I,IAAI,IAAI6I,SAAS5I,GAAGD,EAAEE,EAAEH,EAAEC,EAAE,CAAC,aAAa,OAAO,IAAI8I,gBAAgB,CAAC/I,GAAGI,OAAO4I,KAAKhJ,GAAG8G,QAAQ7G,QAAG,IAASD,EAAEC,KAAKgJ,QAAM,CAAGhJ,EAAEC,IAAIE,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAE,EAACjJ,GAAG,CAACC,CAACA,GAAGF,EAAEE,MAAM,CAAA,GAA5G,CAAiHE,OAAO8I,OAAO,CAACC,UAAUjJ,GAAGC,KAAKiF,UAAQ,EAAIgE,EAAEpJ,GAAG,CAACA,GAAGqJ,mBAAmBC,KAAKtJ,GAAGuJ,MAAM,IAAIC,KAAKxJ,GAAG,KAAK,KAAKA,EAAEyJ,WAAW,GAAGrE,SAAS,KAAK4B,OAAO,KAAK0C,KAAK,KAA1G,CAAgH1J,EAAE2J,QAAQ,KAAK,KAAKA,QAAQ,KAAK,MAAMC,EAAEC,MAAM7J,EAAEC,KAAK,MAAMC,QAAQ4J,MAAM9J,EAAEC,GAAG,MAAM,CAAC8J,GAAG7J,EAAE6J,GAAGC,WAAW9J,EAAE8J,OAAM,EAAmiBC,EAAEJ,MAAM7J,EAAEC,EAAEC,EAAEC,EAAEO,EAAE8B,EAAEE,EAAE,MAAMhC,EAA5UmJ,OAAM7J,EAAEC,EAAEC,EAAEC,EAAEO,EAAE8B,EAAEE,KAAK,OAAOG,EAAE,CAACqH,KAAK,CAAChC,SAASjI,EAAEkI,MAAMjI,GAAGiK,QAAQzJ,EAAE0J,SAASpK,EAAEqK,aAAalK,EAAEmK,YAAY5H,GAAGC,EAAEH,EAAE,IAAIP,SAAO,SAAWjC,EAAEC,GAAG,MAAMC,EAAE,IAAIqK,eAAerK,EAAEsK,MAAMC,UAAU,SAAStK,GAAGA,EAAEuK,KAAKlD,MAAMvH,EAAE,IAAI4D,MAAM1D,EAAEuK,KAAKlD,QAAQxH,EAAEG,EAAEuK,MAAMxK,EAAEsK,MAAMG,OAAO,EAAEhI,EAAEiI,YAAY/H,EAAE,CAAC3C,EAAE2K,OAAQ,IAAG,IAAIhI,EAAEF,GAAiCmI,CAAE9K,EAAEC,EAAEC,EAAEC,EAAEuC,EAAEhC,EAAE8B,GAA1kBqH,OAAM7J,EAAEC,EAAEC,KAAK,MAAMC,EAAE,IAAI4K,gBAAgB,IAAIrK,EAAE,OAAOT,EAAE+K,OAAO7K,EAAE6K,OAAO/I,QAAQgJ,KAAK,CAACrB,EAAE5J,EAAEC,GAAG,IAAIgC,SAAO,CAAGjC,EAAEC,KAAKS,EAAE0B,YAAU,KAAOjC,EAAE+K,QAAQjL,EAAE,IAAI4D,MAAM,kCAAmC,GAAE3D,EAAG,MAAKiL,cAAczE,aAAahG,EAAG,GAAE,EAAmW0K,CAAEpL,EAAEG,EAAEuC,GAAGmH,eAAewB,EAAEpL,EAAEC,GAAG,IAAIoL,QAAQnL,EAAEgK,QAAQzJ,EAAEwH,SAAS1F,EAAE2F,MAAMzF,EAAE6I,YAAY5I,EAAE2H,YAAY7F,GAAGxE,EAAE4E,EAAE7E,EAAEC,EAAE,CAAC,UAAU,UAAU,WAAW,QAAQ,cAAc,gBAAgB,MAAM2F,EAAEnB,EAAEoE,EAAEhE,GAAGkB,KAAKC,UAAUnB,GAAG,aAAagF,eAAe5J,EAAEC,EAAEC,EAAEO,EAAE8B,EAAEE,EAAEG,GAAG,IAAIF,EAAE8B,EAAE,KAAK,IAAI,IAAIzE,EAAE,EAAEA,EAAE,EAAEA,IAAI,IAAI2C,QAAQsH,EAAEhK,EAAEE,EAAEO,EAAE8B,EAAEE,EAAEG,EAAE3C,GAAGuE,EAAE,KAAK,KAAK,CAAC,MAAMzE,GAAGyE,EAAEzE,CAAC,CAAC,GAAGyE,EAAE,MAAMA,EAAE,MAAMI,EAAElC,EAAEqH,MAAMxC,MAAM5B,EAAE6B,kBAAkB5B,GAAGhB,EAAEoD,EAAEjI,EAAE6E,EAAE,CAAC,QAAQ,uBAAuBkF,GAAG3B,GAAGzF,EAAE,IAAIyF,EAAE,CAAC,MAAMpI,EAAE6F,GAAG,+BAA+B5F,IAAI,GAAG,iBAAiB2F,EAAE,MAAM,IAAIE,EAAEF,EAAE5F,EAAEiI,EAAEF,WAAW,GAAG,0BAA0BnC,EAAE,MAAM,IAAIoC,EAAE7H,EAAEO,GAAG,MAAM,IAAI8D,EAAEoB,GAAG,gBAAgB5F,EAAE,CAAC,OAAOiI,CAAC,CAAla4B,CAAoa,GAAG1J,gBAAgBO,EAAE8B,GAAG,UAAUE,EAAE,CAAC8I,OAAO,OAAOC,KAAK7F,EAAE8F,QAAQ,CAAC,eAAejH,EAAE,oCAAoC,mBAAmB,eAAemE,KAAK7C,KAAKC,UAAUrD,GAAGE,MAAM3C,EAAEuE,EAAE,CAAC,MAAMkH,EAAE,IAAI3L,KAAK,OAAOC,EAAED,EAAE8G,OAAO8E,SAASlC,KAAK,KAAKmC,OAAOtC,MAAM,OAAOhB,MAAMC,KAAK,IAAIzD,IAAI9E,KAAKyJ,KAAK,KAAK,IAAIzJ,GAAG,MAAM6L,EAAE,WAAAxE,CAAYtH,EAAEC,EAAE,iBAAiBC,GAAGqB,KAAKwK,OAAO9L,EAAEsB,KAAKyK,OAAO9L,EAAEqB,KAAKuH,SAAS9I,EAAE8I,SAASvH,KAAK4G,MAAMnI,EAAEmI,MAAM5G,KAAK2G,SAASlI,EAAEkI,QAAQ,CAAC,KAAA+D,GAAQ,MAAM,CAAC1K,KAAKwK,OAAOxK,KAAKuH,SAASvH,KAAK2G,SAAS3G,KAAK4G,MAAM5G,KAAKyK,QAAQlF,OAAO8E,SAASlC,KAAK,KAAK,CAAC,cAAOwC,CAAQlM,GAAG,MAAMC,EAAEC,EAAEC,EAAEO,GAAGV,EAAEuJ,MAAM,MAAM,OAAO,IAAIuC,EAAE,CAAChD,SAAS5I,EAAEiI,MAAMzH,EAAEwH,SAAS/H,GAAGF,EAAE,CAAC,qBAAOkM,CAAenM,GAAG,MAAMmI,MAAMlI,EAAEiI,SAAShI,EAAEiJ,UAAUhJ,GAAGH,EAAE,OAAO,IAAI8L,EAAE,CAAC3D,MAAMlI,EAAEiI,SAAShI,EAAE4I,SAAS3I,GAAG,EAAE,MAAMiM,EAAE,GAAAxK,CAAI5B,EAAEC,GAAG+D,aAAaE,QAAQlE,EAAE+F,KAAKC,UAAU/F,GAAG,CAAC,GAAA0B,CAAI3B,GAAG,MAAMC,EAAEc,OAAOiD,aAAaF,QAAQ9D,GAAG,GAAGC,EAAE,IAAI,OAAO8F,KAAKM,MAAMpG,EAAE,CAAC,MAAMD,GAAG,MAAM,CAAC,CAAC,MAAAqM,CAAOrM,GAAGgE,aAAaC,WAAWjE,EAAE,CAAC,OAAAsM,GAAU,OAAOlM,OAAO4I,KAAKjI,OAAOiD,cAAc8C,QAAQ9G,GAAGA,EAAEuM,WAAW,mBAAmB,EAAE,MAAMC,EAAE,WAAAlF,GAAc/F,KAAKkL,cAAc,WAAW,IAAIzM,EAAE,CAAE,EAAC,MAAM,CAAC,GAAA4B,CAAI3B,EAAEC,GAAGF,EAAEC,GAAGC,CAAC,EAAE,GAAAyB,CAAI1B,GAAG,MAAMC,EAAEF,EAAEC,GAAG,GAAGC,EAAE,OAAOA,CAAC,EAAE,MAAAmM,CAAOpM,UAAUD,EAAEC,EAAE,EAAEqM,QAAQ,IAAIlM,OAAO4I,KAAKhJ,GAAG,CAAjI,EAAoI,EAAE,MAAM0M,EAAE,WAAApF,CAAYtH,EAAEC,EAAEC,GAAGqB,KAAKoL,MAAM3M,EAAEuB,KAAKqL,YAAY3M,EAAEsB,KAAKsL,YAAY3M,GAAGyC,CAAC,CAAC,gBAAMmK,CAAW9M,EAAEC,EAAEC,GAAG,IAAIC,EAAE,MAAMO,EAAEa,KAAKwL,mBAAmB/M,SAASuB,KAAKoL,MAAM/K,IAAIlB,EAAE,CAACsM,SAAS/M,EAAEgN,aAAa/M,UAAU,QAAQC,EAAEoB,KAAKqL,mBAAc,IAASzM,OAAE,EAAOA,EAAEmG,IAAI5F,GAAG,CAAC,gBAAMwM,CAAWlN,GAAG,MAAMC,QAAQsB,KAAKoL,MAAMhL,IAAIJ,KAAKwL,mBAAmB/M,EAAE8I,WAAW,IAAI7I,GAAGD,EAAEmI,OAAOnI,EAAEkI,SAAS,CAAC,MAAMjI,QAAQsB,KAAKI,IAAI3B,GAAG,IAAIC,EAAE,OAAO,IAAIA,EAAE+M,WAAW/M,EAAEgN,aAAa,OAAO,MAAM,CAACD,SAAS/M,EAAE+M,SAASC,aAAahN,EAAEgN,aAAa,CAAC,GAAGhN,EAAE,MAAM,CAAC+M,SAAS/M,EAAE+M,SAASC,aAAahN,EAAEgN,aAAa,CAAC,SAAMtL,CAAI3B,EAAEC,EAAE,GAAG,IAAIC,EAAE,IAAIC,QAAQoB,KAAKoL,MAAMhL,IAAI3B,EAAEiM,SAAS,IAAI9L,EAAE,CAAC,MAAMF,QAAQsB,KAAK4L,eAAe,IAAIlN,EAAE,OAAO,MAAMC,EAAEqB,KAAK6L,sBAAsBpN,EAAEC,GAAGC,IAAIC,QAAQoB,KAAKoL,MAAMhL,IAAIzB,GAAG,CAAC,IAAIC,EAAE,OAAO,MAAMO,QAAQa,KAAKsL,cAAcrK,EAAEkC,KAAKC,MAAMjE,EAAE,KAAK,OAAOP,EAAEkN,UAAUpN,EAAEuC,EAAErC,EAAEsL,KAAK6B,eAAenN,EAAEsL,KAAK,CAAC6B,cAAcnN,EAAEsL,KAAK6B,qBAAqB/L,KAAKoL,MAAM/K,IAAI5B,EAAEiM,QAAQ9L,GAAGA,EAAEsL,aAAalK,KAAKoL,MAAMN,OAAOrM,EAAEiM,oBAAoB,QAAQ/L,EAAEqB,KAAKqL,mBAAc,IAAS1M,OAAE,EAAOA,EAAEmM,OAAOrM,EAAEiM,WAAW9L,EAAEsL,IAAI,CAAC,SAAM7J,CAAI5B,GAAG,IAAIC,EAAE,MAAMC,EAAE,IAAI4L,EAAE,CAAChD,SAAS9I,EAAEmJ,UAAUhB,MAAMnI,EAAEmI,MAAMD,SAASlI,EAAEkI,WAAW/H,QAAQoB,KAAKgM,eAAevN,SAASuB,KAAKoL,MAAM/K,IAAI1B,EAAE+L,QAAQ9L,SAAS,QAAQF,EAAEsB,KAAKqL,mBAAc,IAAS3M,OAAE,EAAOA,EAAEqG,IAAIpG,EAAE+L,SAAS,CAAC,WAAMlI,CAAM/D,GAAG,IAAIC,EAAE,MAAMC,QAAQqB,KAAK4L,eAAejN,UAAUA,EAAE4G,QAAQ7G,IAAID,GAAGC,EAAEiH,SAASlH,KAAKiJ,cAAcjJ,EAAEC,WAAWD,QAAQuB,KAAKoL,MAAMN,OAAOpM,EAAG,GAAEgC,QAAQuL,iBAAiB,QAAQvN,EAAEsB,KAAKqL,mBAAc,IAAS3M,OAAE,EAAOA,EAAE8D,SAAS,CAAC,oBAAMwJ,CAAevN,GAAG,MAAMC,QAAQsB,KAAKsL,cAAc,MAAM,CAACpB,KAAKzL,EAAEqN,UAAU3I,KAAKC,MAAM1E,EAAE,KAAKD,EAAEyN,WAAW,CAAC,kBAAMN,GAAe,IAAInN,EAAE,OAAOuB,KAAKqL,YAAY,QAAQ5M,QAAQuB,KAAKqL,YAAYjL,aAAQ,IAAS3B,OAAE,EAAOA,EAAEgJ,KAAKzH,KAAKoL,MAAML,QAAQ/K,KAAKoL,MAAML,eAAU,CAAM,CAAC,kBAAAS,CAAmB/M,GAAG,OAAO,IAAI8L,EAAE,CAAChD,SAAS9I,GAAG,iBAAiB,YAAYiM,OAAO,CAAC,qBAAAmB,CAAsBpN,EAAEC,GAAG,OAAOA,EAAE6G,QAAQ7G,IAAI,IAAIC,EAAE,MAAMC,EAAE2L,EAAEI,QAAQjM,GAAGS,EAAE,IAAIqE,IAAI5E,EAAEgI,OAAOhI,EAAEgI,MAAMoB,MAAM,MAAM/G,GAAG,QAAQtC,EAAEF,EAAEmI,aAAQ,IAASjI,OAAE,EAAOA,EAAEqJ,MAAM,OAAO,GAAG7G,EAAEvC,EAAEgI,OAAO3F,EAAEyG,QAAQ,CAACjJ,EAAEC,IAAID,GAAGU,EAAEqB,IAAI9B,KAAI,GAAI,MAAM,mBAAmBE,EAAE4L,QAAQ5L,EAAE2I,WAAW9I,EAAE8I,UAAU3I,EAAE+H,WAAWlI,EAAEkI,UAAUxF,CAAE,IAAG,EAAE,EAAE,MAAMgL,EAAE,WAAApG,CAAYtH,EAAEC,EAAEC,GAAGqB,KAAKoM,QAAQ3N,EAAEuB,KAAKuH,SAAS7I,EAAEsB,KAAKqM,aAAa1N,EAAEqB,KAAKsM,WAAW,gBAAgBtM,KAAKuH,UAAU,CAAC,MAAAgF,CAAO9N,GAAGuB,KAAKoM,QAAQI,KAAKxM,KAAKsM,WAAW7N,EAAE,CAACgO,gBAAgB,EAAEJ,aAAarM,KAAKqM,cAAc,CAAC,GAAAjM,GAAM,OAAOJ,KAAKoM,QAAQhM,IAAIJ,KAAKsM,WAAW,CAAC,MAAAxB,GAAS9K,KAAKoM,QAAQtB,OAAO9K,KAAKsM,WAAW,CAACD,aAAarM,KAAKqM,cAAc,EAAE,MAAMK,EAAEjO,GAAG,iBAAiBA,EAAEkO,EAAE,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,QAAQ,YAAY,UAAU,SAAS,MAAM,MAAM,UAAU,MAAM,eAAe,WAAW,aAAa,eAAe,iBAAiB,OAAO,OAAO,MAAM,SAAS,MAAM,MAAM,MAAM,MAAM,MAAM,OAAqwH,IAAIC,EAAEhO,GAAG,SAASH,EAAEE,GAAG,IAAIC,EAAEF,GAAGA,EAAEmO,UAAU,WAAW,OAAOjO,EAAEC,OAAO8I,QAAQ,SAASlJ,GAAG,IAAI,IAAIC,EAAEC,EAAE,EAAEC,EAAEkO,UAAU1N,OAAOT,EAAEC,EAAED,IAAI,IAAI,IAAIQ,KAAKT,EAAEoO,UAAUnO,GAAGE,OAAOC,UAAUC,eAAeC,KAAKN,EAAES,KAAKV,EAAEU,GAAGT,EAAES,IAAI,OAAOV,CAAC,EAAEG,EAAE8C,MAAM1B,KAAK8M,UAAU,EAAE,SAAS3N,EAAEV,EAAEC,GAAG,IAAIA,EAAE,MAAM,GAAG,IAAIC,EAAE,KAAKF,EAAE,OAAM,IAAKC,EAAEC,EAAEA,EAAE,IAAID,CAAC,CAAC,SAASuC,EAAExC,EAAEC,EAAEC,GAAG,OAAOoO,mBAAmBtO,GAAG2J,QAAQ,2BAA2BN,oBAAoBM,QAAQ,MAAM,OAAOA,QAAQ,MAAM,OAAO,IAAI2E,mBAAmBrO,GAAG0J,QAAQ,4DAA4DN,oBAAoB,SAASrJ,GAAG,GAAG,iBAAiBA,EAAEuO,QAAQ,CAAC,IAAItO,EAAE,IAAIiF,KAAKjF,EAAEuO,gBAAgBvO,EAAEwO,kBAAkB,MAAMzO,EAAEuO,SAASvO,EAAEuO,QAAQtO,CAAC,CAAC,OAAOS,EAAE,UAAUV,EAAEuO,QAAQvO,EAAEuO,QAAQG,cAAc,IAAIhO,EAAE,SAASV,EAAE2O,QAAQjO,EAAE,OAAOV,EAAE4O,MAAMlO,EAAE,SAASV,EAAE6O,QAAQnO,EAAE,WAAWV,EAAE8O,SAAS,CAAzQ,CAA2Q5O,EAAE,CAAC,SAASwC,EAAE1C,GAAG,IAAI,IAAIC,EAAE,GAAGC,EAAEF,EAAEA,EAAEuJ,MAAM,MAAM,GAAGpJ,EAAE,mBAAmBO,EAAE,EAAEA,EAAER,EAAES,OAAOD,IAAI,CAAC,IAAI8B,EAAEtC,EAAEQ,GAAG6I,MAAM,KAAK7G,EAAEF,EAAEwE,MAAM,GAAG0C,KAAK,KAAK,MAAMhH,EAAEqM,OAAO,KAAKrM,EAAEA,EAAEsE,MAAM,GAAG,IAAI,IAAI/G,EAAEuC,EAAE,GAAGmH,QAAQxJ,EAAEkJ,qBAAqB3G,EAAEiH,QAAQxJ,EAAEkJ,mBAAmB,CAAC,MAAMrJ,GAAE,CAAE,CAAC,OAAOC,CAAC,CAAC,SAAS4C,IAAI,OAAOH,EAAEsM,SAASC,OAAO,CAAC,SAAStM,EAAE3C,EAAEC,EAAEC,GAAG8O,SAASC,OAAOzM,EAAExC,EAAEC,EAAEE,EAAE,CAACyO,KAAK,KAAK1O,GAAG,CAACA,EAAEgB,YAAW,EAAGhB,EAAEgP,OAAO1M,EAAEtC,EAAEmG,MAAM3D,EAAExC,EAAEiP,OAAOtM,EAAE3C,EAAEyB,IAAI,SAAS3B,GAAG,OAAO6C,IAAI7C,EAAE,EAAEE,EAAE0B,IAAIe,EAAEzC,EAAEmM,OAAO,SAASrM,EAAEC,GAAG0C,EAAE3C,EAAE,GAAGG,EAAEA,EAAE,CAAA,EAAGF,GAAG,CAACsO,SAAS,IAAI,CAAE,IAAGrO,EAAEiO,GAAGA,EAAEe,OAAOf,EAAE9H,MAAM8H,EAAEgB,OAAO,IAAIC,EAAEjB,EAAExM,IAAI0N,EAAElB,EAAEvM,IAAI0N,EAAEnB,EAAE9B,OAAO,MAAMkD,EAAE,CAAC,GAAA5N,CAAI3B,GAAG,MAAMC,EAAEmP,EAAEpP,GAAG,QAAG,IAASC,EAAE,OAAO8F,KAAKM,MAAMpG,EAAE,EAAE,IAAA8N,CAAK/N,EAAEC,EAAEC,GAAG,IAAIC,EAAE,CAAE,EAAC,WAAWY,OAAOyO,SAASC,WAAWtP,EAAE,CAAC0O,QAAO,EAAGC,SAAS,UAAU,MAAM5O,OAAE,EAAOA,EAAE8N,mBAAmB7N,EAAEoO,QAAQrO,EAAE8N,kBAAkB,MAAM9N,OAAE,EAAOA,EAAE0N,gBAAgBzN,EAAEwO,OAAOzO,EAAE0N,cAAcyB,EAAErP,EAAE+F,KAAKC,UAAU/F,GAAGE,EAAE,EAAE,MAAAkM,CAAOrM,EAAEC,GAAG,IAAIC,EAAE,CAAA,GAAI,MAAMD,OAAE,EAAOA,EAAE2N,gBAAgB1N,EAAEyO,OAAO1O,EAAE2N,cAAc0B,EAAEtP,EAAEE,EAAE,GAAGwP,EAAE,CAAC/N,IAAI3B,GAAWuP,EAAE5N,IAAI3B,IAAauP,EAAE5N,IAAI,WAAW3B,KAAM,IAAA+N,CAAK/N,EAAEC,EAAEC,GAAG,IAAIC,EAAE,CAAA,EAAG,WAAWY,OAAOyO,SAASC,WAAWtP,EAAE,CAAC0O,QAAO,KAAM,MAAM3O,OAAE,EAAOA,EAAE8N,mBAAmB7N,EAAEoO,QAAQrO,EAAE8N,kBAAkB,MAAM9N,OAAE,EAAOA,EAAE0N,gBAAgBzN,EAAEwO,OAAOzO,EAAE0N,cAAcyB,EAAE,WAAWrP,IAAI+F,KAAKC,UAAU/F,GAAGE,GAAGoP,EAAExB,KAAK/N,EAAEC,EAAEC,EAAE,EAAE,MAAAmM,CAAOrM,EAAEC,GAAG,IAAIC,EAAE,IAAI,MAAMD,OAAE,EAAOA,EAAE2N,gBAAgB1N,EAAEyO,OAAO1O,EAAE2N,cAAc0B,EAAEtP,EAAEE,GAAGqP,EAAElD,OAAOrM,EAAEC,GAAGsP,EAAElD,OAAO,WAAWrM,IAAIC,EAAE,GAAG0P,EAAE,CAAC,GAAAhO,CAAI3B,GAAG,GAAG,oBAAoB4P,eAAe,OAAO,MAAM3P,EAAE2P,eAAe9L,QAAQ9D,GAAG,OAAO,MAAMC,EAAE8F,KAAKM,MAAMpG,QAAG,CAAM,EAAE,IAAA8N,CAAK/N,EAAEC,GAAG2P,eAAe1L,QAAQlE,EAAE+F,KAAKC,UAAU/F,GAAG,EAAE,MAAAoM,CAAOrM,GAAG4P,eAAe3L,WAAWjE,EAAE,GAAmZ,IAAU6P,EAAEC,EAA4+F,SAAS9P,GAAG,OAAO6P,EAAEA,GAA75G,SAAW7P,EAAEC,EAAEC,GAAG,IAAIC,OAAE,IAASF,EAAE,KAAKA,EAAES,EAAE,SAASV,EAAEC,GAAG,IAAIC,EAAEoJ,KAAKtJ,GAAG,GAAGC,EAAE,CAAC,IAAI,IAAIE,EAAE,IAAIuI,WAAWxI,EAAES,QAAQD,EAAE,EAAE8B,EAAEtC,EAAES,OAAOD,EAAE8B,IAAI9B,EAAEP,EAAEO,GAAGR,EAAEuJ,WAAW/I,GAAG,OAAOqP,OAAOC,aAAa/M,MAAM,KAAK,IAAIgN,YAAY9P,EAAE+P,QAAQ,CAAC,OAAOhQ,CAAC,CAA1L,CAA4LF,OAAE,IAASE,GAAGA,GAAGsC,EAAE9B,EAAEF,QAAQ,KAAK,IAAI,EAAEkC,EAAEhC,EAAEyP,UAAU3N,IAAIrC,EAAE,wBAAwBA,EAAE,IAAI0C,EAAE,IAAIuN,KAAK,CAAC1N,GAAG,CAAC2N,KAAK,2BAA2B,OAAOC,IAAIC,gBAAgB1N,EAAE,CAAihG2N,CAA//F,29FAA69F,KAAO,OAAoC,IAAIC,OAAOZ,EAAE7P,EAAE,EAAG,MAAM0Q,EAAE,CAAA,EAAG,MAAMC,EAAE,WAAArJ,CAAYtH,EAAEC,GAAGsB,KAAKoL,MAAM3M,EAAEuB,KAAKuH,SAAS7I,EAAEsB,KAAKqP,YAAYrP,KAAKsP,sBAAsBtP,KAAKuH,SAAS,CAAC,SAAMxC,CAAItG,GAAG,IAAIC,EAAE,MAAMC,EAAE,IAAI6E,KAAK,QAAQ9E,QAAQsB,KAAKoL,MAAMhL,IAAIJ,KAAKqP,oBAAe,IAAS3Q,OAAE,EAAOA,EAAE+I,OAAO,IAAI9I,EAAEoG,IAAItG,SAASuB,KAAKoL,MAAM/K,IAAIL,KAAKqP,YAAY,CAAC5H,KAAK,IAAI9I,IAAI,CAAC,YAAMmM,CAAOrM,GAAG,MAAMC,QAAQsB,KAAKoL,MAAMhL,IAAIJ,KAAKqP,aAAa,GAAG3Q,EAAE,CAAC,MAAMC,EAAE,IAAI6E,IAAI9E,EAAE+I,MAAM,OAAO9I,EAAEmC,OAAOrC,GAAGE,EAAE4Q,KAAK,QAAQvP,KAAKoL,MAAM/K,IAAIL,KAAKqP,YAAY,CAAC5H,KAAK,IAAI9I,WAAWqB,KAAKoL,MAAMN,OAAO9K,KAAKqP,YAAY,CAAC,CAAC,GAAAjP,GAAM,OAAOJ,KAAKoL,MAAMhL,IAAIJ,KAAKqP,YAAY,CAAC,KAAA7M,GAAQ,OAAOxC,KAAKoL,MAAMN,OAAO9K,KAAKqP,YAAY,CAAC,qBAAAC,CAAsB7Q,GAAG,MAAM,mBAAmBA,GAAG,EAAE,MAAM+Q,EAAE,CAACC,OAAO,KAAI,IAAKxE,GAAGC,cAAcwE,aAAa,IAAI,IAAI7E,GAAG8E,EAAElR,GAAG+Q,EAAE/Q,GAAGmR,EAAElR,IAAI,MAAMmR,QAAQlR,EAAEmR,WAAWlR,GAAGF,EAAES,EAAEV,EAAEC,EAAE,CAAC,UAAU,eAAe,OAAOG,OAAO8I,OAAO9I,OAAO8I,OAAO,GAAGxI,GAAG,CAAC0Q,SAAQ,IAAKlR,GAAGA,EAAEA,EAAEC,GAAE,EAAGmR,EAAG,IAAI9O,EAAE,MAAM+O,GAAG,WAAAjK,CAAYtH,GAAG,IAAIC,EAAEC,EAAE,GAAGqB,KAAKiQ,WAAU,IAAKhF,GAAGC,cAAclL,KAAKkQ,eAAe,CAACC,oBAAoB,CAACvJ,MAAM,wBAAwBwJ,0BAAyB,EAAGrH,aAAY,GAAI/I,KAAKqQ,uBAAuB/H,gBAAgByH,EAAG/L,YAAY,+BAA+BxE,OAAOyF,oBAAoB,WAAWjF,KAAKqQ,uBAAsB,EAAGrQ,KAAKsQ,QAAQzR,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAG3H,KAAKkQ,gBAAgBzR,GAAG,CAAC0R,oBAAoBtR,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAE,EAAC3H,KAAKkQ,eAAeC,qBAAqB1R,EAAE0R,uBAAuB,oBAAoB3Q,QAAQ,MAAM,IAAIqH,IAAI,MAAM,IAAIvE,MAAM,4EAA4E,QAAG,IAASuE,IAAI0J,OAAO,MAAM,IAAIjO,MAAM,iMAAkM,EAAnV,GAAuV7D,EAAE2M,OAAO3M,EAAE+R,eAAeC,QAAQC,KAAK,8IAA8IjS,EAAE2M,MAAMzM,EAAEF,EAAE2M,UAAU,CAAC,GAAG1M,EAAED,EAAE+R,eAAe,UAAUb,EAAEjR,GAAG,MAAM,IAAI4D,MAAM,2BAA2B5D,MAAMC,EAAEgR,EAAEjR,EAAFiR,EAAM,CAAC3P,KAAK2Q,cAAclS,EAAEmS,qBAAqB,IAAInS,EAAEmS,qBAAqB,IAAI5Q,KAAK6Q,eAAc,IAAKpS,EAAEqS,qBAAqB9C,EAAEG,EAAEnO,KAAK+Q,kBAAkB,SAAS/Q,KAAKsQ,QAAQ/I,6BAA6BvH,KAAKgR,0BAA0B,KAAI,SAA+BhR,KAAKsQ,QAAQ/I,4BAAhD,GAA0DvH,KAAKiR,uBAAuBxS,EAAEwS,wBAAwB,EAAE,MAAMrS,EAAEH,EAAEyS,0BAA0BlR,KAAK6Q,cAAczC,EAAE,IAAIjP,EAAEa,KAAK4G,MAAMwD,EAAE,SAASpK,KAAKsQ,QAAQH,oBAAoBvJ,MAAM5G,KAAKsQ,QAAQa,iBAAiB,iBAAiB,IAAInR,KAAKoR,mBAAmB,IAAIjF,EAAEvN,EAAEoB,KAAKsQ,QAAQ/I,SAASvH,KAAKsQ,QAAQjE,cAAcrM,KAAKsL,YAAYtL,KAAKsQ,QAAQhF,aAAalK,EAAEpB,KAAKqR,aAAa,IAAIlG,EAAExM,EAAEA,EAAEoM,aAAQ,EAAO,IAAIqE,EAAEzQ,EAAEqB,KAAKsQ,QAAQ/I,UAAUvH,KAAKsL,aAAatL,KAAKsR,WAAWnS,EAAEa,KAAKsQ,QAAQlD,OAAO,eAAemE,KAAKpS,GAAGA,EAAE,WAAWA,KAAKa,KAAKwR,YAAY,EAAE/S,EAAEC,IAAID,EAAEA,EAAEuM,WAAW,YAAYvM,EAAE,WAAWA,KAAK,GAAGC,KAAxD,CAA8DsB,KAAKsQ,QAAQmB,OAAOzR,KAAKsR,WAAW,oBAAoB9R,QAAQA,OAAO0P,QAAQlP,KAAKsQ,QAAQa,kBAAkB,WAAWzS,IAAIsB,KAAKsQ,QAAQoB,UAAU1R,KAAK2R,OAAO,IAAIzC,OAAOlP,KAAKsQ,QAAQoB,WAAW1R,KAAK2R,OAAO,IAAIpD,EAAE,CAAC,IAAAqD,CAAKnT,GAAG,MAAMC,EAAEqO,mBAAmB1F,KAAK7C,KAAKC,UAAUzE,KAAKsQ,QAAQtG,aAAa1I,KAAK,MAAM,GAAGtB,KAAKsR,YAAY7S,iBAAiBC,GAAG,CAAC,aAAAmT,CAAcpT,GAAG,OAAOuB,KAAK4R,KAAK,cAActK,EAAE7I,KAAK,CAAC,oBAAMqT,CAAerT,EAAEC,EAAEC,GAAG,MAAMC,QAAQoB,KAAKsL,cAAc,MAA/+Z7M,KAAI,IAAIA,EAAEgN,SAAS,MAAM,IAAInJ,MAAM,oCAAoC,MAAM5D,EAAE,CAACD,IAAI,MAAMC,EAAED,EAAEuJ,MAAM,MAAMrJ,EAAEC,EAAEO,GAAGT,EAAE,GAAG,IAAIA,EAAEU,SAAST,IAAIC,IAAIO,EAAE,MAAM,IAAImD,MAAM,iCAAiC,MAAMrB,EAAEuD,KAAKM,MAAM+C,EAAEjJ,IAAIuC,EAAE,CAAC4Q,MAAMtT,GAAG6C,EAAE,GAAG,OAAOzC,OAAO4I,KAAKxG,GAAGyE,SAASjH,IAAI0C,EAAE1C,GAAGwC,EAAExC,GAAGkO,EAAEhH,SAASlH,KAAK6C,EAAE7C,GAAGwC,EAAExC,GAAI,IAAG,CAACuT,QAAQ,CAACC,OAAOtT,EAAEuT,QAAQtT,EAAEuT,UAAUhT,GAAG8S,OAAOzN,KAAKM,MAAM+C,EAAElJ,IAAIyT,OAAOjR,EAAEkR,KAAK/Q,EAAG,EAAzT,CAA2T7C,EAAEgN,UAAU,IAAI/M,EAAE0T,OAAOE,IAAI,MAAM,IAAIhQ,MAAM,+DAA+D,GAAG5D,EAAE0T,OAAOE,MAAM7T,EAAE6T,IAAI,MAAM,IAAIhQ,MAAM,0DAA0D7D,EAAE6T,gBAAgB5T,EAAE0T,OAAOE,QAAQ,IAAI5T,EAAE2T,KAAKE,IAAI,MAAM,IAAIjQ,MAAM,gEAAgE,GAAG,UAAU5D,EAAEuT,OAAOO,IAAI,MAAM,IAAIlQ,MAAM,2BAA2B5D,EAAEuT,OAAOO,2EAA2E,IAAI9T,EAAE0T,OAAOK,KAAK,iBAAiB/T,EAAE0T,OAAOK,MAAMzL,MAAM0L,QAAQhU,EAAE0T,OAAOK,KAAK,MAAM,IAAInQ,MAAM,qFAAqF,GAAG0E,MAAM0L,QAAQhU,EAAE0T,OAAOK,KAAK,CAAC,IAAI/T,EAAE0T,OAAOK,IAAI9M,SAASlH,EAAEgU,KAAK,MAAM,IAAInQ,MAAM,4DAA4D7D,EAAEgU,4BAA4B/T,EAAE0T,OAAOK,IAAItK,KAAK,UAAU,GAAGzJ,EAAE0T,OAAOK,IAAIrT,OAAO,EAAE,CAAC,IAAIV,EAAE0T,OAAOO,IAAI,MAAM,IAAIrQ,MAAM,uHAAuH,GAAG5D,EAAE0T,OAAOO,MAAMlU,EAAEgU,IAAI,MAAM,IAAInQ,MAAM,oEAAoE7D,EAAEgU,gBAAgB/T,EAAE0T,OAAOO,OAAO,CAAC,MAAM,GAAGjU,EAAE0T,OAAOK,MAAMhU,EAAEgU,IAAI,MAAM,IAAInQ,MAAM,4DAA4D7D,EAAEgU,mBAAmB/T,EAAE0T,OAAOK,QAAQ,GAAGhU,EAAEmU,MAAM,CAAC,IAAIlU,EAAE0T,OAAOQ,MAAM,MAAM,IAAItQ,MAAM,gEAAgE,GAAG5D,EAAE0T,OAAOQ,QAAQnU,EAAEmU,MAAM,MAAM,IAAItQ,MAAM,2DAA2D7D,EAAEmU,kBAAkBlU,EAAE0T,OAAOQ,SAAS,CAAC,GAAGnU,EAAEoU,UAAUnG,EAAEhO,EAAE0T,OAAOU,WAAW,MAAM,IAAIxQ,MAAM,sHAAsH,GAAG,MAAM5D,EAAE0T,OAAOW,MAAMrG,EAAEhO,EAAE0T,OAAOW,KAAK,MAAM,IAAIzQ,MAAM,wEAAwE,IAAIoK,EAAEhO,EAAE0T,OAAO1N,KAAK,MAAM,IAAIpC,MAAM,kEAAkE,MAAM3D,EAAEF,EAAEuU,QAAQ,GAAGpU,EAAE,IAAI+E,KAAKlF,EAAEmF,KAAKD,KAAKC,OAAOzE,EAAE,IAAIwE,KAAK,GAAG,GAAGxE,EAAE8T,cAAcvU,EAAE0T,OAAOW,IAAIpU,GAAGC,EAAEO,EAAE,MAAM,IAAImD,MAAM,oEAAoE1D,gCAAgCO,MAAM,GAAG,MAAMT,EAAE0T,OAAOc,KAAKxG,EAAEhO,EAAE0T,OAAOc,KAAK,CAAC,MAAMzU,EAAE,IAAIkF,KAAK,GAAG,GAAGlF,EAAEwU,cAAcvU,EAAE0T,OAAOc,IAAIvU,GAAGC,EAAEH,EAAE,MAAM,IAAI6D,MAAM,+GAA+G1D,gBAAgBH,IAAI,CAAC,GAAG,MAAMC,EAAE0T,OAAOU,WAAWpG,EAAEhO,EAAE0T,OAAOU,WAAW,CAAC,MAAM3T,EAAE,IAAIwE,KAAK,GAAG,GAAGxE,EAAE8T,cAAcE,SAASzU,EAAE0T,OAAOU,WAAWrU,EAAEoU,QAAQlU,GAAGC,EAAEO,EAAE,MAAM,IAAImD,MAAM,uJAAuJ1D,4BAA4BO,IAAI,CAAC,GAAGV,EAAE2U,aAAa,CAAC,MAAMzU,EAAEF,EAAE2U,aAAa9I,OAAO,GAAG3L,EAAEqM,WAAW,QAAQ,CAAC,MAAMvM,EAAEE,EAAE,IAAID,EAAE0T,OAAOiB,OAAO,MAAM,IAAI/Q,MAAM,2EAA2E,GAAG7D,IAAIC,EAAE0T,OAAOiB,OAAO,MAAM,IAAI/Q,MAAM,sEAAsE7D,cAAcC,EAAE0T,OAAOiB,UAAU,KAAK,CAAC,MAAM5U,EAAEE,EAAE2U,cAAc,IAAI5U,EAAE0T,OAAOmB,SAAS,MAAM,IAAIjR,MAAM,+EAA+E,GAAG7D,IAAIC,EAAE0T,OAAOmB,SAAS,MAAM,IAAIjR,MAAM,0EAA0E7D,cAAcC,EAAE0T,OAAOmB,YAAY,CAAC,CAAC,OAAO7U,GAA6vS8U,CAAE,CAAClB,IAAItS,KAAKwR,YAAYiB,IAAIzS,KAAKsQ,QAAQ/I,SAASkE,SAAShN,EAAEmU,MAAMlU,EAAE0U,aAAazU,EAAEqU,OAAOhT,KAAKsQ,QAAQ0C,OAAOH,SAAS1T,EAAEa,KAAKsQ,QAAQH,oBAAoB0C,QAAQ,iBAAiB1T,EAAEA,EAAEgU,SAAShU,EAAE,UAAK,GAAQyE,IAAIhF,IAAI,IAAIO,CAAC,CAAC,eAAAsU,CAAgBhV,GAAGA,EAAEuB,KAAK6Q,cAAcrE,KAAKxM,KAAK+Q,kBAAkBtS,EAAE,CAACgO,gBAAgBzM,KAAKiR,uBAAuB5E,aAAarM,KAAKsQ,QAAQjE,eAAerM,KAAK6Q,cAAc/F,OAAO9K,KAAK+Q,kBAAkB,CAAC1E,aAAarM,KAAKsQ,QAAQjE,cAAc,CAAC,0BAAMqH,CAAqBjV,EAAEC,EAAEC,GAAG,MAAMC,EAAEwI,EAAEL,KAAK5H,EAAEiI,EAAEL,KAAK9F,EAAE8F,IAAI5F,EAAE,CAAC1C,IAAI,MAAMC,EAAE,IAAIyI,WAAW1I,GAAG,MAAM,CAACA,IAAI,MAAMC,EAAE,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,OAAOD,EAAE2J,QAAQ,UAAU3J,GAAGC,EAAED,IAAK,EAA3E,CAA6Ee,OAAO6H,KAAKmH,OAAOC,gBAAgBzH,MAAMC,KAAKvI,KAAM,EAAtK,MAA6K,OAAC4J,IAAU,MAAM5J,EAAEmI,IAAI0J,OAAOoD,OAAO,CAAC9N,KAAK,YAAW,IAAK+N,aAAajG,OAAOlP,IAAI,aAAaC,CAAE,EAAlG,CAAoGuC,IAAIK,EAAE,EAAE7C,EAAEC,EAAEC,EAAEC,EAAEO,EAAE8B,EAAEE,EAAEG,IAAIzC,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,CAACC,UAAUnJ,EAAE8I,UAAU9I,EAAE0R,qBAAqBxR,GAAG,CAACiI,MAAMwD,EAAE1L,EAAEC,EAAEiI,OAAOiN,cAAc,OAAOC,cAAcxS,GAAG,QAAQ+E,MAAMzH,EAAEgU,MAAMzT,EAAE4U,aAAa5S,GAAG1C,EAAE0R,oBAAoB4D,aAAaC,eAAe/S,EAAEgT,sBAAsB,SAA3R,CAAqSjU,KAAKsQ,QAAQtQ,KAAK4G,MAAMnI,EAAEG,EAAEO,EAAEgC,EAAE1C,EAAEsV,cAAc/T,KAAKsQ,QAAQH,oBAAoB4D,cAAcpV,EAAE,MAAMD,OAAE,EAAOA,EAAEoV,eAAe1S,EAAEpB,KAAK6R,cAAcvQ,GAAG,MAAM,CAACsR,MAAMzT,EAAE+U,cAAcjT,EAAE2F,MAAMtF,EAAEsF,MAAMD,SAASrF,EAAEqF,UAAU,UAAUoN,aAAazS,EAAEyS,aAAa1N,MAAMzH,EAAEuV,IAAI/S,EAAE,CAAC,oBAAMgT,CAAe3V,EAAEC,GAAG,IAAIC,EAAE,GAAGF,EAAEA,GAAG,CAAA,IAAKC,EAAEA,GAAG,CAAA,GAAI6H,QAAQ7H,EAAE6H,MAAM,MAAK,MAAM7H,EAAEc,OAAO6U,SAAS7U,OAAO8U,WAAW,KAAK,EAAE3V,EAAEa,OAAO+U,SAAS/U,OAAOgV,YAAY,KAAK,EAAE,OAAOhV,OAAOiV,KAA8G,GAAvG,wBAAwB,QAAQ/V,SAASC,2DAA4D,EAA5N,IAAmOD,EAAE6H,OAAO,MAAM,IAAIjE,MAAM,2EAA2E,MAAM1D,QAAQoB,KAAK0T,qBAAqBjV,EAAE0R,qBAAqB,GAAG,CAAC2D,cAAc,eAAetU,OAAOyO,SAASyG,QAAQhW,EAAE6H,MAAM0H,SAAS0G,KAAK/V,EAAEuV,IAAI,MAAMhV,OAAO,CAACV,GAAG,IAAIiC,SAAS,CAAChC,EAAEC,KAAK,IAAIC,EAAE,MAAMO,EAAEyV,aAAW,KAAOnW,EAAE8H,OAAO9H,EAAE8H,MAAMsO,SAASC,cAAc3V,GAAGgG,aAAalE,GAAGzB,OAAOyF,oBAAoB,UAAUrG,GAAE,GAAID,EAAE,IAAI2F,EAAE7F,EAAE8H,QAAS,GAAE,KAAKtF,EAAEJ,YAAU,KAAOiU,cAAc3V,GAAGR,EAAE,IAAI0F,EAAE5F,EAAE8H,QAAQ/G,OAAOyF,oBAAoB,UAAUrG,GAAE,EAAI,GAAE,KAAKH,EAAEmH,kBAAkB,KAAKhH,EAAE,SAASuC,GAAG,GAAGA,EAAEgI,MAAM,2BAA2BhI,EAAEgI,KAAK2F,KAAK,CAAC,GAAG3J,aAAalE,GAAG6T,cAAc3V,GAAGK,OAAOyF,oBAAoB,UAAUrG,GAAE,GAAIH,EAAE8H,MAAM6C,QAAQjI,EAAEgI,KAAK4L,SAAS9O,MAAM,OAAOtH,EAAEsE,EAAEmD,YAAYjF,EAAEgI,KAAK4L,WAAWrW,EAAEyC,EAAEgI,KAAK4L,SAAS,CAAC,EAAEvV,OAAO4F,iBAAiB,UAAUxG,EAAG,IAAnlB,CAAulBC,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAE,EAACjJ,GAAG,CAACkH,iBAAiBlH,EAAEkH,kBAAkB5F,KAAKsQ,QAAQ0E,2BAA2B,MAAM,GAAGpW,EAAEyH,QAAQlH,EAAEkH,MAAM,MAAM,IAAIpD,EAAE,iBAAiB,iBAAiB,MAAMhC,GAAG,QAAQtC,EAAEF,EAAE0R,2BAAsB,IAASxR,OAAE,EAAOA,EAAEyU,eAAepT,KAAKsQ,QAAQH,oBAAoBiD,mBAAmBpT,KAAKiV,cAAc,CAACtO,SAAS/H,EAAE+H,SAASC,MAAMhI,EAAEgI,MAAMsN,cAActV,EAAEsV,cAAcgB,WAAW,qBAAqBC,KAAKhW,EAAEgW,KAAKpB,aAAanV,EAAEmV,cAAc,CAACqB,QAAQxW,EAAEgU,MAAMQ,aAAanS,GAAG,CAAC,aAAMoU,GAAU,IAAI5W,EAAE,MAAMC,QAAQsB,KAAKsV,uBAAuB,OAAO,QAAQ7W,EAAE,MAAMC,OAAE,EAAOA,EAAEgN,oBAAe,IAASjN,OAAE,EAAOA,EAAE4T,IAAI,CAAC,sBAAMkD,GAAmB,IAAI9W,EAAE,MAAMC,QAAQsB,KAAKsV,uBAAuB,OAAO,QAAQ7W,EAAE,MAAMC,OAAE,EAAOA,EAAEgN,oBAAe,IAASjN,OAAE,EAAOA,EAAE2T,MAAM,CAAC,uBAAMoD,CAAkB9W,EAAE,IAAI,IAAIC,EAAE,MAAMC,EAAEgR,EAAElR,IAAImR,QAAQ1Q,EAAEsW,SAASxU,EAAEqF,SAASnF,GAAGvC,EAAE0C,EAAE7C,EAAEG,EAAE,CAAC,UAAU,WAAW,aAAawC,GAAG,QAAQzC,EAAE2C,EAAE6O,2BAAsB,IAASxR,OAAE,EAAOA,EAAEyU,eAAepT,KAAKsQ,QAAQH,oBAAoBiD,aAAanQ,QAAQjD,KAAK0T,qBAAqBpS,EAAE6O,qBAAqB,CAAA,IAAKgE,IAAIjR,GAAGD,EAAEK,EAAE7E,EAAEwE,EAAE,CAAC,QAAQjD,KAAKoR,mBAAmB7E,OAAO1N,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAGrE,GAAG,CAACgD,SAASnF,IAAIC,GAAG,CAACgS,aAAahS,KAAK,MAAMiD,EAAEpD,EAAE,GAAGiC,KAAKjC,IAAIiC,EAAE/D,QAAQA,EAAEkF,GAAG7E,OAAOyO,SAAStG,OAAOtD,EAAE,CAAC,4BAAMqR,CAAuBjX,EAAEe,OAAOyO,SAAS0G,MAAM,MAAMjW,EAAED,EAAEuJ,MAAM,KAAKvC,MAAM,GAAG,GAAG,IAAI/G,EAAEU,OAAO,MAAM,IAAIkD,MAAM,oDAAoD,MAAM+D,MAAM1H,EAAEwW,KAAKvW,EAAEqH,MAAM9G,EAAE+G,kBAAkBjF,GAAG,CAACxC,IAAIA,EAAEQ,QAAQ,MAAM,IAAIR,EAAEA,EAAEmQ,UAAU,EAAEnQ,EAAEQ,QAAQ,OAAO,MAAMP,EAAE,IAAI8I,gBAAgB/I,GAAG,MAAM,CAAC4H,MAAM3H,EAAE0B,IAAI,SAAS+U,KAAKzW,EAAE0B,IAAI,cAAS,EAAO6F,MAAMvH,EAAE0B,IAAI,eAAU,EAAO8F,kBAAkBxH,EAAE0B,IAAI,2BAAsB,EAAQ,EAAnO,CAAqO1B,EAAEyJ,KAAK,KAAKhH,EAAEnB,KAAKoR,mBAAmBhR,MAAM,IAAIe,EAAE,MAAM,IAAI8B,EAAE,sBAAsB,iBAAiB,GAAGjD,KAAKoR,mBAAmBtG,SAAS3L,EAAE,MAAM,IAAI+D,EAAE/D,EAAE8B,GAAG9B,EAAER,EAAEwC,EAAEmF,UAAU,IAAInF,EAAE+S,eAAe/S,EAAEkF,OAAOlF,EAAEkF,QAAQ1H,EAAE,MAAM,IAAIsE,EAAE,iBAAiB,iBAAiB,MAAM3B,EAAEH,EAAEiS,aAAahS,EAAED,EAAEyR,MAAMtP,EAAEnC,EAAE4S,aAAa,aAAa/T,KAAKiV,cAAcpW,OAAO8I,OAAO,CAAChB,SAASxF,EAAEwF,SAASC,MAAMzF,EAAEyF,MAAMsN,cAAc/S,EAAE+S,cAAcgB,WAAW,qBAAqBC,KAAKvW,GAAG0E,EAAE,CAACyQ,aAAazQ,GAAG,CAAE,GAAE,CAAC8R,QAAQhU,EAAEgS,aAAa9R,IAAI,CAACgF,SAASnF,EAAEmF,SAAS,CAAC,kBAAMqP,CAAalX,GAAG,IAAIuB,KAAK6Q,cAAczQ,IAAIJ,KAAKgR,2BAA2B,CAAC,IAAIhR,KAAK6Q,cAAczQ,IAAI,0BAA0B,OAAOJ,KAAK6Q,cAAcrE,KAAKxM,KAAKgR,2BAA0B,EAAG,CAACvE,gBAAgBzM,KAAKiR,uBAAuB5E,aAAarM,KAAKsQ,QAAQjE,eAAerM,KAAK6Q,cAAc/F,OAAO,yBAAyB,CAAC,UAAU9K,KAAK4V,iBAAiBnX,EAAE,CAAC,MAAMA,GAAI,CAAA,CAAC,sBAAMmX,CAAiBnX,EAAE,IAAI,IAAIC,EAAE,MAAMC,EAAEE,OAAO8I,OAAO9I,OAAO8I,OAAO,CAACkO,UAAU,MAAMpX,GAAG,CAAC0R,oBAAoBtR,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAE,EAAC3H,KAAKsQ,QAAQH,qBAAqB1R,EAAE0R,qBAAqB,CAACvJ,MAAMwD,EAAEpK,KAAK4G,MAAM,QAAQlI,EAAED,EAAE0R,2BAAsB,IAASzR,OAAE,EAAOA,EAAEkI,WAAWhI,OAAO,EAAEH,EAAEC,KAAK,IAAIC,EAAEwQ,EAAEzQ,GAAG,OAAOC,IAAIA,EAAEF,IAAImL,SAAS,YAAYuF,EAAEzQ,GAAGC,EAAE,IAAK,IAAGwQ,EAAEzQ,GAAGC,GAAGA,CAAE,EAAnF,EAAsF,IAAIqB,KAAK8V,kBAAkBnX,IAAI,GAAGqB,KAAKsQ,QAAQ/I,aAAa5I,EAAEwR,oBAAoBxJ,aAAahI,EAAEwR,oBAAoBvJ,SAAS,OAAOnI,EAAEsX,iBAAiBnX,EAAE,MAAMA,OAAE,EAAOA,EAAEoX,YAAY,CAAC,uBAAMF,CAAkBpX,GAAG,MAAMmX,UAAUlX,GAAGD,EAAEE,EAAEH,EAAEC,EAAE,CAAC,cAAc,GAAG,QAAQC,EAAE,CAAC,MAAMF,QAAQuB,KAAKiW,mBAAmB,CAACrP,MAAMhI,EAAEuR,oBAAoBvJ,MAAMD,SAAS/H,EAAEuR,oBAAoBxJ,UAAU,UAAUY,SAASvH,KAAKsQ,QAAQ/I,WAAW,GAAG9I,EAAE,OAAOA,CAAC,CAAC,GAAG,eAAeE,EAAE,CAAC,SAAS,OAAOF,EAAEC,EAAE,KAAK,IAAI,IAAIC,EAAE,EAAEA,EAAED,EAAEC,IAAI,SAASF,IAAI,OAAM,EAAG,OAAM,CAAG,EAAnE,EAAmE,IAAOsR,EAAGjM,YAAY,8BAA8B,MAAM,IAAI,MAAM,IAAIR,EAAE,IAAI,GAAG9D,OAAO4F,iBAAiB,WAAWpF,KAAKqQ,wBAAwB,QAAQ1R,EAAE,CAAC,MAAMF,QAAQuB,KAAKiW,mBAAmB,CAACrP,MAAMhI,EAAEuR,oBAAoBvJ,MAAMD,SAAS/H,EAAEuR,oBAAoBxJ,UAAU,UAAUY,SAASvH,KAAKsQ,QAAQ/I,WAAW,GAAG9I,EAAE,OAAOA,CAAC,CAAC,MAAMA,EAAEuB,KAAKsQ,QAAQa,uBAAuBnR,KAAKkW,2BAA2BtX,SAASoB,KAAKmW,oBAAoBvX,IAAI6M,SAAS/M,EAAEsX,aAAa7W,EAAEiX,gBAAgBnV,EAAEiL,WAAW/K,GAAG1C,EAAE,OAAOI,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAC8D,SAAS/M,EAAEsX,aAAa7W,GAAG8B,EAAE,CAAC2F,MAAM3F,GAAG,MAAM,CAACiL,WAAW/K,GAAG,CAAC,cAAc4O,EAAG/L,YAAY,+BAA+BxE,OAAOyF,oBAAoB,WAAWjF,KAAKqQ,uBAAuB,CAAC,CAAC,CAAC,uBAAMgG,CAAkB5X,EAAE,CAAE,EAACC,EAAE,CAAA,GAAI,IAAIC,EAAE,MAAMC,EAAEC,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAGlJ,GAAG,CAAC0R,oBAAoBtR,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAG3H,KAAKsQ,QAAQH,qBAAqB1R,EAAE0R,qBAAqB,CAACvJ,MAAMwD,EAAEpK,KAAK4G,MAAM,QAAQjI,EAAEF,EAAE0R,2BAAsB,IAASxR,OAAE,EAAOA,EAAEiI,WAAiF,OAAtElI,EAAEG,OAAO8I,OAAO9I,OAAO8I,OAAO,GAAGxG,GAAGzC,SAASsB,KAAKoU,eAAexV,EAAEF,UAAgBsB,KAAKqR,aAAajR,IAAI,IAAImK,EAAE,CAAC3D,MAAMhI,EAAEuR,oBAAoBvJ,MAAMD,SAAS/H,EAAEuR,oBAAoBxJ,UAAU,UAAUY,SAASvH,KAAKsQ,QAAQ/I,aAAayO,YAAY,CAAC,qBAAMM,GAAkB,cAActW,KAAKqV,SAAS,CAAC,eAAAkB,CAAgB7X,GAAG,OAAOA,EAAE6I,SAAS7I,EAAE6I,SAAS7I,EAAE6I,UAAUvH,KAAKsQ,QAAQ/I,gBAAgB7I,EAAE6I,SAAS,MAAM5I,EAAED,EAAE8X,cAAc,CAAE,GAAEC,UAAU7X,GAAGD,EAAEQ,EAAEV,EAAEE,EAAE,CAAC,cAAcsC,EAAErC,EAAE,aAAa,GAAG,OAAOoB,KAAK4R,KAAK,cAActK,EAAEzI,OAAO8I,OAAO,CAACJ,SAAS7I,EAAE6I,UAAUpI,OAAO8B,CAAC,CAAC,YAAMyV,CAAOhY,EAAE,CAAA,GAAI,MAAMC,EAAEiR,EAAElR,IAAImR,QAAQjR,GAAGD,EAAEQ,EAAEV,EAAEE,EAAE,CAAC,YAAY,OAAOD,EAAE6I,eAAevH,KAAKqR,aAAa7O,cAAcxC,KAAKqR,aAAa7O,MAAM9D,EAAE6I,UAAUvH,KAAKsQ,QAAQ/I,UAAUvH,KAAK6Q,cAAc/F,OAAO9K,KAAK+Q,kBAAkB,CAAC1E,aAAarM,KAAKsQ,QAAQjE,eAAerM,KAAK6Q,cAAc/F,OAAO9K,KAAKgR,0BAA0B,CAAC3E,aAAarM,KAAKsQ,QAAQjE,eAAerM,KAAKiQ,UAAUnF,OAAO,YAAY,MAAM7J,EAAEjB,KAAKuW,gBAAgBpX,GAAGP,QAAQA,EAAEqC,IAAG,IAAKrC,GAAGY,OAAOyO,SAAStG,OAAO1G,EAAE,CAAC,yBAAMkV,CAAoB1X,GAAG,MAAMC,EAAEG,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAE,EAAClJ,EAAE0R,qBAAqB,CAACwG,OAAO,SAAShY,EAAEqB,KAAK6Q,cAAczQ,IAAIJ,KAAK+Q,mBAAmBpS,IAAID,EAAE0U,eAAe1U,EAAE0U,aAAazU,GAAG,MAAMwV,IAAIvV,EAAEyH,MAAMlH,EAAEyT,MAAM3R,EAAEiT,cAAc/S,EAAE4S,aAAazS,EAAEsF,MAAMxF,EAAEuF,SAASzD,SAASlD,KAAK0T,qBAAqBhV,EAAE,CAACoV,cAAc,eAAetU,OAAOyO,SAASyG,QAAQ,IAAI,GAAGlV,OAAOoX,oBAAoB,MAAM,IAAI3T,EAAE,iBAAiB,qIAAqI,MAAMtE,EAAEF,EAAEmH,kBAAkB5F,KAAKsQ,QAAQ0E,0BAA0B3Q,OAAO,EAAE5F,EAAEC,EAAEC,EAAE,KAAK,IAAI+B,UAAU9B,EAAEO,KAAK,MAAM8B,EAAEzB,OAAOiO,SAASoJ,cAAc,UAAU5V,EAAE6V,aAAa,QAAQ,KAAK7V,EAAE6V,aAAa,SAAS,KAAK7V,EAAE8V,MAAMC,QAAQ,OAAO,MAAM7V,EAAE,KAAK3B,OAAOiO,SAASvD,KAAK+M,SAAShW,KAAKzB,OAAOiO,SAASvD,KAAKgN,YAAYjW,GAAGzB,OAAOyF,oBAAoB,UAAU3D,GAAE,GAAI,EAAE,IAAIA,EAAE,MAAMF,EAAEP,YAAY,KAAK1B,EAAE,IAAImE,GAAGnC,GAAI,GAAE,IAAIxC,GAAG2C,EAAE,SAAS7C,GAAG,GAAGA,EAAEiW,QAAQhW,EAAE,OAAO,IAAID,EAAE0K,MAAM,2BAA2B1K,EAAE0K,KAAK2F,KAAK,OAAO,MAAMnQ,EAAEF,EAAE0Y,OAAOxY,GAAGA,EAAEyK,QAAQ3K,EAAE0K,KAAK4L,SAAS9O,MAAM9G,EAAE8D,EAAEmD,YAAY3H,EAAE0K,KAAK4L,WAAWnW,EAAEH,EAAE0K,KAAK4L,UAAU5P,aAAa/D,GAAG5B,OAAOyF,oBAAoB,UAAU3D,GAAE,GAAIT,WAAWM,EAAE,IAAI,EAAE3B,OAAO4F,iBAAiB,UAAU9D,GAAE,GAAI9B,OAAOiO,SAASvD,KAAKkN,YAAYnW,GAAGA,EAAE6V,aAAa,MAAMrY,EAAG,IAAjtB,CAAqtBG,EAAEoB,KAAKsR,UAAU3S,GAAG,GAAGQ,IAAIkF,EAAEgC,MAAM,MAAM,IAAIpD,EAAE,iBAAiB,iBAAiB,MAAMqB,QAAQtE,KAAKiV,cAAcpW,OAAO8I,OAAO9I,OAAO8I,OAAO,GAAGlJ,EAAE0R,qBAAqB,CAAC+D,cAAc/S,EAAEgU,KAAK9Q,EAAE8Q,KAAKD,WAAW,qBAAqBnB,aAAazS,EAAEsH,QAAQnK,EAAE0R,oBAAoBvH,SAAS5I,KAAK2Q,gBAAgB,CAACyE,QAAQnU,EAAEmS,aAAa1U,EAAE0U,eAAe,OAAOvU,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAE,EAACrD,GAAG,CAACsC,MAAMxF,EAAEgV,gBAAgB9R,EAAEsC,MAAMD,SAASzD,GAAG,CAAC,MAAMzE,GAAG,KAAK,mBAAmBA,EAAEwH,OAAOjG,KAAK0W,OAAO,CAAC7G,SAAQ,IAAKpR,CAAC,CAAC,CAAC,gCAAMyX,CAA2BzX,GAAG,MAAMC,QAAQsB,KAAKqR,aAAajR,IAAI,IAAImK,EAAE,CAAC3D,MAAMnI,EAAE0R,oBAAoBvJ,MAAMD,SAASlI,EAAE0R,oBAAoBxJ,UAAU,UAAUY,SAASvH,KAAKsQ,QAAQ/I,YAAY,KAAK7I,GAAGA,EAAEqN,eAAe/L,KAAK2R,QAAQ,CAAC,GAAG3R,KAAKsQ,QAAQF,yBAAyB,aAAapQ,KAAKmW,oBAAoB1X,GAAG,MAAM,IAAIgI,EAAEhI,EAAE0R,oBAAoBxJ,UAAU,UAAUlI,EAAE0R,oBAAoBvJ,MAAM,CAAC,MAAMjI,EAAEF,EAAE0R,oBAAoB4D,cAAc/T,KAAKsQ,QAAQH,oBAAoB4D,cAAcvU,OAAOyO,SAASyG,OAAO9V,EAAE,iBAAiBH,EAAEmH,iBAAiB,IAAInH,EAAEmH,iBAAiB,KAAK,IAAI,MAAMzG,QAAQa,KAAKiV,cAAcpW,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,GAAGlJ,EAAE0R,qBAAqB,CAAC+E,WAAW,gBAAgBnJ,cAAcrN,GAAGA,EAAEqN,cAAcgI,aAAapV,IAAIC,GAAG,CAACgK,QAAQhK,KAAK,OAAOC,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAGxI,GAAG,CAACyH,MAAMnI,EAAE0R,oBAAoBvJ,MAAMwP,gBAAgBjX,EAAEyH,MAAMD,SAASlI,EAAE0R,oBAAoBxJ,UAAU,WAAW,CAAC,MAAMjI,GAAG,IAAIA,EAAE2Y,QAAQpY,QAAQ,0BAA0B,GAAGP,EAAE2Y,SAAS3Y,EAAE2Y,QAAQpY,QAAQ,0BAA0B,IAAIe,KAAKsQ,QAAQF,yBAAyB,aAAapQ,KAAKmW,oBAAoB1X,GAAG,MAAMC,CAAC,CAAC,CAAC,uBAAM4Y,CAAkB5Y,GAAG,MAAM+M,SAAS9M,EAAE+M,aAAa9M,GAAGF,EAAES,EAAEV,EAAEC,EAAE,CAAC,WAAW,iBAAiBsB,KAAKiQ,UAAU5P,IAAI,WAAW,CAACoL,SAAS9M,EAAE+M,aAAa9M,UAAUoB,KAAKqR,aAAa9F,WAAWvL,KAAKsQ,QAAQ/I,SAAS7I,EAAE+M,SAAS/M,EAAEgN,oBAAoB1L,KAAKqR,aAAahR,IAAIlB,EAAE,CAAC,0BAAMmW,GAAuB,MAAM7W,EAAEuB,KAAKsQ,QAAQH,oBAAoBxJ,UAAU,UAAUjI,QAAQsB,KAAKqR,aAAa1F,WAAW,IAAIpB,EAAE,CAAChD,SAASvH,KAAKsQ,QAAQ/I,SAASZ,SAASlI,EAAEmI,MAAM5G,KAAK4G,SAASjI,EAAEqB,KAAKiQ,UAAU7P,IAAI,YAAY,OAAO1B,GAAGA,EAAE+M,YAAY,MAAM9M,OAAE,EAAOA,EAAE8M,UAAU9M,GAAGqB,KAAKiQ,UAAU5P,IAAI,WAAW3B,GAAGA,EAAE,CAAC,wBAAMuX,EAAoBrP,MAAMnI,EAAEkI,SAASjI,EAAE6I,SAAS5I,IAAI,MAAMC,QAAQoB,KAAKqR,aAAajR,IAAI,IAAImK,EAAE,CAAC3D,MAAMnI,EAAEkI,SAASjI,EAAE6I,SAAS5I,IAAI,IAAI,GAAGC,GAAGA,EAAEoX,aAAa,CAAC,MAAMA,aAAavX,EAAE2X,gBAAgB1X,EAAEwN,WAAWvN,GAAGC,EAAEO,QAAQa,KAAKsV,uBAAuB,OAAOnW,GAAGN,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAC8D,SAAStM,EAAEsM,SAASuK,aAAavX,GAAGC,EAAE,CAACkI,MAAMlI,GAAG,MAAM,CAACwN,WAAWvN,GAAG,CAAC,CAAC,mBAAMsW,CAAcxW,EAAEC,GAAG,MAAM0W,QAAQzW,EAAEyU,aAAaxU,GAAGF,GAAG,CAAE,EAACS,QAAQ2K,EAAEjL,OAAO8I,OAAO,CAACoC,QAAQ/J,KAAKsR,UAAU1J,UAAU5H,KAAKsQ,QAAQ/I,SAASyC,YAAYhK,KAAKsQ,QAAQtG,YAAYjB,YAAY/I,KAAKsQ,QAAQvH,YAAYH,QAAQ5I,KAAK2Q,eAAelS,GAAGuB,KAAK2R,QAAQ1Q,QAAQjB,KAAK8R,eAAe3S,EAAEsM,SAAS9M,EAAEC,GAAG,aAAaoB,KAAKsX,kBAAkBzY,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAGxI,GAAG,CAACuM,aAAazK,EAAE2F,MAAMnI,EAAEmI,MAAMD,SAASlI,EAAEkI,UAAU,YAAYxH,EAAEyH,MAAM,CAACwP,gBAAgBjX,EAAEyH,OAAO,MAAM,CAACgB,UAAU5H,KAAKsQ,QAAQ/I,YAAYvH,KAAK6Q,cAAcrE,KAAKxM,KAAKgR,2BAA0B,EAAG,CAACvE,gBAAgBzM,KAAKiR,uBAAuB5E,aAAarM,KAAKsQ,QAAQjE,eAAerM,KAAKyT,gBAAgB7U,GAAGqC,EAAEmR,OAAOiB,QAAQxU,OAAO8I,OAAO9I,OAAO8I,OAAO,CAAA,EAAGxI,GAAG,CAACuM,aAAazK,GAAG,ECer5wC,IAAMsW,GAA8B,CACzCjB,iBAAiB,EACjBkB,WAAW,GCkIPC,GAAO,WACX,MAAM,IAAInV,MAAM,wDAClB,EAKaoV,UACRH,IAAgB,CACnBI,kBAAmBF,GACnBG,eAAgBH,GAChBI,uBAAwBJ,GACxBK,wBAAyBL,GACzBlC,iBAAkBkC,GAClBjC,kBAAmBiC,GACnBrD,eAAgBqD,GAChBf,OAAQe,GACR/B,uBAAwB+B,KAMpBM,GAAeC,EAAaA,cAAwBN,ICpK1DO,GAAA,SAAAC,GACE,SAAmBD,EAAAhS,EAAsBC,GAAzC,IAAAiS,EACED,EAAMlZ,KAAAgB,KAAAkG,GAAqBD,IAI5BjG,YALkBmY,EAAKlS,MAALA,EAAsBkS,EAAiBjS,kBAAjBA,EAIvCrH,OAAOsH,eAAegS,EAAMF,EAAWnZ,YACxC,CACH,0PAPgCsZ,CAAKH,EAAAC,GAOpCD,CAAD,CAPA,CAAgC3V,OCJ1B+V,GAAU,iBACVC,GAAW,kBACXC,GAAW,kBAMXC,GACJ,SAACC,GACD,OAAA,SAACxS,GACC,OAAIA,aAAiB3D,MACZ2D,EAIG,OAAVA,GACiB,iBAAVA,GACP,UAAWA,GACY,iBAAhBA,EAAMA,MAGX,sBAAuBA,GACY,iBAA5BA,EAAMC,kBAEN,IAAI+R,GAAWhS,EAAMA,MAAOA,EAAMC,mBAEpC,IAAI+R,GAAWhS,EAAMA,OAEvB,IAAI3D,MAAMmW,GAnBnB,EAsBWC,GAAaF,GAAiB,gBAE9BG,GAAaH,GAAiB,2BAQ9BI,GAAuB,SAACtI,UAC/BA,eAAAA,EAASuI,eACXpI,QAAQC,KACN,mKAEFJ,EAAQH,oBAAsBG,EAAQH,qBAAuB,CAAA,EAC7DG,EAAQH,oBAAoB4D,aAAezD,EAAQuI,mBAC5CvI,EAAQuI,cAGiB,QAA9BC,EAAAxI,aAAO,EAAPA,EAASH,2BAAqB,IAAA2I,OAAA,EAAAA,EAAAD,eAChCpI,QAAQC,KACN,+LAEFJ,EAAQH,oBAAoB4D,aAC1BzD,EAAQH,oBAAoB0I,mBACvBvI,EAAQH,oBAAoB0I,YAEvC,EC3CaE,GAAU,SAAC1S,EAAkB2S,GACxC,OAAQA,EAAOlK,MACb,IAAK,sBACH,OAAAjC,EAAAA,EAAA,GACKxG,GAAK,CACRmR,WAAW,IAEf,IAAK,uBACL,IAAK,cACH,OACK3K,EAAAA,EAAA,CAAA,EAAAxG,IACHiQ,kBAAmB0C,EAAO3G,KAC1BA,KAAM2G,EAAO3G,KACbmF,WAAW,EACXvR,WAAOgT,IAEX,IAAK,2BACL,IAAK,4BACH,OAAI5S,EAAMgM,OAAS2G,EAAO3G,KACjBhM,EAETwG,EAAAA,EAAA,CAAA,EACKxG,GAAK,CACRiQ,kBAAmB0C,EAAO3G,KAC1BA,KAAM2G,EAAO3G,OAEjB,IAAK,SACH,OACKxF,EAAAA,EAAA,CAAA,EAAAxG,IACHiQ,iBAAiB,EACjBjE,UAAM4G,IAEV,IAAK,QACH,OACKpM,EAAAA,EAAA,CAAA,EAAAxG,GACH,CAAAmR,WAAW,EACXvR,MAAO+S,EAAO/S,QAGtB,ECwDMiT,GAA4B,SAAC5S,GACjC9G,OAAO2Z,QAAQC,aACb,CAAA,EACA3L,SAAS4L,OACT/S,aAAQ,EAARA,EAAUgT,WAAY9Z,OAAOyO,SAASsL,SAE1C,EC9FMC,GAAW,SACfC,GAEA,YAFA,IAAAA,IAAAA,EAAsB1B,IAEtB2B,EAAAA,WAAWD,EAAX,ECnBIE,GAAuB,WAAyB,OAAAC,EAAK/C,cAAA+C,EAAAC,SAAA,OAKrDC,GAAgC,WAAA,OAAA5Y,OAAA,OAAA,OAAA,GAAA,WAAA,OAAAS,EAAA3B,MAAA,SAAA8Y,oBAKhCiB,GAAkB,WACtB,MAAA,GAAGC,OAAAxa,OAAOyO,SAASsL,UAAWS,OAAAxa,OAAOyO,SAASgM,OAA9C,oCFiHoB,SAACC,GAEnB,IAAAC,EAKED,EAAIC,SAJNC,EAIEF,EAAIE,qBAHNtB,EAGEoB,EAH4CG,mBAA9CA,OAAqB,IAAAvB,EAAAI,GAAyBJ,EAC9CwB,EAEEJ,EAFoBT,QAAtBA,OAAU,IAAAa,EAAAvC,GAAYuC,EACnBC,2UACDC,CAAAN,EANE,CAAA,WAAA,uBAAA,qBAAA,YAOCO,EAAUC,EAAAA,UACf,WAAM,OAAA,IAAIC,GA9Ce,SAC3BT,GAIA,OAFAtB,GAAqBsB,GAGhBrN,EAAAA,EAAA,CAAA,EAAAqN,GACH,CAAAlQ,YAAa,CACXnE,KAAM,cACNC,QAAS,UAGf,CAkC0B8U,CAAqBL,UAEvCM,EAAoBC,EAAAA,WAAW/B,GAASxB,IAAvClR,EAAKwU,EAAA,GAAEE,OACRC,EAAgBC,UAAO,GAEvBC,EAAcC,eAAY,SAAClV,GAE/B,OADA8U,EAAS,CAAEjM,KAAM,QAAS7I,MAAKA,IACxBA,CACR,GAAE,IAEHmV,EAAAA,WAAU,WACJJ,EAAcK,UAGlBL,EAAcK,SAAU,EACvBna,OAAA,OAAA,OAAA,GAAA,uEAGO,6BADAmR,cF1JkB,IAAAiJ,IAAAA,EAAe9b,OAAOyO,SAASgM,SAC1D5B,GAAQ9G,KAAK+J,KAAiB/C,GAAShH,KAAK+J,KAC7ChD,GAAS/G,KAAK+J,IEyJgBlB,EAAoB,CAAA,EAAA,GACrB,CAAA,EAAMK,EAAO/E,iCAC3B,OADCpP,EAAawS,EAAqCjX,OAA1CyE,SACT,CAAA,EAAMmU,EAAOpF,yBAApBhD,EAAOyG,SACPuB,EAAmB/T,EAAU+L,SAE7B,KAAA,EAAA,MAAA,CAAA,EAAMoI,EAAO9E,uBACN,OADPmD,EAAAjX,OACO,CAAA,EAAM4Y,EAAOpF,kBAApBhD,EAAOyG,iCAETiC,EAAS,CAAEjM,KAAM,cAAeuD,KAAIA,mCAEpC6I,EAAYxC,GAAW6C,2BFrKF,IAACD,IEuKzB,IACF,GAAE,CAACb,EAAQJ,EAAoBD,EAAsBc,IAEtD,IAAM1F,EAAoB2F,eACxB,SAACjB,GAGC,OAFAtB,GAAqBsB,GAEdO,EAAOjF,kBAAkB0E,EAClC,GACA,CAACO,IAGGrG,EAAiB+G,EAAAA,aACrB,SACE7K,EACAkL,GAA2B,OAAAta,OAAA,OAAA,OAAA,GAAA,qEAE3B6Z,EAAS,CAAEjM,KAAM,yCAEf,6BAAM,CAAA,EAAA2L,EAAOrG,eAAe9D,EAASkL,kBAArC1C,EAAAjX,oBAGA,kBADAqZ,EAAYxC,GAAW+C,IAChB,CAAA,GAEI,KAAA,EAAA,MAAA,CAAA,EAAMhB,EAAOpF,yBAApBhD,EAAOyG,EAAsBjX,OACnCkZ,EAAS,CAAEjM,KAAM,uBAAwBuD,KAAIA,WAC9C,GAAA,GACD,CAACoI,IAGG/D,EAASyE,eACb,SAAOjB,eAAA,IAAAA,IAAAA,EAAwB,CAAA,iFAC7B,KAAA,EAAA,MAAA,CAAA,EAAMO,EAAO/D,OAAOwD,kBAApBpB,EAAAjX,QACIqY,EAAKrK,UAA4B,IAAjBqK,EAAKrK,UACvBkL,EAAS,CAAEjM,KAAM,qBAEpB,GACD,CAAC2L,IAGG5C,EAAyBsD,EAAWA,aAExC,SAAOjB,GAA8B,OAAAhZ,OAAA,OAAA,OAAA,GAAA,yEAGzB,8BAAA,CAAA,EAAMuZ,EAAO7E,iBAAiBsE,kBAAtCwB,EAAQb,sBAER,iBAAMlC,GAAWgD,UAIT,OAFR7C,EAAAiC,KACEjM,KAAM,6BACA,CAAA,EAAM2L,EAAOpF,yBAFrByD,EAEEpX,WAAA,EAAA,EAAA4Y,EAAAjI,KAAMwI,EAAsBhZ,gBAGhC,KAAA,EAAA,MAAA,CAAA,EAAO6Z,MACR,GAAA,GACD,CAACjB,IAGG3C,EAA0BqD,EAAAA,aAC9B,SACEjB,EACAsB,GAA2B,OAAAta,OAAA,OAAA,OAAA,GAAA,yEAIjB,8BAAM,CAAA,EAAAuZ,EAAOpE,kBAAkB6D,EAAMsB,kBAA7CE,EAAQb,sBAER,iBAAMlC,GAAWiD,UAIT,OAFR9C,EAAAiC,KACEjM,KAAM,6BACA,CAAA,EAAM2L,EAAOpF,yBAFrByD,EAEEpX,WAAA,EAAA,EAAA4Y,EAAAjI,KAAMwI,EAAsBhZ,gBAGhC,KAAA,EAAA,MAAA,CAAA,EAAO6Z,MACR,GAAA,GACD,CAACjB,IAGGlF,EAAmB4F,eACvB,WAAM,OAAAV,EAAOlF,kBAAkB,GAC/B,CAACkF,IAGG/E,EAAyByF,EAAAA,aAC7B,SAAOhH,GAAY,OAAAjT,OAAA,OAAA,OAAA,GAAA,uEAER,8BAAA,CAAA,EAAMuZ,EAAO/E,uBAAuBvB,IAA3C,KAAA,EAAA,MAAA,CAAA,EAAO0G,iBAEP,iBAAMlC,GAAWkD,UAIT,OAFR/C,EAAAiC,KACEjM,KAAM,4BACA,CAAA,EAAM2L,EAAOpF,yBAFrByD,EAEEpX,WAAA,EAAA,EAAA4Y,EAAAjI,KAAMwI,EAAsBhZ,oCAGjC,GAAA,GACD,CAAC4Y,IAGGqB,EAAeC,EAAAA,SAAqC,WACxD,OACKlP,EAAAA,EAAA,CAAA,EAAAxG,IACHwR,uBAAsBA,EACtBC,wBAAuBA,EACvBvC,iBAAgBA,EAChBC,kBAAiBA,EACjBpB,eAAcA,EACdsC,OAAMA,EACNhB,uBAAsBA,GAE1B,GAAG,CACDrP,EACAwR,EACAC,EACAvC,EACAC,EACApB,EACAsC,EACAhB,IAGF,OAAOkE,EAAA/C,cAAC4C,EAAQuC,SAAQ,CAACjc,MAAO+b,GAAe3B,EACjD,2NNzS85wC,sDS4B54wC,SAChB8B,EACAxC,GAEA,YAFA,IAAAA,IAAAA,EAAsB1B,IAEf,SAAkBmE,GACvB,OACEtC,EAAC/C,cAAA4C,EAAQ0C,eACN,SAACxT,GAAmD,OACnDiR,EAAA/C,cAACoF,EAAepP,EAAA,CAAA,EAAAqP,EAAa,CAAAE,MAAOzT,IADe,GAK3D,CACF,+BDuDmC,SACjCsT,EACA3L,GAEA,YAFA,IAAAA,IAAAA,EAA+C,CAAA,GAExC,SAAoC4L,GAApC,IAqCN/D,EAAAnY,KAnCG8Y,EAKExI,EAAOgJ,SALTA,OAAQ,IAAAR,EAAGiB,GAAejB,EAC1BwB,EAIEhK,gBAJF+L,aAAgB1C,GAAoBW,EACpCO,EAGEvK,EAHoDgM,uBAAtDA,OAAyB,IAAAzB,EAAAf,KACzByC,EAEEjM,EAAOiM,aADTC,EACElM,EADoBmJ,QAGlBgD,EACJjD,QAJU,IAAAgD,EAAAzE,MAGJzB,EAAemG,EAAAnG,gBAAEkB,EAASiF,EAAAjF,UAAEhC,sBA2BpC,OAxBA4F,EAAAA,WAAU,WACR,IAAI5D,IAAalB,EAAjB,CAGA,IAAM4D,EAAIrN,EAAAA,EAAA,GACL0P,GAAY,CACfjW,SAAQuG,EAAAA,EAAA,GACF0P,GAAgBA,EAAajW,UACjC,CAAAgT,SAA8B,mBAAbA,EAA0BA,IAAaA,MAG3DpY,EAAAiX,OAAA,OAAA,GAAA,6DACC,MAAM,CAAA,EAAAmE,YACN,OADAxD,EAAAjX,OACA,CAAA,EAAM2T,EAAkB0E,kBAAxBpB,EAAAjX,cACD,GAXA,CAYH,GAAG,CACD2V,EACAlB,EACAd,EACA8G,EACAC,EACAjD,IAGKhD,EAAkBsD,gBAACqC,EAASpP,EAAA,CAAA,EAAKqP,IAAYG,GACtD,CACF", "x_google_ignoreList": [0]}