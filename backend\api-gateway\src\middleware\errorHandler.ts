/**
 * Middleware de gestion d'erreurs centralisé pour Nouri
 * Gestion uniforme des erreurs avec logging et réponses structurées
 */

import { Request, Response, NextFunction } from 'express';
import { logger, logError, logSecurityEvent } from '../utils/logger';
import { config } from '../config/config';

// Types d'erreurs personnalisées
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public isOperational: boolean;

  constructor(message: string, statusCode: number, code: string = 'GENERIC_ERROR') {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  public details: any[];

  constructor(message: string, details: any[] = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.details = details;
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentification requise') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Permissions insuffisantes') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Ressource non trouvée') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Conflit de données') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Trop de requêtes') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

export class ServiceUnavailableError extends AppError {
  constructor(message: string = 'Service temporairement indisponible') {
    super(message, 503, 'SERVICE_UNAVAILABLE_ERROR');
  }
}

/**
 * Middleware principal de gestion d'erreurs
 */
export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let statusCode = 500;
  let code = 'INTERNAL_SERVER_ERROR';
  let message = 'Une erreur interne s\'est produite';
  let details: any = undefined;

  // Gestion des erreurs personnalisées
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    code = error.code;
    message = error.message;
    
    if (error instanceof ValidationError) {
      details = error.details;
    }
  }
  // Gestion des erreurs Prisma
  else if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    
    switch (prismaError.code) {
      case 'P2002':
        statusCode = 409;
        code = 'DUPLICATE_ENTRY';
        message = 'Cette donnée existe déjà';
        break;
      case 'P2025':
        statusCode = 404;
        code = 'RECORD_NOT_FOUND';
        message = 'Enregistrement non trouvé';
        break;
      case 'P2003':
        statusCode = 400;
        code = 'FOREIGN_KEY_CONSTRAINT';
        message = 'Contrainte de clé étrangère violée';
        break;
      default:
        statusCode = 400;
        code = 'DATABASE_ERROR';
        message = 'Erreur de base de données';
    }
  }
  // Gestion des erreurs de validation Express
  else if (error.name === 'ValidationError') {
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    message = 'Données invalides';
    details = (error as any).details;
  }
  // Gestion des erreurs JWT
  else if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    code = 'INVALID_TOKEN';
    message = 'Token invalide';
    
    // Log d'événement de sécurité
    logSecurityEvent('invalid_jwt_token', {
      error: error.message,
      url: req.originalUrl
    }, req);
  }
  else if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    code = 'TOKEN_EXPIRED';
    message = 'Token expiré';
  }
  // Gestion des erreurs de syntaxe JSON
  else if (error instanceof SyntaxError && 'body' in error) {
    statusCode = 400;
    code = 'INVALID_JSON';
    message = 'Format JSON invalide';
  }
  // Gestion des erreurs de timeout
  else if (error.name === 'TimeoutError') {
    statusCode = 408;
    code = 'REQUEST_TIMEOUT';
    message = 'Délai d\'attente dépassé';
  }

  // Logging de l'erreur
  const errorContext = {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    statusCode,
    code
  };

  if (statusCode >= 500) {
    // Erreurs serveur - log complet avec stack trace
    logError(error, errorContext);
  } else if (statusCode >= 400) {
    // Erreurs client - log simplifié
    logger.warn('Erreur client', {
      message: error.message,
      ...errorContext
    });
  }

  // Construction de la réponse d'erreur
  const errorResponse: any = {
    error: message,
    code,
    timestamp: new Date().toISOString(),
    path: req.originalUrl
  };

  // Ajouter les détails en développement ou pour les erreurs de validation
  if (config.app.environment === 'development' || details) {
    if (details) {
      errorResponse.details = details;
    }
    
    if (config.app.environment === 'development' && statusCode >= 500) {
      errorResponse.stack = error.stack;
    }
  }

  // Ajouter un ID de trace pour le support
  if (statusCode >= 500) {
    errorResponse.traceId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Réponse HTTP
  res.status(statusCode).json(errorResponse);
};

/**
 * Middleware pour capturer les erreurs 404
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} non trouvée`);
  next(error);
};

/**
 * Middleware pour capturer les erreurs asynchrones
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Fonction utilitaire pour créer des erreurs personnalisées
 */
export const createError = {
  validation: (message: string, details?: any[]) => new ValidationError(message, details),
  authentication: (message?: string) => new AuthenticationError(message),
  authorization: (message?: string) => new AuthorizationError(message),
  notFound: (message?: string) => new NotFoundError(message),
  conflict: (message?: string) => new ConflictError(message),
  rateLimit: (message?: string) => new RateLimitError(message),
  serviceUnavailable: (message?: string) => new ServiceUnavailableError(message),
  generic: (message: string, statusCode: number, code?: string) => new AppError(message, statusCode, code)
};

/**
 * Middleware de gestion des erreurs non capturées
 */
export const setupGlobalErrorHandlers = (): void => {
  // Gestion des exceptions non capturées
  process.on('uncaughtException', (error: Error) => {
    logger.error('Exception non capturée:', error);
    
    // En production, on pourrait vouloir redémarrer le processus
    if (config.app.environment === 'production') {
      process.exit(1);
    }
  });

  // Gestion des promesses rejetées non gérées
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    logger.error('Promesse rejetée non gérée:', {
      reason,
      promise: promise.toString()
    });
    
    // En production, on pourrait vouloir redémarrer le processus
    if (config.app.environment === 'production') {
      process.exit(1);
    }
  });
};

/**
 * Middleware de validation des paramètres de requête
 */
export const validateRequest = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      const details = error.details.map((detail: any) => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context?.value
      }));
      
      throw new ValidationError('Données de requête invalides', details);
    }
    
    next();
  };
};
