/**
 * Complete Transactions Page
 * Displays user transactions with filtering and search
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  AppBar,
  Toolbar,
  Avatar,
  Pagination
} from '@mui/material'
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Home as HomeIcon,
  Logout as LogoutIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock transaction data
const mockTransactions = [
  { id: '1', date: '2024-01-15', description: 'Salaire Janvier', amount: 1800.00, type: 'income', category: 'Salaire', account: 'Compte Courant' },
  { id: '2', date: '2024-01-14', description: 'Courses Carrefour', amount: -85.50, type: 'expense', category: 'Alimentation', account: 'Compte Courant' },
  { id: '3', date: '2024-01-13', description: 'Facture STEG', amount: -120.00, type: 'expense', category: 'Utilities', account: 'Compte Courant' },
  { id: '4', date: '2024-01-12', description: 'Freelance Web', amount: 450.00, type: 'income', category: 'Freelance', account: 'Compte Courant' },
  { id: '5', date: '2024-01-11', description: 'Restaurant', amount: -45.00, type: 'expense', category: 'Restaurants', account: 'Carte de Crédit' },
  { id: '6', date: '2024-01-10', description: 'Essence', amount: -60.00, type: 'expense', category: 'Transport', account: 'Compte Courant' },
  { id: '7', date: '2024-01-09', description: 'Virement Épargne', amount: -200.00, type: 'transfer', category: 'Épargne', account: 'Compte Courant' },
  { id: '8', date: '2024-01-08', description: 'Pharmacie', amount: -25.50, type: 'expense', category: 'Santé', account: 'Compte Courant' },
  { id: '9', date: '2024-01-07', description: 'Abonnement Netflix', amount: -15.99, type: 'expense', category: 'Divertissement', account: 'Carte de Crédit' },
  { id: '10', date: '2024-01-06', description: 'Vente en ligne', amount: 120.00, type: 'income', category: 'Vente', account: 'Compte Courant' }
]

export const TransactionsPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [filterCategory, setFilterCategory] = useState('all')
  const [page, setPage] = useState(1)
  const itemsPerPage = 10

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  // Filter transactions
  const filteredTransactions = mockTransactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.category.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || transaction.type === filterType
    const matchesCategory = filterCategory === 'all' || transaction.category === filterCategory

    return matchesSearch && matchesType && matchesCategory
  })

  // Paginate transactions
  const paginatedTransactions = filteredTransactions.slice(
    (page - 1) * itemsPerPage,
    page * itemsPerPage
  )

  const totalPages = Math.ceil(filteredTransactions.length / itemsPerPage)

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'income':
        return <ArrowUpwardIcon sx={{ color: 'success.main' }} />
      case 'expense':
        return <ArrowDownwardIcon sx={{ color: 'error.main' }} />
      default:
        return <ArrowUpwardIcon sx={{ color: 'info.main' }} />
    }
  }

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'income':
        return 'success.main'
      case 'expense':
        return 'error.main'
      default:
        return 'info.main'
    }
  }

  const formatAmount = (amount: number) => {
    const sign = amount >= 0 ? '+' : ''
    return `${sign}${amount.toFixed(2)} TND`
  }

  const categories = [...new Set(mockTransactions.map(t => t.category))]

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Transactions
          </Typography>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Mes Transactions
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Consultez et gérez toutes vos transactions financières
          </Typography>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
              <TextField
                placeholder="Rechercher une transaction..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ minWidth: 300 }}
              />

              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Type</InputLabel>
                <Select
                  value={filterType}
                  label="Type"
                  onChange={(e) => setFilterType(e.target.value)}
                >
                  <MenuItem value="all">Tous</MenuItem>
                  <MenuItem value="income">Revenus</MenuItem>
                  <MenuItem value="expense">Dépenses</MenuItem>
                  <MenuItem value="transfer">Virements</MenuItem>
                </Select>
              </FormControl>

              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Catégorie</InputLabel>
                <Select
                  value={filterCategory}
                  label="Catégorie"
                  onChange={(e) => setFilterCategory(e.target.value)}
                >
                  <MenuItem value="all">Toutes</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category} value={category}>{category}</MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Button
                variant="outlined"
                startIcon={<DownloadIcon />}
                sx={{ ml: 'auto' }}
              >
                Exporter
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* Transactions Table */}
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Description</TableCell>
                  <TableCell>Catégorie</TableCell>
                  <TableCell>Compte</TableCell>
                  <TableCell align="right">Montant</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedTransactions.map((transaction) => (
                  <TableRow key={transaction.id} hover>
                    <TableCell>
                      <Typography variant="body2">
                        {new Date(transaction.date).toLocaleDateString('fr-FR')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'grey.100' }}>
                          {getTransactionIcon(transaction.type)}
                        </Avatar>
                        <Typography variant="body2" fontWeight="medium">
                          {transaction.description}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={transaction.category}
                        size="small"
                        variant="outlined"
                        color={transaction.type === 'income' ? 'success' : transaction.type === 'expense' ? 'error' : 'info'}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" color="text.secondary">
                        {transaction.account}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        fontWeight="bold"
                        color={getTransactionColor(transaction.type)}
                      >
                        {formatAmount(transaction.amount)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={(_, newPage) => setPage(newPage)}
                color="primary"
              />
            </Box>
          )}
        </Card>

        {/* Summary */}
        <Box sx={{ mt: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Card sx={{ flex: 1, minWidth: 200 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="success.main" fontWeight="bold">
                +{mockTransactions.filter(t => t.type === 'income').reduce((sum, t) => sum + t.amount, 0).toFixed(2)} TND
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Revenus
              </Typography>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1, minWidth: 200 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="error.main" fontWeight="bold">
                {mockTransactions.filter(t => t.type === 'expense').reduce((sum, t) => sum + t.amount, 0).toFixed(2)} TND
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Dépenses
              </Typography>
            </CardContent>
          </Card>

          <Card sx={{ flex: 1, minWidth: 200 }}>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="primary.main" fontWeight="bold">
                {filteredTransactions.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Transactions Affichées
              </Typography>
            </CardContent>
          </Card>
        </Box>
      </Container>
    </Box>
  )
}

export default TransactionsPage
