{"author": "Auth0", "name": "@auth0/auth0-react", "version": "2.3.0", "description": "Auth0 SDK for React Single Page Applications (SPA)", "keywords": ["auth0", "login", "Authorization Code Grant Flow", "PKCE", "Single Page Application authentication", "SPA authentication", "react"], "files": ["src", "dist"], "main": "dist/auth0-react.cjs.js", "types": "dist/index.d.ts", "module": "dist/auth0-react.esm.js", "scripts": {"build": "npm run lint && rollup -c --environment NODE_ENV:production", "lint": "eslint --ext=tsx ./src ./__tests__", "start": "rollup -cw", "test": "jest --coverage", "prepack": "npm run build", "docs": "typedoc --options typedoc.js src", "install:examples": "npm i --prefix=examples/cra-react-router --no-package-lock --legacy-peer-deps && npm i --prefix=examples/gatsby-app --no-package-lock --legacy-peer-deps && npm i --prefix=examples/nextjs-app --no-package-lock --legacy-peer-deps && npm ci --prefix=examples/users-api", "start:cra": "npm start --prefix=examples/cra-react-router", "start:gatsby": "npm start --prefix=examples/gatsby-app", "start:nextjs": "npm run dev --prefix=examples/nextjs-app", "start:api": "npm start --prefix=examples/users-api", "test:cra": "start-server-and-test start:api 3001 start:cra http-get://127.0.0.1:3000 cypress:run", "test:cra:watch": "start-server-and-test start:api 3001 start:cra 3000 cypress:open", "test:gatsby": "start-server-and-test start:api 3001 start:gatsby http-get://localhost:3000 cypress:run", "test:gatsby:watch": "start-server-and-test start:api 3001 start:gatsby 3000 cypress:open", "test:nextjs": "start-server-and-test start:api 3001 start:nextjs 3000 cypress:run", "test:nextjs:watch": "start-server-and-test start:api 3001 start:nextjs 3000 cypress:open", "test:integration": "npm run test:cra && npm run test:gatsby && npm run test:nextjs", "cypress:run": "cypress run --spec 'cypress/e2e/smoke.cy.ts'", "cypress:open": "cypress open"}, "repository": {"type": "git", "url": "git+https://github.com/auth0/auth0-react.git"}, "license": "MIT", "bugs": {"url": "https://github.com/auth0/auth0-react/issues"}, "homepage": "https://github.com/auth0/auth0-react#readme", "devDependencies": {"@rollup/plugin-node-resolve": "^15.0.1", "@rollup/plugin-replace": "^5.0.1", "@rollup/plugin-terser": "^0.4.3", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.1.0", "@testing-library/dom": "^10.4.0", "@types/jest": "^29.2.3", "@types/react": "19.0.7", "@types/react-dom": "19.0.3", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "browserstack-cypress-cli": "^1.19.1", "cypress": "^13.1.0", "eslint": "^8.28.0", "eslint-plugin-react": "^7.31.11", "eslint-plugin-react-hooks": "^4.6.0", "husky": "^8.0.3", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1", "jest-junit": "^15.0.0", "oidc-provider": "^8.0.0", "prettier": "^2.8.1", "pretty-quick": "^3.1.3", "react": "^19.0.0", "react-dom": "19.0.0", "rollup": "^3.7.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-delete": "^2.0.0", "rollup-plugin-dev": "^1.1.3", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-serve": "^2.0.2", "rollup-plugin-typescript2": "^0.36.0", "start-server-and-test": "^2.0", "ts-jest": "^29.0.3", "tslib": "^2.4.1", "typedoc": "^0.25.3", "typescript": "^4.9.4"}, "peerDependencies": {"react": "^16.11.0 || ^17 || ^18 || ^19", "react-dom": "^16.11.0 || ^17 || ^18 || ^19"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.3"}}