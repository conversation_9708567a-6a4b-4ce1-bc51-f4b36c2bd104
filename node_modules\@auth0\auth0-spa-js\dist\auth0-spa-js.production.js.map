{"version": 3, "file": "auth0-spa-js.production.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../node_modules/browser-tabs-lock/processLock.js", "../node_modules/browser-tabs-lock/index.js", "../src/constants.ts", "../src/version.ts", "../src/errors.ts", "../src/utils.ts", "../src/http.ts", "../src/worker/worker.utils.ts", "../src/api.ts", "../src/scope.ts", "../src/cache/shared.ts", "../src/cache/cache-localstorage.ts", "../src/cache/cache-memory.ts", "../src/cache/cache-manager.ts", "../src/transaction-manager.ts", "../src/jwt.ts", "../node_modules/es-cookie/src/es-cookie.js", "../src/storage.ts", "../src/promise-utils.ts", "../src/cache/key-manifest.ts", "../src/Auth0Client.utils.ts", "../src/Auth0Client.ts", "../src/index.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ProcessLocking = /** @class */ (function () {\n    function ProcessLocking() {\n        var _this = this;\n        this.locked = new Map();\n        this.addToLocked = function (key, toAdd) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined) {\n                if (toAdd === undefined) {\n                    _this.locked.set(key, []);\n                }\n                else {\n                    _this.locked.set(key, [toAdd]);\n                }\n            }\n            else {\n                if (toAdd !== undefined) {\n                    callbacks.unshift(toAdd);\n                    _this.locked.set(key, callbacks);\n                }\n            }\n        };\n        this.isLocked = function (key) {\n            return _this.locked.has(key);\n        };\n        this.lock = function (key) {\n            return new Promise(function (resolve, reject) {\n                if (_this.isLocked(key)) {\n                    _this.addToLocked(key, resolve);\n                }\n                else {\n                    _this.addToLocked(key);\n                    resolve();\n                }\n            });\n        };\n        this.unlock = function (key) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined || callbacks.length === 0) {\n                _this.locked.delete(key);\n                return;\n            }\n            var toCall = callbacks.pop();\n            _this.locked.set(key, callbacks);\n            if (toCall !== undefined) {\n                setTimeout(toCall, 0);\n            }\n        };\n    }\n    ProcessLocking.getInstance = function () {\n        if (ProcessLocking.instance === undefined) {\n            ProcessLocking.instance = new ProcessLocking();\n        }\n        return ProcessLocking.instance;\n    };\n    return ProcessLocking;\n}());\nfunction getLock() {\n    return ProcessLocking.getInstance();\n}\nexports.default = getLock;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar _this = this;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar processLock_1 = require(\"./processLock\");\n/**\n * @author: SuperTokens (https://github.com/supertokens)\n * This library was created as a part of a larger project, SuperTokens(https://supertokens.io) - the best session management solution.\n * You can also check out our other projects on https://github.com/supertokens\n *\n * To contribute to this package visit https://github.com/supertokens/browser-tabs-lock\n * If you face any problems you can file an issue on https://github.com/supertokens/browser-tabs-lock/issues\n *\n * If you have any questions or if you just want to say hi visit https://supertokens.io/discord\n */\n/**\n * @constant\n * @type {string}\n * @default\n * @description All the locks taken by this package will have this as prefix\n*/\nvar LOCK_STORAGE_KEY = 'browser-tabs-lock-key';\nvar DEFAULT_STORAGE_HANDLER = {\n    key: function (index) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    getItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    clear: function () { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, window.localStorage.clear()];\n        });\n    }); },\n    removeItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    setItem: function (key, value) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    keySync: function (index) {\n        return window.localStorage.key(index);\n    },\n    getItemSync: function (key) {\n        return window.localStorage.getItem(key);\n    },\n    clearSync: function () {\n        return window.localStorage.clear();\n    },\n    removeItemSync: function (key) {\n        return window.localStorage.removeItem(key);\n    },\n    setItemSync: function (key, value) {\n        return window.localStorage.setItem(key, value);\n    },\n};\n/**\n * @function delay\n * @param {number} milliseconds - How long the delay should be in terms of milliseconds\n * @returns {Promise<void>}\n */\nfunction delay(milliseconds) {\n    return new Promise(function (resolve) { return setTimeout(resolve, milliseconds); });\n}\n/**\n * @function generateRandomString\n * @params {number} length - How long the random string should be\n * @returns {string}\n * @description returns random string whose length is equal to the length passed as parameter\n */\nfunction generateRandomString(length) {\n    var CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz';\n    var randomstring = '';\n    for (var i = 0; i < length; i++) {\n        var INDEX = Math.floor(Math.random() * CHARS.length);\n        randomstring += CHARS[INDEX];\n    }\n    return randomstring;\n}\n/**\n * @function getLockId\n * @returns {string}\n * @description Generates an id which will be unique for the browser tab\n */\nfunction getLockId() {\n    return Date.now().toString() + generateRandomString(15);\n}\nvar SuperTokensLock = /** @class */ (function () {\n    function SuperTokensLock(storageHandler) {\n        this.acquiredIatSet = new Set();\n        this.storageHandler = undefined;\n        this.id = getLockId();\n        this.acquireLock = this.acquireLock.bind(this);\n        this.releaseLock = this.releaseLock.bind(this);\n        this.releaseLock__private__ = this.releaseLock__private__.bind(this);\n        this.waitForSomethingToChange = this.waitForSomethingToChange.bind(this);\n        this.refreshLockWhileAcquired = this.refreshLockWhileAcquired.bind(this);\n        this.storageHandler = storageHandler;\n        if (SuperTokensLock.waiters === undefined) {\n            SuperTokensLock.waiters = [];\n        }\n    }\n    /**\n     * @async\n     * @memberOf Lock\n     * @function acquireLock\n     * @param {string} lockKey - Key for which the lock is being acquired\n     * @param {number} [timeout=5000] - Maximum time for which the function will wait to acquire the lock\n     * @returns {Promise<boolean>}\n     * @description Will return true if lock is being acquired, else false.\n     *              Also the lock can be acquired for maximum 10 secs\n     */\n    SuperTokensLock.prototype.acquireLock = function (lockKey, timeout) {\n        if (timeout === void 0) { timeout = 5000; }\n        return __awaiter(this, void 0, void 0, function () {\n            var iat, MAX_TIME, STORAGE_KEY, STORAGE, lockObj, TIMEOUT_KEY, lockObjPostDelay, parsedLockObjPostDelay;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        iat = Date.now() + generateRandomString(4);\n                        MAX_TIME = Date.now() + timeout;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        _a.label = 1;\n                    case 1:\n                        if (!(Date.now() < MAX_TIME)) return [3 /*break*/, 8];\n                        return [4 /*yield*/, delay(30)];\n                    case 2:\n                        _a.sent();\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (!(lockObj === null)) return [3 /*break*/, 5];\n                        TIMEOUT_KEY = this.id + \"-\" + lockKey + \"-\" + iat;\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        return [4 /*yield*/, delay(Math.floor(Math.random() * 25))];\n                    case 3:\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        _a.sent();\n                        STORAGE.setItemSync(STORAGE_KEY, JSON.stringify({\n                            id: this.id,\n                            iat: iat,\n                            timeoutKey: TIMEOUT_KEY,\n                            timeAcquired: Date.now(),\n                            timeRefreshed: Date.now()\n                        }));\n                        return [4 /*yield*/, delay(30)];\n                    case 4:\n                        _a.sent(); // this is to prevent race conditions. This time must be more than the time it takes for storage.setItem\n                        lockObjPostDelay = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObjPostDelay !== null) {\n                            parsedLockObjPostDelay = JSON.parse(lockObjPostDelay);\n                            if (parsedLockObjPostDelay.id === this.id && parsedLockObjPostDelay.iat === iat) {\n                                this.acquiredIatSet.add(iat);\n                                this.refreshLockWhileAcquired(STORAGE_KEY, iat);\n                                return [2 /*return*/, true];\n                            }\n                        }\n                        return [3 /*break*/, 7];\n                    case 5:\n                        SuperTokensLock.lockCorrector(this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler);\n                        return [4 /*yield*/, this.waitForSomethingToChange(MAX_TIME)];\n                    case 6:\n                        _a.sent();\n                        _a.label = 7;\n                    case 7:\n                        iat = Date.now() + generateRandomString(4);\n                        return [3 /*break*/, 1];\n                    case 8: return [2 /*return*/, false];\n                }\n            });\n        });\n    };\n    SuperTokensLock.prototype.refreshLockWhileAcquired = function (storageKey, iat) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {\n                    var STORAGE, lockObj, parsedLockObj;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0: return [4 /*yield*/, processLock_1.default().lock(iat)];\n                            case 1:\n                                _a.sent();\n                                if (!this.acquiredIatSet.has(iat)) {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                                lockObj = STORAGE.getItemSync(storageKey);\n                                if (lockObj !== null) {\n                                    parsedLockObj = JSON.parse(lockObj);\n                                    parsedLockObj.timeRefreshed = Date.now();\n                                    STORAGE.setItemSync(storageKey, JSON.stringify(parsedLockObj));\n                                    processLock_1.default().unlock(iat);\n                                }\n                                else {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                this.refreshLockWhileAcquired(storageKey, iat);\n                                return [2 /*return*/];\n                        }\n                    });\n                }); }, 1000);\n                return [2 /*return*/];\n            });\n        });\n    };\n    SuperTokensLock.prototype.waitForSomethingToChange = function (MAX_TIME) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, new Promise(function (resolve) {\n                            var resolvedCalled = false;\n                            var startedAt = Date.now();\n                            var MIN_TIME_TO_WAIT = 50; // ms\n                            var removedListeners = false;\n                            function stopWaiting() {\n                                if (!removedListeners) {\n                                    window.removeEventListener('storage', stopWaiting);\n                                    SuperTokensLock.removeFromWaiting(stopWaiting);\n                                    clearTimeout(timeOutId);\n                                    removedListeners = true;\n                                }\n                                if (!resolvedCalled) {\n                                    resolvedCalled = true;\n                                    var timeToWait = MIN_TIME_TO_WAIT - (Date.now() - startedAt);\n                                    if (timeToWait > 0) {\n                                        setTimeout(resolve, timeToWait);\n                                    }\n                                    else {\n                                        resolve(null);\n                                    }\n                                }\n                            }\n                            window.addEventListener('storage', stopWaiting);\n                            SuperTokensLock.addToWaiting(stopWaiting);\n                            var timeOutId = setTimeout(stopWaiting, Math.max(0, MAX_TIME - Date.now()));\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    SuperTokensLock.addToWaiting = function (func) {\n        this.removeFromWaiting(func);\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters.push(func);\n    };\n    SuperTokensLock.removeFromWaiting = function (func) {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters = SuperTokensLock.waiters.filter(function (i) { return i !== func; });\n    };\n    SuperTokensLock.notifyWaiters = function () {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        var waiters = SuperTokensLock.waiters.slice(); // so that if Lock.waiters is changed it's ok.\n        waiters.forEach(function (i) { return i(); });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.releaseLock__private__(lockKey)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock__private__ = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            var STORAGE, STORAGE_KEY, lockObj, parsedlockObj;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObj === null) {\n                            return [2 /*return*/];\n                        }\n                        parsedlockObj = JSON.parse(lockObj);\n                        if (!(parsedlockObj.id === this.id)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, processLock_1.default().lock(parsedlockObj.iat)];\n                    case 1:\n                        _a.sent();\n                        this.acquiredIatSet.delete(parsedlockObj.iat);\n                        STORAGE.removeItemSync(STORAGE_KEY);\n                        processLock_1.default().unlock(parsedlockObj.iat);\n                        SuperTokensLock.notifyWaiters();\n                        _a.label = 2;\n                    case 2: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * @function lockCorrector\n     * @returns {void}\n     * @description If a lock is acquired by a tab and the tab is closed before the lock is\n     *              released, this function will release those locks\n     */\n    SuperTokensLock.lockCorrector = function (storageHandler) {\n        var MIN_ALLOWED_TIME = Date.now() - 5000;\n        var STORAGE = storageHandler;\n        var KEYS = [];\n        var currIndex = 0;\n        while (true) {\n            var key = STORAGE.keySync(currIndex);\n            if (key === null) {\n                break;\n            }\n            KEYS.push(key);\n            currIndex++;\n        }\n        var notifyWaiters = false;\n        for (var i = 0; i < KEYS.length; i++) {\n            var LOCK_KEY = KEYS[i];\n            if (LOCK_KEY.includes(LOCK_STORAGE_KEY)) {\n                var lockObj = STORAGE.getItemSync(LOCK_KEY);\n                if (lockObj !== null) {\n                    var parsedlockObj = JSON.parse(lockObj);\n                    if ((parsedlockObj.timeRefreshed === undefined && parsedlockObj.timeAcquired < MIN_ALLOWED_TIME) ||\n                        (parsedlockObj.timeRefreshed !== undefined && parsedlockObj.timeRefreshed < MIN_ALLOWED_TIME)) {\n                        STORAGE.removeItemSync(LOCK_KEY);\n                        notifyWaiters = true;\n                    }\n                }\n            }\n        }\n        if (notifyWaiters) {\n            SuperTokensLock.notifyWaiters();\n        }\n    };\n    SuperTokensLock.waiters = undefined;\n    return SuperTokensLock;\n}());\nexports.default = SuperTokensLock;\n", "import { PopupConfigOptions } from './global';\nimport version from './version';\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS = 60;\n\n/**\n * @ignore\n */\nexport const DEFAULT_POPUP_CONFIG_OPTIONS: PopupConfigOptions = {\n  timeoutInSeconds: DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n};\n\n/**\n * @ignore\n */\nexport const DEFAULT_SILENT_TOKEN_RETRY_COUNT = 3;\n\n/**\n * @ignore\n */\nexport const CLEANUP_IFRAME_TIMEOUT_IN_SECONDS = 2;\n\n/**\n * @ignore\n */\nexport const DEFAULT_FETCH_TIMEOUT_MS = 10000;\n\nexport const CACHE_LOCATION_MEMORY = 'memory';\nexport const CACHE_LOCATION_LOCAL_STORAGE = 'localstorage';\n\n/**\n * @ignore\n */\nexport const MISSING_REFRESH_TOKEN_ERROR_MESSAGE = 'Missing Refresh Token';\n\n/**\n * @ignore\n */\nexport const INVALID_REFRESH_TOKEN_ERROR_MESSAGE = 'invalid refresh token';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SCOPE = 'openid profile email';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SESSION_CHECK_EXPIRY_DAYS = 1;\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTH0_CLIENT = {\n  name: 'auth0-spa-js',\n  version: version\n};\n\nexport const DEFAULT_NOW_PROVIDER = () => Date.now();\n", "export default '2.2.0';\n", "/**\n * Thrown when network requests to the Auth server fail.\n */\nexport class GenericError extends Error {\n  constructor(public error: string, public error_description: string) {\n    super(error_description);\n    Object.setPrototypeOf(this, GenericError.prototype);\n  }\n\n  static fromPayload({\n    error,\n    error_description\n  }: {\n    error: string;\n    error_description: string;\n  }) {\n    return new GenericError(error, error_description);\n  }\n}\n\n/**\n * Thrown when handling the redirect callback fails, will be one of Auth0's\n * Authentication API's Standard Error Responses: https://auth0.com/docs/api/authentication?javascript#standard-error-responses\n */\nexport class AuthenticationError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public state: string,\n    public appState: any = null\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, AuthenticationError.prototype);\n  }\n}\n\n/**\n * Thrown when silent auth times out (usually due to a configuration issue) or\n * when network requests to the Auth server timeout.\n */\nexport class TimeoutError extends GenericError {\n  constructor() {\n    super('timeout', 'Timeout');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Error thrown when the login popup times out (if the user does not complete auth)\n */\nexport class PopupTimeoutError extends TimeoutError {\n  constructor(public popup: Window) {\n    super();\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupTimeoutError.prototype);\n  }\n}\n\nexport class PopupCancelledError extends GenericError {\n  constructor(public popup: Window) {\n    super('cancelled', 'Popup closed');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupCancelledError.prototype);\n  }\n}\n\n/**\n * Error thrown when the token exchange results in a `mfa_required` error\n */\nexport class MfaRequiredError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public mfa_token: string\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, MfaRequiredError.prototype);\n  }\n}\n\n/**\n * Error thrown when there is no refresh token to use\n */\nexport class MissingRefreshTokenError extends GenericError {\n  constructor(public audience: string, public scope: string) {\n    super(\n      'missing_refresh_token',\n      `Missing Refresh Token (audience: '${valueOrEmptyString(audience, [\n        'default'\n      ])}', scope: '${valueOrEmptyString(scope)}')`\n    );\n    Object.setPrototypeOf(this, MissingRefreshTokenError.prototype);\n  }\n}\n\n/**\n * Returns an empty string when value is falsy, or when it's value is included in the exclude argument.\n * @param value The value to check\n * @param exclude An array of values that should result in an empty string.\n * @returns The value, or an empty string when falsy or included in the exclude argument.\n */\nfunction valueOrEmptyString(value: string, exclude: string[] = []) {\n  return value && !exclude.includes(value) ? value : '';\n}\n", "import { AuthenticationResult, PopupConfigOptions } from './global';\n\nimport {\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  CLEANUP_IFRAME_TIMEOUT_IN_SECONDS\n} from './constants';\n\nimport {\n  PopupTimeoutError,\n  TimeoutError,\n  GenericError,\n  PopupCancelledError\n} from './errors';\n\nexport const parseAuthenticationResult = (\n  queryString: string\n): AuthenticationResult => {\n  if (queryString.indexOf('#') > -1) {\n    queryString = queryString.substring(0, queryString.indexOf('#'));\n  }\n\n  const searchParams = new URLSearchParams(queryString);\n\n  return {\n    state: searchParams.get('state')!,\n    code: searchParams.get('code') || undefined,\n    error: searchParams.get('error') || undefined,\n    error_description: searchParams.get('error_description') || undefined\n  };\n};\n\nexport const runIframe = (\n  authorizeUrl: string,\n  eventOrigin: string,\n  timeoutInSeconds: number = DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n) => {\n  return new Promise<AuthenticationResult>((res, rej) => {\n    const iframe = window.document.createElement('iframe');\n\n    iframe.setAttribute('width', '0');\n    iframe.setAttribute('height', '0');\n    iframe.style.display = 'none';\n\n    const removeIframe = () => {\n      if (window.document.body.contains(iframe)) {\n        window.document.body.removeChild(iframe);\n        window.removeEventListener('message', iframeEventHandler, false);\n      }\n    };\n\n    let iframeEventHandler: (e: MessageEvent) => void;\n\n    const timeoutSetTimeoutId = setTimeout(() => {\n      rej(new TimeoutError());\n      removeIframe();\n    }, timeoutInSeconds * 1000);\n\n    iframeEventHandler = function (e: MessageEvent) {\n      if (e.origin != eventOrigin) return;\n      if (!e.data || e.data.type !== 'authorization_response') return;\n\n      const eventSource = e.source;\n\n      if (eventSource) {\n        (eventSource as any).close();\n      }\n\n      e.data.response.error\n        ? rej(GenericError.fromPayload(e.data.response))\n        : res(e.data.response);\n\n      clearTimeout(timeoutSetTimeoutId);\n      window.removeEventListener('message', iframeEventHandler, false);\n\n      // Delay the removal of the iframe to prevent hanging loading status\n      // in Chrome: https://github.com/auth0/auth0-spa-js/issues/240\n      setTimeout(removeIframe, CLEANUP_IFRAME_TIMEOUT_IN_SECONDS * 1000);\n    };\n\n    window.addEventListener('message', iframeEventHandler, false);\n    window.document.body.appendChild(iframe);\n    iframe.setAttribute('src', authorizeUrl);\n  });\n};\n\nexport const openPopup = (url: string) => {\n  const width = 400;\n  const height = 600;\n  const left = window.screenX + (window.innerWidth - width) / 2;\n  const top = window.screenY + (window.innerHeight - height) / 2;\n\n  return window.open(\n    url,\n    'auth0:authorize:popup',\n    `left=${left},top=${top},width=${width},height=${height},resizable,scrollbars=yes,status=1`\n  );\n};\n\nexport const runPopup = (config: PopupConfigOptions) => {\n  return new Promise<AuthenticationResult>((resolve, reject) => {\n    let popupEventListener: (e: MessageEvent) => void;\n\n    // Check each second if the popup is closed triggering a PopupCancelledError\n    const popupTimer = setInterval(() => {\n      if (config.popup && config.popup.closed) {\n        clearInterval(popupTimer);\n        clearTimeout(timeoutId);\n        window.removeEventListener('message', popupEventListener, false);\n        reject(new PopupCancelledError(config.popup));\n      }\n    }, 1000);\n\n    const timeoutId = setTimeout(() => {\n      clearInterval(popupTimer);\n      reject(new PopupTimeoutError(config.popup));\n      window.removeEventListener('message', popupEventListener, false);\n    }, (config.timeoutInSeconds || DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS) * 1000);\n\n    popupEventListener = function (e: MessageEvent) {\n      if (!e.data || e.data.type !== 'authorization_response') {\n        return;\n      }\n\n      clearTimeout(timeoutId);\n      clearInterval(popupTimer);\n      window.removeEventListener('message', popupEventListener, false);\n      config.popup.close();\n\n      if (e.data.response.error) {\n        return reject(GenericError.fromPayload(e.data.response));\n      }\n\n      resolve(e.data.response);\n    };\n\n    window.addEventListener('message', popupEventListener);\n  });\n};\n\nexport const getCrypto = () => {\n  return window.crypto;\n};\n\nexport const createRandomString = () => {\n  const charset =\n    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.';\n  let random = '';\n  const randomValues = Array.from(\n    getCrypto().getRandomValues(new Uint8Array(43))\n  );\n  randomValues.forEach(v => (random += charset[v % charset.length]));\n  return random;\n};\n\nexport const encode = (value: string) => btoa(value);\nexport const decode = (value: string) => atob(value);\n\nconst stripUndefined = (params: any) => {\n  return Object.keys(params)\n    .filter(k => typeof params[k] !== 'undefined')\n    .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {});\n};\n\nexport const createQueryParams = ({ clientId: client_id, ...params }: any) => {\n  return new URLSearchParams(\n    stripUndefined({ client_id, ...params })\n  ).toString();\n};\n\nexport const sha256 = async (s: string) => {\n  const digestOp: any = getCrypto().subtle.digest(\n    { name: 'SHA-256' },\n    new TextEncoder().encode(s)\n  );\n\n  return await digestOp;\n};\n\nconst urlEncodeB64 = (input: string) => {\n  const b64Chars: { [index: string]: string } = { '+': '-', '/': '_', '=': '' };\n  return input.replace(/[+/=]/g, (m: string) => b64Chars[m]);\n};\n\n// https://stackoverflow.com/questions/30106476/\nconst decodeB64 = (input: string) =>\n  decodeURIComponent(\n    atob(input)\n      .split('')\n      .map(c => {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n      })\n      .join('')\n  );\n\nexport const urlDecodeB64 = (input: string) =>\n  decodeB64(input.replace(/_/g, '/').replace(/-/g, '+'));\n\nexport const bufferToBase64UrlEncoded = (input: number[] | Uint8Array) => {\n  const ie11SafeInput = new Uint8Array(input);\n  return urlEncodeB64(\n    window.btoa(String.fromCharCode(...Array.from(ie11SafeInput)))\n  );\n};\n\nexport const validateCrypto = () => {\n  if (!getCrypto()) {\n    throw new Error(\n      'For security reasons, `window.crypto` is required to run `auth0-spa-js`.'\n    );\n  }\n  if (typeof getCrypto().subtle === 'undefined') {\n    throw new Error(`\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    `);\n  }\n};\n\n/**\n * @ignore\n */\nexport const getDomain = (domainUrl: string) => {\n  if (!/^https?:\\/\\//.test(domainUrl)) {\n    return `https://${domainUrl}`;\n  }\n\n  return domainUrl;\n};\n\n/**\n * @ignore\n */\nexport const getTokenIssuer = (\n  issuer: string | undefined,\n  domainUrl: string\n) => {\n  if (issuer) {\n    return issuer.startsWith('https://') ? issuer : `https://${issuer}/`;\n  }\n\n  return `${domainUrl}/`;\n};\n\nexport const parseNumber = (value: any): number | undefined => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  return parseInt(value, 10) || undefined;\n};\n", "import {\n  DEFAULT_FETCH_TIMEOUT_MS,\n  DEFAULT_SILENT_TOKEN_RETRY_COUNT\n} from './constants';\n\nimport { sendMessage } from './worker/worker.utils';\nimport { FetchOptions } from './global';\nimport {\n  GenericError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport const createAbortController = () => new AbortController();\n\nconst dofetch = async (fetchUrl: string, fetchOptions: FetchOptions) => {\n  const response = await fetch(fetchUrl, fetchOptions);\n\n  return {\n    ok: response.ok,\n    json: await response.json()\n  };\n};\n\nconst fetchWithoutWorker = async (\n  fetchUrl: string,\n  fetchOptions: FetchOptions,\n  timeout: number\n) => {\n  const controller = createAbortController();\n  fetchOptions.signal = controller.signal;\n\n  let timeoutId: NodeJS.Timeout;\n\n  // The promise will resolve with one of these two promises (the fetch or the timeout), whichever completes first.\n  return Promise.race([\n    dofetch(fetchUrl, fetchOptions),\n\n    new Promise((_, reject) => {\n      timeoutId = setTimeout(() => {\n        controller.abort();\n        reject(new Error(\"Timeout when executing 'fetch'\"));\n      }, timeout);\n    })\n  ]).finally(() => {\n    clearTimeout(timeoutId);\n  });\n};\n\nconst fetchWithWorker = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  timeout: number,\n  worker: Worker,\n  useFormData?: boolean\n) => {\n  return sendMessage(\n    {\n      auth: {\n        audience,\n        scope\n      },\n      timeout,\n      fetchUrl,\n      fetchOptions,\n      useFormData\n    },\n    worker\n  );\n};\n\nexport const switchFetch = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean,\n  timeout = DEFAULT_FETCH_TIMEOUT_MS\n): Promise<any> => {\n  if (worker) {\n    return fetchWithWorker(\n      fetchUrl,\n      audience,\n      scope,\n      fetchOptions,\n      timeout,\n      worker,\n      useFormData\n    );\n  } else {\n    return fetchWithoutWorker(fetchUrl, fetchOptions, timeout);\n  }\n};\n\nexport async function getJSON<T>(\n  url: string,\n  timeout: number | undefined,\n  audience: string,\n  scope: string,\n  options: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean\n): Promise<T> {\n  let fetchError: null | Error = null;\n  let response: any;\n\n  for (let i = 0; i < DEFAULT_SILENT_TOKEN_RETRY_COUNT; i++) {\n    try {\n      response = await switchFetch(\n        url,\n        audience,\n        scope,\n        options,\n        worker,\n        useFormData,\n        timeout\n      );\n      fetchError = null;\n      break;\n    } catch (e) {\n      // Fetch only fails in the case of a network issue, so should be\n      // retried here. Failure status (4xx, 5xx, etc) return a resolved Promise\n      // with the failure in the body.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\n      fetchError = e;\n    }\n  }\n\n  if (fetchError) {\n    throw fetchError;\n  }\n\n  const {\n    json: { error, error_description, ...data },\n    ok\n  } = response;\n\n  if (!ok) {\n    const errorMessage =\n      error_description || `HTTP error. Unable to fetch ${url}`;\n\n    if (error === 'mfa_required') {\n      throw new MfaRequiredError(error, errorMessage, data.mfa_token);\n    }\n\n    if (error === 'missing_refresh_token') {\n      throw new MissingRefreshTokenError(audience, scope);\n    }\n\n    throw new GenericError(error || 'request_error', errorMessage);\n  }\n\n  return data;\n}\n", "import { WorkerRefreshTokenMessage } from './worker.types';\n\n/**\n * Sends the specified message to the web worker\n * @param message The message to send\n * @param to The worker to send the message to\n */\nexport const sendMessage = (message: WorkerRefreshTokenMessage, to: Worker) =>\n  new Promise(function (resolve, reject) {\n    const messageChannel = new MessageChannel();\n\n    messageChannel.port1.onmessage = function (event) {\n      // Only for fetch errors, as these get retried\n      if (event.data.error) {\n        reject(new Error(event.data.error));\n      } else {\n        resolve(event.data);\n      }\n      messageChannel.port1.close();\n    };\n\n    to.postMessage(message, [messageChannel.port2]);\n  });\n", "import { TokenEndpointOptions, TokenEndpointResponse } from './global';\nimport { DEFAULT_AUTH0_CLIENT } from './constants';\nimport { getJSON } from './http';\nimport { createQueryParams } from './utils';\n\nexport async function oauthToken(\n  {\n    baseUrl,\n    timeout,\n    audience,\n    scope,\n    auth0Client,\n    useFormData,\n    ...options\n  }: TokenEndpointOptions,\n  worker?: Worker\n) {\n  const body = useFormData\n    ? createQueryParams(options)\n    : JSON.stringify(options);\n\n  return await getJSON<TokenEndpointResponse>(\n    `${baseUrl}/oauth/token`,\n    timeout,\n    audience || 'default',\n    scope,\n    {\n      method: 'POST',\n      body,\n      headers: {\n        'Content-Type': useFormData\n          ? 'application/x-www-form-urlencoded'\n          : 'application/json',\n        'Auth0-Client': btoa(\n          JSON.stringify(auth0Client || DEFAULT_AUTH0_CLIENT)\n        )\n      }\n    },\n    worker,\n    useFormData\n  );\n}\n", "/**\n * @ignore\n */\nconst dedupe = (arr: string[]) => Array.from(new Set(arr));\n\n/**\n * @ignore\n */\n/**\n * Returns a string of unique scopes by removing duplicates and unnecessary whitespace.\n *\n * @param {...(string | undefined)[]} scopes - A list of scope strings or undefined values.\n * @returns {string} A string containing unique scopes separated by a single space.\n */\nexport const getUniqueScopes = (...scopes: (string | undefined)[]) => {\n  return dedupe(scopes.filter(Boolean).join(' ').trim().split(/\\s+/)).join(' ');\n};\n", "import { IdToken, User } from '../global';\n\nexport const CACHE_KEY_PREFIX = '@@auth0spajs@@';\nexport const CACHE_KEY_ID_TOKEN_SUFFIX = '@@user@@';\n\nexport type CacheKeyData = {\n  audience?: string;\n  scope?: string;\n  clientId: string;\n};\n\nexport class C<PERSON><PERSON>ey {\n  public clientId: string;\n  public scope?: string;\n  public audience?: string;\n\n  constructor(\n    data: CacheKeyData,\n    public prefix: string = CACHE_KEY_PREFIX,\n    public suffix?: string\n  ) {\n    this.clientId = data.clientId;\n    this.scope = data.scope;\n    this.audience = data.audience;\n  }\n\n  /**\n   * Converts this `<PERSON><PERSON><PERSON><PERSON>` instance into a string for use in a cache\n   * @returns A string representation of the key\n   */\n  toKey(): string {\n    return [this.prefix, this.clientId, this.audience, this.scope, this.suffix]\n      .filter(Boolean)\n      .join('::');\n  }\n\n  /**\n   * Converts a cache key string into a `CacheKey` instance.\n   * @param key The key to convert\n   * @returns An instance of `<PERSON><PERSON><PERSON><PERSON>`\n   */\n  static from<PERSON><PERSON>(key: string): <PERSON>ache<PERSON><PERSON> {\n    const [prefix, clientId, audience, scope] = key.split('::');\n\n    return new CacheKey({ clientId, scope, audience }, prefix);\n  }\n\n  /**\n   * Utility function to build a `CacheKey` instance from a cache entry\n   * @param entry The entry\n   * @returns An instance of `CacheKey`\n   */\n  static fromCacheEntry(entry: CacheEntry): CacheKey {\n    const { scope, audience, client_id: clientId } = entry;\n\n    return new CacheKey({\n      scope,\n      audience,\n      clientId\n    });\n  }\n}\n\nexport interface DecodedToken {\n  claims: IdToken;\n  user: User;\n}\n\nexport interface IdTokenEntry {\n  id_token: string;\n  decodedToken: DecodedToken;\n}\n\nexport type CacheEntry = {\n  id_token?: string;\n  access_token: string;\n  expires_in: number;\n  decodedToken?: DecodedToken;\n  audience: string;\n  scope: string;\n  client_id: string;\n  refresh_token?: string;\n  oauthTokenScope?: string;\n};\n\nexport type WrappedCacheEntry = {\n  body: Partial<CacheEntry>;\n  expiresAt: number;\n};\n\nexport type KeyManifestEntry = {\n  keys: string[];\n};\n\nexport type Cacheable = WrappedCacheEntry | KeyManifestEntry;\n\nexport type MaybePromise<T> = Promise<T> | T;\n\nexport interface ICache {\n  set<T = Cacheable>(key: string, entry: T): MaybePromise<void>;\n  get<T = Cacheable>(key: string): MaybePromise<T | undefined>;\n  remove(key: string): MaybePromise<void>;\n  allKeys?(): MaybePromise<string[]>;\n}\n", "import { ICache, Cacheable, CACHE_KEY_PREFIX, Maybe<PERSON>rom<PERSON> } from './shared';\n\nexport class LocalStorageCache implements ICache {\n  public set<T = Cacheable>(key: string, entry: T) {\n    localStorage.setItem(key, JSON.stringify(entry));\n  }\n\n  public get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n    const json = window.localStorage.getItem(key);\n\n    if (!json) return;\n\n    try {\n      const payload = JSON.parse(json) as T;\n      return payload;\n      /* c8 ignore next 3 */\n    } catch (e) {\n      return;\n    }\n  }\n\n  public remove(key: string) {\n    localStorage.removeItem(key);\n  }\n\n  public allKeys() {\n    return Object.keys(window.localStorage).filter(key =>\n      key.startsWith(CACHE_KEY_PREFIX)\n    );\n  }\n}\n", "import { Cacheable, ICache, MaybePromise } from './shared';\n\nexport class InMemoryCache {\n  public enclosedCache: ICache = (function () {\n    let cache: Record<string, unknown> = {};\n\n    return {\n      set<T = Cacheable>(key: string, entry: T) {\n        cache[key] = entry;\n      },\n\n      get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n        const cacheEntry = cache[key] as T;\n\n        if (!cacheEntry) {\n          return;\n        }\n\n        return cacheEntry;\n      },\n\n      remove(key: string) {\n        delete cache[key];\n      },\n\n      allKeys(): string[] {\n        return Object.keys(cache);\n      }\n    };\n  })();\n}\n", "import { DEFAULT_NOW_PROVIDER } from '../constants';\nimport { CacheKeyManifest } from './key-manifest';\n\nimport {\n  CacheEntry,\n  ICache,\n  CacheKey,\n  CACHE_KEY_PREFIX,\n  WrappedCacheEntry,\n  DecodedToken,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  IdTokenEntry\n} from './shared';\n\nconst DEFAULT_EXPIRY_ADJUSTMENT_SECONDS = 0;\n\nexport class CacheManager {\n  private nowProvider: () => number | Promise<number>;\n\n  constructor(\n    private cache: ICache,\n    private keyManifest?: CacheKeyManifest,\n    nowProvider?: () => number | Promise<number>\n  ) {\n    this.nowProvider = nowProvider || DEFAULT_NOW_PROVIDER;\n  }\n\n  async setIdToken(\n    clientId: string,\n    idToken: string,\n    decodedToken: DecodedToken\n  ): Promise<void> {\n    const cacheKey = this.getIdTokenCacheKey(clientId);\n    await this.cache.set(cacheKey, {\n      id_token: idToken,\n      decodedToken\n    });\n    await this.keyManifest?.add(cacheKey);\n  }\n\n  async getIdToken(cacheKey: <PERSON>acheK<PERSON>): Promise<IdTokenEntry | undefined> {\n    const entry = await this.cache.get<IdTokenEntry>(\n      this.getIdTokenCacheKey(cacheKey.clientId)\n    );\n\n    if (!entry && cacheKey.scope && cacheKey.audience) {\n      const entryByScope = await this.get(cacheKey);\n\n      if (!entryByScope) {\n        return;\n      }\n\n      if (!entryByScope.id_token || !entryByScope.decodedToken) {\n        return;\n      }\n\n      return {\n        id_token: entryByScope.id_token,\n        decodedToken: entryByScope.decodedToken\n      };\n    }\n\n    if (!entry) {\n      return;\n    }\n\n    return { id_token: entry.id_token, decodedToken: entry.decodedToken };\n  }\n\n  async get(\n    cacheKey: CacheKey,\n    expiryAdjustmentSeconds = DEFAULT_EXPIRY_ADJUSTMENT_SECONDS\n  ): Promise<Partial<CacheEntry> | undefined> {\n    let wrappedEntry = await this.cache.get<WrappedCacheEntry>(\n      cacheKey.toKey()\n    );\n\n    if (!wrappedEntry) {\n      const keys = await this.getCacheKeys();\n\n      if (!keys) return;\n\n      const matchedKey = this.matchExistingCacheKey(cacheKey, keys);\n\n      if (matchedKey) {\n        wrappedEntry = await this.cache.get<WrappedCacheEntry>(matchedKey);\n      }\n    }\n\n    // If we still don't have an entry, exit.\n    if (!wrappedEntry) {\n      return;\n    }\n\n    const now = await this.nowProvider();\n    const nowSeconds = Math.floor(now / 1000);\n\n    if (wrappedEntry.expiresAt - expiryAdjustmentSeconds < nowSeconds) {\n      if (wrappedEntry.body.refresh_token) {\n        wrappedEntry.body = {\n          refresh_token: wrappedEntry.body.refresh_token\n        };\n\n        await this.cache.set(cacheKey.toKey(), wrappedEntry);\n        return wrappedEntry.body;\n      }\n\n      await this.cache.remove(cacheKey.toKey());\n      await this.keyManifest?.remove(cacheKey.toKey());\n\n      return;\n    }\n\n    return wrappedEntry.body;\n  }\n\n  async set(entry: CacheEntry): Promise<void> {\n    const cacheKey = new CacheKey({\n      clientId: entry.client_id,\n      scope: entry.scope,\n      audience: entry.audience\n    });\n\n    const wrappedEntry = await this.wrapCacheEntry(entry);\n\n    await this.cache.set(cacheKey.toKey(), wrappedEntry);\n    await this.keyManifest?.add(cacheKey.toKey());\n  }\n\n  async clear(clientId?: string): Promise<void> {\n    const keys = await this.getCacheKeys();\n\n    /* c8 ignore next */\n    if (!keys) return;\n\n    await keys\n      .filter(key => (clientId ? key.includes(clientId) : true))\n      .reduce(async (memo, key) => {\n        await memo;\n        await this.cache.remove(key);\n      }, Promise.resolve());\n\n    await this.keyManifest?.clear();\n  }\n\n  private async wrapCacheEntry(entry: CacheEntry): Promise<WrappedCacheEntry> {\n    const now = await this.nowProvider();\n    const expiresInTime = Math.floor(now / 1000) + entry.expires_in;\n\n    return {\n      body: entry,\n      expiresAt: expiresInTime\n    };\n  }\n\n  private async getCacheKeys(): Promise<string[] | undefined> {\n    if (this.keyManifest) {\n      return (await this.keyManifest.get())?.keys;\n    } else if (this.cache.allKeys) {\n      return this.cache.allKeys();\n    }\n  }\n\n  /**\n   * Returns the cache key to be used to store the id token\n   * @param clientId The client id used to link to the id token\n   * @returns The constructed cache key, as a string, to store the id token\n   */\n  private getIdTokenCacheKey(clientId: string) {\n    return new CacheKey(\n      { clientId },\n      CACHE_KEY_PREFIX,\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ).toKey();\n  }\n\n  /**\n   * Finds the corresponding key in the cache based on the provided cache key.\n   * The keys inside the cache are in the format {prefix}::{clientId}::{audience}::{scope}.\n   * The first key in the cache that satisfies the following conditions is returned\n   *  - `prefix` is strict equal to Auth0's internally configured `keyPrefix`\n   *  - `clientId` is strict equal to the `cacheKey.clientId`\n   *  - `audience` is strict equal to the `cacheKey.audience`\n   *  - `scope` contains at least all the `cacheKey.scope` values\n   *  *\n   * @param keyToMatch The provided cache key\n   * @param allKeys A list of existing cache keys\n   */\n  private matchExistingCacheKey(keyToMatch: CacheKey, allKeys: Array<string>) {\n    return allKeys.filter(key => {\n      const cacheKey = CacheKey.fromKey(key);\n      const scopeSet = new Set(cacheKey.scope && cacheKey.scope.split(' '));\n      const scopesToMatch = keyToMatch.scope?.split(' ') || [];\n\n      const hasAllScopes =\n        cacheKey.scope &&\n        scopesToMatch.reduce(\n          (acc, current) => acc && scopeSet.has(current),\n          true\n        );\n\n      return (\n        cacheKey.prefix === CACHE_KEY_PREFIX &&\n        cacheKey.clientId === keyToMatch.clientId &&\n        cacheKey.audience === keyToMatch.audience &&\n        hasAllScopes\n      );\n    })[0];\n  }\n}\n", "import { ClientStorage } from './storage';\n\nconst TRANSACTION_STORAGE_KEY_PREFIX = 'a0.spajs.txs';\n\ninterface Transaction {\n  nonce: string;\n  scope: string;\n  audience: string;\n  appState?: any;\n  code_verifier: string;\n  redirect_uri?: string;\n  organization?: string;\n  state?: string;\n}\n\nexport class TransactionManager {\n  private storageKey: string;\n\n  constructor(\n    private storage: ClientStorage,\n    private clientId: string,\n    private cookieDomain?: string\n  ) {\n    this.storageKey = `${TRANSACTION_STORAGE_KEY_PREFIX}.${this.clientId}`;\n  }\n\n  public create(transaction: Transaction) {\n    this.storage.save(this.storageKey, transaction, {\n      daysUntilExpire: 1,\n      cookieDomain: this.cookieDomain\n    });\n  }\n\n  public get(): Transaction | undefined {\n    return this.storage.get(this.storageKey);\n  }\n\n  public remove() {\n    this.storage.remove(this.storageKey, {\n      cookieDomain: this.cookieDomain\n    });\n  }\n}\n", "import { urlDecodeB64 } from './utils';\nimport { IdToken, JWTVerifyOptions } from './global';\n\nconst isNumber = (n: any) => typeof n === 'number';\n\nconst idTokendecoded = [\n  'iss',\n  'aud',\n  'exp',\n  'nbf',\n  'iat',\n  'jti',\n  'azp',\n  'nonce',\n  'auth_time',\n  'at_hash',\n  'c_hash',\n  'acr',\n  'amr',\n  'sub_jwk',\n  'cnf',\n  'sip_from_tag',\n  'sip_date',\n  'sip_callid',\n  'sip_cseq_num',\n  'sip_via_branch',\n  'orig',\n  'dest',\n  'mky',\n  'events',\n  'toe',\n  'txn',\n  'rph',\n  'sid',\n  'vot',\n  'vtm'\n];\n\nexport const decode = (token: string) => {\n  const parts = token.split('.');\n  const [header, payload, signature] = parts;\n\n  if (parts.length !== 3 || !header || !payload || !signature) {\n    throw new Error('ID token could not be decoded');\n  }\n  const payloadJSON = JSON.parse(urlDecodeB64(payload));\n  const claims: IdToken = { __raw: token };\n  const user: any = {};\n  Object.keys(payloadJSON).forEach(k => {\n    claims[k] = payloadJSON[k];\n    if (!idTokendecoded.includes(k)) {\n      user[k] = payloadJSON[k];\n    }\n  });\n  return {\n    encoded: { header, payload, signature },\n    header: JSON.parse(urlDecodeB64(header)),\n    claims,\n    user\n  };\n};\n\nexport const verify = (options: JWTVerifyOptions) => {\n  if (!options.id_token) {\n    throw new Error('ID token is required but missing');\n  }\n\n  const decoded = decode(options.id_token);\n\n  if (!decoded.claims.iss) {\n    throw new Error(\n      'Issuer (iss) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.claims.iss !== options.iss) {\n    throw new Error(\n      `Issuer (iss) claim mismatch in the ID token; expected \"${options.iss}\", found \"${decoded.claims.iss}\"`\n    );\n  }\n\n  if (!decoded.user.sub) {\n    throw new Error(\n      'Subject (sub) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.header.alg !== 'RS256') {\n    throw new Error(\n      `Signature algorithm of \"${decoded.header.alg}\" is not supported. Expected the ID token to be signed with \"RS256\".`\n    );\n  }\n\n  if (\n    !decoded.claims.aud ||\n    !(\n      typeof decoded.claims.aud === 'string' ||\n      Array.isArray(decoded.claims.aud)\n    )\n  ) {\n    throw new Error(\n      'Audience (aud) claim must be a string or array of strings present in the ID token'\n    );\n  }\n  if (Array.isArray(decoded.claims.aud)) {\n    if (!decoded.claims.aud.includes(options.aud)) {\n      throw new Error(\n        `Audience (aud) claim mismatch in the ID token; expected \"${\n          options.aud\n        }\" but was not one of \"${decoded.claims.aud.join(', ')}\"`\n      );\n    }\n    if (decoded.claims.aud.length > 1) {\n      if (!decoded.claims.azp) {\n        throw new Error(\n          'Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values'\n        );\n      }\n      if (decoded.claims.azp !== options.aud) {\n        throw new Error(\n          `Authorized Party (azp) claim mismatch in the ID token; expected \"${options.aud}\", found \"${decoded.claims.azp}\"`\n        );\n      }\n    }\n  } else if (decoded.claims.aud !== options.aud) {\n    throw new Error(\n      `Audience (aud) claim mismatch in the ID token; expected \"${options.aud}\" but found \"${decoded.claims.aud}\"`\n    );\n  }\n  if (options.nonce) {\n    if (!decoded.claims.nonce) {\n      throw new Error(\n        'Nonce (nonce) claim must be a string present in the ID token'\n      );\n    }\n    if (decoded.claims.nonce !== options.nonce) {\n      throw new Error(\n        `Nonce (nonce) claim mismatch in the ID token; expected \"${options.nonce}\", found \"${decoded.claims.nonce}\"`\n      );\n    }\n  }\n\n  if (options.max_age && !isNumber(decoded.claims.auth_time)) {\n    throw new Error(\n      'Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified'\n    );\n  }\n\n  /* c8 ignore next 5 */\n  if (decoded.claims.exp == null || !isNumber(decoded.claims.exp)) {\n    throw new Error(\n      'Expiration Time (exp) claim must be a number present in the ID token'\n    );\n  }\n  if (!isNumber(decoded.claims.iat)) {\n    throw new Error(\n      'Issued At (iat) claim must be a number present in the ID token'\n    );\n  }\n\n  const leeway = options.leeway || 60;\n  const now = new Date(options.now || Date.now());\n  const expDate = new Date(0);\n\n  expDate.setUTCSeconds(decoded.claims.exp + leeway);\n\n  if (now > expDate) {\n    throw new Error(\n      `Expiration Time (exp) claim error in the ID token; current time (${now}) is after expiration time (${expDate})`\n    );\n  }\n\n  if (decoded.claims.nbf != null && isNumber(decoded.claims.nbf)) {\n    const nbfDate = new Date(0);\n    nbfDate.setUTCSeconds(decoded.claims.nbf - leeway);\n    if (now < nbfDate) {\n      throw new Error(\n        `Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${now}) is before ${nbfDate}`\n      );\n    }\n  }\n\n  if (decoded.claims.auth_time != null && isNumber(decoded.claims.auth_time)) {\n    const authTimeDate = new Date(0);\n    authTimeDate.setUTCSeconds(\n      parseInt(decoded.claims.auth_time) + (options.max_age as number) + leeway\n    );\n\n    if (now > authTimeDate) {\n      throw new Error(\n        `Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${now}) is after last auth at ${authTimeDate}`\n      );\n    }\n  }\n\n  if (options.organization) {\n    const org = options.organization.trim();\n    if (org.startsWith('org_')) {\n      const orgId = org;\n      if (!decoded.claims.org_id) {\n        throw new Error(\n          'Organization ID (org_id) claim must be a string present in the ID token'\n        );\n      } else if (orgId !== decoded.claims.org_id) {\n        throw new Error(\n          `Organization ID (org_id) claim mismatch in the ID token; expected \"${orgId}\", found \"${decoded.claims.org_id}\"`\n        );\n      }\n    } else {\n      const orgName = org.toLowerCase();\n      // TODO should we verify if there is an `org_id` claim?\n      if (!decoded.claims.org_name) {\n        throw new Error(\n          'Organization Name (org_name) claim must be a string present in the ID token'\n        );\n      } else if (orgName !== decoded.claims.org_name) {\n        throw new Error(\n          `Organization Name (org_name) claim mismatch in the ID token; expected \"${orgName}\", found \"${decoded.claims.org_name}\"`\n        );\n      }\n    }\n  }\n\n  return decoded;\n};\n", "\"use strict\";\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nexports.__esModule = true;\r\nfunction stringifyAttribute(name, value) {\r\n    if (!value) {\r\n        return '';\r\n    }\r\n    var stringified = '; ' + name;\r\n    if (value === true) {\r\n        return stringified; // boolean attributes shouldn't have a value\r\n    }\r\n    return stringified + '=' + value;\r\n}\r\nfunction stringifyAttributes(attributes) {\r\n    if (typeof attributes.expires === 'number') {\r\n        var expires = new Date();\r\n        expires.setMilliseconds(expires.getMilliseconds() + attributes.expires * 864e+5);\r\n        attributes.expires = expires;\r\n    }\r\n    return stringifyAttribute('Expires', attributes.expires ? attributes.expires.toUTCString() : '')\r\n        + stringifyAttribute('Domain', attributes.domain)\r\n        + stringifyAttribute('Path', attributes.path)\r\n        + stringifyAttribute('Secure', attributes.secure)\r\n        + stringifyAttribute('SameSite', attributes.sameSite);\r\n}\r\nfunction encode(name, value, attributes) {\r\n    return encodeURIComponent(name)\r\n        .replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent) // allowed special characters\r\n        .replace(/\\(/g, '%28').replace(/\\)/g, '%29') // replace opening and closing parens\r\n        + '=' + encodeURIComponent(value)\r\n        // allowed special characters\r\n        .replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent)\r\n        + stringifyAttributes(attributes);\r\n}\r\nexports.encode = encode;\r\nfunction parse(cookieString) {\r\n    var result = {};\r\n    var cookies = cookieString ? cookieString.split('; ') : [];\r\n    var rdecode = /(%[\\dA-F]{2})+/gi;\r\n    for (var i = 0; i < cookies.length; i++) {\r\n        var parts = cookies[i].split('=');\r\n        var cookie = parts.slice(1).join('=');\r\n        if (cookie.charAt(0) === '\"') {\r\n            cookie = cookie.slice(1, -1);\r\n        }\r\n        try {\r\n            var name_1 = parts[0].replace(rdecode, decodeURIComponent);\r\n            result[name_1] = cookie.replace(rdecode, decodeURIComponent);\r\n        }\r\n        catch (e) {\r\n            // ignore cookies with invalid name/value encoding\r\n        }\r\n    }\r\n    return result;\r\n}\r\nexports.parse = parse;\r\nfunction getAll() {\r\n    return parse(document.cookie);\r\n}\r\nexports.getAll = getAll;\r\nfunction get(name) {\r\n    return getAll()[name];\r\n}\r\nexports.get = get;\r\nfunction set(name, value, attributes) {\r\n    document.cookie = encode(name, value, __assign({ path: '/' }, attributes));\r\n}\r\nexports.set = set;\r\nfunction remove(name, attributes) {\r\n    set(name, '', __assign(__assign({}, attributes), { expires: -1 }));\r\n}\r\nexports.remove = remove;\r\n", "import * as Cookies from 'es-cookie';\n\ninterface ClientStorageOptions {\n  daysUntilExpire?: number;\n  cookieDomain?: string;\n}\n\n/**\n * Defines a type that handles storage to/from a storage location\n */\nexport type ClientStorage = {\n  get<T extends Object>(key: string): T | undefined;\n  save(key: string, value: any, options?: ClientStorageOptions): void;\n  remove(key: string, options?: ClientStorageOptions): void;\n};\n\n/**\n * A storage protocol for marshalling data to/from cookies\n */\nexport const CookieStorage = {\n  get<T extends Object>(key: string) {\n    const value = Cookies.get(key);\n\n    if (typeof value === 'undefined') {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = {\n        secure: true,\n        sameSite: 'none'\n      };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(key, JSON.stringify(value), cookieAttributes);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n  }\n} as ClientStorage;\n\n/**\n * @ignore\n */\nconst LEGACY_PREFIX = '_legacy_';\n\n/**\n * Cookie storage that creates a cookie for modern and legacy browsers.\n * See: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\n */\nexport const CookieStorageWithLegacySameSite = {\n  get<T extends Object>(key: string) {\n    const value = CookieStorage.get<T>(key);\n\n    if (value) {\n      return value;\n    }\n\n    return CookieStorage.get<T>(`${LEGACY_PREFIX}${key}`);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = { secure: true };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(\n      `${LEGACY_PREFIX}${key}`,\n      JSON.stringify(value),\n      cookieAttributes\n    );\n    CookieStorage.save(key, value, options);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n    CookieStorage.remove(key, options);\n    CookieStorage.remove(`${LEGACY_PREFIX}${key}`, options);\n  }\n} as ClientStorage;\n\n/**\n * A storage protocol for marshalling data to/from session storage\n */\nexport const SessionStorage = {\n  get<T extends Object>(key: string) {\n    /* c8 ignore next 3 */\n    if (typeof sessionStorage === 'undefined') {\n      return;\n    }\n\n    const value = sessionStorage.getItem(key);\n\n    if (value == null) {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any): void {\n    sessionStorage.setItem(key, JSON.stringify(value));\n  },\n\n  remove(key: string) {\n    sessionStorage.removeItem(key);\n  }\n} as ClientStorage;\n", "const singlePromiseMap: Record<string, Promise<any>> = {};\n\nexport const singlePromise = <T>(\n  cb: () => Promise<T>,\n  key: string\n): Promise<T> => {\n  let promise: null | Promise<T> = singlePromiseMap[key];\n  if (!promise) {\n    promise = cb().finally(() => {\n      delete singlePromiseMap[key];\n      promise = null;\n    });\n    singlePromiseMap[key] = promise;\n  }\n  return promise;\n};\n\nexport const retryPromise = async (\n  cb: () => Promise<boolean>,\n  maxNumberOfRetries = 3\n) => {\n  for (let i = 0; i < maxNumberOfRetries; i++) {\n    if (await cb()) {\n      return true;\n    }\n  }\n\n  return false;\n};\n", "import {\n  CACHE_KEY_PREFIX,\n  ICache,\n  KeyManifestEntry,\n  MaybePromise\n} from './shared';\n\nexport class CacheKeyManifest {\n  private readonly manifestKey: string;\n\n  constructor(private cache: ICache, private clientId: string) {\n    this.manifestKey = this.createManifestKeyFrom(this.clientId);\n  }\n\n  async add(key: string): Promise<void> {\n    const keys = new Set(\n      (await this.cache.get<KeyManifestEntry>(this.manifestKey))?.keys || []\n    );\n\n    keys.add(key);\n\n    await this.cache.set<KeyManifestEntry>(this.manifestKey, {\n      keys: [...keys]\n    });\n  }\n\n  async remove(key: string): Promise<void> {\n    const entry = await this.cache.get<KeyManifestEntry>(this.manifestKey);\n\n    if (entry) {\n      const keys = new Set(entry.keys);\n      keys.delete(key);\n\n      if (keys.size > 0) {\n        return await this.cache.set(this.manifestKey, { keys: [...keys] });\n      }\n\n      return await this.cache.remove(this.manifestKey);\n    }\n  }\n\n  get(): MaybePromise<KeyManifestEntry | undefined> {\n    return this.cache.get<KeyManifestEntry>(this.manifestKey);\n  }\n\n  clear(): MaybePromise<void> {\n    return this.cache.remove(this.manifestKey);\n  }\n\n  private createManifestKeyFrom(clientId: string): string {\n    return `${CACHE_KEY_PREFIX}::${clientId}`;\n  }\n}\n", "import { ICache, InMemoryCache, LocalStorageCache } from './cache';\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  LogoutOptions\n} from './global';\nimport { getUniqueScopes } from './scope';\n\n/**\n * @ignore\n */\nexport const GET_TOKEN_SILENTLY_LOCK_KEY = 'auth0.lock.getTokenSilently';\n\n/**\n * @ignore\n */\nexport const buildOrganizationHintCookieName = (clientId: string) =>\n  `auth0.${clientId}.organization_hint`;\n\n/**\n * @ignore\n */\nexport const OLD_IS_AUTHENTICATED_COOKIE_NAME = 'auth0.is.authenticated';\n\n/**\n * @ignore\n */\nexport const buildIsAuthenticatedCookieName = (clientId: string) =>\n  `auth0.${clientId}.is.authenticated`;\n\n/**\n * @ignore\n */\nconst cacheLocationBuilders: Record<string, () => ICache> = {\n  memory: () => new InMemoryCache().enclosedCache,\n  localstorage: () => new LocalStorageCache()\n};\n\n/**\n * @ignore\n */\nexport const cacheFactory = (location: string) => {\n  return cacheLocationBuilders[location];\n};\n\n/**\n * @ignore\n */\nexport const getAuthorizeParams = (\n  clientOptions: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  },\n  scope: string,\n  authorizationParams: AuthorizationParams,\n  state: string,\n  nonce: string,\n  code_challenge: string,\n  redirect_uri: string | undefined,\n  response_mode: string | undefined\n): AuthorizeOptions => {\n  return {\n    client_id: clientOptions.clientId,\n    ...clientOptions.authorizationParams,\n    ...authorizationParams,\n    scope: getUniqueScopes(scope, authorizationParams.scope),\n    response_type: 'code',\n    response_mode: response_mode || 'query',\n    state,\n    nonce,\n    redirect_uri:\n      redirect_uri || clientOptions.authorizationParams.redirect_uri,\n    code_challenge,\n    code_challenge_method: 'S256'\n  };\n};\n\n/**\n * @ignore\n *\n * Function used to provide support for the deprecated onRedirect through openUrl.\n */\nexport const patchOpenUrlWithOnRedirect = <\n  T extends Pick<LogoutOptions, 'openUrl' | 'onRedirect'>\n>(\n  options: T\n) => {\n  const { openUrl, onRedirect, ...originalOptions } = options;\n\n  const result = {\n    ...originalOptions,\n    openUrl: openUrl === false || openUrl ? openUrl : onRedirect\n  };\n\n  return result as T;\n};\n", "import Lock from 'browser-tabs-lock';\n\nimport {\n  createQuery<PERSON>ara<PERSON>,\n  runPopup,\n  parseAuthenticationResult,\n  encode,\n  createRandomString,\n  runIframe,\n  sha256,\n  bufferToBase64UrlEncoded,\n  validateCrypto,\n  openPopup,\n  getDomain,\n  getTokenIssuer,\n  parseNumber\n} from './utils';\n\nimport { oauthToken } from './api';\n\nimport { getUniqueScopes } from './scope';\n\nimport {\n  InMemoryCache,\n  ICache,\n  <PERSON>ache<PERSON>ey,\n  CacheManager,\n  CacheEntry,\n  IdTokenEntry,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  DecodedToken\n} from './cache';\n\nimport { TransactionManager } from './transaction-manager';\nimport { verify as verifyIdToken } from './jwt';\nimport {\n  AuthenticationError,\n  GenericError,\n  MissingRefreshTokenError,\n  TimeoutError\n} from './errors';\n\nimport {\n  ClientStorage,\n  CookieStorage,\n  CookieStorageWithLegacySameSite,\n  SessionStorage\n} from './storage';\n\nimport {\n  CACHE_LOCATION_MEMORY,\n  DEFAULT_POPUP_CONFIG_OPTIONS,\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  MISSING_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_SCOPE,\n  DEFAULT_SESSION_CHECK_EXPIRY_DAYS,\n  DEFAULT_AUTH0_CLIENT,\n  INVALID_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_NOW_PROVIDER,\n  DEFAULT_FETCH_TIMEOUT_MS\n} from './constants';\n\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  RedirectLoginOptions,\n  PopupLoginOptions,\n  PopupConfigOptions,\n  RedirectLoginResult,\n  GetTokenSilentlyOptions,\n  GetTokenWithPopupOptions,\n  LogoutOptions,\n  CacheLocation,\n  LogoutUrlOptions,\n  User,\n  IdToken,\n  GetTokenSilentlyVerboseResponse,\n  TokenEndpointResponse\n} from './global';\n\n// @ts-ignore\nimport TokenWorker from './worker/token.worker.ts';\nimport { singlePromise, retryPromise } from './promise-utils';\nimport { CacheKeyManifest } from './cache/key-manifest';\nimport {\n  buildIsAuthenticatedCookieName,\n  buildOrganizationHintCookieName,\n  cacheFactory,\n  getAuthorizeParams,\n  GET_TOKEN_SILENTLY_LOCK_KEY,\n  OLD_IS_AUTHENTICATED_COOKIE_NAME,\n  patchOpenUrlWithOnRedirect\n} from './Auth0Client.utils';\nimport { CustomTokenExchangeOptions } from './TokenExchange';\n\n/**\n * @ignore\n */\ntype GetTokenSilentlyResult = TokenEndpointResponse & {\n  decodedToken: ReturnType<typeof verifyIdToken>;\n  scope: string;\n  oauthTokenScope?: string;\n  audience: string;\n};\n\n/**\n * @ignore\n */\nconst lock = new Lock();\n\n/**\n * Auth0 SDK for Single Page Applications using [Authorization Code Grant Flow with PKCE](https://auth0.com/docs/api-auth/tutorials/authorization-code-grant-pkce).\n */\nexport class Auth0Client {\n  private readonly transactionManager: TransactionManager;\n  private readonly cacheManager: CacheManager;\n  private readonly domainUrl: string;\n  private readonly tokenIssuer: string;\n  private readonly scope: string;\n  private readonly cookieStorage: ClientStorage;\n  private readonly sessionCheckExpiryDays: number;\n  private readonly orgHintCookieName: string;\n  private readonly isAuthenticatedCookieName: string;\n  private readonly nowProvider: () => number | Promise<number>;\n  private readonly httpTimeoutMs: number;\n  private readonly options: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  };\n  private readonly userCache: ICache = new InMemoryCache().enclosedCache;\n\n  private worker?: Worker;\n\n  private readonly defaultOptions: Partial<Auth0ClientOptions> = {\n    authorizationParams: {\n      scope: DEFAULT_SCOPE\n    },\n    useRefreshTokensFallback: false,\n    useFormData: true\n  };\n\n  constructor(options: Auth0ClientOptions) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options,\n      authorizationParams: {\n        ...this.defaultOptions.authorizationParams,\n        ...options.authorizationParams\n      }\n    };\n\n    typeof window !== 'undefined' && validateCrypto();\n\n    if (options.cache && options.cacheLocation) {\n      console.warn(\n        'Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`.'\n      );\n    }\n\n    let cacheLocation: CacheLocation | undefined;\n    let cache: ICache;\n\n    if (options.cache) {\n      cache = options.cache;\n    } else {\n      cacheLocation = options.cacheLocation || CACHE_LOCATION_MEMORY;\n\n      if (!cacheFactory(cacheLocation)) {\n        throw new Error(`Invalid cache location \"${cacheLocation}\"`);\n      }\n\n      cache = cacheFactory(cacheLocation)();\n    }\n\n    this.httpTimeoutMs = options.httpTimeoutInSeconds\n      ? options.httpTimeoutInSeconds * 1000\n      : DEFAULT_FETCH_TIMEOUT_MS;\n\n    this.cookieStorage =\n      options.legacySameSiteCookie === false\n        ? CookieStorage\n        : CookieStorageWithLegacySameSite;\n\n    this.orgHintCookieName = buildOrganizationHintCookieName(\n      this.options.clientId\n    );\n\n    this.isAuthenticatedCookieName = buildIsAuthenticatedCookieName(\n      this.options.clientId\n    );\n\n    this.sessionCheckExpiryDays =\n      options.sessionCheckExpiryDays || DEFAULT_SESSION_CHECK_EXPIRY_DAYS;\n\n    const transactionStorage = options.useCookiesForTransactions\n      ? this.cookieStorage\n      : SessionStorage;\n\n    // Construct the scopes based on the following:\n    // 1. Always include `openid`\n    // 2. Include the scopes provided in `authorizationParams. This defaults to `profile email`\n    // 3. Add `offline_access` if `useRefreshTokens` is enabled\n    this.scope = getUniqueScopes(\n      'openid',\n      this.options.authorizationParams.scope,\n      this.options.useRefreshTokens ? 'offline_access' : ''\n    );\n\n    this.transactionManager = new TransactionManager(\n      transactionStorage,\n      this.options.clientId,\n      this.options.cookieDomain\n    );\n\n    this.nowProvider = this.options.nowProvider || DEFAULT_NOW_PROVIDER;\n\n    this.cacheManager = new CacheManager(\n      cache,\n      !cache.allKeys\n        ? new CacheKeyManifest(cache, this.options.clientId)\n        : undefined,\n      this.nowProvider\n    );\n\n    this.domainUrl = getDomain(this.options.domain);\n    this.tokenIssuer = getTokenIssuer(this.options.issuer, this.domainUrl);\n\n    // Don't use web workers unless using refresh tokens in memory\n    if (\n      typeof window !== 'undefined' &&\n      window.Worker &&\n      this.options.useRefreshTokens &&\n      cacheLocation === CACHE_LOCATION_MEMORY\n    ) {\n      if (this.options.workerUrl) {\n        this.worker = new Worker(this.options.workerUrl);\n      } else {\n        this.worker = new TokenWorker();\n      }\n    }\n  }\n\n  private _url(path: string) {\n    const auth0Client = encodeURIComponent(\n      btoa(JSON.stringify(this.options.auth0Client || DEFAULT_AUTH0_CLIENT))\n    );\n    return `${this.domainUrl}${path}&auth0Client=${auth0Client}`;\n  }\n\n  private _authorizeUrl(authorizeOptions: AuthorizeOptions) {\n    return this._url(`/authorize?${createQueryParams(authorizeOptions)}`);\n  }\n\n  private async _verifyIdToken(\n    id_token: string,\n    nonce?: string,\n    organization?: string\n  ) {\n    const now = await this.nowProvider();\n\n    return verifyIdToken({\n      iss: this.tokenIssuer,\n      aud: this.options.clientId,\n      id_token,\n      nonce,\n      organization,\n      leeway: this.options.leeway,\n      max_age: parseNumber(this.options.authorizationParams.max_age),\n      now\n    });\n  }\n\n  private _processOrgHint(organization?: string) {\n    if (organization) {\n      this.cookieStorage.save(this.orgHintCookieName, organization, {\n        daysUntilExpire: this.sessionCheckExpiryDays,\n        cookieDomain: this.options.cookieDomain\n      });\n    } else {\n      this.cookieStorage.remove(this.orgHintCookieName, {\n        cookieDomain: this.options.cookieDomain\n      });\n    }\n  }\n\n  private async _prepareAuthorizeUrl(\n    authorizationParams: AuthorizationParams,\n    authorizeOptions?: Partial<AuthorizeOptions>,\n    fallbackRedirectUri?: string\n  ): Promise<{\n    scope: string;\n    audience: string;\n    redirect_uri?: string;\n    nonce: string;\n    code_verifier: string;\n    state: string;\n    url: string;\n  }> {\n    const state = encode(createRandomString());\n    const nonce = encode(createRandomString());\n    const code_verifier = createRandomString();\n    const code_challengeBuffer = await sha256(code_verifier);\n    const code_challenge = bufferToBase64UrlEncoded(code_challengeBuffer);\n\n    const params = getAuthorizeParams(\n      this.options,\n      this.scope,\n      authorizationParams,\n      state,\n      nonce,\n      code_challenge,\n      authorizationParams.redirect_uri ||\n        this.options.authorizationParams.redirect_uri ||\n        fallbackRedirectUri,\n      authorizeOptions?.response_mode\n    );\n\n    const url = this._authorizeUrl(params);\n\n    return {\n      nonce,\n      code_verifier,\n      scope: params.scope,\n      audience: params.audience || 'default',\n      redirect_uri: params.redirect_uri,\n      state,\n      url\n    };\n  }\n\n  /**\n   * ```js\n   * try {\n   *  await auth0.loginWithPopup(options);\n   * } catch(e) {\n   *  if (e instanceof PopupCancelledError) {\n   *    // Popup was closed before login completed\n   *  }\n   * }\n   * ```\n   *\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * IMPORTANT: This method has to be called from an event handler\n   * that was started by the user like a button click, for example,\n   * otherwise the popup will be blocked in most browsers.\n   *\n   * @param options\n   * @param config\n   */\n  public async loginWithPopup(\n    options?: PopupLoginOptions,\n    config?: PopupConfigOptions\n  ) {\n    options = options || {};\n    config = config || {};\n\n    if (!config.popup) {\n      config.popup = openPopup('');\n\n      if (!config.popup) {\n        throw new Error(\n          'Unable to open a popup for loginWithPopup - window.open returned `null`'\n        );\n      }\n    }\n\n    const params = await this._prepareAuthorizeUrl(\n      options.authorizationParams || {},\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    config.popup.location.href = params.url;\n\n    const codeResult = await runPopup({\n      ...config,\n      timeoutInSeconds:\n        config.timeoutInSeconds ||\n        this.options.authorizeTimeoutInSeconds ||\n        DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n    });\n\n    if (params.state !== codeResult.state) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization =\n      options.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    await this._requestToken(\n      {\n        audience: params.audience,\n        scope: params.scope,\n        code_verifier: params.code_verifier,\n        grant_type: 'authorization_code',\n        code: codeResult.code as string,\n        redirect_uri: params.redirect_uri\n      },\n      {\n        nonceIn: params.nonce,\n        organization\n      }\n    );\n  }\n\n  /**\n   * ```js\n   * const user = await auth0.getUser();\n   * ```\n   *\n   * Returns the user information if available (decoded\n   * from the `id_token`).\n   *\n   * @typeparam TUser The type to return, has to extend {@link User}.\n   */\n  public async getUser<TUser extends User>(): Promise<TUser | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.user as TUser;\n  }\n\n  /**\n   * ```js\n   * const claims = await auth0.getIdTokenClaims();\n   * ```\n   *\n   * Returns all claims from the id_token if available.\n   */\n  public async getIdTokenClaims(): Promise<IdToken | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.claims;\n  }\n\n  /**\n   * ```js\n   * await auth0.loginWithRedirect(options);\n   * ```\n   *\n   * Performs a redirect to `/authorize` using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated.\n   *\n   * @param options\n   */\n  public async loginWithRedirect<TAppState = any>(\n    options: RedirectLoginOptions<TAppState> = {}\n  ) {\n    const { openUrl, fragment, appState, ...urlOptions } =\n      patchOpenUrlWithOnRedirect(options);\n\n    const organization =\n      urlOptions.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    const { url, ...transaction } = await this._prepareAuthorizeUrl(\n      urlOptions.authorizationParams || {}\n    );\n\n    this.transactionManager.create({\n      ...transaction,\n      appState,\n      ...(organization && { organization })\n    });\n\n    const urlWithFragment = fragment ? `${url}#${fragment}` : url;\n\n    if (openUrl) {\n      await openUrl(urlWithFragment);\n    } else {\n      window.location.assign(urlWithFragment);\n    }\n  }\n\n  /**\n   * After the browser redirects back to the callback page,\n   * call `handleRedirectCallback` to handle success and error\n   * responses from Auth0. If the response is successful, results\n   * will be valid according to their expiration times.\n   */\n  public async handleRedirectCallback<TAppState = any>(\n    url: string = window.location.href\n  ): Promise<RedirectLoginResult<TAppState>> {\n    const queryStringFragments = url.split('?').slice(1);\n\n    if (queryStringFragments.length === 0) {\n      throw new Error('There are no query params available for parsing.');\n    }\n\n    const { state, code, error, error_description } = parseAuthenticationResult(\n      queryStringFragments.join('')\n    );\n\n    const transaction = this.transactionManager.get();\n\n    if (!transaction) {\n      throw new GenericError('missing_transaction', 'Invalid state');\n    }\n\n    this.transactionManager.remove();\n\n    if (error) {\n      throw new AuthenticationError(\n        error,\n        error_description || error,\n        state,\n        transaction.appState\n      );\n    }\n\n    // Transaction should have a `code_verifier` to do PKCE for CSRF protection\n    if (\n      !transaction.code_verifier ||\n      (transaction.state && transaction.state !== state)\n    ) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization = transaction.organization;\n    const nonceIn = transaction.nonce;\n    const redirect_uri = transaction.redirect_uri;\n\n    await this._requestToken(\n      {\n        audience: transaction.audience,\n        scope: transaction.scope,\n        code_verifier: transaction.code_verifier,\n        grant_type: 'authorization_code',\n        code: code as string,\n        ...(redirect_uri ? { redirect_uri } : {})\n      },\n      { nonceIn, organization }\n    );\n\n    return {\n      appState: transaction.appState\n    };\n  }\n\n  /**\n   * ```js\n   * await auth0.checkSession();\n   * ```\n   *\n   * Check if the user is logged in using `getTokenSilently`. The difference\n   * with `getTokenSilently` is that this doesn't return a token, but it will\n   * pre-fill the token cache.\n   *\n   * This method also heeds the `auth0.{clientId}.is.authenticated` cookie, as an optimization\n   *  to prevent calling Auth0 unnecessarily. If the cookie is not present because\n   * there was no previous login (or it has expired) then tokens will not be refreshed.\n   *\n   * It should be used for silently logging in the user when you instantiate the\n   * `Auth0Client` constructor. You should not need this if you are using the\n   * `createAuth0Client` factory.\n   *\n   * **Note:** the cookie **may not** be present if running an app using a private tab, as some\n   * browsers clear JS cookie data and local storage when the tab or page is closed, or on page reload. This effectively\n   * means that `checkSession` could silently return without authenticating the user on page refresh when\n   * using a private tab, despite having previously logged in. As a workaround, use `getTokenSilently` instead\n   * and handle the possible `login_required` error [as shown in the readme](https://github.com/auth0/auth0-spa-js#creating-the-client).\n   *\n   * @param options\n   */\n  public async checkSession(options?: GetTokenSilentlyOptions) {\n    if (!this.cookieStorage.get(this.isAuthenticatedCookieName)) {\n      if (!this.cookieStorage.get(OLD_IS_AUTHENTICATED_COOKIE_NAME)) {\n        return;\n      } else {\n        // Migrate the existing cookie to the new name scoped by client ID\n        this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n          daysUntilExpire: this.sessionCheckExpiryDays,\n          cookieDomain: this.options.cookieDomain\n        });\n\n        this.cookieStorage.remove(OLD_IS_AUTHENTICATED_COOKIE_NAME);\n      }\n    }\n\n    try {\n      await this.getTokenSilently(options);\n    } catch (_) {}\n  }\n\n  /**\n   * Fetches a new access token and returns the response from the /oauth/token endpoint, omitting the refresh token.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions & { detailedResponse: true }\n  ): Promise<GetTokenSilentlyVerboseResponse>;\n\n  /**\n   * Fetches a new access token and returns it.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options?: GetTokenSilentlyOptions\n  ): Promise<string>;\n\n  /**\n   * Fetches a new access token, and either returns just the access token (the default) or the response from the /oauth/token endpoint, depending on the `detailedResponse` option.\n   *\n   * ```js\n   * const token = await auth0.getTokenSilently(options);\n   * ```\n   *\n   * If there's a valid token stored and it has more than 60 seconds\n   * remaining before expiration, return the token. Otherwise, attempt\n   * to obtain a new token.\n   *\n   * A new token will be obtained either by opening an iframe or a\n   * refresh token (if `useRefreshTokens` is `true`).\n\n   * If iframes are used, opens an iframe with the `/authorize` URL\n   * using the parameters provided as arguments. Random and secure `state`\n   * and `nonce` parameters will be auto-generated. If the response is successful,\n   * results will be validated according to their expiration times.\n   *\n   * If refresh tokens are used, the token endpoint is called directly with the\n   * 'refresh_token' grant. If no refresh token is available to make this call,\n   * the SDK will only fall back to using an iframe to the '/authorize' URL if \n   * the `useRefreshTokensFallback` setting has been set to `true`. By default this\n   * setting is `false`.\n   *\n   * This method may use a web worker to perform the token call if the in-memory\n   * cache is used.\n   *\n   * If an `audience` value is given to this function, the SDK always falls\n   * back to using an iframe to make the token exchange.\n   *\n   * Note that in all cases, falling back to an iframe requires access to\n   * the `auth0` cookie.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions = {}\n  ): Promise<undefined | string | GetTokenSilentlyVerboseResponse> {\n    const localOptions: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    } = {\n      cacheMode: 'on',\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    const result = await singlePromise(\n      () => this._getTokenSilently(localOptions),\n      `${this.options.clientId}::${localOptions.authorizationParams.audience}::${localOptions.authorizationParams.scope}`\n    );\n\n    return options.detailedResponse ? result : result?.access_token;\n  }\n\n  private async _getTokenSilently(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const { cacheMode, ...getTokenOptions } = options;\n\n    // Check the cache before acquiring the lock to avoid the latency of\n    // `lock.acquireLock` when the cache is populated.\n    if (cacheMode !== 'off') {\n      const entry = await this._getEntryFromCache({\n        scope: getTokenOptions.authorizationParams.scope,\n        audience: getTokenOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      });\n\n      if (entry) {\n        return entry;\n      }\n    }\n\n    if (cacheMode === 'cache-only') {\n      return;\n    }\n\n    if (\n      await retryPromise(\n        () => lock.acquireLock(GET_TOKEN_SILENTLY_LOCK_KEY, 5000),\n        10\n      )\n    ) {\n      try {\n        window.addEventListener('pagehide', this._releaseLockOnPageHide);\n\n        // Check the cache a second time, because it may have been populated\n        // by a previous call while this call was waiting to acquire the lock.\n        if (cacheMode !== 'off') {\n          const entry = await this._getEntryFromCache({\n            scope: getTokenOptions.authorizationParams.scope,\n            audience: getTokenOptions.authorizationParams.audience || 'default',\n            clientId: this.options.clientId\n          });\n\n          if (entry) {\n            return entry;\n          }\n        }\n\n        const authResult = this.options.useRefreshTokens\n          ? await this._getTokenUsingRefreshToken(getTokenOptions)\n          : await this._getTokenFromIFrame(getTokenOptions);\n\n        const { id_token, access_token, oauthTokenScope, expires_in } =\n          authResult;\n\n        return {\n          id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        };\n      } finally {\n        await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n        window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n      }\n    } else {\n      throw new TimeoutError();\n    }\n  }\n\n  /**\n   * ```js\n   * const token = await auth0.getTokenWithPopup(options);\n   * ```\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * @param options\n   * @param config\n   */\n  public async getTokenWithPopup(\n    options: GetTokenWithPopupOptions = {},\n    config: PopupConfigOptions = {}\n  ) {\n    const localOptions = {\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    config = {\n      ...DEFAULT_POPUP_CONFIG_OPTIONS,\n      ...config\n    };\n\n    await this.loginWithPopup(localOptions, config);\n\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: localOptions.authorizationParams.scope,\n        audience: localOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    return cache!.access_token;\n  }\n\n  /**\n   * ```js\n   * const isAuthenticated = await auth0.isAuthenticated();\n   * ```\n   *\n   * Returns `true` if there's valid information stored,\n   * otherwise returns `false`.\n   *\n   */\n  public async isAuthenticated() {\n    const user = await this.getUser();\n    return !!user;\n  }\n\n  /**\n   * ```js\n   * await auth0.buildLogoutUrl(options);\n   * ```\n   *\n   * Builds a URL to the logout endpoint using the parameters provided as arguments.\n   * @param options\n   */\n  private _buildLogoutUrl(options: LogoutUrlOptions): string {\n    if (options.clientId !== null) {\n      options.clientId = options.clientId || this.options.clientId;\n    } else {\n      delete options.clientId;\n    }\n\n    const { federated, ...logoutOptions } = options.logoutParams || {};\n    const federatedQuery = federated ? `&federated` : '';\n    const url = this._url(\n      `/v2/logout?${createQueryParams({\n        clientId: options.clientId,\n        ...logoutOptions\n      })}`\n    );\n\n    return url + federatedQuery;\n  }\n\n  /**\n   * ```js\n   * await auth0.logout(options);\n   * ```\n   *\n   * Clears the application session and performs a redirect to `/v2/logout`, using\n   * the parameters provided as arguments, to clear the Auth0 session.\n   *\n   * If the `federated` option is specified it also clears the Identity Provider session.\n   * [Read more about how Logout works at Auth0](https://auth0.com/docs/logout).\n   *\n   * @param options\n   */\n  public async logout(options: LogoutOptions = {}): Promise<void> {\n    const { openUrl, ...logoutOptions } = patchOpenUrlWithOnRedirect(options);\n\n    if (options.clientId === null) {\n      await this.cacheManager.clear();\n    } else {\n      await this.cacheManager.clear(options.clientId || this.options.clientId);\n    }\n\n    this.cookieStorage.remove(this.orgHintCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.cookieStorage.remove(this.isAuthenticatedCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.userCache.remove(CACHE_KEY_ID_TOKEN_SUFFIX);\n\n    const url = this._buildLogoutUrl(logoutOptions);\n\n    if (openUrl) {\n      await openUrl(url);\n    } else if (openUrl !== false) {\n      window.location.assign(url);\n    }\n  }\n\n  private async _getTokenFromIFrame(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const params: AuthorizationParams & { scope: string } = {\n      ...options.authorizationParams,\n      prompt: 'none'\n    };\n\n    const orgHint = this.cookieStorage.get<string>(this.orgHintCookieName);\n\n    if (orgHint && !params.organization) {\n      params.organization = orgHint;\n    }\n\n    const {\n      url,\n      state: stateIn,\n      nonce: nonceIn,\n      code_verifier,\n      redirect_uri,\n      scope,\n      audience\n    } = await this._prepareAuthorizeUrl(\n      params,\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    try {\n      // When a browser is running in a Cross-Origin Isolated context, using iframes is not possible.\n      // It doesn't throw an error but times out instead, so we should exit early and inform the user about the reason.\n      // https://developer.mozilla.org/en-US/docs/Web/API/crossOriginIsolated\n      if ((window as any).crossOriginIsolated) {\n        throw new GenericError(\n          'login_required',\n          'The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.'\n        );\n      }\n\n      const authorizeTimeout =\n        options.timeoutInSeconds || this.options.authorizeTimeoutInSeconds;\n\n      const codeResult = await runIframe(url, this.domainUrl, authorizeTimeout);\n\n      if (stateIn !== codeResult.state) {\n        throw new GenericError('state_mismatch', 'Invalid state');\n      }\n\n      const tokenResult = await this._requestToken(\n        {\n          ...options.authorizationParams,\n          code_verifier,\n          code: codeResult.code as string,\n          grant_type: 'authorization_code',\n          redirect_uri,\n          timeout: options.authorizationParams.timeout || this.httpTimeoutMs\n        },\n        {\n          nonceIn,\n          organization: params.organization\n        }\n      );\n\n      return {\n        ...tokenResult,\n        scope: scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: audience\n      };\n    } catch (e) {\n      if (e.error === 'login_required') {\n        this.logout({\n          openUrl: false\n        });\n      }\n      throw e;\n    }\n  }\n\n  private async _getTokenUsingRefreshToken(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: options.authorizationParams.scope,\n        audience: options.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    // If you don't have a refresh token in memory\n    // and you don't have a refresh token in web worker memory\n    // and useRefreshTokensFallback was explicitly enabled\n    // fallback to an iframe\n    if ((!cache || !cache.refresh_token) && !this.worker) {\n      if (this.options.useRefreshTokensFallback) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw new MissingRefreshTokenError(\n        options.authorizationParams.audience || 'default',\n        options.authorizationParams.scope\n      );\n    }\n\n    const redirect_uri =\n      options.authorizationParams.redirect_uri ||\n      this.options.authorizationParams.redirect_uri ||\n      window.location.origin;\n\n    const timeout =\n      typeof options.timeoutInSeconds === 'number'\n        ? options.timeoutInSeconds * 1000\n        : null;\n\n    try {\n      const tokenResult = await this._requestToken({\n        ...options.authorizationParams,\n        grant_type: 'refresh_token',\n        refresh_token: cache && cache.refresh_token,\n        redirect_uri,\n        ...(timeout && { timeout })\n      });\n\n      return {\n        ...tokenResult,\n        scope: options.authorizationParams.scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: options.authorizationParams.audience || 'default'\n      };\n    } catch (e) {\n      if (\n        // The web worker didn't have a refresh token in memory so\n        // fallback to an iframe.\n        (e.message.indexOf(MISSING_REFRESH_TOKEN_ERROR_MESSAGE) > -1 ||\n          // A refresh token was found, but is it no longer valid\n          // and useRefreshTokensFallback is explicitly enabled. Fallback to an iframe.\n          (e.message &&\n            e.message.indexOf(INVALID_REFRESH_TOKEN_ERROR_MESSAGE) > -1)) &&\n        this.options.useRefreshTokensFallback\n      ) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw e;\n    }\n  }\n\n  private async _saveEntryInCache(\n    entry: CacheEntry & { id_token: string; decodedToken: DecodedToken }\n  ) {\n    const { id_token, decodedToken, ...entryWithoutIdToken } = entry;\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, {\n      id_token,\n      decodedToken\n    });\n\n    await this.cacheManager.setIdToken(\n      this.options.clientId,\n      entry.id_token,\n      entry.decodedToken\n    );\n\n    await this.cacheManager.set(entryWithoutIdToken);\n  }\n\n  private async _getIdTokenFromCache() {\n    const audience = this.options.authorizationParams.audience || 'default';\n\n    const cache = await this.cacheManager.getIdToken(\n      new CacheKey({\n        clientId: this.options.clientId,\n        audience,\n        scope: this.scope\n      })\n    );\n\n    const currentCache = this.userCache.get<IdTokenEntry>(\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ) as IdTokenEntry;\n\n    // If the id_token in the cache matches the value we previously cached in memory return the in-memory\n    // value so that object comparison will work\n    if (cache && cache.id_token === currentCache?.id_token) {\n      return currentCache;\n    }\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, cache);\n    return cache;\n  }\n\n  private async _getEntryFromCache({\n    scope,\n    audience,\n    clientId\n  }: {\n    scope: string;\n    audience: string;\n    clientId: string;\n  }): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const entry = await this.cacheManager.get(\n      new CacheKey({\n        scope,\n        audience,\n        clientId\n      }),\n      60 // get a new token if within 60 seconds of expiring\n    );\n\n    if (entry && entry.access_token) {\n      const { access_token, oauthTokenScope, expires_in } = entry as CacheEntry;\n      const cache = await this._getIdTokenFromCache();\n      return (\n        cache && {\n          id_token: cache.id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        }\n      );\n    }\n  }\n\n  /**\n   * Releases any lock acquired by the current page that's not released yet\n   *\n   * Get's called on the `pagehide` event.\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/pagehide_event\n   */\n  private _releaseLockOnPageHide = async () => {\n    await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n\n    window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n  };\n\n  private async _requestToken(\n    options:\n      | PKCERequestTokenOptions\n      | RefreshTokenRequestTokenOptions\n      | TokenExchangeRequestOptions,\n    additionalParameters?: RequestTokenAdditionalParameters\n  ) {\n    const { nonceIn, organization } = additionalParameters || {};\n    const authResult = await oauthToken(\n      {\n        baseUrl: this.domainUrl,\n        client_id: this.options.clientId,\n        auth0Client: this.options.auth0Client,\n        useFormData: this.options.useFormData,\n        timeout: this.httpTimeoutMs,\n        ...options\n      },\n      this.worker\n    );\n\n    const decodedToken = await this._verifyIdToken(\n      authResult.id_token,\n      nonceIn,\n      organization\n    );\n\n    await this._saveEntryInCache({\n      ...authResult,\n      decodedToken,\n      scope: options.scope,\n      audience: options.audience || 'default',\n      ...(authResult.scope ? { oauthTokenScope: authResult.scope } : null),\n      client_id: this.options.clientId\n    });\n\n    this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n      daysUntilExpire: this.sessionCheckExpiryDays,\n      cookieDomain: this.options.cookieDomain\n    });\n\n    this._processOrgHint(organization || decodedToken.claims.org_id);\n\n    return { ...authResult, decodedToken };\n  }\n\n  /*\n  Custom Token Exchange\n  * **Implementation Notes:**\n  * - Ensure that the `subject_token` provided has been securely obtained and is valid according\n  *   to your external identity provider's policies before invoking this function.\n  * - The function leverages internal helper methods:\n  *   - `validateTokenType` confirms that the `subject_token_type` is supported.\n  *   - `getUniqueScopes` merges and de-duplicates scopes between the provided options and\n  *     the instance's default scopes.\n  *   - `_requestToken` performs the actual HTTP request to the token endpoint.\n  */\n\n  /**\n   * Exchanges an external subject token for an Auth0 token via a token exchange request.\n   *\n   * @param {CustomTokenExchangeOptions} options - The options required to perform the token exchange.\n   *\n   * @returns {Promise<TokenEndpointResponse>} A promise that resolves to the token endpoint response,\n   * which contains the issued Auth0 tokens.\n   *\n   * This method implements the token exchange grant as specified in RFC 8693 by first validating\n   * the provided subject token type and then constructing a token request to the /oauth/token endpoint.\n   * The request includes the following parameters:\n   *\n   * - `grant_type`: Hard-coded to \"urn:ietf:params:oauth:grant-type:token-exchange\".\n   * - `subject_token`: The external token provided via the options.\n   * - `subject_token_type`: The type of the external token (validated by this function).\n   * - `scope`: A unique set of scopes, generated by merging the scopes supplied in the options\n   *            with the SDK’s default scopes.\n   * - `audience`: The target audience, as determined by the SDK's authorization configuration.\n   *\n   * **Example Usage:**\n   *\n   * ```\n   * // Define the token exchange options\n   * const options: CustomTokenExchangeOptions = {\n   *   subject_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6Ikp...',\n   *   subject_token_type: 'urn:acme:legacy-system-token',\n   *   scope: ['openid', 'profile']\n   * };\n   *\n   * // Exchange the external token for Auth0 tokens\n   * try {\n   *   const tokenResponse = await instance.exchangeToken(options);\n   *   console.log('Token response:', tokenResponse);\n   * } catch (error) {\n   *   console.error('Token exchange failed:', error);\n   * }\n   * ```\n   */\n  async exchangeToken(\n    options: CustomTokenExchangeOptions\n  ): Promise<TokenEndpointResponse> {\n    return this._requestToken({\n      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',\n      subject_token: options.subject_token,\n      subject_token_type: options.subject_token_type,\n      scope: getUniqueScopes(options.scope, this.scope),\n      audience: this.options.authorizationParams.audience\n    });\n  }\n}\n\ninterface BaseRequestTokenOptions {\n  audience?: string;\n  scope: string;\n  timeout?: number;\n  redirect_uri?: string;\n}\n\ninterface PKCERequestTokenOptions extends BaseRequestTokenOptions {\n  code: string;\n  grant_type: 'authorization_code';\n  code_verifier: string;\n}\n\ninterface RefreshTokenRequestTokenOptions extends BaseRequestTokenOptions {\n  grant_type: 'refresh_token';\n  refresh_token?: string;\n}\n\ninterface TokenExchangeRequestOptions extends BaseRequestTokenOptions {\n  grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange';\n  subject_token: string;\n  subject_token_type: string;\n  actor_token?: string;\n  actor_token_type?: string;\n}\n\ninterface RequestTokenAdditionalParameters {\n  nonceIn?: string;\n  organization?: string;\n}\n", "import { Auth0Client } from './Auth0Client';\nimport { Auth0ClientOptions } from './global';\n\nimport './global';\n\nexport * from './global';\n\n/**\n * Asynchronously creates the Auth0Client instance and calls `checkSession`.\n *\n * **Note:** There are caveats to using this in a private browser tab, which may not silently authenticae\n * a user on page refresh. Please see [the checkSession docs](https://auth0.github.io/auth0-spa-js/classes/Auth0Client.html#checksession) for more info.\n *\n * @param options The client options\n * @returns An instance of Auth0Client\n */\nexport async function createAuth0Client(options: Auth0ClientOptions) {\n  const auth0 = new Auth0Client(options);\n  await auth0.checkSession();\n  return auth0;\n}\n\nexport { Auth0Client };\n\nexport {\n  GenericError,\n  AuthenticationError,\n  TimeoutError,\n  PopupTimeoutError,\n  PopupCancelledError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport {\n  ICache,\n  LocalStorageCache,\n  InMemoryCache,\n  Cacheable,\n  DecodedToken,\n  CacheEntry,\n  WrappedCacheEntry,\n  KeyManifestEntry,\n  MaybePromise,\n  CacheKey,\n  CacheKeyData\n} from './cache';\n"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "SuppressedError", "defineProperty", "exports", "value", "ProcessLocking", "_this", "this", "locked", "Map", "addToLocked", "key", "toAdd", "callbacks", "get", "undefined", "set", "unshift", "isLocked", "has", "lock", "Promise", "resolve", "reject", "unlock", "toCall", "pop", "setTimeout", "delete", "getInstance", "instance", "default", "__awaiter", "thisArg", "_arguments", "P", "generator", "fulfilled", "step", "next", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "TypeError", "push", "LOCK_STORAGE_KEY", "DEFAULT_STORAGE_HANDLER", "index", "_a", "Error", "getItem", "clear", "window", "localStorage", "removeItem", "setItem", "keySync", "getItemSync", "clearSync", "removeItemSync", "setItemSync", "delay", "milliseconds", "generateRandomString", "CHARS", "randomstring", "Math", "floor", "random", "SuperTokensLock", "storageHandler", "acquiredIatSet", "Set", "id", "Date", "now", "toString", "acquireLock", "bind", "releaseLock", "releaseLock__private__", "waitForSomethingToChange", "refreshLockWhileAcquired", "waiters", "<PERSON><PERSON><PERSON>", "timeout", "iat", "MAX_TIME", "STORAGE_KEY", "STORAGE", "TIMEOUT_KEY", "lockObjPostDelay", "parsedLockObjPostDelay", "JSON", "stringify", "timeout<PERSON><PERSON>", "timeAcquired", "timeRefreshed", "parse", "add", "lock<PERSON><PERSON><PERSON><PERSON>", "storageKey", "lock<PERSON>bj", "parsedLockObj", "processLock_1", "resolvedCalled", "startedAt", "removedListeners", "stopWaiting", "removeEventListener", "removeFromWaiting", "clearTimeout", "timeOutId", "timeToWait", "addEventListener", "addToWaiting", "max", "func", "filter", "notify<PERSON><PERSON><PERSON>", "slice", "for<PERSON>ach", "parsedlockObj", "MIN_ALLOWED_TIME", "KEYS", "currIndex", "LOCK_KEY", "includes", "DEFAULT_POPUP_CONFIG_OPTIONS", "timeoutInSeconds", "CACHE_LOCATION_MEMORY", "DEFAULT_AUTH0_CLIENT", "name", "version", "DEFAULT_NOW_PROVIDER", "GenericError", "constructor", "error", "error_description", "super", "setPrototypeOf", "static", "AuthenticationError", "state", "appState", "TimeoutError", "PopupTimeoutError", "popup", "PopupCancelledError", "MfaRequiredError", "mfa_token", "MissingRefreshTokenError", "audience", "scope", "valueOrEmptyString", "exclude", "getCrypto", "crypto", "createRandomString", "charset", "Array", "from", "getRandomValues", "Uint8Array", "encode", "btoa", "createQueryParams", "clientId", "client_id", "params", "URLSearchParams", "keys", "k", "reduce", "acc", "assign", "stripUndefined", "urlDecodeB64", "input", "decodeURIComponent", "atob", "split", "map", "c", "charCodeAt", "join", "decodeB64", "replace", "dofetch", "async", "fetchUrl", "fetchOptions", "response", "fetch", "ok", "json", "fetchWithoutWorker", "controller", "AbortController", "timeoutId", "signal", "race", "abort", "finally", "fetchWithWorker", "worker", "useFormData", "message", "auth", "to", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "data", "close", "postMessage", "port2", "switchFetch", "oauthToken", "baseUrl", "auth0Client", "options", "url", "fetchError", "errorMessage", "getJSON", "method", "headers", "getUniqueScopes", "scopes", "arr", "Boolean", "trim", "CACHE_KEY_PREFIX", "CACHE_KEY_ID_TOKEN_SUFFIX", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "suffix", "to<PERSON><PERSON>", "entry", "LocalStorageCache", "remove", "allKeys", "startsWith", "InMemoryCache", "enclosedCache", "cache", "cacheEntry", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyManifest", "nowProvider", "idToken", "decodedToken", "cache<PERSON>ey", "getIdTokenCache<PERSON>ey", "id_token", "entryByScope", "expiryAdjustmentSeconds", "wrappedEntry", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "matchExisting<PERSON>ache<PERSON>ey", "nowSeconds", "expiresAt", "refresh_token", "wrapCacheEntry", "memo", "expires_in", "keyToMatch", "fromKey", "scopeSet", "scopesToMatch", "hasAllScopes", "current", "TransactionManager", "storage", "cookieDomain", "create", "transaction", "save", "daysUntilExpire", "isNumber", "idTokendecoded", "verify", "decoded", "token", "parts", "header", "payload", "signature", "payloadJSON", "claims", "__raw", "user", "encoded", "decode", "iss", "sub", "alg", "aud", "isArray", "azp", "nonce", "max_age", "auth_time", "exp", "leeway", "expDate", "setUTCSeconds", "nbf", "nbfDate", "authTimeDate", "parseInt", "organization", "org", "orgId", "org_id", "orgName", "toLowerCase", "org_name", "__assign", "arguments", "stringifyAttribute", "stringified", "attributes", "encodeURIComponent", "expires", "setMilliseconds", "getMilliseconds", "toUTCString", "domain", "path", "secure", "sameSite", "stringifyAttributes", "cookieString", "cookies", "rdecode", "cookie", "char<PERSON>t", "getAll", "document", "__esModule", "Cookie<PERSON>torage", "Cookies.get", "cookieAttributes", "location", "protocol", "Cookies.set", "Cookies.remove", "CookieStorageWithLegacySameSite", "SessionStorage", "sessionStorage", "singlePromiseMap", "CacheKeyManifest", "manifest<PERSON>ey", "createManifestKeyFrom", "size", "GET_TOKEN_SILENTLY_LOCK_KEY", "OLD_IS_AUTHENTICATED_COOKIE_NAME", "cacheLocationBuilders", "memory", "localstorage", "cacheFactory", "patchOpenUrlWithOnRedirect", "openUrl", "onRedirect", "originalOptions", "Lock", "Auth0Client", "cacheLocation", "userCache", "defaultOptions", "authorizationParams", "useRefreshTokensFallback", "_releaseLockOnPageHide", "subtle", "validateCrypto", "console", "warn", "httpTimeoutMs", "httpTimeoutInSeconds", "cookieStorage", "legacySameSiteCookie", "orgHintCookieName", "isAuthenticatedCookieName", "buildIsAuthenticatedCookieName", "sessionCheckExpiryDays", "transactionStorage", "useCookiesForTransactions", "domainUrl", "useRefreshTokens", "transactionManager", "cacheManager", "test", "token<PERSON>ssuer", "issuer", "getT<PERSON><PERSON><PERSON><PERSON>", "Worker", "workerUrl", "TokenWorker", "_url", "_authorizeUrl", "authorizeOptions", "verifyIdToken", "_processOrgHint", "fallbackRedirectUri", "code_verifier", "code_challenge", "ie11SafeInput", "b64Chars", "m", "urlEncodeB64", "String", "fromCharCode", "bufferToBase64UrlEncoded", "digestOp", "digest", "TextEncoder", "sha256", "clientOptions", "redirect_uri", "response_mode", "response_type", "code_challenge_method", "getAuthorizeParams", "config", "left", "screenX", "innerWidth", "top", "screenY", "innerHeight", "open", "openPopup", "_prepareAuthorizeUrl", "origin", "href", "codeResult", "popupEventListener", "popupTimer", "setInterval", "closed", "clearInterval", "type", "fromPayload", "runPopup", "authorizeTimeoutInSeconds", "_requestToken", "grant_type", "code", "nonceIn", "_getIdTokenFromCache", "_b", "fragment", "urlOptions", "_c", "urlWithFragment", "queryStringFragments", "queryString", "substring", "searchParams", "parseAuthenticationResult", "getTokenSilently", "localOptions", "cacheMode", "cb", "promise", "singlePromise", "_getTokenSilently", "detailedResponse", "access_token", "getTokenOptions", "_getEntryFromCache", "maxNumberOfRetries", "retryPromise", "authResult", "_getTokenUsingRefreshToken", "_getTokenFromIFrame", "oauthTokenScope", "loginWithPopup", "getUser", "_buildLogoutUrl", "logoutParams", "federated", "logoutOptions", "federatedQuery", "prompt", "orgHint", "stateIn", "crossOriginIsolated", "authorizeTimeout", "authorizeUrl", "<PERSON><PERSON><PERSON><PERSON>", "res", "rej", "iframe", "createElement", "setAttribute", "style", "display", "removeIframe", "contains", "<PERSON><PERSON><PERSON><PERSON>", "iframeEventHandler", "timeoutSetTimeoutId", "eventSource", "source", "CLEANUP_IFRAME_TIMEOUT_IN_SECONDS", "append<PERSON><PERSON><PERSON>", "runIframe", "tokenResult", "logout", "entryWithoutIdToken", "setIdToken", "getIdToken", "currentCache", "additionalParameters", "_verifyIdToken", "_saveEntryInCache", "subject_token", "subject_token_type", "auth0", "checkSession"], "mappings": "6OA0CO,SAASA,EAAOC,EAAGC,GACtB,IAAIC,EAAI,CAAA,EACR,IAAK,IAAIC,KAAKH,EAAOI,OAAOC,UAAUC,eAAeC,KAAKP,EAAGG,IAAMF,EAAEO,QAAQL,GAAK,IAC9ED,EAAEC,GAAKH,EAAEG,IACb,GAAS,MAALH,GAAqD,mBAAjCI,OAAOK,sBACtB,KAAIC,EAAI,EAAb,IAAgBP,EAAIC,OAAOK,sBAAsBT,GAAIU,EAAIP,EAAEQ,OAAQD,IAC3DT,EAAEO,QAAQL,EAAEO,IAAM,GAAKN,OAAOC,UAAUO,qBAAqBL,KAAKP,EAAGG,EAAEO,MACvER,EAAEC,EAAEO,IAAMV,EAAEG,EAAEO,IAF4B,CAItD,OAAOR,CACX,CAuQkD,mBAApBW,iBAAiCA,6VC1T/DT,OAAOU,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtD,IAAIC,EAAgC,WAChC,SAASA,IACL,IAAIC,EAAQC,KACZA,KAAKC,OAAS,IAAIC,IAClBF,KAAKG,YAAc,SAAUC,EAAKC,GAC9B,IAAIC,EAAYP,EAAME,OAAOM,IAAIH,QACfI,IAAdF,OACcE,IAAVH,EACAN,EAAME,OAAOQ,IAAIL,EAAK,IAGtBL,EAAME,OAAOQ,IAAIL,EAAK,CAACC,SAIbG,IAAVH,IACAC,EAAUI,QAAQL,GAClBN,EAAME,OAAOQ,IAAIL,EAAKE,GAG1C,EACQN,KAAKW,SAAW,SAAUP,GACtB,OAAOL,EAAME,OAAOW,IAAIR,EACpC,EACQJ,KAAKa,KAAO,SAAUT,GAClB,OAAO,IAAIU,SAAQ,SAAUC,EAASC,GAC9BjB,EAAMY,SAASP,GACfL,EAAMI,YAAYC,EAAKW,IAGvBhB,EAAMI,YAAYC,GAClBW,IAEpB,GACA,EACQf,KAAKiB,OAAS,SAAUb,GACpB,IAAIE,EAAYP,EAAME,OAAOM,IAAIH,GACjC,QAAkBI,IAAdF,GAAgD,IAArBA,EAAUd,OAAzC,CAIA,IAAI0B,EAASZ,EAAUa,MACvBpB,EAAME,OAAOQ,IAAIL,EAAKE,QACPE,IAAXU,GACAE,WAAWF,EAAQ,EAJtB,MAFGnB,EAAME,OAAOoB,OAAOjB,EAQpC,CACK,CAOD,OANAN,EAAewB,YAAc,WAIzB,YAHgCd,IAA5BV,EAAeyB,WACfzB,EAAeyB,SAAW,IAAIzB,GAE3BA,EAAeyB,QAC9B,EACWzB,CACX,IAIAF,EAAA4B,QAHA,WACI,OAAO1B,EAAewB,aAC1B,iCC3DA,IAAIG,EAAazB,GAAQA,EAAKyB,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAC1E,OAAO,IAAKD,IAAMA,EAAId,WAAU,SAAUC,EAASC,GAC/C,SAASc,EAAUjC,GAAS,IAAMkC,EAAKF,EAAUG,KAAKnC,IAAW,MAAOf,GAAKkC,EAAOlC,GAAO,CAC3F,SAASmD,EAASpC,GAAS,IAAMkC,EAAKF,EAAiB,MAAEhC,IAAW,MAAOf,GAAKkC,EAAOlC,GAAO,CAC9F,SAASiD,EAAKG,GAAUA,EAAOC,KAAOpB,EAAQmB,EAAOrC,OAAS,IAAI+B,GAAE,SAAUb,GAAWA,EAAQmB,EAAOrC,UAAWuC,KAAKN,EAAWG,EAAY,CAC/IF,GAAMF,EAAYA,EAAUQ,MAAMX,EAASC,GAAc,KAAKK,OACtE,GACA,EACIM,EAAetC,GAAQA,EAAKsC,aAAgB,SAAUZ,EAASa,GAC/D,IAAsGC,EAAGC,EAAG1D,EAAG2D,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP9D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAK,EAAE+D,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEV,KAAMgB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOpD,IAAO,GAAG0C,EACvJ,SAASM,EAAKK,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIf,EAAG,MAAM,IAAIgB,UAAU,mCAC3B,KAAOb,OACH,GAAIH,EAAI,EAAGC,IAAM1D,EAAY,EAARwE,EAAG,GAASd,EAAU,OAAIc,EAAG,GAAKd,EAAS,SAAO1D,EAAI0D,EAAU,SAAM1D,EAAEK,KAAKqD,GAAI,GAAKA,EAAET,SAAWjD,EAAIA,EAAEK,KAAKqD,EAAGc,EAAG,KAAKpB,KAAM,OAAOpD,EAE3J,OADI0D,EAAI,EAAG1D,IAAGwE,EAAK,CAAS,EAARA,EAAG,GAAQxE,EAAEc,QACzB0D,EAAG,IACP,KAAK,EAAG,KAAK,EAAGxE,EAAIwE,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAE/C,MAAO0D,EAAG,GAAIpB,MAAM,GAChD,KAAK,EAAGQ,EAAEC,QAASH,EAAIc,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAI5B,MAAOwB,EAAEG,KAAK3B,MAAO,SACxC,QACI,KAAMpC,EAAI4D,EAAEG,MAAM/D,EAAIA,EAAES,OAAS,GAAKT,EAAEA,EAAES,OAAS,KAAkB,IAAV+D,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVY,EAAG,MAAcxE,GAAMwE,EAAG,GAAKxE,EAAE,IAAMwE,EAAG,GAAKxE,EAAE,IAAM,CAAE4D,EAAEC,MAAQW,EAAG,GAAI,KAAQ,CACtF,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQ7D,EAAE,GAAI,CAAE4D,EAAEC,MAAQ7D,EAAE,GAAIA,EAAIwE,EAAI,KAAQ,CACrE,GAAIxE,GAAK4D,EAAEC,MAAQ7D,EAAE,GAAI,CAAE4D,EAAEC,MAAQ7D,EAAE,GAAI4D,EAAEI,IAAIU,KAAKF,GAAK,KAAQ,CAC/DxE,EAAE,IAAI4D,EAAEI,IAAI5B,MAChBwB,EAAEG,KAAK3B,MAAO,SAEtBoC,EAAKhB,EAAKnD,KAAKsC,EAASiB,GAC1B,MAAO7D,GAAKyE,EAAK,CAAC,EAAGzE,GAAI2D,EAAI,CAAE,CAAW,QAAED,EAAIzD,EAAI,CAAI,CAC1D,GAAY,EAARwE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE1D,MAAO0D,EAAG,GAAKA,EAAG,QAAK,EAAQpB,MAAM,EAC7E,CAtB+CJ,CAAK,CAACsB,EAAGC,GAAM,CAAG,CAuBtE,EACIvD,EAAQC,EACZf,OAAOU,eAAeC,EAAS,aAAc,CAAEC,OAAO,IAkBtD,IAAI6D,EAAmB,wBACnBC,EAA0B,CAC1BvD,IAAK,SAAUwD,GAAS,OAAOnC,EAAU1B,OAAO,OAAQ,GAAQ,WAC5D,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLC,QAAS,SAAU3D,GAAO,OAAOqB,EAAU1B,OAAO,OAAQ,GAAQ,WAC9D,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLE,MAAO,WAAc,OAAOvC,EAAU1B,OAAO,OAAQ,GAAQ,WACzD,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAO,CAAC,EAAcI,OAAOC,aAAaF,QACtD,GACK,GAAI,EACLG,WAAY,SAAU/D,GAAO,OAAOqB,EAAU1B,OAAO,OAAQ,GAAQ,WACjE,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLM,QAAS,SAAUhE,EAAKP,GAAS,OAAO4B,EAAU1B,OAAO,OAAQ,GAAQ,WACrE,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLO,QAAS,SAAUT,GACf,OAAOK,OAAOC,aAAa9D,IAAIwD,EAClC,EACDU,YAAa,SAAUlE,GACnB,OAAO6D,OAAOC,aAAaH,QAAQ3D,EACtC,EACDmE,UAAW,WACP,OAAON,OAAOC,aAAaF,OAC9B,EACDQ,eAAgB,SAAUpE,GACtB,OAAO6D,OAAOC,aAAaC,WAAW/D,EACzC,EACDqE,YAAa,SAAUrE,EAAKP,GACxB,OAAOoE,OAAOC,aAAaE,QAAQhE,EAAKP,EAC3C,GAOL,SAAS6E,EAAMC,GACX,OAAO,IAAI7D,SAAQ,SAAUC,GAAW,OAAOK,WAAWL,EAAS4D,EAAc,GACrF,CAOA,SAASC,EAAqBpF,GAG1B,IAFA,IAAIqF,EAAQ,gEACRC,EAAe,GACVvF,EAAI,EAAGA,EAAIC,EAAQD,IAAK,CAE7BuF,GAAgBD,EADJE,KAAKC,MAAMD,KAAKE,SAAWJ,EAAMrF,QAEhD,CACD,OAAOsF,CACX,CASA,IAAII,EAAiC,WACjC,SAASA,EAAgBC,GACrBnF,KAAKoF,eAAiB,IAAIC,IAC1BrF,KAAKmF,oBAAiB3E,EACtBR,KAAKsF,GANFC,KAAKC,MAAMC,WAAab,EAAqB,IAOhD5E,KAAK0F,YAAc1F,KAAK0F,YAAYC,KAAK3F,MACzCA,KAAK4F,YAAc5F,KAAK4F,YAAYD,KAAK3F,MACzCA,KAAK6F,uBAAyB7F,KAAK6F,uBAAuBF,KAAK3F,MAC/DA,KAAK8F,yBAA2B9F,KAAK8F,yBAAyBH,KAAK3F,MACnEA,KAAK+F,yBAA2B/F,KAAK+F,yBAAyBJ,KAAK3F,MACnEA,KAAKmF,eAAiBA,OACU3E,IAA5B0E,EAAgBc,UAChBd,EAAgBc,QAAU,GAEjC,CA8PD,OAnPAd,EAAgBhG,UAAUwG,YAAc,SAAUO,EAASC,GAEvD,YADgB,IAAZA,IAAsBA,EAAU,KAC7BzE,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,IAAImG,EAAKC,EAAUC,EAAaC,EAAkBC,EAAaC,EAAkBC,EACjF,OAAOnE,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EACDuD,EAAMZ,KAAKC,MAAQZ,EAAqB,GACxCwB,EAAWb,KAAKC,MAAQU,EACxBG,EAAc3C,EAAmB,IAAMuC,EACvCK,OAAkC9F,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,eAC7EtB,EAAGjB,MAAQ,EACf,KAAK,EACD,OAAM2C,KAAKC,MAAQY,EACZ,CAAC,EAAa1B,EAAM,KADU,CAAC,EAAa,GAEvD,KAAK,EAGD,OAFAb,EAAGhB,OAEe,OADRyD,EAAQhC,YAAY+B,GACE,CAAC,EAAa,IAC9CE,EAAcvG,KAAKsF,GAAK,IAAMW,EAAU,IAAME,EAEvC,CAAC,EAAazB,EAAMK,KAAKC,MAAsB,GAAhBD,KAAKE,aAC/C,KAAK,EAUD,OARApB,EAAGhB,OACHyD,EAAQ7B,YAAY4B,EAAaK,KAAKC,UAAU,CAC5CrB,GAAItF,KAAKsF,GACTa,IAAKA,EACLS,WAAYL,EACZM,aAActB,KAAKC,MACnBsB,cAAevB,KAAKC,SAEjB,CAAC,EAAad,EAAM,KAC/B,KAAK,EAGD,OAFAb,EAAGhB,OAEsB,QADzB2D,EAAmBF,EAAQhC,YAAY+B,MAEnCI,EAAyBC,KAAKK,MAAMP,IACTlB,KAAOtF,KAAKsF,IAAMmB,EAAuBN,MAAQA,GACxEnG,KAAKoF,eAAe4B,IAAIb,GACxBnG,KAAK+F,yBAAyBM,EAAaF,GACpC,CAAC,GAAc,IAGvB,CAAC,EAAa,GACzB,KAAK,EAED,OADAjB,EAAgB+B,mBAAsCzG,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,gBAC1F,CAAC,EAAanF,KAAK8F,yBAAyBM,IACvD,KAAK,EACDvC,EAAGhB,OACHgB,EAAGjB,MAAQ,EACf,KAAK,EAED,OADAuD,EAAMZ,KAAKC,MAAQZ,EAAqB,GACjC,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAAc,GAElD,GACA,GACA,EACIM,EAAgBhG,UAAU6G,yBAA2B,SAAUmB,EAAYf,GACvE,OAAO1E,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,IAAID,EAAQC,KACZ,OAAOsC,EAAYtC,MAAM,SAAU6D,GA6B/B,OA5BAzC,YAAW,WAAc,OAAOK,EAAU1B,OAAO,OAAQ,GAAQ,WAC7D,IAAIuG,EAASa,EAASC,EACtB,OAAO9E,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAAG,MAAO,CAAC,EAAayE,EAAc7F,UAAUX,KAAKsF,IAC1D,KAAK,EAED,OADAtC,EAAGhB,OACE7C,KAAKoF,eAAexE,IAAIuF,IAI7BG,OAAkC9F,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,eAE7D,QADhBgC,EAAUb,EAAQhC,YAAY4C,KAQ1BG,EAAc7F,UAAUP,OAAOkF,GACxB,CAAC,MAPRiB,EAAgBV,KAAKK,MAAMI,IACbL,cAAgBvB,KAAKC,MACnCc,EAAQ7B,YAAYyC,EAAYR,KAAKC,UAAUS,IAC/CC,EAAc7F,UAAUP,OAAOkF,GAMnCnG,KAAK+F,yBAAyBmB,EAAYf,GACnC,CAAC,MAhBJkB,EAAc7F,UAAUP,OAAOkF,GACxB,CAAC,IAiB5C,GACA,GAAmB,GAAI,KACA,CAAC,EACxB,GACA,GACA,EACIjB,EAAgBhG,UAAU4G,yBAA2B,SAAUM,GAC3D,OAAO3E,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,OAAOsC,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAAG,MAAO,CAAC,EAAa,IAAI9B,SAAQ,SAAUC,GAC3C,IAAIuG,GAAiB,EACjBC,EAAYhC,KAAKC,MAEjBgC,GAAmB,EACvB,SAASC,IAOL,GANKD,IACDvD,OAAOyD,oBAAoB,UAAWD,GACtCvC,EAAgByC,kBAAkBF,GAClCG,aAAaC,GACbL,GAAmB,IAElBF,EAAgB,CACjBA,GAAiB,EACjB,IAAIQ,EAXW,IAWsBvC,KAAKC,MAAQ+B,GAC9CO,EAAa,EACb1G,WAAWL,EAAS+G,GAGpB/G,EAAQ,KAEf,CACJ,CACDkD,OAAO8D,iBAAiB,UAAWN,GACnCvC,EAAgB8C,aAAaP,GAC7B,IAAII,EAAYzG,WAAWqG,EAAa1C,KAAKkD,IAAI,EAAG7B,EAAWb,KAAKC,OACvE,KACL,KAAK,EAED,OADA3B,EAAGhB,OACI,CAAC,GAEhC,GACA,GACA,EACIqC,EAAgB8C,aAAe,SAAUE,GACrClI,KAAK2H,kBAAkBO,QACS1H,IAA5B0E,EAAgBc,SAGpBd,EAAgBc,QAAQvC,KAAKyE,EACrC,EACIhD,EAAgByC,kBAAoB,SAAUO,QACV1H,IAA5B0E,EAAgBc,UAGpBd,EAAgBc,QAAUd,EAAgBc,QAAQmC,QAAO,SAAU5I,GAAK,OAAOA,IAAM2I,CAAO,IACpG,EACIhD,EAAgBkD,cAAgB,gBACI5H,IAA5B0E,EAAgBc,SAGNd,EAAgBc,QAAQqC,QAC9BC,SAAQ,SAAU/I,GAAK,OAAOA,GAAI,GAClD,EAQI2F,EAAgBhG,UAAU0G,YAAc,SAAUK,GAC9C,OAAOxE,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,OAAOsC,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAAG,MAAO,CAAC,EAAa5C,KAAK6F,uBAAuBI,IACzD,KAAK,EAAG,MAAO,CAAC,EAAcpC,EAAGhB,QAErD,GACA,GACA,EAQIqC,EAAgBhG,UAAU2G,uBAAyB,SAAUI,GACzD,OAAOxE,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,IAAIsG,EAASD,EAAac,EAASoB,EACnC,OAAOjG,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAID,OAHA0D,OAAkC9F,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,eAC7EkB,EAAc3C,EAAmB,IAAMuC,EAEvB,QADhBkB,EAAUb,EAAQhC,YAAY+B,IAEnB,CAAC,IAEZkC,EAAgB7B,KAAKK,MAAMI,IACP7B,KAAOtF,KAAKsF,GAAY,CAAC,EAAa,GACnD,CAAC,EAAa+B,EAAc7F,UAAUX,KAAK0H,EAAcpC,MACpE,KAAK,EACDtC,EAAGhB,OACH7C,KAAKoF,eAAe/D,OAAOkH,EAAcpC,KACzCG,EAAQ9B,eAAe6B,GACvBgB,EAAc7F,UAAUP,OAAOsH,EAAcpC,KAC7CjB,EAAgBkD,gBAChBvE,EAAGjB,MAAQ,EACf,KAAK,EAAG,MAAO,CAAC,GAEpC,GACA,GACA,EAOIsC,EAAgB+B,cAAgB,SAAU9B,GAKtC,IAJA,IAAIqD,EAAmBjD,KAAKC,MAAQ,IAChCc,EAAUnB,EACVsD,EAAO,GACPC,EAAY,IACH,CACT,IAAItI,EAAMkG,EAAQjC,QAAQqE,GAC1B,GAAY,OAARtI,EACA,MAEJqI,EAAKhF,KAAKrD,GACVsI,GACH,CAED,IADA,IAAIN,GAAgB,EACX7I,EAAI,EAAGA,EAAIkJ,EAAKjJ,OAAQD,IAAK,CAClC,IAAIoJ,EAAWF,EAAKlJ,GACpB,GAAIoJ,EAASC,SAASlF,GAAmB,CACrC,IAAIyD,EAAUb,EAAQhC,YAAYqE,GAClC,GAAgB,OAAZxB,EAAkB,CAClB,IAAIoB,EAAgB7B,KAAKK,MAAMI,SACM3G,IAAhC+H,EAAczB,eAA+ByB,EAAc1B,aAAe2B,QAC1ChI,IAAhC+H,EAAczB,eAA+ByB,EAAczB,cAAgB0B,KAC5ElC,EAAQ9B,eAAemE,GACvBP,GAAgB,EAEvB,CACJ,CACJ,CACGA,GACAlD,EAAgBkD,eAE5B,EACIlD,EAAgBc,aAAUxF,EACnB0E,CACX,IACAtF,EAAA4B,QAAkB0D,YCzYX,MAKM2D,EAAmD,CAC9DC,iBANkD,IAwBvCC,EAAwB,SA0BxBC,EAAuB,CAClCC,KAAM,eACNC,QC1Da,SD6DFC,EAAuB,IAAM5D,KAAKC,ME1DzC,MAAO4D,UAAqBtF,MAChCuF,YAAmBC,EAAsBC,GACvCC,MAAMD,GADWvJ,KAAKsJ,MAALA,EAAsBtJ,KAAiBuJ,kBAAjBA,EAEvCtK,OAAOwK,eAAezJ,KAAMoJ,EAAalK,UAC1C,CAEDwK,oBAAmBJ,MACjBA,EAAKC,kBACLA,IAKA,OAAO,IAAIH,EAAaE,EAAOC,EAChC,EAOG,MAAOI,UAA4BP,EACvCC,YACEC,EACAC,EACOK,EACAC,EAAgB,MAEvBL,MAAMF,EAAOC,GAHNvJ,KAAK4J,MAALA,EACA5J,KAAQ6J,SAARA,EAIP5K,OAAOwK,eAAezJ,KAAM2J,EAAoBzK,UACjD,EAOG,MAAO4K,UAAqBV,EAChCC,cACEG,MAAM,UAAW,WAEjBvK,OAAOwK,eAAezJ,KAAM8J,EAAa5K,UAC1C,EAMG,MAAO6K,UAA0BD,EACrCT,YAAmBW,GACjBR,QADiBxJ,KAAKgK,MAALA,EAGjB/K,OAAOwK,eAAezJ,KAAM+J,EAAkB7K,UAC/C,EAGG,MAAO+K,UAA4Bb,EACvCC,YAAmBW,GACjBR,MAAM,YAAa,gBADFxJ,KAAKgK,MAALA,EAGjB/K,OAAOwK,eAAezJ,KAAMiK,EAAoB/K,UACjD,EAMG,MAAOgL,UAAyBd,EACpCC,YACEC,EACAC,EACOY,GAEPX,MAAMF,EAAOC,GAFNvJ,KAASmK,UAATA,EAIPlL,OAAOwK,eAAezJ,KAAMkK,EAAiBhL,UAC9C,EAMG,MAAOkL,UAAiChB,EAC5CC,YAAmBgB,EAAyBC,GAC1Cd,MACE,wBACA,qCAAqCe,EAAmBF,EAAU,CAChE,yBACcE,EAAmBD,QALpBtK,KAAQqK,SAARA,EAAyBrK,KAAKsK,MAALA,EAO1CrL,OAAOwK,eAAezJ,KAAMoK,EAAyBlL,UACtD,EASH,SAASqL,EAAmB1K,EAAe2K,EAAoB,IAC7D,OAAO3K,IAAU2K,EAAQ5B,SAAS/I,GAASA,EAAQ,EACrD,CC5FO,MA6HM4K,EAAY,IAChBxG,OAAOyG,OAGHC,EAAqB,KAChC,MAAMC,EACJ,qEACF,IAAI3F,EAAS,GAKb,OAJqB4F,MAAMC,KACzBL,IAAYM,gBAAgB,IAAIC,WAAW,MAEhC1C,SAAQhF,GAAM2B,GAAU2F,EAAQtH,EAAIsH,EAAQpL,UAClDyF,CAAM,EAGFgG,EAAUpL,GAAkBqL,KAAKrL,GASjCsL,EAAqBtH,QAAEuH,SAAUC,GAASxH,EAAKyH,EAAM1M,EAAAiF,EAAhC,cAChC,OAAO,IAAI0H,gBAPU,CAACD,GACfrM,OAAOuM,KAAKF,GAChBnD,QAAOsD,QAA0B,IAAdH,EAAOG,KAC1BC,QAAO,CAACC,EAAKvL,IAAQnB,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAMD,GAAG,CAAEvL,CAACA,GAAMkL,EAAOlL,MAAS,CAAA,GAKxDyL,CAAiB5M,OAAA2M,OAAA,CAAAP,aAAcC,KAC/B7F,UAAU,EA4BDqG,EAAgBC,GAVX,CAACA,GACjBC,mBACEC,KAAKF,GACFG,MAAM,IACNC,KAAIC,GACI,KAAO,KAAOA,EAAEC,WAAW,GAAG5G,SAAS,KAAK4C,OAAO,KAE3DiE,KAAK,KAIVC,CAAUR,EAAMS,QAAQ,KAAM,KAAKA,QAAQ,KAAM,MCpL7CC,EAAUC,MAAOC,EAAkBC,KACvC,MAAMC,QAAiBC,MAAMH,EAAUC,GAEvC,MAAO,CACLG,GAAIF,EAASE,GACbC,WAAYH,EAASG,OACtB,EAGGC,EAAqBP,MACzBC,EACAC,EACA1G,KAEA,MAAMgH,EAhBmC,IAAIC,gBAmB7C,IAAIC,EAGJ,OALAR,EAAaS,OAASH,EAAWG,OAK1BvM,QAAQwM,KAAK,CAClBb,EAAQE,EAAUC,GAElB,IAAI9L,SAAQ,CAAC6B,EAAG3B,KACdoM,EAAYhM,YAAW,KACrB8L,EAAWK,QACXvM,EAAO,IAAI8C,MAAM,kCAAkC,GAClDoC,EAAQ,MAEZsH,SAAQ,KACT5F,aAAawF,EAAU,GACvB,EAGEK,EAAkBf,MACtBC,EACAtC,EACAC,EACAsC,EACA1G,EACAwH,EACAC,KAEA,OCnD0BC,EDoDxB,CACEC,KAAM,CACJxD,WACAC,SAEFpE,UACAyG,WACAC,eACAe,eC5D0DG,ED8D5DJ,EC7DF,IAAI5M,SAAQ,SAAUC,EAASC,GAC7B,MAAM+M,EAAiB,IAAIC,eAE3BD,EAAeE,MAAMC,UAAY,SAAUC,GAErCA,EAAMC,KAAK9E,MACbtI,EAAO,IAAI8C,MAAMqK,EAAMC,KAAK9E,QAE5BvI,EAAQoN,EAAMC,MAEhBL,EAAeE,MAAMI,OACvB,EAEAP,EAAGQ,YAAYV,EAAS,CAACG,EAAeQ,OAC1C,IAfyB,IAACX,EAAoCE,CD+D7D,EAGUU,EAAc9B,MACzBC,EACAtC,EACAC,EACAsC,EACAc,EACAC,EACAzH,EJpDsC,MIsDlCwH,EACKD,EACLd,EACAtC,EACAC,EACAsC,EACA1G,EACAwH,EACAC,GAGKV,EAAmBN,EAAUC,EAAc1G,GExF/CwG,eAAe+B,EACpB5K,EASA6J,GATA,IAAAgB,QACEA,EAAOxI,QACPA,EAAOmE,SACPA,EAAQC,MACRA,EAAKqE,YACLA,EAAWhB,YACXA,GAEqB9J,EADlB+K,EAAOhQ,EAAAiF,EAPZ,sEAWA,MAAMtB,EAAOoL,EACTxC,EAAkByD,GAClBlI,KAAKC,UAAUiI,GAEnB,aF4EKlC,eACLmC,EACA3I,EACAmE,EACAC,EACAsE,EACAlB,EACAC,GAEA,IACId,EADAiC,EAA2B,KAG/B,IAAK,IAAIvP,EAAI,EAAGA,EJ3F8B,EI2FQA,IACpD,IACEsN,QAAiB2B,EACfK,EACAxE,EACAC,EACAsE,EACAlB,EACAC,EACAzH,GAEF4I,EAAa,KACb,KAOD,CANC,MAAOhQ,GAKPgQ,EAAahQ,CACd,CAGH,GAAIgQ,EACF,MAAMA,EAGR,MACEjL,EAEEgJ,EAAQG,MAFV1D,MAAQA,EAAKC,kBAAEA,GAAiB1F,EAAKuK,EAAIxP,EAAAiF,EAAnC,gCADFkJ,GAEJA,GACEF,EAEJ,IAAKE,EAAI,CACP,MAAMgC,EACJxF,GAAqB,+BAA+BsF,IAEtD,GAAc,iBAAVvF,EACF,MAAM,IAAIY,EAAiBZ,EAAOyF,EAAcX,EAAKjE,WAGvD,GAAc,0BAAVb,EACF,MAAM,IAAIc,EAAyBC,EAAUC,GAG/C,MAAM,IAAIlB,EAAaE,GAAS,gBAAiByF,EAClD,CAED,OAAOX,CACT,CEvIeY,CACX,GAAGN,gBACHxI,EACAmE,GAAY,UACZC,EACA,CACE2E,OAAQ,OACR1M,OACA2M,QAAS,CACP,eAAgBvB,EACZ,oCACA,mBACJ,eAAgBzC,KACdxE,KAAKC,UAAUgI,GAAe3F,MAIpC0E,EACAC,EAEJ,CCtCA,MAWawB,EAAkB,IAAIC,KACjC,OAZcC,EAYAD,EAAOjH,OAAOmH,SAAShD,KAAK,KAAKiD,OAAOrD,MAAM,OAZ5BrB,MAAMC,KAAK,IAAIzF,IAAIgK,KAYiB/C,KAAK,KAZ5D,IAAC+C,CAY+D,ECblEG,EAAmB,iBACnBC,EAA4B,iBAQ5BC,EAKXrG,YACE+E,EACOuB,EAAiBH,iBACjBI,GADA5P,KAAM2P,OAANA,EACA3P,KAAM4P,OAANA,EAEP5P,KAAKoL,SAAWgD,EAAKhD,SACrBpL,KAAKsK,MAAQ8D,EAAK9D,MAClBtK,KAAKqK,SAAW+D,EAAK/D,QACtB,CAMDwF,QACE,MAAO,CAAC7P,KAAK2P,OAAQ3P,KAAKoL,SAAUpL,KAAKqK,SAAUrK,KAAKsK,MAAOtK,KAAK4P,QACjEzH,OAAOmH,SACPhD,KAAK,KACT,CAOD5C,eAAetJ,GACb,MAAOuP,EAAQvE,EAAUf,EAAUC,GAASlK,EAAI8L,MAAM,MAEtD,OAAO,IAAIwD,EAAS,CAAEtE,WAAUd,QAAOD,YAAYsF,EACpD,CAODjG,sBAAsBoG,GACpB,MAAMxF,MAAEA,EAAKD,SAAEA,EAAUgB,UAAWD,GAAa0E,EAEjD,OAAO,IAAIJ,EAAS,CAClBpF,QACAD,WACAe,YAEH,QC1DU2E,EACJtP,IAAmBL,EAAa0P,GACrC5L,aAAaE,QAAQhE,EAAKsG,KAAKC,UAAUmJ,GAC1C,CAEMvP,IAAmBH,GACxB,MAAM4M,EAAO/I,OAAOC,aAAaH,QAAQ3D,GAEzC,GAAK4M,EAEL,IAEE,OADgBtG,KAAKK,MAAMiG,EAK5B,CAFC,MAAOlO,GACP,MACD,CACF,CAEMkR,OAAO5P,GACZ8D,aAAaC,WAAW/D,EACzB,CAEM6P,UACL,OAAOhR,OAAOuM,KAAKvH,OAAOC,cAAciE,QAAO/H,GAC7CA,EAAI8P,WAAWV,IAElB,QC3BUW,EAAb9G,cACSrJ,KAAAoQ,cAAwB,WAC7B,IAAIC,EAAiC,CAAA,EAErC,MAAO,CACL5P,IAAmBL,EAAa0P,GAC9BO,EAAMjQ,GAAO0P,CACd,EAEDvP,IAAmBH,GACjB,MAAMkQ,EAAaD,EAAMjQ,GAEzB,GAAKkQ,EAIL,OAAOA,CACR,EAEDN,OAAO5P,UACEiQ,EAAMjQ,EACd,EAED6P,QAAO,IACEhR,OAAOuM,KAAK6E,GAGxB,CA1B8B,EA2BhC,QCdYE,EAGXlH,YACUgH,EACAG,EACRC,GAFQzQ,KAAKqQ,MAALA,EACArQ,KAAWwQ,YAAXA,EAGRxQ,KAAKyQ,YAAcA,GAAetH,CACnC,CAEDuD,iBACEtB,EACAsF,EACAC,SAEA,MAAMC,EAAW5Q,KAAK6Q,mBAAmBzF,SACnCpL,KAAKqQ,MAAM5P,IAAImQ,EAAU,CAC7BE,SAAUJ,EACVC,uBAEsB,QAAlB9M,EAAA7D,KAAKwQ,mBAAa,IAAA3M,OAAA,EAAAA,EAAAmD,IAAI4J,GAC7B,CAEDlE,iBAAiBkE,GACf,MAAMd,QAAc9P,KAAKqQ,MAAM9P,IAC7BP,KAAK6Q,mBAAmBD,EAASxF,WAGnC,IAAK0E,GAASc,EAAStG,OAASsG,EAASvG,SAAU,CACjD,MAAM0G,QAAqB/Q,KAAKO,IAAIqQ,GAEpC,IAAKG,EACH,OAGF,IAAKA,EAAaD,WAAaC,EAAaJ,aAC1C,OAGF,MAAO,CACLG,SAAUC,EAAaD,SACvBH,aAAcI,EAAaJ,aAE9B,CAED,GAAKb,EAIL,MAAO,CAAEgB,SAAUhB,EAAMgB,SAAUH,aAAcb,EAAMa,aACxD,CAEDjE,UACEkE,EACAI,EAzDsC,SA2DtC,IAAIC,QAAqBjR,KAAKqQ,MAAM9P,IAClCqQ,EAASf,SAGX,IAAKoB,EAAc,CACjB,MAAMzF,QAAaxL,KAAKkR,eAExB,IAAK1F,EAAM,OAEX,MAAM2F,EAAanR,KAAKoR,sBAAsBR,EAAUpF,GAEpD2F,IACFF,QAAqBjR,KAAKqQ,MAAM9P,IAAuB4Q,GAE1D,CAGD,IAAKF,EACH,OAGF,MAAMzL,QAAYxF,KAAKyQ,cACjBY,EAAatM,KAAKC,MAAMQ,EAAM,KAEpC,OAAIyL,EAAaK,UAAYN,EAA0BK,EACjDJ,EAAa1O,KAAKgP,eACpBN,EAAa1O,KAAO,CAClBgP,cAAeN,EAAa1O,KAAKgP,qBAG7BvR,KAAKqQ,MAAM5P,IAAImQ,EAASf,QAASoB,GAChCA,EAAa1O,aAGhBvC,KAAKqQ,MAAML,OAAOY,EAASf,oBACT,QAAlBhM,EAAA7D,KAAKwQ,mBAAa,IAAA3M,OAAA,EAAAA,EAAAmM,OAAOY,EAASf,WAKnCoB,EAAa1O,IACrB,CAEDmK,UAAUoD,SACR,MAAMc,EAAW,IAAIlB,EAAS,CAC5BtE,SAAU0E,EAAMzE,UAChBf,MAAOwF,EAAMxF,MACbD,SAAUyF,EAAMzF,WAGZ4G,QAAqBjR,KAAKwR,eAAe1B,SAEzC9P,KAAKqQ,MAAM5P,IAAImQ,EAASf,QAASoB,SACf,QAAlBpN,EAAA7D,KAAKwQ,mBAAa,IAAA3M,OAAA,EAAAA,EAAAmD,IAAI4J,EAASf,SACtC,CAEDnD,YAAYtB,SACV,MAAMI,QAAaxL,KAAKkR,eAGnB1F,UAECA,EACHrD,QAAO/H,IAAQgL,GAAWhL,EAAIwI,SAASwC,KACvCM,QAAOgB,MAAO+E,EAAMrR,WACbqR,QACAzR,KAAKqQ,MAAML,OAAO5P,EAAI,GAC3BU,QAAQC,iBAEW,UAAlBf,KAAKwQ,mBAAa,IAAA3M,OAAA,EAAAA,EAAAG,SACzB,CAEO0I,qBAAqBoD,GAC3B,MAAMtK,QAAYxF,KAAKyQ,cAGvB,MAAO,CACLlO,KAAMuN,EACNwB,UAJoBvM,KAAKC,MAAMQ,EAAM,KAAQsK,EAAM4B,WAMtD,CAEOhF,2BACN,OAAI1M,KAAKwQ,YACgC,QAAhC3M,QAAO7D,KAAKwQ,YAAYjQ,aAAQ,IAAAsD,OAAA,EAAAA,EAAA2H,KAC9BxL,KAAKqQ,MAAMJ,QACbjQ,KAAKqQ,MAAMJ,eADb,CAGR,CAOOY,mBAAmBzF,GACzB,OAAO,IAAIsE,EACT,CAAEtE,YACFoE,EACAC,GACAI,OACH,CAcOuB,sBAAsBO,EAAsB1B,GAClD,OAAOA,EAAQ9H,QAAO/H,UACpB,MAAMwQ,EAAWlB,EAASkC,QAAQxR,GAC5ByR,EAAW,IAAIxM,IAAIuL,EAAStG,OAASsG,EAAStG,MAAM4B,MAAM,MAC1D4F,GAAkC,QAAlBjO,EAAA8N,EAAWrH,aAAO,IAAAzG,OAAA,EAAAA,EAAAqI,MAAM,OAAQ,GAEhD6F,EACJnB,EAAStG,OACTwH,EAAcpG,QACZ,CAACC,EAAKqG,IAAYrG,GAAOkG,EAASjR,IAAIoR,KACtC,GAGJ,OACEpB,EAASjB,SAAWH,GACpBoB,EAASxF,WAAauG,EAAWvG,UACjCwF,EAASvG,WAAasH,EAAWtH,UACjC0H,CACA,IACD,EACJ,QCjMUE,EAGX5I,YACU6I,EACA9G,EACA+G,GAFAnS,KAAOkS,QAAPA,EACAlS,KAAQoL,SAARA,EACApL,KAAYmS,aAAZA,EAERnS,KAAKkH,WAAa,gBAAqClH,KAAKoL,UAC7D,CAEMgH,OAAOC,GACZrS,KAAKkS,QAAQI,KAAKtS,KAAKkH,WAAYmL,EAAa,CAC9CE,gBAAiB,EACjBJ,aAAcnS,KAAKmS,cAEtB,CAEM5R,MACL,OAAOP,KAAKkS,QAAQ3R,IAAIP,KAAKkH,WAC9B,CAEM8I,SACLhQ,KAAKkS,QAAQlC,OAAOhQ,KAAKkH,WAAY,CACnCiL,aAAcnS,KAAKmS,cAEtB,ECtCH,MAAMK,EAAYnP,GAAwB,iBAANA,EAE9BoP,EAAiB,CACrB,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QACA,YACA,UACA,SACA,MACA,MACA,UACA,MACA,eACA,WACA,aACA,eACA,iBACA,OACA,OACA,MACA,SACA,MACA,MACA,MACA,MACA,MACA,OA2BWC,EAAU9D,IACrB,IAAKA,EAAQkC,SACX,MAAM,IAAIhN,MAAM,oCAGlB,MAAM6O,EA7Bc,CAACC,IACrB,MAAMC,EAAQD,EAAM1G,MAAM,MACnB4G,EAAQC,EAASC,GAAaH,EAErC,GAAqB,IAAjBA,EAAMrT,SAAiBsT,IAAWC,IAAYC,EAChD,MAAM,IAAIlP,MAAM,iCAElB,MAAMmP,EAAcvM,KAAKK,MAAM+E,EAAaiH,IACtCG,EAAkB,CAAEC,MAAOP,GAC3BQ,EAAY,CAAA,EAOlB,OANAnU,OAAOuM,KAAKyH,GAAa3K,SAAQmD,IAC/ByH,EAAOzH,GAAKwH,EAAYxH,GACnBgH,EAAe7J,SAAS6C,KAC3B2H,EAAK3H,GAAKwH,EAAYxH,GACvB,IAEI,CACL4H,QAAS,CAAEP,SAAQC,UAASC,aAC5BF,OAAQpM,KAAKK,MAAM+E,EAAagH,IAChCI,SACAE,OACD,EAQeE,CAAO1E,EAAQkC,UAE/B,IAAK6B,EAAQO,OAAOK,IAClB,MAAM,IAAIzP,MACR,+DAIJ,GAAI6O,EAAQO,OAAOK,MAAQ3E,EAAQ2E,IACjC,MAAM,IAAIzP,MACR,0DAA0D8K,EAAQ2E,gBAAgBZ,EAAQO,OAAOK,QAIrG,IAAKZ,EAAQS,KAAKI,IAChB,MAAM,IAAI1P,MACR,gEAIJ,GAA2B,UAAvB6O,EAAQG,OAAOW,IACjB,MAAM,IAAI3P,MACR,2BAA2B6O,EAAQG,OAAOW,2EAI9C,IACGd,EAAQO,OAAOQ,KAEgB,iBAAvBf,EAAQO,OAAOQ,MACtB7I,MAAM8I,QAAQhB,EAAQO,OAAOQ,KAG/B,MAAM,IAAI5P,MACR,qFAGJ,GAAI+G,MAAM8I,QAAQhB,EAAQO,OAAOQ,KAAM,CACrC,IAAKf,EAAQO,OAAOQ,IAAI9K,SAASgG,EAAQ8E,KACvC,MAAM,IAAI5P,MACR,4DACE8K,EAAQ8E,4BACef,EAAQO,OAAOQ,IAAIpH,KAAK,UAGrD,GAAIqG,EAAQO,OAAOQ,IAAIlU,OAAS,EAAG,CACjC,IAAKmT,EAAQO,OAAOU,IAClB,MAAM,IAAI9P,MACR,uHAGJ,GAAI6O,EAAQO,OAAOU,MAAQhF,EAAQ8E,IACjC,MAAM,IAAI5P,MACR,oEAAoE8K,EAAQ8E,gBAAgBf,EAAQO,OAAOU,OAGhH,CACF,MAAM,GAAIjB,EAAQO,OAAOQ,MAAQ9E,EAAQ8E,IACxC,MAAM,IAAI5P,MACR,4DAA4D8K,EAAQ8E,mBAAmBf,EAAQO,OAAOQ,QAG1G,GAAI9E,EAAQiF,MAAO,CACjB,IAAKlB,EAAQO,OAAOW,MAClB,MAAM,IAAI/P,MACR,gEAGJ,GAAI6O,EAAQO,OAAOW,QAAUjF,EAAQiF,MACnC,MAAM,IAAI/P,MACR,2DAA2D8K,EAAQiF,kBAAkBlB,EAAQO,OAAOW,SAGzG,CAED,GAAIjF,EAAQkF,UAAYtB,EAASG,EAAQO,OAAOa,WAC9C,MAAM,IAAIjQ,MACR,sHAKJ,GAA0B,MAAtB6O,EAAQO,OAAOc,MAAgBxB,EAASG,EAAQO,OAAOc,KACzD,MAAM,IAAIlQ,MACR,wEAGJ,IAAK0O,EAASG,EAAQO,OAAO/M,KAC3B,MAAM,IAAIrC,MACR,kEAIJ,MAAMmQ,EAASrF,EAAQqF,QAAU,GAC3BzO,EAAM,IAAID,KAAKqJ,EAAQpJ,KAAOD,KAAKC,OACnC0O,EAAU,IAAI3O,KAAK,GAIzB,GAFA2O,EAAQC,cAAcxB,EAAQO,OAAOc,IAAMC,GAEvCzO,EAAM0O,EACR,MAAM,IAAIpQ,MACR,oEAAoE0B,gCAAkC0O,MAI1G,GAA0B,MAAtBvB,EAAQO,OAAOkB,KAAe5B,EAASG,EAAQO,OAAOkB,KAAM,CAC9D,MAAMC,EAAU,IAAI9O,KAAK,GAEzB,GADA8O,EAAQF,cAAcxB,EAAQO,OAAOkB,IAAMH,GACvCzO,EAAM6O,EACR,MAAM,IAAIvQ,MACR,+GAA+G0B,gBAAkB6O,IAGtI,CAED,GAAgC,MAA5B1B,EAAQO,OAAOa,WAAqBvB,EAASG,EAAQO,OAAOa,WAAY,CAC1E,MAAMO,EAAe,IAAI/O,KAAK,GAK9B,GAJA+O,EAAaH,cACXI,SAAS5B,EAAQO,OAAOa,WAAcnF,EAAQkF,QAAqBG,GAGjEzO,EAAM8O,EACR,MAAM,IAAIxQ,MACR,uJAAuJ0B,4BAA8B8O,IAG1L,CAED,GAAI1F,EAAQ4F,aAAc,CACxB,MAAMC,EAAM7F,EAAQ4F,aAAajF,OACjC,GAAIkF,EAAIvE,WAAW,QAAS,CAC1B,MAAMwE,EAAQD,EACd,IAAK9B,EAAQO,OAAOyB,OAClB,MAAM,IAAI7Q,MACR,2EAEG,GAAI4Q,IAAU/B,EAAQO,OAAOyB,OAClC,MAAM,IAAI7Q,MACR,sEAAsE4Q,cAAkB/B,EAAQO,OAAOyB,UAG5G,KAAM,CACL,MAAMC,EAAUH,EAAII,cAEpB,IAAKlC,EAAQO,OAAO4B,SAClB,MAAM,IAAIhR,MACR,+EAEG,GAAI8Q,IAAYjC,EAAQO,OAAO4B,SACpC,MAAM,IAAIhR,MACR,0EAA0E8Q,cAAoBjC,EAAQO,OAAO4B,YAGlH,CACF,CAED,OAAOnC,CAAO,yBC9NhB,IAAIoC,EAAY/U,GAAQA,EAAK+U,UAAa,WAStC,OARAA,EAAW9V,OAAO2M,QAAU,SAAS7M,GACjC,IAAK,IAAIF,EAAGU,EAAI,EAAG8D,EAAI2R,UAAUxV,OAAQD,EAAI8D,EAAG9D,IAE5C,IAAK,IAAIP,KADTH,EAAImW,UAAUzV,GACON,OAAOC,UAAUC,eAAeC,KAAKP,EAAGG,KACzDD,EAAEC,GAAKH,EAAEG,IAEjB,OAAOD,CACf,EACWgW,EAAS1S,MAAMrC,KAAMgV,UAChC,EAEA,SAASC,EAAmBhM,EAAMpJ,GAC9B,IAAKA,EACD,MAAO,GAEX,IAAIqV,EAAc,KAAOjM,EACzB,OAAc,IAAVpJ,EACOqV,EAEJA,EAAc,IAAMrV,CAC/B,CAaA,SAASoL,EAAOhC,EAAMpJ,EAAOsV,GACzB,OAAOC,mBAAmBnM,GACrBuD,QAAQ,2BAA4BR,oBACpCQ,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OACpC,IAAM4I,mBAAmBvV,GAE1B2M,QAAQ,4DAA6DR,oBAlB9E,SAA6BmJ,GACzB,GAAkC,iBAAvBA,EAAWE,QAAsB,CACxC,IAAIA,EAAU,IAAI9P,KAClB8P,EAAQC,gBAAgBD,EAAQE,kBAAyC,MAArBJ,EAAWE,SAC/DF,EAAWE,QAAUA,CACxB,CACD,OAAOJ,EAAmB,UAAWE,EAAWE,QAAUF,EAAWE,QAAQG,cAAgB,IACvFP,EAAmB,SAAUE,EAAWM,QACxCR,EAAmB,OAAQE,EAAWO,MACtCT,EAAmB,SAAUE,EAAWQ,QACxCV,EAAmB,WAAYE,EAAWS,SACpD,CAQUC,CAAoBV,EAC9B,CAEA,SAASpO,EAAM+O,GAIX,IAHA,IAAI5T,EAAS,CAAA,EACT6T,EAAUD,EAAeA,EAAa5J,MAAM,MAAQ,GACpD8J,EAAU,mBACLzW,EAAI,EAAGA,EAAIwW,EAAQvW,OAAQD,IAAK,CACrC,IAAIsT,EAAQkD,EAAQxW,GAAG2M,MAAM,KACzB+J,EAASpD,EAAMxK,MAAM,GAAGiE,KAAK,KACR,MAArB2J,EAAOC,OAAO,KACdD,EAASA,EAAO5N,MAAM,GAAI,IAE9B,IAEInG,EADa2Q,EAAM,GAAGrG,QAAQwJ,EAAShK,qBACtBiK,EAAOzJ,QAAQwJ,EAAShK,mBAI5C,CAFD,MAAOlN,GAEN,CACJ,CACD,OAAOoD,CACX,CAEA,SAASiU,IACL,OAAOpP,EAAMqP,SAASH,OAC1B,CAMA,SAASxV,EAAIwI,EAAMpJ,EAAOsV,GACtBiB,SAASH,OAAShL,EAAOhC,EAAMpJ,EAAOkV,EAAS,CAAEW,KAAM,KAAOP,GAClE,CAhEAvV,EAAkByW,YAAG,EAgCrBzW,EAAcqL,OAAGA,EAqBjBrL,EAAamH,MAAGA,EAIhBnH,EAAcuW,OAAGA,EAIjBvW,EAAWW,IAHX,SAAa0I,GACT,OAAOkN,IAASlN,EACpB,EAKArJ,EAAWa,IAAGA,EAIdb,EAAAoQ,OAHA,SAAgB/G,EAAMkM,GAClB1U,EAAIwI,EAAM,GAAI8L,EAASA,EAAS,CAAA,EAAII,GAAa,CAAEE,SAAU,IACjE,mEC7DO,MAAMiB,EAAgB,CAC3B/V,IAAsBH,GACpB,MAAMP,EAAQ0W,EAAYnW,GAE1B,QAAqB,IAAVP,EAIX,OAAU6G,KAAKK,MAAMlH,EACtB,EAEDyS,KAAKlS,EAAaP,EAAY+O,GAC5B,IAAI4H,EAA6C,CAAA,EAE7C,WAAavS,OAAOwS,SAASC,WAC/BF,EAAmB,CACjBb,QAAQ,EACRC,SAAU,UAIVhH,eAAAA,EAAS2D,mBACXiE,EAAiBnB,QAAUzG,EAAQ2D,kBAGjC3D,eAAAA,EAASuD,gBACXqE,EAAiBf,OAAS7G,EAAQuD,cAGpCwE,EAAYvW,EAAKsG,KAAKC,UAAU9G,GAAQ2W,EACzC,EAEDxG,OAAO5P,EAAawO,GAClB,IAAI4H,EAA6C,CAAA,GAE7C5H,eAAAA,EAASuD,gBACXqE,EAAiBf,OAAS7G,EAAQuD,cAGpCyE,EAAexW,EAAKoW,EACrB,GAYUK,EAAkC,CAC7CtW,IAAsBH,GACpB,MAAMP,EAAQyW,EAAc/V,IAAOH,GAEnC,OAAIP,GAIGyW,EAAc/V,IAAO,WAAmBH,IAChD,EAEDkS,KAAKlS,EAAaP,EAAY+O,GAC5B,IAAI4H,EAA6C,CAAA,EAE7C,WAAavS,OAAOwS,SAASC,WAC/BF,EAAmB,CAAEb,QAAQ,KAG3B/G,eAAAA,EAAS2D,mBACXiE,EAAiBnB,QAAUzG,EAAQ2D,kBAGjC3D,eAAAA,EAASuD,gBACXqE,EAAiBf,OAAS7G,EAAQuD,cAGpCwE,EACE,WAAmBvW,IACnBsG,KAAKC,UAAU9G,GACf2W,GAEFF,EAAchE,KAAKlS,EAAKP,EAAO+O,EAChC,EAEDoB,OAAO5P,EAAawO,GAClB,IAAI4H,EAA6C,CAAA,GAE7C5H,eAAAA,EAASuD,gBACXqE,EAAiBf,OAAS7G,EAAQuD,cAGpCyE,EAAexW,EAAKoW,GACpBF,EAActG,OAAO5P,EAAKwO,GAC1B0H,EAActG,OAAO,WAAmB5P,IAAOwO,EAChD,GAMUkI,EAAiB,CAC5BvW,IAAsBH,GAEpB,GAA8B,oBAAnB2W,eACT,OAGF,MAAMlX,EAAQkX,eAAehT,QAAQ3D,GAErC,OAAa,MAATP,EAIM6G,KAAKK,MAAMlH,QAJrB,CAKD,EAEDyS,KAAKlS,EAAaP,GAChBkX,eAAe3S,QAAQhE,EAAKsG,KAAKC,UAAU9G,GAC5C,EAEDmQ,OAAO5P,GACL2W,eAAe5S,WAAW/D,EAC3B,87GC/IH,MAAM4W,GAAiD,CAAA,QCO1CC,GAGX5N,YAAoBgH,EAAuBjF,GAAvBpL,KAAKqQ,MAALA,EAAuBrQ,KAAQoL,SAARA,EACzCpL,KAAKkX,YAAclX,KAAKmX,sBAAsBnX,KAAKoL,SACpD,CAEDsB,UAAUtM,SACR,MAAMoL,EAAO,IAAInG,KAC2C,QAA1DxB,QAAO7D,KAAKqQ,MAAM9P,IAAsBP,KAAKkX,oBAAa,IAAArT,OAAA,EAAAA,EAAE2H,OAAQ,IAGtEA,EAAKxE,IAAI5G,SAEHJ,KAAKqQ,MAAM5P,IAAsBT,KAAKkX,YAAa,CACvD1L,KAAM,IAAIA,IAEb,CAEDkB,aAAatM,GACX,MAAM0P,QAAc9P,KAAKqQ,MAAM9P,IAAsBP,KAAKkX,aAE1D,GAAIpH,EAAO,CACT,MAAMtE,EAAO,IAAInG,IAAIyK,EAAMtE,MAG3B,OAFAA,EAAKnK,OAAOjB,GAERoL,EAAK4L,KAAO,QACDpX,KAAKqQ,MAAM5P,IAAIT,KAAKkX,YAAa,CAAE1L,KAAM,IAAIA,WAG/CxL,KAAKqQ,MAAML,OAAOhQ,KAAKkX,YACrC,CACF,CAED3W,MACE,OAAOP,KAAKqQ,MAAM9P,IAAsBP,KAAKkX,YAC9C,CAEDlT,QACE,OAAOhE,KAAKqQ,MAAML,OAAOhQ,KAAKkX,YAC/B,CAEOC,sBAAsB/L,GAC5B,MAAO,mBAAwBA,GAChC,ECvCI,MAAMiM,GAA8B,8BAW9BC,GAAmC,yBAW1CC,GAAsD,CAC1DC,OAAQ,KAAM,IAAIrH,GAAgBC,cAClCqH,aAAc,IAAM,IAAI1H,GAMb2H,GAAgBjB,GACpBc,GAAsBd,GAuClBkB,GAGX/I,IAEA,MAAMgJ,QAAEA,EAAOC,WAAEA,GAAmCjJ,EAApBkJ,EAAoBlZ,EAAAgQ,EAA9C,CAAA,UAAA,eAON,sCAJKkJ,GAAe,CAClBF,SAAqB,IAAZA,GAAqBA,EAAUA,EAAUC,GAGlC,ECedhX,GAAO,IAAIkX,QAKJC,GA2BX3O,YAAYuF,GAkBV,IAAIqJ,EACA5H,EAEJ,GAjCerQ,KAAAkY,WAAoB,IAAI/H,GAAgBC,cAIxCpQ,KAAAmY,eAA8C,CAC7DC,oBAAqB,CACnB9N,MnBzFuB,wBmB2FzB+N,0BAA0B,EAC1B1K,aAAa,GA27BP3N,KAAsBsY,uBAAG5L,gBACzB7L,GAAK+E,YAAYyR,IAEvBpT,OAAOyD,oBAAoB,WAAY1H,KAAKsY,uBAAuB,EA17BnEtY,KAAK4O,QACA3P,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAA5L,KAAKmY,gBACLvJ,GACH,CAAAwJ,mDACKpY,KAAKmY,eAAeC,qBACpBxJ,EAAQwJ,uBAIG,oBAAXnU,QhBqDmB,MAC5B,IAAKwG,IACH,MAAM,IAAI3G,MACR,4EAGJ,QAAkC,IAAvB2G,IAAY8N,OACrB,MAAM,IAAIzU,MAAM,iMAGjB,EgB/DkC0U,GAE7B5J,EAAQyB,OAASzB,EAAQqJ,eAC3BQ,QAAQC,KACN,8IAOA9J,EAAQyB,MACVA,EAAQzB,EAAQyB,UACX,CAGL,GAFA4H,EAAgBrJ,EAAQqJ,eAAiBlP,GAEpC2O,GAAaO,GAChB,MAAM,IAAInU,MAAM,2BAA2BmU,MAG7C5H,EAAQqH,GAAaO,EAAbP,EACT,CAED1X,KAAK2Y,cAAgB/J,EAAQgK,qBACM,IAA/BhK,EAAQgK,qBnBnJwB,ImBsJpC5Y,KAAK6Y,eAC8B,IAAjCjK,EAAQkK,qBACJxC,EACAO,EAEN7W,KAAK+Y,kBDrKP,SCsKI/Y,KAAK4O,QAAQxD,6BAGfpL,KAAKgZ,0BD/JqC,CAAC5N,GAC7C,SAASA,qBC8J0B6N,CAC/BjZ,KAAK4O,QAAQxD,UAGfpL,KAAKkZ,uBACHtK,EAAQsK,wBnB7ImC,EmB+I7C,MAAMC,EAAqBvK,EAAQwK,0BAC/BpZ,KAAK6Y,cACL/B,EhBwBiB,IAACuC,EgBlBtBrZ,KAAKsK,MAAQ6E,EACX,SACAnP,KAAK4O,QAAQwJ,oBAAoB9N,MACjCtK,KAAK4O,QAAQ0K,iBAAmB,iBAAmB,IAGrDtZ,KAAKuZ,mBAAqB,IAAItH,EAC5BkH,EACAnZ,KAAK4O,QAAQxD,SACbpL,KAAK4O,QAAQuD,cAGfnS,KAAKyQ,YAAczQ,KAAK4O,QAAQ6B,aAAetH,EAE/CnJ,KAAKwZ,aAAe,IAAIjJ,EACtBF,EACCA,EAAMJ,aAEHzP,EADA,IAAIyW,GAAiB5G,EAAOrQ,KAAK4O,QAAQxD,UAE7CpL,KAAKyQ,aAGPzQ,KAAKqZ,WhBJiBA,EgBIKrZ,KAAK4O,QAAQ6G,OhBHrC,eAAegE,KAAKJ,GAIlBA,EAHE,WAAWA,KgBGlBrZ,KAAK0Z,YhBMqB,EAC5BC,EACAN,IAEIM,EACKA,EAAOzJ,WAAW,YAAcyJ,EAAS,WAAWA,KAGtD,GAAGN,KgBdWO,CAAe5Z,KAAK4O,QAAQ+K,OAAQ3Z,KAAKqZ,WAIxC,oBAAXpV,QACPA,OAAO4V,QACP7Z,KAAK4O,QAAQ0K,kBACbrB,IAAkBlP,IAEd/I,KAAK4O,QAAQkL,UACf9Z,KAAK0N,OAAS,IAAImM,OAAO7Z,KAAK4O,QAAQkL,WAEtC9Z,KAAK0N,OAAS,IAAIqM,EAGvB,CAEOC,KAAKtE,GACX,MAAM/G,EAAcyG,mBAClBlK,KAAKxE,KAAKC,UAAU3G,KAAK4O,QAAQD,aAAe3F,KAElD,MAAO,GAAGhJ,KAAKqZ,YAAY3D,iBAAoB/G,GAChD,CAEOsL,cAAcC,GACpB,OAAOla,KAAKga,KAAK,cAAc7O,EAAkB+O,KAClD,CAEOxN,qBACNoE,EACA+C,EACAW,GAEA,MAAMhP,QAAYxF,KAAKyQ,cAEvB,OAAO0J,EAAc,CACnB5G,IAAKvT,KAAK0Z,YACVhG,IAAK1T,KAAK4O,QAAQxD,SAClB0F,WACA+C,QACAW,eACAP,OAAQjU,KAAK4O,QAAQqF,OACrBH,ShBzBsBjU,EgByBDG,KAAK4O,QAAQwJ,oBAAoBtE,QhBxBrC,iBAAVjU,EACFA,EAEF0U,SAAS1U,EAAO,UAAOW,GgBsB1BgF,QhB1BqB,IAAC3F,CgB4BzB,CAEOua,gBAAgB5F,GAClBA,EACFxU,KAAK6Y,cAAcvG,KAAKtS,KAAK+Y,kBAAmBvE,EAAc,CAC5DjC,gBAAiBvS,KAAKkZ,uBACtB/G,aAAcnS,KAAK4O,QAAQuD,eAG7BnS,KAAK6Y,cAAc7I,OAAOhQ,KAAK+Y,kBAAmB,CAChD5G,aAAcnS,KAAK4O,QAAQuD,cAGhC,CAEOzF,2BACN0L,EACA8B,EACAG,GAUA,MAAMzQ,EAAQqB,EAAON,KACfkJ,EAAQ5I,EAAON,KACf2P,EAAgB3P,IAEhB4P,EhBzG8B,CAACxO,IACvC,MAAMyO,EAAgB,IAAIxP,WAAWe,GACrC,MArBmB,CAACA,IACpB,MAAM0O,EAAwC,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,IACzE,OAAO1O,EAAMS,QAAQ,UAAWkO,GAAcD,EAASC,IAAG,EAmBnDC,CACL1W,OAAOiH,KAAK0P,OAAOC,gBAAgBhQ,MAAMC,KAAK0P,KAC/C,EgBqGwBM,MhBrILpO,OAAO7N,IAC3B,MAAMkc,EAAgBtQ,IAAY8N,OAAOyC,OACvC,CAAE/R,KAAM,YACR,IAAIgS,aAAchQ,OAAOpM,IAG3B,aAAakc,CAAQ,EgB8HgBG,CAAOZ,IAGpChP,ED/PwB,EAChC6P,EAGA7Q,EACA8N,EACAxO,EACAiK,EACA0G,EACAa,EACAC,IAEApc,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,CACEP,UAAW8P,EAAc/P,UACtB+P,EAAc/C,qBACdA,GACH,CAAA9N,MAAO6E,EAAgB7E,EAAO8N,EAAoB9N,OAClDgR,cAAe,OACfD,cAAeA,GAAiB,QAChCzR,QACAiK,QACAuH,aACEA,GAAgBD,EAAc/C,oBAAoBgD,aACpDb,iBACAgB,sBAAuB,SCuORC,CACbxb,KAAK4O,QACL5O,KAAKsK,MACL8N,EACAxO,EACAiK,EACA0G,EACAnC,EAAoBgD,cAClBpb,KAAK4O,QAAQwJ,oBAAoBgD,cACjCf,EACFH,eAAAA,EAAkBmB,eAGdxM,EAAM7O,KAAKia,cAAc3O,GAE/B,MAAO,CACLuI,QACAyG,gBACAhQ,MAAOgB,EAAOhB,MACdD,SAAUiB,EAAOjB,UAAY,UAC7B+Q,aAAc9P,EAAO8P,aACrBxR,QACAiF,MAEH,CAyBMnC,qBACLkC,EACA6M,SAKA,GAHA7M,EAAUA,GAAW,KACrB6M,EAASA,GAAU,IAEPzR,QACVyR,EAAOzR,MhBpRY,CAAC6E,IACxB,MAEM6M,EAAOzX,OAAO0X,SAAW1X,OAAO2X,WAFxB,KAE8C,EACtDC,EAAM5X,OAAO6X,SAAW7X,OAAO8X,YAFtB,KAE8C,EAE7D,OAAO9X,OAAO+X,KACZnN,EACA,wBACA,QAAQ6M,SAAYG,2DACrB,EgB0QkBI,CAAU,KAEpBR,EAAOzR,OACV,MAAM,IAAIlG,MACR,2EAKN,MAAMwH,QAAetL,KAAKkc,qBACxBtN,EAAQwJ,qBAAuB,CAAA,EAC/B,CAAEiD,cAAe,eACjBpX,OAAOwS,SAAS0F,QAGlBV,EAAOzR,MAAMyM,SAAS2F,KAAO9Q,EAAOuD,IAEpC,MAAMwN,OhBxRc,CAACZ,GAChB,IAAI3a,SAA8B,CAACC,EAASC,KACjD,IAAIsb,EAGJ,MAAMC,EAAaC,aAAY,KACzBf,EAAOzR,OAASyR,EAAOzR,MAAMyS,SAC/BC,cAAcH,GACd3U,aAAawF,GACbnJ,OAAOyD,oBAAoB,UAAW4U,GAAoB,GAC1Dtb,EAAO,IAAIiJ,EAAoBwR,EAAOzR,QACvC,GACA,KAEGoD,EAAYhM,YAAW,KAC3Bsb,cAAcH,GACdvb,EAAO,IAAI+I,EAAkB0R,EAAOzR,QACpC/F,OAAOyD,oBAAoB,UAAW4U,GAAoB,EAAM,GACK,KAAnEb,EAAO3S,kBH9GqC,KGgHhDwT,EAAqB,SAAUxd,GAC7B,GAAKA,EAAEsP,MAAwB,2BAAhBtP,EAAEsP,KAAKuO,KAAtB,CASA,GALA/U,aAAawF,GACbsP,cAAcH,GACdtY,OAAOyD,oBAAoB,UAAW4U,GAAoB,GAC1Db,EAAOzR,MAAMqE,QAETvP,EAAEsP,KAAKvB,SAASvD,MAClB,OAAOtI,EAAOoI,EAAawT,YAAY9d,EAAEsP,KAAKvB,WAGhD9L,EAAQjC,EAAEsP,KAAKvB,SAXd,CAYH,EAEA5I,OAAO8D,iBAAiB,UAAWuU,EAAmB,IgBmP7BO,CAAQ5d,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAC5B6P,GAAM,CACT3S,iBACE2S,EAAO3S,kBACP9I,KAAK4O,QAAQkO,2BnBxX+B,MmB4XhD,GAAIxR,EAAO1B,QAAUyS,EAAWzS,MAC9B,MAAM,IAAIR,EAAa,iBAAkB,iBAG3C,MAAMoL,WACJ3Q,EAAA+K,EAAQwJ,0CAAqB5D,eAC7BxU,KAAK4O,QAAQwJ,oBAAoB5D,mBAE7BxU,KAAK+c,cACT,CACE1S,SAAUiB,EAAOjB,SACjBC,MAAOgB,EAAOhB,MACdgQ,cAAehP,EAAOgP,cACtB0C,WAAY,qBACZC,KAAMZ,EAAWY,KACjB7B,aAAc9P,EAAO8P,cAEvB,CACE8B,QAAS5R,EAAOuI,MAChBW,gBAGL,CAYM9H,sBACL,MAAM2D,QAAcrQ,KAAKmd,uBAEzB,OAA4B,QAArBtZ,EAAAwM,aAAK,EAALA,EAAOM,oBAAc,IAAA9M,OAAA,EAAAA,EAAAuP,IAC7B,CASM1G,+BACL,MAAM2D,QAAcrQ,KAAKmd,uBAEzB,OAA4B,QAArBtZ,EAAAwM,aAAK,EAALA,EAAOM,oBAAc,IAAA9M,OAAA,EAAAA,EAAAqP,MAC7B,CAaMxG,wBACLkC,EAA2C,UAE3C,MAAMwO,EACJzF,GAA2B/I,IADvBgJ,QAAEA,EAAOyF,SAAEA,EAAQxT,SAAEA,GACUuT,EADGE,EAAlC1e,EAAAwe,EAAA,CAAA,UAAA,WAAA,aAGA5I,WACJ3Q,EAAAyZ,EAAWlF,0CAAqB5D,eAChCxU,KAAK4O,QAAQwJ,oBAAoB5D,aAE7B+I,QAAgCvd,KAAKkc,qBACzCoB,EAAWlF,qBAAuB,KAD9BvJ,IAAEA,GAAG0O,EAAKlL,EAAWzT,EAAA2e,EAArB,CAAuB,QAI7Bvd,KAAKuZ,mBAAmBnH,OAAMnT,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EACzByG,GACH,CAAAxI,aACI2K,GAAgB,CAAEA,kBAGxB,MAAMgJ,EAAkBH,EAAW,GAAGxO,KAAOwO,IAAaxO,EAEtD+I,QACIA,EAAQ4F,GAEdvZ,OAAOwS,SAAS7K,OAAO4R,EAE1B,CAQM9Q,6BACLmC,EAAc5K,OAAOwS,SAAS2F,MAE9B,MAAMqB,EAAuB5O,EAAI3C,MAAM,KAAK7D,MAAM,GAElD,GAAoC,IAAhCoV,EAAqBje,OACvB,MAAM,IAAIsE,MAAM,oDAGlB,MAAM8F,MAAEA,EAAKqT,KAAEA,EAAI3T,MAAEA,EAAKC,kBAAEA,GhBheS,CACvCmU,IAEIA,EAAYre,QAAQ,MAAQ,IAC9Bqe,EAAcA,EAAYC,UAAU,EAAGD,EAAYre,QAAQ,OAG7D,MAAMue,EAAe,IAAIrS,gBAAgBmS,GAEzC,MAAO,CACL9T,MAAOgU,EAAard,IAAI,SACxB0c,KAAMW,EAAard,IAAI,cAAWC,EAClC8I,MAAOsU,EAAard,IAAI,eAAYC,EACpC+I,kBAAmBqU,EAAard,IAAI,2BAAwBC,EAC7D,EgBkdmDqd,CAChDJ,EAAqBnR,KAAK,KAGtB+F,EAAcrS,KAAKuZ,mBAAmBhZ,MAE5C,IAAK8R,EACH,MAAM,IAAIjJ,EAAa,sBAAuB,iBAKhD,GAFApJ,KAAKuZ,mBAAmBvJ,SAEpB1G,EACF,MAAM,IAAIK,EACRL,EACAC,GAAqBD,EACrBM,EACAyI,EAAYxI,UAKhB,IACGwI,EAAYiI,eACZjI,EAAYzI,OAASyI,EAAYzI,QAAUA,EAE5C,MAAM,IAAIR,EAAa,iBAAkB,iBAG3C,MAAMoL,EAAenC,EAAYmC,aAC3B0I,EAAU7K,EAAYwB,MACtBuH,EAAe/I,EAAY+I,aAcjC,aAZMpb,KAAK+c,cAAa9d,OAAA2M,OAAA,CAEpBvB,SAAUgI,EAAYhI,SACtBC,MAAO+H,EAAY/H,MACnBgQ,cAAejI,EAAYiI,cAC3B0C,WAAY,qBACZC,KAAMA,GACF7B,EAAe,CAAEA,gBAAiB,CAAE,GAE1C,CAAE8B,UAAS1I,iBAGN,CACL3K,SAAUwI,EAAYxI,SAEzB,CA2BM6C,mBAAmBkC,GACxB,IAAK5O,KAAK6Y,cAActY,IAAIP,KAAKgZ,2BAA4B,CAC3D,IAAKhZ,KAAK6Y,cAActY,IAAI+W,IAC1B,OAGAtX,KAAK6Y,cAAcvG,KAAKtS,KAAKgZ,2BAA2B,EAAM,CAC5DzG,gBAAiBvS,KAAKkZ,uBACtB/G,aAAcnS,KAAK4O,QAAQuD,eAG7BnS,KAAK6Y,cAAc7I,OAAOsH,GAE7B,CAED,UACQtX,KAAK8d,iBAAiBlP,EAChB,CAAZ,MAAOjM,GAAK,CACf,CAwDM+J,uBACLkC,EAAmC,UAEnC,MAAMmP,EAGJ9e,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAAoS,UAAW,MACRpP,GAAO,CACVwJ,oBAAmBnZ,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,GACd5L,KAAK4O,QAAQwJ,qBACbxJ,EAAQwJ,qBAAmB,CAC9B9N,MAAO6E,EAAgBnP,KAAKsK,MAAkC,QAA3BzG,EAAA+K,EAAQwJ,2BAAmB,IAAAvU,OAAA,EAAAA,EAAEyG,WAI9DpI,OHhpBmB,EAC3B+b,EACA7d,KAEA,IAAI8d,EAA6BlH,GAAiB5W,GAQlD,OAPK8d,IACHA,EAAUD,IAAKzQ,SAAQ,YACdwJ,GAAiB5W,GACxB8d,EAAU,IAAI,IAEhBlH,GAAiB5W,GAAO8d,GAEnBA,CAAO,EGooBSC,EACnB,IAAMne,KAAKoe,kBAAkBL,IAC7B,GAAG/d,KAAK4O,QAAQxD,aAAa2S,EAAa3F,oBAAoB/N,aAAa0T,EAAa3F,oBAAoB9N,SAG9G,OAAOsE,EAAQyP,iBAAmBnc,EAASA,eAAAA,EAAQoc,YACpD,CAEO5R,wBACNkC,GAIA,MAAMoP,UAAEA,GAAkCpP,EAApB2P,EAAe3f,EAAKgQ,EAApC,CAAiC,cAIvC,GAAkB,QAAdoP,EAAqB,CACvB,MAAMlO,QAAc9P,KAAKwe,mBAAmB,CAC1ClU,MAAOiU,EAAgBnG,oBAAoB9N,MAC3CD,SAAUkU,EAAgBnG,oBAAoB/N,UAAY,UAC1De,SAAUpL,KAAK4O,QAAQxD,WAGzB,GAAI0E,EACF,OAAOA,CAEV,CAED,GAAkB,eAAdkO,EAAJ,CAIA,SHlqBwBtR,OAC1BuR,EACAQ,EAAqB,KAErB,IAAK,IAAIlf,EAAI,EAAGA,EAAIkf,EAAoBlf,IACtC,SAAU0e,IACR,OAAO,EAIX,OAAO,CAAK,EGypBFS,EACJ,IAAM7d,GAAK6E,YAAY2R,GAA6B,MACpD,IAsCF,MAAM,IAAIvN,EAnCV,IAKE,GAJA7F,OAAO8D,iBAAiB,WAAY/H,KAAKsY,wBAIvB,QAAd0F,EAAqB,CACvB,MAAMlO,QAAc9P,KAAKwe,mBAAmB,CAC1ClU,MAAOiU,EAAgBnG,oBAAoB9N,MAC3CD,SAAUkU,EAAgBnG,oBAAoB/N,UAAY,UAC1De,SAAUpL,KAAK4O,QAAQxD,WAGzB,GAAI0E,EACF,OAAOA,CAEV,CAED,MAAM6O,EAAa3e,KAAK4O,QAAQ0K,uBACtBtZ,KAAK4e,2BAA2BL,SAChCve,KAAK6e,oBAAoBN,IAE7BzN,SAAEA,EAAQwN,aAAEA,EAAYQ,gBAAEA,EAAepN,WAAEA,GAC/CiN,EAEF,OAAA1f,OAAA2M,OAAA3M,OAAA2M,OAAA,CACEkF,WACAwN,gBACIQ,EAAkB,CAAExU,MAAOwU,GAAoB,MAAK,CACxDpN,cAKH,CAHS,cACF7Q,GAAK+E,YAAYyR,IACvBpT,OAAOyD,oBAAoB,WAAY1H,KAAKsY,uBAC7C,CAzCF,CA6CF,CAcM5L,wBACLkC,EAAoC,GACpC6M,EAA6B,CAAA,SAE7B,MAAMsC,EAAY9e,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EACbgD,GAAO,CACVwJ,oBACKnZ,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAA5L,KAAK4O,QAAQwJ,qBACbxJ,EAAQwJ,qBAAmB,CAC9B9N,MAAO6E,EAAgBnP,KAAKsK,MAAkC,QAA3BzG,EAAA+K,EAAQwJ,2BAAmB,IAAAvU,OAAA,EAAAA,EAAEyG,WAIpEmR,EACKxc,OAAA2M,OAAA3M,OAAA2M,OAAA,GAAA/C,GACA4S,SAGCzb,KAAK+e,eAAehB,EAActC,GAUxC,aARoBzb,KAAKwZ,aAAajZ,IACpC,IAAImP,EAAS,CACXpF,MAAOyT,EAAa3F,oBAAoB9N,MACxCD,SAAU0T,EAAa3F,oBAAoB/N,UAAY,UACvDe,SAAUpL,KAAK4O,QAAQxD,aAIbkT,YACf,CAWM5R,wBAEL,cADmB1M,KAAKgf,SAEzB,CAUOC,gBAAgBrQ,GACG,OAArBA,EAAQxD,SACVwD,EAAQxD,SAAWwD,EAAQxD,UAAYpL,KAAK4O,QAAQxD,gBAE7CwD,EAAQxD,SAGjB,MAAMvH,EAAkC+K,EAAQsQ,cAAgB,CAAA,GAA1DC,UAAEA,KAAcC,EAAhBxgB,EAAAiF,EAAA,CAAA,cACAwb,EAAiBF,EAAY,aAAe,GAQlD,OAPYnf,KAAKga,KACf,cAAc7O,EAAiBlM,OAAA2M,OAAA,CAC7BR,SAAUwD,EAAQxD,UACfgU,OAIMC,CACd,CAeM3S,aAAakC,EAAyB,IAC3C,MAAM/K,EAAgC8T,GAA2B/I,IAA3DgJ,QAAEA,GAAO/T,EAAKub,EAAdxgB,EAAAiF,EAAA,CAAA,YAEmB,OAArB+K,EAAQxD,eACJpL,KAAKwZ,aAAaxV,cAElBhE,KAAKwZ,aAAaxV,MAAM4K,EAAQxD,UAAYpL,KAAK4O,QAAQxD,UAGjEpL,KAAK6Y,cAAc7I,OAAOhQ,KAAK+Y,kBAAmB,CAChD5G,aAAcnS,KAAK4O,QAAQuD,eAE7BnS,KAAK6Y,cAAc7I,OAAOhQ,KAAKgZ,0BAA2B,CACxD7G,aAAcnS,KAAK4O,QAAQuD,eAE7BnS,KAAKkY,UAAUlI,OAAOP,GAEtB,MAAMZ,EAAM7O,KAAKif,gBAAgBG,GAE7BxH,QACIA,EAAQ/I,IACO,IAAZ+I,GACT3T,OAAOwS,SAAS7K,OAAOiD,EAE1B,CAEOnC,0BACNkC,GAIA,MAAMtD,EACDrM,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAAgD,EAAQwJ,sBACXkH,OAAQ,SAGJC,EAAUvf,KAAK6Y,cAActY,IAAYP,KAAK+Y,mBAEhDwG,IAAYjU,EAAOkJ,eACrBlJ,EAAOkJ,aAAe+K,GAGxB,MAAM1Q,IACJA,EACAjF,MAAO4V,EACP3L,MAAOqJ,EAAO5C,cACdA,EAAac,aACbA,EAAY9Q,MACZA,EAAKD,SACLA,SACQrK,KAAKkc,qBACb5Q,EACA,CAAE+P,cAAe,eACjBpX,OAAOwS,SAAS0F,QAGlB,IAIE,GAAKlY,OAAewb,oBAClB,MAAM,IAAIrW,EACR,iBACA,qIAIJ,MAAMsW,EACJ9Q,EAAQ9F,kBAAoB9I,KAAK4O,QAAQkO,0BAErCT,OhBx2Ba,EACvBsD,EACAC,EACA9W,EH5BkD,KG8B3C,IAAIhI,SAA8B,CAAC+e,EAAKC,KAC7C,MAAMC,EAAS9b,OAAOmS,SAAS4J,cAAc,UAE7CD,EAAOE,aAAa,QAAS,KAC7BF,EAAOE,aAAa,SAAU,KAC9BF,EAAOG,MAAMC,QAAU,OAEvB,MAAMC,EAAe,KACfnc,OAAOmS,SAAS7T,KAAK8d,SAASN,KAChC9b,OAAOmS,SAAS7T,KAAK+d,YAAYP,GACjC9b,OAAOyD,oBAAoB,UAAW6Y,GAAoB,GAC3D,EAGH,IAAIA,EAEJ,MAAMC,EAAsBpf,YAAW,KACrC0e,EAAI,IAAIhW,GACRsW,GAAc,GACM,IAAnBtX,GAEHyX,EAAqB,SAAUzhB,GAC7B,GAAIA,EAAEqd,QAAUyD,EAAa,OAC7B,IAAK9gB,EAAEsP,MAAwB,2BAAhBtP,EAAEsP,KAAKuO,KAAmC,OAEzD,MAAM8D,EAAc3hB,EAAE4hB,OAElBD,GACDA,EAAoBpS,QAGvBvP,EAAEsP,KAAKvB,SAASvD,MACZwW,EAAI1W,EAAawT,YAAY9d,EAAEsP,KAAKvB,WACpCgT,EAAI/gB,EAAEsP,KAAKvB,UAEfjF,aAAa4Y,GACbvc,OAAOyD,oBAAoB,UAAW6Y,GAAoB,GAI1Dnf,WAAWgf,EAAcO,IAC3B,EAEA1c,OAAO8D,iBAAiB,UAAWwY,GAAoB,GACvDtc,OAAOmS,SAAS7T,KAAKqe,YAAYb,GACjCA,EAAOE,aAAa,MAAON,EAAa,IgBszBbkB,CAAUhS,EAAK7O,KAAKqZ,UAAWqG,GAExD,GAAIF,IAAYnD,EAAWzS,MACzB,MAAM,IAAIR,EAAa,iBAAkB,iBAG3C,MAAM0X,QAAoB9gB,KAAK+c,cAExB9d,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAAgD,EAAQwJ,sBACXkC,gBACA2C,KAAMZ,EAAWY,KACjBD,WAAY,qBACZ5B,eACAlV,QAAS0I,EAAQwJ,oBAAoBlS,SAAWlG,KAAK2Y,gBAEvD,CACEuE,UACA1I,aAAclJ,EAAOkJ,eAIzB,OAAAvV,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EACKkV,GAAW,CACdxW,MAAOA,EACPwU,gBAAiBgC,EAAYxW,MAC7BD,SAAUA,GASb,CAPC,MAAOvL,GAMP,KALgB,mBAAZA,EAAEwK,OACJtJ,KAAK+gB,OAAO,CACVnJ,SAAS,IAGP9Y,CACP,CACF,CAEO4N,iCACNkC,GAIA,MAAMyB,QAAcrQ,KAAKwZ,aAAajZ,IACpC,IAAImP,EAAS,CACXpF,MAAOsE,EAAQwJ,oBAAoB9N,MACnCD,SAAUuE,EAAQwJ,oBAAoB/N,UAAY,UAClDe,SAAUpL,KAAK4O,QAAQxD,YAQ3B,KAAMiF,GAAUA,EAAMkB,eAAmBvR,KAAK0N,QAAQ,CACpD,GAAI1N,KAAK4O,QAAQyJ,yBACf,aAAarY,KAAK6e,oBAAoBjQ,GAGxC,MAAM,IAAIxE,EACRwE,EAAQwJ,oBAAoB/N,UAAY,UACxCuE,EAAQwJ,oBAAoB9N,MAE/B,CAED,MAAM8Q,EACJxM,EAAQwJ,oBAAoBgD,cAC5Bpb,KAAK4O,QAAQwJ,oBAAoBgD,cACjCnX,OAAOwS,SAAS0F,OAEZjW,EACgC,iBAA7B0I,EAAQ9F,iBACgB,IAA3B8F,EAAQ9F,iBACR,KAEN,IACE,MAAMgY,QAAoB9gB,KAAK+c,cAAa9d,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EACvCgD,EAAQwJ,qBAAmB,CAC9B4E,WAAY,gBACZzL,cAAelB,GAASA,EAAMkB,cAC9B6J,iBACIlV,GAAW,CAAEA,aAGnB,OACKjH,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAAkV,GACH,CAAAxW,MAAOsE,EAAQwJ,oBAAoB9N,MACnCwU,gBAAiBgC,EAAYxW,MAC7BD,SAAUuE,EAAQwJ,oBAAoB/N,UAAY,WAiBrD,CAfC,MAAOvL,GACP,IAGGA,EAAE8O,QAAQvO,QnBj8BgC,0BmBi8BgB,GAGxDP,EAAE8O,SACD9O,EAAE8O,QAAQvO,QnBh8B6B,0BmBg8BmB,IAC9DW,KAAK4O,QAAQyJ,yBAEb,aAAarY,KAAK6e,oBAAoBjQ,GAGxC,MAAM9P,CACP,CACF,CAEO4N,wBACNoD,GAEA,MAAMgB,SAAEA,EAAQH,aAAEA,GAAyCb,EAAxBkR,EAAwBpiB,EAAAkR,EAArD,CAAA,WAAA,iBAEN9P,KAAKkY,UAAUzX,IAAIgP,EAA2B,CAC5CqB,WACAH,uBAGI3Q,KAAKwZ,aAAayH,WACtBjhB,KAAK4O,QAAQxD,SACb0E,EAAMgB,SACNhB,EAAMa,oBAGF3Q,KAAKwZ,aAAa/Y,IAAIugB,EAC7B,CAEOtU,6BACN,MAAMrC,EAAWrK,KAAK4O,QAAQwJ,oBAAoB/N,UAAY,UAExDgG,QAAcrQ,KAAKwZ,aAAa0H,WACpC,IAAIxR,EAAS,CACXtE,SAAUpL,KAAK4O,QAAQxD,SACvBf,WACAC,MAAOtK,KAAKsK,SAIV6W,EAAenhB,KAAKkY,UAAU3X,IAClCkP,GAKF,OAAIY,GAASA,EAAMS,YAAaqQ,aAAA,EAAAA,EAAcrQ,UACrCqQ,GAGTnhB,KAAKkY,UAAUzX,IAAIgP,EAA2BY,GACvCA,EACR,CAEO3D,0BAAyBpC,MAC/BA,EAAKD,SACLA,EAAQe,SACRA,IAMA,MAAM0E,QAAc9P,KAAKwZ,aAAajZ,IACpC,IAAImP,EAAS,CACXpF,QACAD,WACAe,aAEF,IAGF,GAAI0E,GAASA,EAAMwO,aAAc,CAC/B,MAAMA,aAAEA,EAAYQ,gBAAEA,EAAepN,WAAEA,GAAe5B,EAChDO,QAAcrQ,KAAKmd,uBACzB,OACE9M,GACEpR,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAAkF,SAAUT,EAAMS,SAChBwN,gBACIQ,EAAkB,CAAExU,MAAOwU,GAAoB,MAAK,CACxDpN,cAGL,CACF,CAcOhF,oBACNkC,EAIAwS,GAEA,MAAMlE,QAAEA,EAAO1I,aAAEA,GAAiB4M,GAAwB,CAAA,EACpDzC,QAAmBlQ,iBAErBC,QAAS1O,KAAKqZ,UACdhO,UAAWrL,KAAK4O,QAAQxD,SACxBuD,YAAa3O,KAAK4O,QAAQD,YAC1BhB,YAAa3N,KAAK4O,QAAQjB,YAC1BzH,QAASlG,KAAK2Y,eACX/J,GAEL5O,KAAK0N,QAGDiD,QAAqB3Q,KAAKqhB,eAC9B1C,EAAW7N,SACXoM,EACA1I,GAmBF,aAhBMxU,KAAKshB,kBAAiBriB,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EACvB+S,GACH,CAAAhO,eACArG,MAAOsE,EAAQtE,MACfD,SAAUuE,EAAQvE,UAAY,YAC1BsU,EAAWrU,MAAQ,CAAEwU,gBAAiBH,EAAWrU,OAAU,MAC/D,CAAAe,UAAWrL,KAAK4O,QAAQxD,YAG1BpL,KAAK6Y,cAAcvG,KAAKtS,KAAKgZ,2BAA2B,EAAM,CAC5DzG,gBAAiBvS,KAAKkZ,uBACtB/G,aAAcnS,KAAK4O,QAAQuD,eAG7BnS,KAAKoa,gBAAgB5F,GAAgB7D,EAAauC,OAAOyB,QAE7C1V,OAAA2M,OAAA3M,OAAA2M,OAAA,CAAA,EAAA+S,GAAY,CAAAhO,gBACzB,CAoDDjE,oBACEkC,GAEA,OAAO5O,KAAK+c,cAAc,CACxBC,WAAY,kDACZuE,cAAe3S,EAAQ2S,cACvBC,mBAAoB5S,EAAQ4S,mBAC5BlX,MAAO6E,EAAgBP,EAAQtE,MAAOtK,KAAKsK,OAC3CD,SAAUrK,KAAK4O,QAAQwJ,oBAAoB/N,UAE9C,qQCpqCIqC,eAAiCkC,GACtC,MAAM6S,EAAQ,IAAIzJ,GAAYpJ,GAE9B,aADM6S,EAAMC,eACLD,CACT"}