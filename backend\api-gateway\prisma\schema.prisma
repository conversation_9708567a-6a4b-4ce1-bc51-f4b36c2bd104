// Schéma Prisma pour Nouri - Coach Financier IA
// Base de données PostgreSQL

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ================================
// MODÈLES UTILISATEURS
// ================================

model User {
  id                String   @id @default(cuid())
  auth0Id           String   @unique @map("auth0_id")
  email             String   @unique
  emailVerified     Boolean  @default(false) @map("email_verified")
  firstName         String?  @map("first_name")
  lastName          String?  @map("last_name")
  phoneNumber       String?  @map("phone_number")
  dateOfBirth       DateTime? @map("date_of_birth")
  profilePicture    String?  @map("profile_picture")
  
  // Préférences
  language          String   @default("fr")
  currency          String   @default("TND")
  timezone          String   @default("Africa/Tunis")
  
  // Paramètres de notification
  emailNotifications    Boolean @default(true) @map("email_notifications")
  pushNotifications     Boolean @default(true) @map("push_notifications")
  smsNotifications      Boolean @default(false) @map("sms_notifications")
  
  // Métadonnées
  onboardingCompleted   Boolean @default(false) @map("onboarding_completed")
  kycStatus            String  @default("pending") // pending, verified, rejected
  riskProfile          String? @map("risk_profile") // conservative, moderate, aggressive
  
  // Timestamps
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")
  lastLoginAt      DateTime? @map("last_login_at")
  
  // Relations
  accounts         Account[]
  budgets          Budget[]
  savingsGoals     SavingsGoal[] @relation("UserSavingsGoals")
  transactions     Transaction[]
  notifications    Notification[]
  chatSessions     ChatSession[]
  educationProgress EducationProgress[]
  userSessions     UserSession[]
  
  @@map("users")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  sessionToken String   @unique @map("session_token")
  deviceInfo   Json?    @map("device_info")
  ipAddress    String?  @map("ip_address")
  userAgent    String?  @map("user_agent")
  expiresAt    DateTime @map("expires_at")
  createdAt    DateTime @default(now()) @map("created_at")
  
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_sessions")
}

// ================================
// MODÈLES BANCAIRES
// ================================

model Account {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  bankId          String   @map("bank_id")
  accountNumber   String   @map("account_number")
  accountType     String   @map("account_type") // checking, savings, credit
  accountName     String   @map("account_name")
  balance         Decimal  @db.Decimal(15, 3)
  currency        String   @default("TND")
  
  // Métadonnées bancaires
  iban            String?
  swift           String?
  bankName        String   @map("bank_name")
  branchCode      String?  @map("branch_code")
  
  // Statut
  isActive        Boolean  @default(true) @map("is_active")
  isLinked        Boolean  @default(true) @map("is_linked")
  lastSyncAt      DateTime? @map("last_sync_at")
  
  // Timestamps
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")
  
  // Relations
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions    Transaction[]
  budgets         Budget[]
  
  @@unique([userId, accountNumber])
  @@map("accounts")
}

model Transaction {
  id                String   @id @default(cuid())
  userId            String   @map("user_id")
  accountId         String   @map("account_id")
  
  // Données de transaction
  amount            Decimal  @db.Decimal(15, 3)
  currency          String   @default("TND")
  description       String
  transactionDate   DateTime @map("transaction_date")
  valueDate         DateTime? @map("value_date")
  
  // Classification
  category          String?
  subcategory       String?
  merchantName      String?  @map("merchant_name")
  merchantCategory  String?  @map("merchant_category")
  
  // Métadonnées
  transactionType   String   @map("transaction_type") // debit, credit
  paymentMethod     String?  @map("payment_method") // card, transfer, cash, etc.
  reference         String?
  bankTransactionId String?  @map("bank_transaction_id")
  
  // IA et analyse
  isRecurring       Boolean  @default(false) @map("is_recurring")
  recurringGroupId  String?  @map("recurring_group_id")
  confidenceScore   Float?   @map("confidence_score")
  aiTags            String[] @map("ai_tags")
  
  // Timestamps
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")
  
  // Relations
  user              User @relation(fields: [userId], references: [id], onDelete: Cascade)
  account           Account @relation(fields: [accountId], references: [id], onDelete: Cascade)
  budgetEntries     BudgetEntry[]
  
  @@map("transactions")
}

// ================================
// MODÈLES BUDGETS ET OBJECTIFS
// ================================

model Budget {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  accountId   String?  @map("account_id")
  name        String
  description String?
  
  // Période
  startDate   DateTime @map("start_date")
  endDate     DateTime @map("end_date")
  period      String   // monthly, weekly, yearly
  
  // Montants
  totalAmount Decimal  @db.Decimal(15, 3) @map("total_amount")
  spentAmount Decimal  @default(0) @db.Decimal(15, 3) @map("spent_amount")
  currency    String   @default("TND")
  
  // Statut
  isActive    Boolean  @default(true) @map("is_active")
  isAutomatic Boolean  @default(false) @map("is_automatic")
  
  // Timestamps
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  account     Account? @relation(fields: [accountId], references: [id], onDelete: SetNull)
  categories  BudgetCategory[]
  entries     BudgetEntry[]
  
  @@map("budgets")
}

model BudgetCategory {
  id          String   @id @default(cuid())
  budgetId    String   @map("budget_id")
  category    String
  subcategory String?
  
  // Montants
  allocatedAmount Decimal @db.Decimal(15, 3) @map("allocated_amount")
  spentAmount     Decimal @default(0) @db.Decimal(15, 3) @map("spent_amount")
  
  // Timestamps
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  
  // Relations
  budget      Budget @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  entries     BudgetEntry[]
  
  @@unique([budgetId, category, subcategory])
  @@map("budget_categories")
}

model BudgetEntry {
  id               String   @id @default(cuid())
  budgetId         String   @map("budget_id")
  budgetCategoryId String   @map("budget_category_id")
  transactionId    String   @map("transaction_id")
  amount           Decimal  @db.Decimal(15, 3)
  
  // Timestamps
  createdAt        DateTime @default(now()) @map("created_at")
  
  // Relations
  budget           Budget @relation(fields: [budgetId], references: [id], onDelete: Cascade)
  budgetCategory   BudgetCategory @relation(fields: [budgetCategoryId], references: [id], onDelete: Cascade)
  transaction      Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  
  @@unique([transactionId, budgetCategoryId])
  @@map("budget_entries")
}

model SavingsGoal {
  id            String   @id @default(cuid())
  userId        String   @map("user_id")
  name          String
  description   String?
  
  // Objectif
  targetAmount  Decimal  @db.Decimal(15, 3) @map("target_amount")
  currentAmount Decimal  @default(0) @db.Decimal(15, 3) @map("current_amount")
  currency      String   @default("TND")
  
  // Dates
  targetDate    DateTime? @map("target_date")
  startDate     DateTime @default(now()) @map("start_date")
  
  // Configuration
  monthlyTarget Decimal? @db.Decimal(15, 3) @map("monthly_target")
  isAutomatic   Boolean  @default(false) @map("is_automatic")
  priority      Int      @default(1)
  
  // Métadonnées
  category      String?  // vacation, emergency, house, etc.
  imageUrl      String?  @map("image_url")
  isCompleted   Boolean  @default(false) @map("is_completed")
  completedAt   DateTime? @map("completed_at")
  
  // Timestamps
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")
  
  // Relations
  user          User @relation("UserSavingsGoals", fields: [userId], references: [id], onDelete: Cascade)
  contributions SavingsContribution[]
  
  @@map("savings_goals")
}

model SavingsContribution {
  id            String   @id @default(cuid())
  savingsGoalId String   @map("savings_goal_id")
  amount        Decimal  @db.Decimal(15, 3)
  description   String?
  isAutomatic   Boolean  @default(false) @map("is_automatic")

  // Timestamps
  createdAt     DateTime @default(now()) @map("created_at")

  // Relations
  savingsGoal   SavingsGoal @relation(fields: [savingsGoalId], references: [id], onDelete: Cascade)

  @@map("savings_contributions")
}

// ================================
// MODÈLES NOTIFICATIONS
// ================================

model Notification {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  title       String
  message     String
  type        String   // info, warning, success, error, budget_alert, goal_achieved, etc.
  category    String?  // budget, savings, transaction, education, etc.

  // Contenu
  data        Json?    // Données additionnelles
  actionUrl   String?  @map("action_url")
  imageUrl    String?  @map("image_url")

  // Statut
  isRead      Boolean  @default(false) @map("is_read")
  isArchived  Boolean  @default(false) @map("is_archived")
  priority    String   @default("normal") // low, normal, high, urgent

  // Canaux de notification
  sentEmail   Boolean  @default(false) @map("sent_email")
  sentPush    Boolean  @default(false) @map("sent_push")
  sentSms     Boolean  @default(false) @map("sent_sms")

  // Timestamps
  readAt      DateTime? @map("read_at")
  archivedAt  DateTime? @map("archived_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// ================================
// MODÈLES CHATBOT ET IA
// ================================

model ChatSession {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  title       String?
  language    String   @default("fr")

  // Métadonnées
  context     Json?    // Contexte de la conversation
  metadata    Json?    // Métadonnées additionnelles

  // Statut
  isActive    Boolean  @default(true) @map("is_active")
  endedAt     DateTime? @map("ended_at")

  // Timestamps
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user        User @relation(fields: [userId], references: [id], onDelete: Cascade)
  messages    ChatMessage[]

  @@map("chat_sessions")
}

model ChatMessage {
  id            String   @id @default(cuid())
  sessionId     String   @map("session_id")
  role          String   // user, assistant, system
  content       String

  // Métadonnées IA
  tokens        Int?     // Nombre de tokens utilisés
  model         String?  // Modèle IA utilisé
  confidence    Float?   // Score de confiance
  intent        String?  // Intention détectée
  entities      Json?    // Entités extraites

  // Feedback
  rating        Int?     // 1-5 étoiles
  feedback      String?  // Commentaire utilisateur

  // Timestamps
  createdAt     DateTime @default(now()) @map("created_at")

  // Relations
  session       ChatSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("chat_messages")
}

// ================================
// MODÈLES ÉDUCATION
// ================================

model EducationModule {
  id            String   @id @default(cuid())
  title         String
  description   String
  content       Json     // Contenu structuré du module

  // Configuration
  category      String   // budgeting, investing, debt, etc.
  difficulty    String   @default("beginner") // beginner, intermediate, advanced
  estimatedTime Int      @map("estimated_time") // en minutes
  language      String   @default("fr")

  // Gamification
  points        Int      @default(0)
  badges        String[] // Badges à débloquer

  // Ordre et prérequis
  order         Int      @default(0)
  prerequisites String[] // IDs des modules prérequis

  // Statut
  isPublished   Boolean  @default(false) @map("is_published")
  isActive      Boolean  @default(true) @map("is_active")

  // Timestamps
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  progress      EducationProgress[]
  quizzes       Quiz[]

  @@map("education_modules")
}

model EducationProgress {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  moduleId        String   @map("module_id")

  // Progression
  status          String   @default("not_started") // not_started, in_progress, completed
  progressPercent Int      @default(0) @map("progress_percent")
  currentStep     Int      @default(0) @map("current_step")
  totalSteps      Int      @default(1) @map("total_steps")

  // Scores
  score           Int?     // Score final
  timeSpent       Int      @default(0) @map("time_spent") // en secondes
  attempts        Int      @default(0)

  // Timestamps
  startedAt       DateTime? @map("started_at")
  completedAt     DateTime? @map("completed_at")
  lastAccessedAt  DateTime? @map("last_accessed_at")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  // Relations
  user            User @relation(fields: [userId], references: [id], onDelete: Cascade)
  module          EducationModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)

  @@unique([userId, moduleId])
  @@map("education_progress")
}

model Quiz {
  id          String   @id @default(cuid())
  moduleId    String   @map("module_id")
  title       String
  description String?
  questions   Json     // Questions structurées

  // Configuration
  passingScore Int     @default(70) @map("passing_score")
  timeLimit   Int?    @map("time_limit") // en minutes
  maxAttempts Int     @default(3) @map("max_attempts")

  // Timestamps
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  module      EducationModule @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  attempts    QuizAttempt[]

  @@map("quizzes")
}

model QuizAttempt {
  id          String   @id @default(cuid())
  quizId      String   @map("quiz_id")
  userId      String   @map("user_id")

  // Résultats
  score       Int
  answers     Json     // Réponses données
  timeSpent   Int      @map("time_spent") // en secondes
  passed      Boolean

  // Timestamps
  startedAt   DateTime @map("started_at")
  completedAt DateTime @map("completed_at")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  quiz        Quiz @relation(fields: [quizId], references: [id], onDelete: Cascade)

  @@map("quiz_attempts")
}

// ================================
// MODÈLES SYSTÈME
// ================================

model PasswordResetToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expiresAt DateTime @map("expires_at")
  used      Boolean  @default(false)
  createdAt DateTime @default(now()) @map("created_at")

  @@map("password_reset_tokens")
}

model SystemConfig {
  id        String   @id @default(cuid())
  key       String   @unique
  value     Json
  category  String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("system_config")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?  @map("user_id")
  action    String
  resource  String
  details   Json?
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  createdAt DateTime @default(now()) @map("created_at")

  @@map("audit_logs")
}
