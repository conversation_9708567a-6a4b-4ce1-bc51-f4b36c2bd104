/**
 * Fonctions utilitaires pour Nouri
 */

// Formatage des devises
export const formatCurrency = (amount: number, currency: string = 'TND'): string => {
  const symbols: Record<string, string> = {
    TND: 'د.ت',
    EUR: '€',
    USD: '$'
  }
  
  return `${amount.toLocaleString()} ${symbols[currency] || currency}`
}

// Formatage des dates
export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('fr-TN')
}

// Validation email
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export default {
  formatCurrency,
  formatDate,
  isValidEmail
}
