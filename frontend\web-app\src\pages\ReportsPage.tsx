/**
 * Complete Reports Page
 * Financial reports and analytics
 */

import React, { useState, useContext } from 'react'
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  IconButton,
  AppBar,
  Toolbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,

} from '@mui/material'
import {
  Download as DownloadIcon,
  Home as HomeIcon,
  Logout as LogoutIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { AuthContext } from '@/App'
import { ROUTES } from '@/config'

// Mock report data
const monthlyReports = [
  { month: 'Janvier 2024', income: 2250, expenses: 1850, savings: 400, budgetCompliance: 85 },
  { month: 'Décembre 2023', income: 2100, expenses: 1950, savings: 150, budgetCompliance: 78 },
  { month: 'Novembre 2023', income: 2300, expenses: 1750, savings: 550, budgetCompliance: 92 },
  { month: 'Octobre 2023', income: 2200, expenses: 1800, savings: 400, budgetCompliance: 88 },
  { month: 'Septembre 2023', income: 2150, expenses: 1900, savings: 250, budgetCompliance: 82 }
]

const categoryBreakdown = [
  { category: 'Alimentation', amount: 450, percentage: 24, trend: 'up' },
  { category: 'Transport', amount: 320, percentage: 17, trend: 'down' },
  { category: 'Loisirs', amount: 280, percentage: 15, trend: 'up' },
  { category: 'Utilities', amount: 250, percentage: 13, trend: 'stable' },
  { category: 'Santé', amount: 180, percentage: 10, trend: 'down' },
  { category: 'Vêtements', amount: 150, percentage: 8, trend: 'up' },
  { category: 'Autres', amount: 220, percentage: 13, trend: 'stable' }
]

export const ReportsPage: React.FC = () => {
  const navigate = useNavigate()
  const { logout } = useContext(AuthContext)
  const [selectedPeriod, setSelectedPeriod] = useState('monthly')
  const [selectedYear, setSelectedYear] = useState('2024')

  const handleLogout = () => {
    logout()
    navigate(ROUTES.HOME)
  }

  const handleExportReport = () => {
    // Mock export functionality
    alert('Rapport exporté avec succès!')
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUpIcon color="error" fontSize="small" />
      case 'down': return <TrendingDownIcon color="success" fontSize="small" />
      default: return <span>→</span>
    }
  }



  const currentMonth = monthlyReports[0]
  const previousMonth = monthlyReports[1]
  const incomeChange = ((currentMonth.income - previousMonth.income) / previousMonth.income * 100)
  const expenseChange = ((currentMonth.expenses - previousMonth.expenses) / previousMonth.expenses * 100)

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* Navigation Bar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
            🇹🇳 Nouri - Rapports Financiers
          </Typography>
          <IconButton color="inherit" onClick={handleExportReport}>
            <DownloadIcon />
          </IconButton>
          <IconButton color="inherit" onClick={() => navigate(ROUTES.DASHBOARD)}>
            <HomeIcon />
          </IconButton>
          <IconButton color="inherit" onClick={handleLogout}>
            <LogoutIcon />
          </IconButton>
        </Toolbar>
      </AppBar>

      <Container maxWidth="xl" sx={{ py: 4 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                Rapports Financiers
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Analysez vos performances financières et tendances
              </Typography>
            </Box>
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={handleExportReport}
            >
              Exporter PDF
            </Button>
          </Box>
        </Box>

        {/* Filters */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <FormControl sx={{ minWidth: 150 }}>
                <InputLabel>Période</InputLabel>
                <Select
                  value={selectedPeriod}
                  label="Période"
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                >
                  <MenuItem value="monthly">Mensuel</MenuItem>
                  <MenuItem value="quarterly">Trimestriel</MenuItem>
                  <MenuItem value="yearly">Annuel</MenuItem>
                </Select>
              </FormControl>

              <FormControl sx={{ minWidth: 120 }}>
                <InputLabel>Année</InputLabel>
                <Select
                  value={selectedYear}
                  label="Année"
                  onChange={(e) => setSelectedYear(e.target.value)}
                >
                  <MenuItem value="2024">2024</MenuItem>
                  <MenuItem value="2023">2023</MenuItem>
                  <MenuItem value="2022">2022</MenuItem>
                </Select>
              </FormControl>
            </Box>
          </CardContent>
        </Card>

        {/* Summary Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" fontWeight="bold">
                  {currentMonth.income.toLocaleString()} TND
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Revenus ce mois
                </Typography>
                <Chip
                  label={`${incomeChange > 0 ? '+' : ''}${incomeChange.toFixed(1)}%`}
                  size="small"
                  color={incomeChange > 0 ? 'success' : 'error'}
                />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="error.main" fontWeight="bold">
                  {currentMonth.expenses.toLocaleString()} TND
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Dépenses ce mois
                </Typography>
                <Chip
                  label={`${expenseChange > 0 ? '+' : ''}${expenseChange}%`}
                  size="small"
                  color={parseFloat(expenseChange) > 0 ? 'error' : 'success'}
                />
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary.main" fontWeight="bold">
                  {currentMonth.savings.toLocaleString()} TND
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Épargne ce mois
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="info.main" fontWeight="bold">
                  {currentMonth.budgetCompliance}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Respect du budget
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Grid container spacing={3}>
          {/* Monthly Performance */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AssessmentIcon />
                  Performance Mensuelle
                </Typography>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Mois</TableCell>
                        <TableCell align="right">Revenus</TableCell>
                        <TableCell align="right">Dépenses</TableCell>
                        <TableCell align="right">Épargne</TableCell>
                        <TableCell align="right">Budget</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {monthlyReports.map((report, index) => (
                        <TableRow key={index} hover>
                          <TableCell>{report.month}</TableCell>
                          <TableCell align="right" sx={{ color: 'success.main', fontWeight: 'bold' }}>
                            {report.income.toLocaleString()} TND
                          </TableCell>
                          <TableCell align="right" sx={{ color: 'error.main', fontWeight: 'bold' }}>
                            {report.expenses.toLocaleString()} TND
                          </TableCell>
                          <TableCell align="right" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
                            {report.savings.toLocaleString()} TND
                          </TableCell>
                          <TableCell align="right">
                            <Chip
                              label={`${report.budgetCompliance}%`}
                              size="small"
                              color={report.budgetCompliance >= 85 ? 'success' : report.budgetCompliance >= 70 ? 'warning' : 'error'}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Category Breakdown */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Répartition par Catégorie
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {categoryBreakdown.map((category, index) => (
                    <Box key={index}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" fontWeight="medium">
                            {category.category}
                          </Typography>
                          {getTrendIcon(category.trend)}
                        </Box>
                        <Typography variant="body2" fontWeight="bold">
                          {category.amount} TND
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Box sx={{
                          width: '70%',
                          height: 6,
                          bgcolor: 'grey.200',
                          borderRadius: 3,
                          overflow: 'hidden'
                        }}>
                          <Box sx={{
                            width: `${category.percentage}%`,
                            height: '100%',
                            bgcolor: 'primary.main'
                          }} />
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          {category.percentage}%
                        </Typography>
                      </Box>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Insights */}
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              💡 Insights et Recommandations
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'success.50', borderRadius: 2, border: '1px solid', borderColor: 'success.200' }}>
                  <Typography variant="body2" color="success.dark" fontWeight="bold">
                    ✅ Point positif
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Vos revenus ont augmenté de {incomeChange}% ce mois-ci. Excellente performance !
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'warning.50', borderRadius: 2, border: '1px solid', borderColor: 'warning.200' }}>
                  <Typography variant="body2" color="warning.dark" fontWeight="bold">
                    ⚠️ Attention
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Les dépenses en loisirs ont augmenté de 15%. Surveillez ce budget.
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={4}>
                <Box sx={{ p: 2, bgcolor: 'info.50', borderRadius: 2, border: '1px solid', borderColor: 'info.200' }}>
                  <Typography variant="body2" color="info.dark" fontWeight="bold">
                    💡 Conseil
                  </Typography>
                  <Typography variant="body2" sx={{ mt: 1 }}>
                    Augmentez votre épargne de 100 TND pour atteindre vos objectifs plus rapidement.
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Container>
    </Box>
  )
}

export default ReportsPage
