/**
 * Routes d'authentification pour Nouri
 * Gestion de l'inscription, connexion, et gestion des tokens
 */

import { Router, Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { config } from '../config/config';
import { logger, logSecurityEvent, logBusinessEvent } from '../utils/logger';
import { getPrismaClient } from '../config/database';
import { cache } from '../config/redis';
import { authMiddleware, revokeToken } from '../middleware/auth';

const router = Router();
const prisma = getPrismaClient();

/**
 * POST /api/auth/register
 * Inscription d'un nouvel utilisateur
 */
router.post('/register', [
  body('email').isEmail().normalizeEmail().withMessage('Email invalide'),
  body('password').isLength({ min: 8 }).withMessage('Le mot de passe doit contenir au moins 8 caractères'),
  body('firstName').trim().isLength({ min: 2 }).withMessage('Le prénom est requis'),
  body('lastName').trim().isLength({ min: 2 }).withMessage('Le nom est requis'),
  body('phoneNumber').optional().isMobilePhone('any').withMessage('Numéro de téléphone invalide'),
  body('language').optional().isIn(['ar', 'fr', 'en']).withMessage('Langue non supportée'),
], async (req: Request, res: Response) => {
  try {
    // Validation des données
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array(),
        code: 'VALIDATION_ERROR'
      });
    }

    const { email, password, firstName, lastName, phoneNumber, language = 'fr' } = req.body;

    // Vérifier si l'utilisateur existe déjà
    const existingUser = await prisma.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      logSecurityEvent('registration_attempt_existing_email', { email }, req);
      return res.status(409).json({
        error: 'Un compte existe déjà avec cette adresse email',
        code: 'EMAIL_ALREADY_EXISTS'
      });
    }

    // Hacher le mot de passe
    const hashedPassword = await bcrypt.hash(password, config.security.hashSaltRounds);

    // Créer l'utilisateur
    const user = await prisma.user.create({
      data: {
        email,
        firstName,
        lastName,
        phoneNumber,
        language,
        auth0Id: `local_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // Temporaire pour les comptes locaux
      }
    });

    // Générer un token JWT
    const token = jwt.sign(
      { 
        userId: user.id,
        email: user.email,
        sub: user.auth0Id
      },
      config.auth.jwtSecret,
      { expiresIn: config.auth.jwtExpiresIn }
    );

    // Log de l'événement business
    logBusinessEvent('user_registered', user.id, {
      email: user.email,
      registrationMethod: 'local',
      language: user.language
    });

    // Réponse (sans le mot de passe)
    res.status(201).json({
      message: 'Compte créé avec succès',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        language: user.language,
        onboardingCompleted: user.onboardingCompleted
      },
      token,
      expiresIn: config.auth.jwtExpiresIn
    });

  } catch (error) {
    logger.error('Erreur lors de l\'inscription:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * POST /api/auth/login
 * Connexion utilisateur
 */
router.post('/login', [
  body('email').isEmail().normalizeEmail().withMessage('Email invalide'),
  body('password').notEmpty().withMessage('Mot de passe requis'),
], async (req: Request, res: Response) => {
  try {
    // Validation des données
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Données invalides',
        details: errors.array(),
        code: 'VALIDATION_ERROR'
      });
    }

    const { email, password } = req.body;

    // Vérifier les tentatives de connexion (rate limiting par IP)
    const loginAttemptsKey = `login_attempts:${req.ip}`;
    const attempts = await cache.get<number>(loginAttemptsKey) || 0;
    
    if (attempts >= 5) {
      logSecurityEvent('too_many_login_attempts', { email, attempts }, req);
      return res.status(429).json({
        error: 'Trop de tentatives de connexion. Réessayez dans 15 minutes.',
        code: 'TOO_MANY_ATTEMPTS'
      });
    }

    // Trouver l'utilisateur
    const user = await prisma.user.findUnique({
      where: { email }
    });

    if (!user) {
      await cache.set(loginAttemptsKey, attempts + 1, 900); // 15 minutes
      logSecurityEvent('login_attempt_invalid_email', { email }, req);
      return res.status(401).json({
        error: 'Email ou mot de passe incorrect',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Note: Pour l'intégration Auth0, cette vérification sera remplacée
    // par la validation du token Auth0
    
    // Générer un token JWT
    const token = jwt.sign(
      { 
        userId: user.id,
        email: user.email,
        sub: user.auth0Id
      },
      config.auth.jwtSecret,
      { expiresIn: config.auth.jwtExpiresIn }
    );

    // Mettre à jour la dernière connexion
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() }
    });

    // Réinitialiser les tentatives de connexion
    await cache.delete(loginAttemptsKey);

    // Log de l'événement business
    logBusinessEvent('user_logged_in', user.id, {
      email: user.email,
      loginMethod: 'local'
    });

    // Réponse
    res.json({
      message: 'Connexion réussie',
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        language: user.language,
        onboardingCompleted: user.onboardingCompleted,
        lastLoginAt: user.lastLoginAt
      },
      token,
      expiresIn: config.auth.jwtExpiresIn
    });

  } catch (error) {
    logger.error('Erreur lors de la connexion:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * POST /api/auth/logout
 * Déconnexion utilisateur (révocation du token)
 */
router.post('/logout', authMiddleware, async (req: Request, res: Response) => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      await revokeToken(token);
    }

    // Log de l'événement business
    logBusinessEvent('user_logged_out', req.user!.id, {
      email: req.user!.email
    });

    res.json({
      message: 'Déconnexion réussie'
    });

  } catch (error) {
    logger.error('Erreur lors de la déconnexion:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * POST /api/auth/refresh
 * Renouvellement du token
 */
router.post('/refresh', authMiddleware, async (req: Request, res: Response) => {
  try {
    // Générer un nouveau token
    const newToken = jwt.sign(
      { 
        userId: req.user!.id,
        email: req.user!.email,
        sub: req.user!.sub
      },
      config.auth.jwtSecret,
      { expiresIn: config.auth.jwtExpiresIn }
    );

    res.json({
      message: 'Token renouvelé avec succès',
      token: newToken,
      expiresIn: config.auth.jwtExpiresIn
    });

  } catch (error) {
    logger.error('Erreur lors du renouvellement du token:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * GET /api/auth/me
 * Informations de l'utilisateur connecté
 */
router.get('/me', authMiddleware, async (req: Request, res: Response) => {
  try {
    const user = await prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phoneNumber: true,
        profilePicture: true,
        language: true,
        currency: true,
        timezone: true,
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: true,
        onboardingCompleted: true,
        kycStatus: true,
        riskProfile: true,
        createdAt: true,
        lastLoginAt: true
      }
    });

    if (!user) {
      return res.status(404).json({
        error: 'Utilisateur non trouvé',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      user
    });

  } catch (error) {
    logger.error('Erreur lors de la récupération du profil:', error);
    res.status(500).json({
      error: 'Erreur interne du serveur',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
});

export default router;
