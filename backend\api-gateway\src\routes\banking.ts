/**
 * Routes bancaires pour Nouri - Proxy vers le service bancaire
 * Gestion des comptes, transactions et synchronisation bancaire
 */

import { Router, Request, Response } from 'express';
import axios from 'axios';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { requirePermission } from '../middleware/auth';

const router = Router();

/**
 * Fonction utilitaire pour faire un proxy vers le service bancaire
 */
const proxyToBankingService = async (req: Request, res: Response, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') => {
  try {
    const url = `${config.services.bankingService}${endpoint}`;
    
    const axiosConfig = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': req.user!.id,
        'X-User-Email': req.user!.email,
        'Authorization': req.headers.authorization
      },
      ...(method !== 'GET' && { data: req.body })
    };

    const response = await axios(axiosConfig);
    res.status(response.status).json(response.data);

  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      logger.error('Erreur lors du proxy vers le service bancaire:', error);
      res.status(500).json({
        error: 'Erreur de communication avec le service bancaire',
        code: 'BANKING_SERVICE_ERROR'
      });
    }
  }
};

/**
 * GET /api/banking/accounts
 * Récupération des comptes bancaires de l'utilisateur
 */
router.get('/accounts', async (req: Request, res: Response) => {
  await proxyToBankingService(req, res, '/accounts');
});

/**
 * POST /api/banking/accounts/sync
 * Synchronisation des comptes bancaires
 */
router.post('/accounts/sync', requirePermission('banking:sync'), async (req: Request, res: Response) => {
  await proxyToBankingService(req, res, '/accounts/sync', 'POST');
});

/**
 * GET /api/banking/accounts/:accountId
 * Détails d'un compte spécifique
 */
router.get('/accounts/:accountId', async (req: Request, res: Response) => {
  await proxyToBankingService(req, res, `/accounts/${req.params.accountId}`);
});

/**
 * GET /api/banking/transactions
 * Récupération des transactions
 */
router.get('/transactions', async (req: Request, res: Response) => {
  const queryString = new URLSearchParams(req.query as any).toString();
  const endpoint = `/transactions${queryString ? `?${queryString}` : ''}`;
  await proxyToBankingService(req, res, endpoint);
});

/**
 * GET /api/banking/transactions/:transactionId
 * Détails d'une transaction spécifique
 */
router.get('/transactions/:transactionId', async (req: Request, res: Response) => {
  await proxyToBankingService(req, res, `/transactions/${req.params.transactionId}`);
});

/**
 * PUT /api/banking/transactions/:transactionId/category
 * Mise à jour de la catégorie d'une transaction
 */
router.put('/transactions/:transactionId/category', async (req: Request, res: Response) => {
  await proxyToBankingService(req, res, `/transactions/${req.params.transactionId}/category`, 'PUT');
});

/**
 * GET /api/banking/balance
 * Solde total de tous les comptes
 */
router.get('/balance', async (req: Request, res: Response) => {
  await proxyToBankingService(req, res, '/balance');
});

/**
 * GET /api/banking/analytics/spending
 * Analyse des dépenses
 */
router.get('/analytics/spending', async (req: Request, res: Response) => {
  const queryString = new URLSearchParams(req.query as any).toString();
  const endpoint = `/analytics/spending${queryString ? `?${queryString}` : ''}`;
  await proxyToBankingService(req, res, endpoint);
});

/**
 * GET /api/banking/analytics/income
 * Analyse des revenus
 */
router.get('/analytics/income', async (req: Request, res: Response) => {
  const queryString = new URLSearchParams(req.query as any).toString();
  const endpoint = `/analytics/income${queryString ? `?${queryString}` : ''}`;
  await proxyToBankingService(req, res, endpoint);
});

export default router;
