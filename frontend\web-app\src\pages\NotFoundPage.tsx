/**
 * Page 404 - Page non trouvée
 */

import React from 'react'
import { Box, Typography, Button } from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { Home as HomeIcon } from '@mui/icons-material'

export const NotFoundPage: React.FC = () => {
  const navigate = useNavigate()

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        p: 3
      }}
    >
      <Typography variant="h1" sx={{ fontSize: '6rem', fontWeight: 'bold', color: 'primary.main', mb: 2 }}>
        🇹🇳 404
      </Typography>
      <Typography variant="h4" gutterBottom>
        Page non trouvée
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 500 }}>
        La page que vous recherchez n'existe pas ou a été déplacée.
        Retournez à l'accueil pour continuer à utiliser Nouri, votre assistant financier.
      </Typography>
      <Button
        variant="contained"
        startIcon={<HomeIcon />}
        onClick={() => navigate('/')}
      >
        Retour à l'accueil
      </Button>
    </Box>
  )
}

export default NotFoundPage
