/**
 * Optimized Main App Component
 * Simplified for better performance
 */

import React, { Suspense } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline, Box, CircularProgress, Typography } from '@mui/material'

// Optimized theme
import { optimizedTheme } from './theme/optimized'
import { ROUTES } from './config'

// Lazy load pages
const HomePage = React.lazy(() => import('./pages/HomePage'))
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'))
const DashboardPage = React.lazy(() => import('./pages/DashboardPage'))

// Global loading fallback
const GlobalLoader: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      gap: 2
    }}
  >
    <CircularProgress size={40} />
    <Box sx={{ textAlign: 'center' }}>
      <Typography variant="h3" sx={{ mb: 1 }}>🇹🇳</Typography>
      <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 'bold' }}>
        Nouri
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
        Chargement de votre coach financier...
      </Typography>
    </Box>
  </Box>
)

const App: React.FC = () => {
  return (
    <ThemeProvider theme={optimizedTheme}>
      <CssBaseline />
      <BrowserRouter>
        <Suspense fallback={<GlobalLoader />}>
          <Routes>
            <Route path={ROUTES.HOME} element={<HomePage />} />
            <Route path={ROUTES.LOGIN} element={<LoginPage />} />
            <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />
            <Route path="*" element={<Navigate to={ROUTES.HOME} replace />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ThemeProvider>
  )
}

export default App
