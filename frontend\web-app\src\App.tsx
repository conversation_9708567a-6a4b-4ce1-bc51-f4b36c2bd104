/**
 * Streamlined Nouri Preview Application
 * Optimized for performance and simplicity
 */

import React, { Suspense, useState } from 'react'
import { <PERSON>rows<PERSON><PERSON>outer, Routes, Route } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline, Box, CircularProgress, Typography } from '@mui/material'

// Optimized theme
import { optimizedTheme } from './theme/optimized'
import { ROUTES } from './config'

// Simple context for demo authentication
export const AuthContext = React.createContext<{
  isAuthenticated: boolean
  login: () => void
  logout: () => void
}>({
  isAuthenticated: false,
  login: () => {},
  logout: () => {}
})

// Lazy load pages for optimal performance
const HomePage = React.lazy(() => import('./pages/HomePage'))
const LoginPage = React.lazy(() => import('./pages/auth/LoginPage'))
const RegisterPage = React.lazy(() => import('./pages/auth/RegisterPage'))
const DashboardPage = React.lazy(() => import('./pages/DashboardPageSimple'))
const TransactionsPage = React.lazy(() => import('./pages/TransactionsPage'))
const BudgetsPage = React.lazy(() => import('./pages/BudgetsPage'))
const SavingsPage = React.lazy(() => import('./pages/SavingsPage'))
const ChatbotPage = React.lazy(() => import('./pages/ChatbotPage'))
const EducationPage = React.lazy(() => import('./pages/EducationPage'))
const ReportsPage = React.lazy(() => import('./pages/ReportsPage'))
const SettingsPage = React.lazy(() => import('./pages/SettingsPage'))
const ProfilePage = React.lazy(() => import('./pages/ProfilePage'))
const HelpPage = React.lazy(() => import('./pages/HelpPage'))
const NotFoundPage = React.lazy(() => import('./pages/NotFoundPage'))

// Optimized loading component
const GlobalLoader: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      gap: 2,
      background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
      color: 'white'
    }}
  >
    <CircularProgress size={40} sx={{ color: 'white' }} />
    <Box sx={{ textAlign: 'center' }}>
      <Typography variant="h3" sx={{ mb: 1 }}>🇹🇳</Typography>
      <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 1 }}>
        Nouri
      </Typography>
      <Typography variant="body2" sx={{ opacity: 0.9 }}>
        Chargement de votre coach financier...
      </Typography>
    </Box>
  </Box>
)

const App: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  const authValue = {
    isAuthenticated,
    login: () => setIsAuthenticated(true),
    logout: () => setIsAuthenticated(false)
  }

  return (
    <AuthContext.Provider value={authValue}>
      <ThemeProvider theme={optimizedTheme}>
        <CssBaseline />
        <BrowserRouter>
          <Suspense fallback={<GlobalLoader />}>
            <Routes>
              <Route path={ROUTES.HOME} element={<HomePage />} />
              <Route path={ROUTES.LOGIN} element={<LoginPage />} />
              <Route path={ROUTES.REGISTER} element={<RegisterPage />} />
              <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />
              <Route path={ROUTES.TRANSACTIONS} element={<TransactionsPage />} />
              <Route path={ROUTES.BUDGETS} element={<BudgetsPage />} />
              <Route path={ROUTES.SAVINGS} element={<SavingsPage />} />
              <Route path={ROUTES.CHATBOT} element={<ChatbotPage />} />
              <Route path={ROUTES.EDUCATION} element={<EducationPage />} />
              <Route path={ROUTES.REPORTS} element={<ReportsPage />} />
              <Route path={ROUTES.SETTINGS} element={<SettingsPage />} />
              <Route path={ROUTES.PROFILE} element={<ProfilePage />} />
              <Route path={ROUTES.HELP} element={<HelpPage />} />
              <Route path="/demo" element={<DashboardPage />} />
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Suspense>
        </BrowserRouter>
      </ThemeProvider>
    </AuthContext.Provider>
  )
}

export default App
