/**
 * Composant principal de l'application Nouri
 */

import React from 'react'
import { Box, Typography, Button } from '@mui/material'

const App: React.FC = () => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        p: 3
      }}
    >
      <Typography variant="h2" component="h1" gutterBottom sx={{ color: 'primary.main', fontWeight: 'bold' }}>
        🇹🇳 Nouri
      </Typography>
      <Typography variant="h5" gutterBottom>
        Coach Financier IA pour la Tunisie
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4, maxWidth: 600 }}>
        Votre assistant financier intelligent pour une meilleure gestion de vos finances.
        Budgets intelligents, épargne automatique, et conseils personnalisés.
      </Typography>
      <Button variant="contained" size="large" sx={{ px: 4, py: 1.5 }}>
        Commencer maintenant
      </Button>
      <Typography variant="caption" color="text.disabled" sx={{ mt: 4 }}>
        Version 1.0.0 - Développé avec ❤️ en Tunisie
      </Typography>
    </Box>
  )
}

export default App
