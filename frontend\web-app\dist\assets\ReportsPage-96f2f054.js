import{j as e,B as s,o as n,p as r,b as i,I as t,x as o,H as a,q as l,d as c,f as d,g as x,h,F as m,v as j,w as g,M as p,G as u,e as b,ab as v,y,z as f,E as C,J as w,K as W,N as A,n as D,m as R}from"./mui-vendor-b761306f.js";import{u as S,r as N}from"./react-vendor-2f216e43.js";import{A as I,R as T}from"./index-bd55ea72.js";const z=[{month:"Janvier 2024",income:2250,expenses:1850,savings:400,budgetCompliance:85},{month:"Décembre 2023",income:2100,expenses:1950,savings:150,budgetCompliance:78},{month:"Novembre 2023",income:2300,expenses:1750,savings:550,budgetCompliance:92},{month:"Octobre 2023",income:2200,expenses:1800,savings:400,budgetCompliance:88},{month:"Septembre 2023",income:2150,expenses:1900,savings:250,budgetCompliance:82}],L=[{category:"Alimentation",amount:450,percentage:24,trend:"up"},{category:"Transport",amount:320,percentage:17,trend:"down"},{category:"Loisirs",amount:280,percentage:15,trend:"up"},{category:"Utilities",amount:250,percentage:13,trend:"stable"},{category:"Santé",amount:180,percentage:10,trend:"down"},{category:"Vêtements",amount:150,percentage:8,trend:"up"},{category:"Autres",amount:220,percentage:13,trend:"stable"}],k=()=>{const k=S(),{logout:B}=N.useContext(I),[F,$]=N.useState("monthly"),[M,P]=N.useState("2024"),E=()=>{alert("Rapport exporté avec succès!")},G=s=>{switch(s){case"up":return e.jsx(R,{color:"error",fontSize:"small"});case"down":return e.jsx(D,{color:"success",fontSize:"small"});default:return e.jsx("span",{children:"→"})}},H=z[0],O=z[1],q=(H.income-O.income)/O.income*100,J=(H.expenses-O.expenses)/O.expenses*100;return e.jsxs(s,{sx:{flexGrow:1},children:[e.jsx(n,{position:"static",elevation:1,children:e.jsxs(r,{children:[e.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Rapports Financiers"}),e.jsx(t,{color:"inherit",onClick:E,children:e.jsx(o,{})}),e.jsx(t,{color:"inherit",onClick:()=>k(T.DASHBOARD),children:e.jsx(a,{})}),e.jsx(t,{color:"inherit",onClick:()=>{B(),k(T.HOME)},children:e.jsx(l,{})})]})}),e.jsxs(c,{maxWidth:"xl",sx:{py:4},children:[e.jsx(s,{sx:{mb:4},children:e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(s,{children:[e.jsx(i,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Rapports Financiers"}),e.jsx(i,{variant:"body1",color:"text.secondary",children:"Analysez vos performances financières et tendances"})]}),e.jsx(d,{variant:"contained",startIcon:e.jsx(o,{}),onClick:E,children:"Exporter PDF"})]})}),e.jsx(x,{sx:{mb:4},children:e.jsx(h,{children:e.jsxs(s,{sx:{display:"flex",gap:2,alignItems:"center"},children:[e.jsxs(m,{sx:{minWidth:150},children:[e.jsx(j,{children:"Période"}),e.jsxs(g,{value:F,label:"Période",onChange:e=>$(e.target.value),children:[e.jsx(p,{value:"monthly",children:"Mensuel"}),e.jsx(p,{value:"quarterly",children:"Trimestriel"}),e.jsx(p,{value:"yearly",children:"Annuel"})]})]}),e.jsxs(m,{sx:{minWidth:120},children:[e.jsx(j,{children:"Année"}),e.jsxs(g,{value:M,label:"Année",onChange:e=>P(e.target.value),children:[e.jsx(p,{value:"2024",children:"2024"}),e.jsx(p,{value:"2023",children:"2023"}),e.jsx(p,{value:"2022",children:"2022"})]})]})]})})}),e.jsxs(u,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(u,{item:!0,xs:12,md:3,children:e.jsx(x,{children:e.jsxs(h,{sx:{textAlign:"center"},children:[e.jsxs(i,{variant:"h4",color:"success.main",fontWeight:"bold",children:[H.income.toLocaleString()," TND"]}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Revenus ce mois"}),e.jsx(b,{label:`${q>0?"+":""}${q.toFixed(1)}%`,size:"small",color:q>0?"success":"error"})]})})}),e.jsx(u,{item:!0,xs:12,md:3,children:e.jsx(x,{children:e.jsxs(h,{sx:{textAlign:"center"},children:[e.jsxs(i,{variant:"h4",color:"error.main",fontWeight:"bold",children:[H.expenses.toLocaleString()," TND"]}),e.jsx(i,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"Dépenses ce mois"}),e.jsx(b,{label:`${J>0?"+":""}${J.toFixed(1)}%`,size:"small",color:J>0?"error":"success"})]})})}),e.jsx(u,{item:!0,xs:12,md:3,children:e.jsx(x,{children:e.jsxs(h,{sx:{textAlign:"center"},children:[e.jsxs(i,{variant:"h4",color:"primary.main",fontWeight:"bold",children:[H.savings.toLocaleString()," TND"]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Épargne ce mois"})]})})}),e.jsx(u,{item:!0,xs:12,md:3,children:e.jsx(x,{children:e.jsxs(h,{sx:{textAlign:"center"},children:[e.jsxs(i,{variant:"h4",color:"info.main",fontWeight:"bold",children:[H.budgetCompliance,"%"]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Respect du budget"})]})})})]}),e.jsxs(u,{container:!0,spacing:3,children:[e.jsx(u,{item:!0,xs:12,md:8,children:e.jsx(x,{children:e.jsxs(h,{children:[e.jsxs(i,{variant:"h6",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(v,{}),"Performance Mensuelle"]}),e.jsx(y,{children:e.jsxs(f,{children:[e.jsx(C,{children:e.jsxs(w,{children:[e.jsx(W,{children:"Mois"}),e.jsx(W,{align:"right",children:"Revenus"}),e.jsx(W,{align:"right",children:"Dépenses"}),e.jsx(W,{align:"right",children:"Épargne"}),e.jsx(W,{align:"right",children:"Budget"})]})}),e.jsx(A,{children:z.map(((s,n)=>e.jsxs(w,{hover:!0,children:[e.jsx(W,{children:s.month}),e.jsxs(W,{align:"right",sx:{color:"success.main",fontWeight:"bold"},children:[s.income.toLocaleString()," TND"]}),e.jsxs(W,{align:"right",sx:{color:"error.main",fontWeight:"bold"},children:[s.expenses.toLocaleString()," TND"]}),e.jsxs(W,{align:"right",sx:{color:"primary.main",fontWeight:"bold"},children:[s.savings.toLocaleString()," TND"]}),e.jsx(W,{align:"right",children:e.jsx(b,{label:`${s.budgetCompliance}%`,size:"small",color:s.budgetCompliance>=85?"success":s.budgetCompliance>=70?"warning":"error"})})]},n)))})]})})]})})}),e.jsx(u,{item:!0,xs:12,md:4,children:e.jsx(x,{children:e.jsxs(h,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Répartition par Catégorie"}),e.jsx(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:L.map(((n,r)=>e.jsxs(s,{children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:1},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(i,{variant:"body2",fontWeight:"medium",children:n.category}),G(n.trend)]}),e.jsxs(i,{variant:"body2",fontWeight:"bold",children:[n.amount," TND"]})]}),e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx(s,{sx:{width:"70%",height:6,bgcolor:"grey.200",borderRadius:3,overflow:"hidden"},children:e.jsx(s,{sx:{width:`${n.percentage}%`,height:"100%",bgcolor:"primary.main"}})}),e.jsxs(i,{variant:"caption",color:"text.secondary",children:[n.percentage,"%"]})]})]},r)))})]})})})]}),e.jsx(x,{sx:{mt:3},children:e.jsxs(h,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"💡 Insights et Recommandations"}),e.jsxs(u,{container:!0,spacing:2,children:[e.jsx(u,{item:!0,xs:12,md:4,children:e.jsxs(s,{sx:{p:2,bgcolor:"success.50",borderRadius:2,border:"1px solid",borderColor:"success.200"},children:[e.jsx(i,{variant:"body2",color:"success.dark",fontWeight:"bold",children:"✅ Point positif"}),e.jsxs(i,{variant:"body2",sx:{mt:1},children:["Vos revenus ont augmenté de ",q,"% ce mois-ci. Excellente performance !"]})]})}),e.jsx(u,{item:!0,xs:12,md:4,children:e.jsxs(s,{sx:{p:2,bgcolor:"warning.50",borderRadius:2,border:"1px solid",borderColor:"warning.200"},children:[e.jsx(i,{variant:"body2",color:"warning.dark",fontWeight:"bold",children:"⚠️ Attention"}),e.jsx(i,{variant:"body2",sx:{mt:1},children:"Les dépenses en loisirs ont augmenté de 15%. Surveillez ce budget."})]})}),e.jsx(u,{item:!0,xs:12,md:4,children:e.jsxs(s,{sx:{p:2,bgcolor:"info.50",borderRadius:2,border:"1px solid",borderColor:"info.200"},children:[e.jsx(i,{variant:"body2",color:"info.dark",fontWeight:"bold",children:"💡 Conseil"}),e.jsx(i,{variant:"body2",sx:{mt:1},children:"Augmentez votre épargne de 100 TND pour atteindre vos objectifs plus rapidement."})]})})]})]})})]})]})};export{k as ReportsPage,k as default};
