/**
 * Layout principal de l'application Nouri
 * Contient la sidebar, header et le contenu principal
 */

import React, { useState } from 'react'
import {
  Box,
  Drawer,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  useTheme,
  useMediaQuery,
  Container
} from '@mui/material'
import {
  Menu as MenuIcon,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon
} from '@mui/icons-material'

// Composants
import { Sidebar } from './Sidebar'
import { NotificationPanel } from '../common/NotificationPanel'
import { UserMenu } from '../common/UserMenu'

// Hooks
import { useSidebar } from '@/store/useAppStore'
import { useThemeStore } from '@/store/useThemeStore'
import { config } from '@/config'

interface MainLayoutProps {
  children: React.ReactNode
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  
  // État local
  const [notificationPanelOpen, setNotificationPanelOpen] = useState(false)
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null)
  
  // Store
  const { isOpen: sidebarOpen, toggle: toggleSidebar, setOpen: setSidebarOpen } = useSidebar()
  const { sidebarCollapsed } = useThemeStore()

  // Largeur de la sidebar
  const sidebarWidth = sidebarCollapsed ? 72 : config.ui.sidebarWidth

  // Gestion de la sidebar sur mobile
  const handleSidebarToggle = () => {
    if (isMobile) {
      toggleSidebar()
    } else {
      toggleSidebar()
    }
  }

  // Gestion du menu utilisateur
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget)
  }

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null)
  }

  // Gestion du panel de notifications
  const handleNotificationPanelToggle = () => {
    setNotificationPanelOpen(!notificationPanelOpen)
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      {/* Header */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          backgroundColor: theme.palette.background.paper,
          color: theme.palette.text.primary,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.12)',
          borderBottom: `1px solid ${theme.palette.divider}`,
          ml: isMobile ? 0 : sidebarOpen ? `${sidebarWidth}px` : 0,
          width: isMobile ? '100%' : sidebarOpen ? `calc(100% - ${sidebarWidth}px)` : '100%',
          transition: theme.transitions.create(['margin', 'width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        <Toolbar sx={{ minHeight: config.ui.headerHeight }}>
          {/* Bouton menu (mobile uniquement) */}
          {isMobile && (
            <IconButton
              edge="start"
              color="inherit"
              aria-label="menu"
              onClick={handleSidebarToggle}
              sx={{ mr: 2 }}
            >
              <MenuIcon />
            </IconButton>
          )}

          {/* Titre de la page */}
          <Typography variant="h6" component="h1" sx={{ flexGrow: 1 }}>
            Tableau de bord
          </Typography>

          {/* Actions du header */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Notifications */}
            <IconButton
              color="inherit"
              onClick={handleNotificationPanelToggle}
              aria-label="notifications"
            >
              <NotificationsIcon />
            </IconButton>

            {/* Menu utilisateur */}
            <IconButton
              color="inherit"
              onClick={handleUserMenuOpen}
              aria-label="user menu"
            >
              <AccountIcon />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Box
        component="nav"
        sx={{
          width: isMobile ? 0 : sidebarOpen ? sidebarWidth : 0,
          flexShrink: 0,
        }}
      >
        {isMobile ? (
          // Drawer temporaire pour mobile
          <Drawer
            variant="temporary"
            open={sidebarOpen}
            onClose={() => setSidebarOpen(false)}
            ModalProps={{
              keepMounted: true, // Meilleure performance sur mobile
            }}
            sx={{
              '& .MuiDrawer-paper': {
                width: sidebarWidth,
                boxSizing: 'border-box',
              },
            }}
          >
            <Sidebar />
          </Drawer>
        ) : (
          // Drawer persistant pour desktop
          <Drawer
            variant="persistent"
            open={sidebarOpen}
            sx={{
              '& .MuiDrawer-paper': {
                width: sidebarWidth,
                boxSizing: 'border-box',
                transition: theme.transitions.create('width', {
                  easing: theme.transitions.easing.sharp,
                  duration: theme.transitions.duration.enteringScreen,
                }),
              },
            }}
          >
            <Sidebar />
          </Drawer>
        )}
      </Box>

      {/* Contenu principal */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          mt: `${config.ui.headerHeight}px`,
          ml: isMobile ? 0 : sidebarOpen ? 0 : `-${sidebarWidth}px`,
          transition: theme.transitions.create(['margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          minHeight: `calc(100vh - ${config.ui.headerHeight}px)`,
          backgroundColor: theme.palette.background.default,
        }}
      >
        <Container maxWidth="xl" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
          {children}
        </Container>
      </Box>

      {/* Panel de notifications */}
      <NotificationPanel
        open={notificationPanelOpen}
        onClose={() => setNotificationPanelOpen(false)}
      />

      {/* Menu utilisateur */}
      <UserMenu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
      />
    </Box>
  )
}

export default MainLayout
