/**
 * Routes de gestion des budgets pour Nouri - Proxy vers le service budget
 */

import { Router, Request, Response } from 'express';
import axios from 'axios';
import { config } from '../config/config';
import { logger } from '../utils/logger';

const router = Router();

/**
 * Fonction utilitaire pour faire un proxy vers le service budget
 */
const proxyToBudgetService = async (req: Request, res: Response, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') => {
  try {
    const url = `${config.services.budgetService}${endpoint}`;
    
    const axiosConfig = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': req.user!.id,
        'X-User-Email': req.user!.email,
        'Authorization': req.headers.authorization
      },
      ...(method !== 'GET' && { data: req.body })
    };

    const response = await axios(axiosConfig);
    res.status(response.status).json(response.data);

  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      logger.error('Erreur lors du proxy vers le service budget:', error);
      res.status(500).json({
        error: 'Erreur de communication avec le service budget',
        code: 'BUDGET_SERVICE_ERROR'
      });
    }
  }
};

// Routes des budgets
router.get('/', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, '/budgets');
});

router.post('/', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, '/budgets', 'POST');
});

router.get('/:budgetId', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, `/budgets/${req.params.budgetId}`);
});

router.put('/:budgetId', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, `/budgets/${req.params.budgetId}`, 'PUT');
});

router.delete('/:budgetId', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, `/budgets/${req.params.budgetId}`, 'DELETE');
});

// Routes des objectifs d'épargne
router.get('/savings-goals', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, '/savings-goals');
});

router.post('/savings-goals', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, '/savings-goals', 'POST');
});

router.get('/savings-goals/:goalId', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, `/savings-goals/${req.params.goalId}`);
});

router.put('/savings-goals/:goalId', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, `/savings-goals/${req.params.goalId}`, 'PUT');
});

router.delete('/savings-goals/:goalId', async (req: Request, res: Response) => {
  await proxyToBudgetService(req, res, `/savings-goals/${req.params.goalId}`, 'DELETE');
});

export default router;
