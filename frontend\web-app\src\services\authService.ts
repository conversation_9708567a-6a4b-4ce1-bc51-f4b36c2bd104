/**
 * Service d'authentification pour Nouri
 * Gestion des appels API liés à l'authentification
 */

import { apiGet, apiPost, apiPut } from './api'
import { API_ENDPOINTS } from '@/config'

// Types
export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
  phoneNumber?: string
  language?: string
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    firstName: string
    lastName: string
    language: string
    onboardingCompleted: boolean
  }
  token: string
  expiresIn: string
}

export interface UserProfile {
  id: string
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  dateOfBirth?: string
  profilePicture?: string
  language: string
  currency: string
  timezone: string
  emailNotifications: boolean
  pushNotifications: boolean
  smsNotifications: boolean
  onboardingCompleted: boolean
  kycStatus: 'pending' | 'verified' | 'rejected'
  riskProfile?: string
  createdAt: string
  lastLoginAt?: string
}

export interface UpdateProfileData {
  firstName?: string
  lastName?: string
  phoneNumber?: string
  dateOfBirth?: string
  language?: string
  currency?: string
  timezone?: string
}

export interface NotificationPreferences {
  emailNotifications?: boolean
  pushNotifications?: boolean
  smsNotifications?: boolean
}

// Service d'authentification
export class AuthService {
  /**
   * Connexion utilisateur
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    return apiPost<AuthResponse>(API_ENDPOINTS.AUTH.LOGIN, credentials)
  }

  /**
   * Inscription utilisateur
   */
  static async register(data: RegisterData): Promise<AuthResponse> {
    return apiPost<AuthResponse>('/api/auth/register', data)
  }

  /**
   * Déconnexion utilisateur
   */
  static async logout(): Promise<void> {
    return apiPost<void>(API_ENDPOINTS.AUTH.LOGOUT)
  }

  /**
   * Renouvellement du token
   */
  static async refreshToken(): Promise<{ token: string; expiresIn: string }> {
    return apiPost<{ token: string; expiresIn: string }>(API_ENDPOINTS.AUTH.REFRESH)
  }

  /**
   * Récupération du profil utilisateur connecté
   */
  static async getMe(): Promise<{ user: UserProfile }> {
    return apiGet<{ user: UserProfile }>(API_ENDPOINTS.AUTH.ME)
  }

  /**
   * Récupération du profil utilisateur complet
   */
  static async getProfile(): Promise<{
    user: UserProfile
    accounts: any[]
    budgets: any[]
    savingsGoals: any[]
    statistics: any
  }> {
    return apiGet(API_ENDPOINTS.USERS.PROFILE)
  }

  /**
   * Mise à jour du profil utilisateur
   */
  static async updateProfile(data: UpdateProfileData): Promise<{ user: UserProfile }> {
    return apiPut<{ user: UserProfile }>(API_ENDPOINTS.USERS.PROFILE, data)
  }

  /**
   * Mise à jour des préférences de notification
   */
  static async updateNotificationPreferences(
    preferences: NotificationPreferences
  ): Promise<{ preferences: NotificationPreferences }> {
    return apiPut<{ preferences: NotificationPreferences }>(
      API_ENDPOINTS.USERS.NOTIFICATIONS,
      preferences
    )
  }

  /**
   * Finalisation de l'onboarding
   */
  static async completeOnboarding(): Promise<{ user: { onboardingCompleted: boolean } }> {
    return apiPost<{ user: { onboardingCompleted: boolean } }>('/api/users/complete-onboarding')
  }

  /**
   * Récupération des données du tableau de bord
   */
  static async getDashboard(): Promise<{
    summary: {
      totalBalance: number
      weeklySpending: number
      unreadNotifications: number
      activeAccounts: number
      activeBudgets: number
      activeSavingsGoals: number
    }
    accounts: any[]
    recentTransactions: any[]
    budgets: any[]
    savingsGoals: any[]
  }> {
    return apiGet(API_ENDPOINTS.USERS.DASHBOARD)
  }

  /**
   * Suppression du compte utilisateur
   */
  static async deleteAccount(): Promise<void> {
    return apiPost<void>('/api/users/account')
  }

  /**
   * Demande de réinitialisation de mot de passe
   */
  static async requestPasswordReset(email: string): Promise<void> {
    return apiPost<void>('/api/auth/password-reset', { email })
  }

  /**
   * Réinitialisation du mot de passe
   */
  static async resetPassword(token: string, newPassword: string): Promise<void> {
    return apiPost<void>('/api/auth/password-reset/confirm', {
      token,
      password: newPassword
    })
  }

  /**
   * Changement de mot de passe
   */
  static async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    return apiPut<void>('/api/auth/change-password', {
      currentPassword,
      newPassword
    })
  }

  /**
   * Vérification de l'email
   */
  static async verifyEmail(token: string): Promise<void> {
    return apiPost<void>('/api/auth/verify-email', { token })
  }

  /**
   * Renvoi de l'email de vérification
   */
  static async resendVerificationEmail(): Promise<void> {
    return apiPost<void>('/api/auth/resend-verification')
  }

  /**
   * Activation de l'authentification à deux facteurs
   */
  static async enableTwoFactor(): Promise<{
    qrCode: string
    secret: string
    backupCodes: string[]
  }> {
    return apiPost<{
      qrCode: string
      secret: string
      backupCodes: string[]
    }>('/api/auth/2fa/enable')
  }

  /**
   * Confirmation de l'authentification à deux facteurs
   */
  static async confirmTwoFactor(token: string): Promise<void> {
    return apiPost<void>('/api/auth/2fa/confirm', { token })
  }

  /**
   * Désactivation de l'authentification à deux facteurs
   */
  static async disableTwoFactor(token: string): Promise<void> {
    return apiPost<void>('/api/auth/2fa/disable', { token })
  }

  /**
   * Génération de nouveaux codes de sauvegarde
   */
  static async generateBackupCodes(): Promise<{ backupCodes: string[] }> {
    return apiPost<{ backupCodes: string[] }>('/api/auth/2fa/backup-codes')
  }

  /**
   * Récupération des sessions actives
   */
  static async getActiveSessions(): Promise<{
    sessions: Array<{
      id: string
      deviceInfo: any
      ipAddress: string
      userAgent: string
      createdAt: string
      lastUsed: string
      isCurrent: boolean
    }>
  }> {
    return apiGet('/api/auth/sessions')
  }

  /**
   * Révocation d'une session
   */
  static async revokeSession(sessionId: string): Promise<void> {
    return apiPost<void>(`/api/auth/sessions/${sessionId}/revoke`)
  }

  /**
   * Révocation de toutes les sessions (sauf la courante)
   */
  static async revokeAllSessions(): Promise<void> {
    return apiPost<void>('/api/auth/sessions/revoke-all')
  }
}

export default AuthService
