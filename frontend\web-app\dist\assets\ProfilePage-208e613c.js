import{j as e,B as s,o as i,p as r,b as n,I as a,H as t,q as c,d as o,g as l,h as d,l as x,e as h,an as j,m,f as g,Z as u,L as p,G as v,D as f,ae as b,af as y,ag as S,A,ah as C,ao as N,a7 as D}from"./mui-vendor-b761306f.js";import{u as I,r as R}from"./react-vendor-2f216e43.js";import{A as w,R as B}from"./index-bd55ea72.js";const G={firstName:"Ahmed",lastName:"<PERSON>",email:"<EMAIL>",phone:"+216 20 123 456",joinDate:"2023-06-15",location:"Tunis, Tunisie",financialScore:85,level:"Expert",achievements:[{id:1,title:"Premier budget créé",icon:"🎯",earned:!0,date:"2023-06-20"},{id:2,title:"Object<PERSON> d'épargne atteint",icon:"💰",earned:!0,date:"2023-08-15"},{id:3,title:"3 mois de budget respecté",icon:"📊",earned:!0,date:"2023-09-30"},{id:4,title:"Formation terminée",icon:"🎓",earned:!0,date:"2023-10-10"},{id:5,title:"Épargne de 10,000 TND",icon:"🏆",earned:!1,progress:65},{id:6,title:"Maître de l'investissement",icon:"📈",earned:!1,progress:20}],stats:{totalSavings:30250,budgetsCreated:12,goalsAchieved:3,coursesCompleted:4,daysActive:195}},O=()=>{const O=I(),{logout:T}=R.useContext(w),W=e=>e>=80?"success":e>=60?"warning":"error";return e.jsxs(s,{sx:{flexGrow:1},children:[e.jsx(i,{position:"static",elevation:1,children:e.jsxs(r,{children:[e.jsx(n,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Mon Profil"}),e.jsx(a,{color:"inherit",onClick:()=>O(B.DASHBOARD),children:e.jsx(t,{})}),e.jsx(a,{color:"inherit",onClick:()=>{T(),O(B.HOME)},children:e.jsx(c,{})})]})}),e.jsxs(o,{maxWidth:"lg",sx:{py:4},children:[e.jsx(l,{sx:{mb:4},children:e.jsxs(d,{children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:3,mb:3},children:[e.jsxs(x,{sx:{width:100,height:100,bgcolor:"primary.main",fontSize:"2rem"},children:[G.firstName[0],G.lastName[0]]}),e.jsxs(s,{sx:{flexGrow:1},children:[e.jsxs(n,{variant:"h4",fontWeight:"bold",children:[G.firstName," ",G.lastName]}),e.jsx(n,{variant:"body1",color:"text.secondary",sx:{mb:1},children:G.email}),e.jsxs(n,{variant:"body2",color:"text.secondary",sx:{mb:2},children:["📍 ",G.location," • Membre depuis ",new Date(G.joinDate).toLocaleDateString("fr-FR")]}),e.jsxs(s,{sx:{display:"flex",gap:1},children:[e.jsx(h,{label:`Niveau ${G.level}`,color:(e=>{switch(e){case"Expert":return"success";case"Avancé":return"info";case"Intermédiaire":return"warning";default:return"default"}})(G.level),icon:e.jsx(j,{})}),e.jsx(h,{label:`Score: ${G.financialScore}/100`,color:W(G.financialScore),icon:e.jsx(m,{})})]})]}),e.jsx(g,{variant:"outlined",startIcon:e.jsx(u,{}),onClick:()=>O(B.SETTINGS),children:"Modifier"})]}),e.jsxs(s,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"Score Financier"}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:2},children:[e.jsx(p,{variant:"determinate",value:G.financialScore,sx:{flexGrow:1,height:10,borderRadius:5},color:W(G.financialScore)}),e.jsxs(n,{variant:"h6",fontWeight:"bold",children:[G.financialScore,"/100"]})]}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Votre score s'améliore avec une meilleure gestion financière"})]})]})}),e.jsxs(v,{container:!0,spacing:3,children:[e.jsx(v,{item:!0,xs:12,md:8,children:e.jsx(l,{children:e.jsxs(d,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"📊 Mes Statistiques"}),e.jsxs(v,{container:!0,spacing:3,children:[e.jsx(v,{item:!0,xs:6,md:3,children:e.jsxs(s,{sx:{textAlign:"center"},children:[e.jsx(n,{variant:"h4",color:"primary.main",fontWeight:"bold",children:G.stats.totalSavings.toLocaleString()}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"TND Épargnés"})]})}),e.jsx(v,{item:!0,xs:6,md:3,children:e.jsxs(s,{sx:{textAlign:"center"},children:[e.jsx(n,{variant:"h4",color:"info.main",fontWeight:"bold",children:G.stats.budgetsCreated}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Budgets Créés"})]})}),e.jsx(v,{item:!0,xs:6,md:3,children:e.jsxs(s,{sx:{textAlign:"center"},children:[e.jsx(n,{variant:"h4",color:"success.main",fontWeight:"bold",children:G.stats.goalsAchieved}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Objectifs Atteints"})]})}),e.jsx(v,{item:!0,xs:6,md:3,children:e.jsxs(s,{sx:{textAlign:"center"},children:[e.jsx(n,{variant:"h4",color:"warning.main",fontWeight:"bold",children:G.stats.daysActive}),e.jsx(n,{variant:"caption",color:"text.secondary",children:"Jours Actifs"})]})})]}),e.jsx(f,{sx:{my:3}}),e.jsx(n,{variant:"h6",gutterBottom:!0,children:"🎯 Activité Récente"}),e.jsxs(b,{children:[e.jsxs(y,{children:[e.jsx(S,{children:e.jsx(A,{color:"primary"})}),e.jsx(C,{primary:"Nouveau budget créé",secondary:"Budget Loisirs - Il y a 2 jours"})]}),e.jsxs(y,{children:[e.jsx(S,{children:e.jsx(N,{color:"success"})}),e.jsx(C,{primary:"Objectif d'épargne atteint",secondary:"Vacances d'été - Il y a 1 semaine"})]}),e.jsxs(y,{children:[e.jsx(S,{children:e.jsx(D,{color:"info"})}),e.jsx(C,{primary:"Formation terminée",secondary:"Gestion des budgets - Il y a 2 semaines"})]})]})]})})}),e.jsx(v,{item:!0,xs:12,md:4,children:e.jsx(l,{children:e.jsxs(d,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"🏆 Mes Réussites"}),e.jsx(s,{sx:{display:"flex",flexDirection:"column",gap:2},children:G.achievements.map((i=>e.jsxs(s,{sx:{p:2,border:1,borderColor:i.earned?"success.main":"grey.300",borderRadius:2,bgcolor:i.earned?"success.50":"grey.50",opacity:i.earned?1:.7},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1,mb:1},children:[e.jsx(n,{variant:"h6",children:i.icon}),e.jsx(n,{variant:"body2",fontWeight:"bold",children:i.title})]}),i.earned?e.jsxs(n,{variant:"caption",color:"success.dark",children:["✅ Obtenu le ",new Date(i.date).toLocaleDateString("fr-FR")]}):e.jsxs(s,{children:[e.jsx(p,{variant:"determinate",value:i.progress||0,sx:{height:6,borderRadius:3,mb:1}}),e.jsxs(n,{variant:"caption",color:"text.secondary",children:["Progression: ",i.progress,"%"]})]})]},i.id)))})]})})})]}),e.jsx(l,{sx:{mt:3},children:e.jsxs(d,{children:[e.jsx(n,{variant:"h6",gutterBottom:!0,children:"🚀 Actions Rapides"}),e.jsxs(s,{sx:{display:"flex",gap:2,flexWrap:"wrap"},children:[e.jsx(g,{variant:"outlined",onClick:()=>O(B.BUDGETS),children:"Créer un Budget"}),e.jsx(g,{variant:"outlined",onClick:()=>O(B.SAVINGS),children:"Nouvel Objectif"}),e.jsx(g,{variant:"outlined",onClick:()=>O(B.EDUCATION),children:"Suivre une Formation"}),e.jsx(g,{variant:"outlined",onClick:()=>O(B.REPORTS),children:"Voir mes Rapports"})]})]})})]})]})};export{O as ProfilePage,O as default};
