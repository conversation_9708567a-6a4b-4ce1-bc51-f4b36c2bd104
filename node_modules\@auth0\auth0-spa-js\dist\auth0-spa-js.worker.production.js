!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){"use strict";class e extends Error{constructor(t,r){super(r),this.error=t,this.error_description=r,Object.setPrototypeOf(this,e.prototype)}static fromPayload({error:t,error_description:r}){return new e(t,r)}}class t extends e{constructor(e,s){super("missing_refresh_token",`Missing Refresh Token (audience: '${r(e,["default"])}', scope: '${r(s)}')`),this.audience=e,this.scope=s,Object.setPrototypeOf(this,t.prototype)}}function r(e,t=[]){return e&&!t.includes(e)?e:""}"function"==typeof SuppressedError&&SuppressedError;const s=e=>{var{clientId:t}=e,r=function(e,t){var r={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(r[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(s=Object.getOwnPropertySymbols(e);o<s.length;o++)t.indexOf(s[o])<0&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(r[s[o]]=e[s[o]])}return r}(e,["clientId"]);return new URLSearchParams((e=>Object.keys(e).filter((t=>void 0!==e[t])).reduce(((t,r)=>Object.assign(Object.assign({},t),{[r]:e[r]})),{}))(Object.assign({client_id:t},r))).toString()};let o={};const n=(e,t)=>`${e}|${t}`;addEventListener("message",(async({data:{timeout:e,auth:r,fetchUrl:i,fetchOptions:c,useFormData:a},ports:[f]})=>{let p;const{audience:u,scope:d}=r||{};try{const r=a?(e=>{const t=new URLSearchParams(e),r={};return t.forEach(((e,t)=>{r[t]=e})),r})(c.body):JSON.parse(c.body);if(!r.refresh_token&&"refresh_token"===r.grant_type){const e=((e,t)=>o[n(e,t)])(u,d);if(!e)throw new t(u,d);c.body=a?s(Object.assign(Object.assign({},r),{refresh_token:e})):JSON.stringify(Object.assign(Object.assign({},r),{refresh_token:e}))}let h,g;"function"==typeof AbortController&&(h=new AbortController,c.signal=h.signal);try{g=await Promise.race([(l=e,new Promise((e=>setTimeout(e,l)))),fetch(i,Object.assign({},c))])}catch(e){return void f.postMessage({error:e.message})}if(!g)return h&&h.abort(),void f.postMessage({error:"Timeout when executing 'fetch'"});p=await g.json(),p.refresh_token?(((e,t,r)=>{o[n(t,r)]=e})(p.refresh_token,u,d),delete p.refresh_token):((e,t)=>{delete o[n(e,t)]})(u,d),f.postMessage({ok:g.ok,json:p})}catch(e){f.postMessage({ok:!1,json:{error:e.error,error_description:e.message}})}var l}))}));
//# sourceMappingURL=auth0-spa-js.worker.production.js.map
