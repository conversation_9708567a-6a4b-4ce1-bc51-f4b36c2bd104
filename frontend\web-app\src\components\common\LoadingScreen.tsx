/**
 * Écran de chargement pour Nouri
 * Affichage pendant les chargements initiaux
 */

import React from 'react'
import {
  Box,
  CircularProgress,
  Typography,
  LinearProgress,
  Avatar,
  Fade
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { config } from '@/config'

interface LoadingScreenProps {
  message?: string
  progress?: number
  showProgress?: boolean
  variant?: 'splash' | 'overlay' | 'inline'
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message,
  progress,
  showProgress = false,
  variant = 'splash'
}) => {
  const { t } = useTranslation()

  const defaultMessage = message || t('common.loading')

  // Variante splash (écran complet)
  if (variant === 'splash') {
    return (
      <Fade in timeout={300}>
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'background.default',
            zIndex: 9999
          }}
        >
          {/* Logo */}
          <Box sx={{ mb: 4 }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'primary.main',
                fontSize: '2rem',
                fontWeight: 'bold',
                mb: 2
              }}
            >
              N
            </Avatar>
            <Typography
              variant="h4"
              component="h1"
              align="center"
              sx={{
                fontWeight: 'bold',
                color: 'primary.main',
                mb: 1
              }}
            >
              Nouri
            </Typography>
            <Typography
              variant="subtitle1"
              align="center"
              color="text.secondary"
            >
              Coach Financier IA
            </Typography>
          </Box>

          {/* Indicateur de chargement */}
          <Box sx={{ mb: 3 }}>
            <CircularProgress
              size={40}
              thickness={4}
              sx={{ color: 'primary.main' }}
            />
          </Box>

          {/* Message */}
          <Typography
            variant="body1"
            color="text.secondary"
            align="center"
            sx={{ mb: 2 }}
          >
            {defaultMessage}
          </Typography>

          {/* Barre de progression */}
          {showProgress && typeof progress === 'number' && (
            <Box sx={{ width: 200, mb: 2 }}>
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: 'grey.200',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 3
                  }
                }}
              />
              <Typography
                variant="caption"
                color="text.secondary"
                align="center"
                display="block"
                sx={{ mt: 1 }}
              >
                {Math.round(progress)}%
              </Typography>
            </Box>
          )}

          {/* Version */}
          <Typography
            variant="caption"
            color="text.disabled"
            align="center"
            sx={{ position: 'absolute', bottom: 20 }}
          >
            Version {config.app.version}
          </Typography>
        </Box>
      </Fade>
    )
  }

  // Variante overlay (par-dessus le contenu)
  if (variant === 'overlay') {
    return (
      <Fade in timeout={300}>
        <Box
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(4px)',
            zIndex: 1300
          }}
        >
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              p: 4,
              borderRadius: 2,
              backgroundColor: 'background.paper',
              boxShadow: 3,
              minWidth: 200
            }}
          >
            <CircularProgress
              size={40}
              thickness={4}
              sx={{ mb: 2, color: 'primary.main' }}
            />
            <Typography variant="body1" color="text.secondary" align="center">
              {defaultMessage}
            </Typography>
            
            {showProgress && typeof progress === 'number' && (
              <Box sx={{ width: '100%', mt: 2 }}>
                <LinearProgress
                  variant="determinate"
                  value={progress}
                  sx={{
                    height: 4,
                    borderRadius: 2
                  }}
                />
                <Typography
                  variant="caption"
                  color="text.secondary"
                  align="center"
                  display="block"
                  sx={{ mt: 1 }}
                >
                  {Math.round(progress)}%
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </Fade>
    )
  }

  // Variante inline (dans le contenu)
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        py: 4,
        minHeight: 200
      }}
    >
      <CircularProgress
        size={32}
        thickness={4}
        sx={{ mb: 2, color: 'primary.main' }}
      />
      <Typography variant="body2" color="text.secondary" align="center">
        {defaultMessage}
      </Typography>
      
      {showProgress && typeof progress === 'number' && (
        <Box sx={{ width: 150, mt: 2 }}>
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 3,
              borderRadius: 2
            }}
          />
          <Typography
            variant="caption"
            color="text.secondary"
            align="center"
            display="block"
            sx={{ mt: 0.5 }}
          >
            {Math.round(progress)}%
          </Typography>
        </Box>
      )}
    </Box>
  )
}

export default LoadingScreen
