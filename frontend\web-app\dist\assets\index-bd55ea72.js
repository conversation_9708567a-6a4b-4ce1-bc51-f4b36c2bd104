var e=Object.defineProperty,t=(t,r,n)=>(((t,r,n)=>{r in t?e(t,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[r]=n})(t,"symbol"!=typeof r?r+"":r,n),n);import{c as r,j as n,T as s,C as o,B as i,a as E,b as a}from"./mui-vendor-b761306f.js";import{d as _,b as T,r as I,B as c,e as l,f as d}from"./react-vendor-2f216e43.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const r of e)if("childList"===r.type)for(const e of r.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var p={},A=_;p.createRoot=A.createRoot,p.hydrateRoot=A.hydrateRoot;const h={},f=function(e,t,r){if(!t||0===t.length)return e();const n=document.getElementsByTagName("link");return Promise.all(t.map((e=>{if((e=function(e){return"/"+e}(e))in h)return;h[e]=!0;const t=e.endsWith(".css"),s=t?'[rel="stylesheet"]':"";if(!!r)for(let r=n.length-1;r>=0;r--){const s=n[r];if(s.href===e&&(!t||"stylesheet"===s.rel))return}else if(document.querySelector(`link[href="${e}"]${s}`))return;const o=document.createElement("link");return o.rel=t?"stylesheet":"modulepreload",t||(o.as="script",o.crossOrigin=""),o.href=e,document.head.appendChild(o),t?new Promise(((t,r)=>{o.addEventListener("load",t),o.addEventListener("error",(()=>r(new Error(`Unable to preload CSS for ${e}`))))})):void 0}))).then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))},m=r({palette:{primary:{main:"#1976d2",light:"#42a5f5",dark:"#1565c0",contrastText:"#ffffff"},secondary:{main:"#dc004e",light:"#ff5983",dark:"#9a0036",contrastText:"#ffffff"},success:{main:"#2e7d32",light:"#4caf50",dark:"#1b5e20",contrastText:"#ffffff"},warning:{main:"#ed6c02",light:"#ff9800",dark:"#e65100",contrastText:"#ffffff"},error:{main:"#d32f2f",light:"#ef5350",dark:"#c62828",contrastText:"#ffffff"},background:{default:"#fafafa",paper:"#ffffff"},text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"}},typography:{fontFamily:["Inter","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial","sans-serif"].join(","),h1:{fontWeight:700,fontSize:"2.5rem",lineHeight:1.2},h2:{fontWeight:700,fontSize:"2rem",lineHeight:1.3},h3:{fontWeight:600,fontSize:"1.75rem",lineHeight:1.3},h4:{fontWeight:600,fontSize:"1.5rem",lineHeight:1.4},h5:{fontWeight:500,fontSize:"1.25rem",lineHeight:1.4},h6:{fontWeight:500,fontSize:"1.125rem",lineHeight:1.4},body1:{fontSize:"1rem",lineHeight:1.5},body2:{fontSize:"0.875rem",lineHeight:1.5},button:{fontWeight:500,textTransform:"none"}},shape:{borderRadius:8},spacing:8,components:{MuiButton:{styleOverrides:{root:{borderRadius:8,textTransform:"none",fontWeight:500,boxShadow:"none","&:hover":{boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"}},contained:{"&:hover":{boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)"}}}},MuiCard:{styleOverrides:{root:{borderRadius:12,boxShadow:"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)","&:hover":{boxShadow:"0 4px 6px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08)"}}}},MuiChip:{styleOverrides:{root:{borderRadius:16}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}}}}),u=(e,t="")=>({VITE_API_URL:"http://localhost:3000",VITE_AUTH0_DOMAIN:"dev-nouri.eu.auth0.com",VITE_AUTH0_CLIENT_ID:"test-client-id",VITE_APP_VERSION:"1.0.0",VITE_NODE_ENV:"development",VITE_AUTH0_AUDIENCE:"https://api.nouri.tn",VITE_DEFAULT_LANGUAGE:"fr",VITE_DEFAULT_CURRENCY:"TND",VITE_ENABLE_CHATBOT:"true",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1}[e]||t),R=(e,t=!1)=>{const r={VITE_API_URL:"http://localhost:3000",VITE_AUTH0_DOMAIN:"dev-nouri.eu.auth0.com",VITE_AUTH0_CLIENT_ID:"test-client-id",VITE_APP_VERSION:"1.0.0",VITE_NODE_ENV:"development",VITE_AUTH0_AUDIENCE:"https://api.nouri.tn",VITE_DEFAULT_LANGUAGE:"fr",VITE_DEFAULT_CURRENCY:"TND",VITE_ENABLE_CHATBOT:"true",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1}[e];return void 0===r?t:"true"===r||"1"===r},O={name:"Nouri - Coach Financier IA",version:u("VITE_APP_VERSION","1.0.0"),environment:u("VITE_NODE_ENV","development"),baseUrl:u("VITE_BASE_URL",window.location.origin),supportEmail:u("VITE_SUPPORT_EMAIL","<EMAIL>"),isDevelopment:"development"===u("VITE_NODE_ENV","development"),isProduction:"production"===u("VITE_NODE_ENV","development")};u("VITE_API_URL","http://localhost:3000"),parseInt(u("VITE_API_TIMEOUT","30000"),10),parseInt(u("VITE_API_RETRY_ATTEMPTS","3"),10),parseInt(u("VITE_API_RETRY_DELAY","1000"),10),u("VITE_AUTH0_DOMAIN",""),u("VITE_AUTH0_CLIENT_ID",""),u("VITE_AUTH0_AUDIENCE","https://api.nouri.tn"),u("VITE_AUTH0_REDIRECT_URI",window.location.origin),u("VITE_AUTH0_LOGOUT_URI",window.location.origin),u("VITE_DEFAULT_LANGUAGE","fr"),u("VITE_SUPPORTED_LANGUAGES","fr,ar,en").split(","),u("VITE_FALLBACK_LANGUAGE","fr"),R("VITE_DETECT_BROWSER_LANGUAGE",!0),u("VITE_DEFAULT_CURRENCY","TND"),u("VITE_SUPPORTED_CURRENCIES","TND,EUR,USD").split(","),parseInt(u("VITE_DECIMAL_PLACES","3"),10),R("VITE_ENABLE_PWA",!0),R("VITE_ENABLE_NOTIFICATIONS",!0),R("VITE_ENABLE_ANALYTICS",!1),R("VITE_ENABLE_CHATBOT",!0),R("VITE_ENABLE_EDUCATION",!0),R("VITE_ENABLE_REPORTING",!0),R("VITE_ENABLE_DARK_MODE",!0),R("VITE_ENABLE_OFFLINE_MODE",!0),parseInt(u("VITE_SIDEBAR_WIDTH","280"),10),parseInt(u("VITE_HEADER_HEIGHT","64"),10),parseInt(u("VITE_MOBILE_BREAKPOINT","768"),10),parseInt(u("VITE_ANIMATION_DURATION","300"),10),parseInt(u("VITE_DEBOUNCE_DELAY","300"),10),parseInt(u("VITE_USER_DATA_TTL","300000"),10),parseInt(u("VITE_TRANSACTIONS_TTL","60000"),10),parseInt(u("VITE_BUDGETS_TTL","300000"),10),parseInt(u("VITE_STATIC_DATA_TTL","3600000"),10),R("VITE_ENABLE_CSP",!0),parseInt(u("VITE_SESSION_TIMEOUT","1800000"),10),parseInt(u("VITE_MAX_LOGIN_ATTEMPTS","5"),10),parseInt(u("VITE_PASSWORD_MIN_LENGTH","8"),10),R("VITE_ENABLE_ERROR_REPORTING",!1),R("VITE_ENABLE_PERFORMANCE_MONITORING",!1),u("VITE_SENTRY_DSN",""),u("VITE_LOG_LEVEL","info"),u("VITE_GOOGLE_ANALYTICS_ID",""),u("VITE_HOTJAR_ID",""),u("VITE_INTERCOM_APP_ID",""),u("VITE_CRISP_WEBSITE_ID",""),R("VITE_CRISP_ENABLED",!1),parseInt(u("VITE_MAX_FILE_UPLOAD_SIZE","5242880"),10),parseInt(u("VITE_MAX_TRANSACTIONS_PER_PAGE","50"),10),parseInt(u("VITE_MAX_BUDGET_CATEGORIES","20"),10),parseInt(u("VITE_MAX_SAVINGS_GOALS","10"),10),u("VITE_PRIVACY_POLICY_URL","/privacy"),u("VITE_TERMS_OF_SERVICE_URL","/terms"),u("VITE_HELP_URL","/help"),u("VITE_CONTACT_URL","/contact"),u("VITE_BLOG_URL","https://blog.nouri.tn"),u("VITE_DOCUMENTATION_URL","https://docs.nouri.tn");if(O.isDevelopment){["VITE_API_URL","VITE_AUTH0_DOMAIN","VITE_AUTH0_CLIENT_ID"].filter((e=>!u(e))).length}const V={AUTH_TOKEN:"nouri_auth_token",USER_PREFERENCES:"nouri_user_preferences",LANGUAGE:"nouri_language",THEME:"nouri_theme",ONBOARDING_COMPLETED:"nouri_onboarding_completed",LAST_SYNC:"nouri_last_sync"},N={HOME:"/",LOGIN:"/login",REGISTER:"/register",DASHBOARD:"/dashboard",TRANSACTIONS:"/transactions",BUDGETS:"/budgets",SAVINGS:"/savings",CHATBOT:"/chat",EDUCATION:"/education",REPORTS:"/reports",SETTINGS:"/settings",PROFILE:"/profile",HELP:"/help"},L=T.createContext({isAuthenticated:!1,login:()=>{},logout:()=>{}}),g=T.lazy((()=>f((()=>import("./HomePage-0ada9779.js")),["assets/HomePage-0ada9779.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js","assets/index.module-cdaeb88a.js"]))),S=T.lazy((()=>f((()=>import("./LoginPage-ea398c22.js")),["assets/LoginPage-ea398c22.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),P=T.lazy((()=>f((()=>import("./RegisterPage-21442259.js")),["assets/RegisterPage-21442259.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),D=T.lazy((()=>f((()=>import("./DashboardPage-fc988770.js")),["assets/DashboardPage-fc988770.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js","assets/index.module-cdaeb88a.js","assets/utils-vendor-4cf31f2d.js"]))),x=T.lazy((()=>f((()=>import("./TransactionsPage-e7d76e58.js")),["assets/TransactionsPage-e7d76e58.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),b=T.lazy((()=>f((()=>import("./BudgetsPage-77025a69.js")),["assets/BudgetsPage-77025a69.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),v=T.lazy((()=>f((()=>import("./SavingsPage-5a8403c6.js")),["assets/SavingsPage-5a8403c6.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),U=T.lazy((()=>f((()=>import("./ChatbotPage-47259bf8.js")),["assets/ChatbotPage-47259bf8.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),C=T.lazy((()=>f((()=>import("./EducationPage-69494e4e.js")),["assets/EducationPage-69494e4e.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),y=T.lazy((()=>f((()=>import("./ReportsPage-96f2f054.js")),["assets/ReportsPage-96f2f054.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),j=T.lazy((()=>f((()=>import("./SettingsPage-c9b5b38a.js")),["assets/SettingsPage-c9b5b38a.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),B=T.lazy((()=>f((()=>import("./ProfilePage-208e613c.js")),["assets/ProfilePage-208e613c.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),H=T.lazy((()=>f((()=>import("./HelpPage-3e1a0643.js")),["assets/HelpPage-3e1a0643.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),G=T.lazy((()=>f((()=>import("./NotFoundPage-6352ec0f.js")),["assets/NotFoundPage-6352ec0f.js","assets/mui-vendor-b761306f.js","assets/react-vendor-2f216e43.js"]))),M=()=>n.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"100vh",gap:2,background:"linear-gradient(135deg, #1976d2 0%, #1565c0 100%)",color:"white"},children:[n.jsx(E,{size:40,sx:{color:"white"}}),n.jsxs(i,{sx:{textAlign:"center"},children:[n.jsx(a,{variant:"h3",sx:{mb:1},children:"🇹🇳"}),n.jsx(a,{variant:"h6",sx:{fontWeight:"bold",mb:1},children:"Nouri"}),n.jsx(a,{variant:"body2",sx:{opacity:.9},children:"Chargement de votre coach financier..."})]})]}),w=()=>{const[e,t]=I.useState(!1),r={isAuthenticated:e,login:()=>t(!0),logout:()=>t(!1)};return n.jsx(L.Provider,{value:r,children:n.jsxs(s,{theme:m,children:[n.jsx(o,{}),n.jsx(c,{children:n.jsx(I.Suspense,{fallback:n.jsx(M,{}),children:n.jsxs(l,{children:[n.jsx(d,{path:N.HOME,element:n.jsx(g,{})}),n.jsx(d,{path:N.LOGIN,element:n.jsx(S,{})}),n.jsx(d,{path:N.REGISTER,element:n.jsx(P,{})}),n.jsx(d,{path:N.DASHBOARD,element:n.jsx(D,{})}),n.jsx(d,{path:N.TRANSACTIONS,element:n.jsx(x,{})}),n.jsx(d,{path:N.BUDGETS,element:n.jsx(b,{})}),n.jsx(d,{path:N.SAVINGS,element:n.jsx(v,{})}),n.jsx(d,{path:N.CHATBOT,element:n.jsx(U,{})}),n.jsx(d,{path:N.EDUCATION,element:n.jsx(C,{})}),n.jsx(d,{path:N.REPORTS,element:n.jsx(y,{})}),n.jsx(d,{path:N.SETTINGS,element:n.jsx(j,{})}),n.jsx(d,{path:N.PROFILE,element:n.jsx(B,{})}),n.jsx(d,{path:N.HELP,element:n.jsx(H,{})}),n.jsx(d,{path:"*",element:n.jsx(G,{})})]})})})]})})};new class{constructor(){t(this,"metrics",{}),t(this,"observers",[]),this.initializeObservers()}initializeObservers(){if("PerformanceObserver"in window)try{const e=new PerformanceObserver((e=>{for(const t of e.getEntries())"first-contentful-paint"===t.name&&(this.metrics.fcp=t.startTime)}));e.observe({entryTypes:["paint"]}),this.observers.push(e);const t=new PerformanceObserver((e=>{const t=e.getEntries(),r=t[t.length-1];this.metrics.lcp=r.startTime}));t.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(t);const r=new PerformanceObserver((e=>{for(const t of e.getEntries())this.metrics.fid=t.processingStart-t.startTime}));r.observe({entryTypes:["first-input"]}),this.observers.push(r);const n=new PerformanceObserver((e=>{let t=0;for(const r of e.getEntries())r.hadRecentInput||(t+=r.value);this.metrics.cls=t}));n.observe({entryTypes:["layout-shift"]}),this.observers.push(n)}catch(e){}window.addEventListener("load",(()=>{setTimeout((()=>{const e=performance.getEntriesByType("navigation")[0];e&&(this.metrics.ttfb=e.responseStart-e.requestStart,this.metrics.domContentLoaded=e.domContentLoadedEventEnd-e.domContentLoadedEventStart,this.metrics.loadComplete=e.loadEventEnd-e.loadEventStart),this.reportMetrics()}),0)}))}reportMetrics(){}formatTime(e){return e?`${e.toFixed(2)}ms`:"N/A"}scorePerformance(){const e={fcp:this.scoreFCP(this.metrics.fcp),lcp:this.scoreLCP(this.metrics.lcp),fid:this.scoreFID(this.metrics.fid),cls:this.scoreCLS(this.metrics.cls)};Object.values(e).reduce(((e,t)=>e+t),0),Object.values(e).length}scoreFCP(e){return e?e<=1800?100:e<=3e3?50:0:0}scoreLCP(e){return e?e<=2500?100:e<=4e3?50:0:0}scoreFID(e){return e?e<=100?100:e<=300?50:0:100}scoreCLS(e){return e?e<=.1?100:e<=.25?50:0:100}getScoreLabel(e){return e>=90?"🟢 Good":e>=50?"🟡 Needs Improvement":"🔴 Poor"}getMetrics(){return{...this.metrics}}destroy(){this.observers.forEach((e=>e.disconnect())),this.observers=[]}};p.createRoot(document.getElementById("root")).render(n.jsx(T.StrictMode,{children:n.jsx(w,{})}));export{L as A,N as R,V as S};
