/**
 * Store principal de l'application Nouri
 * Gestion de l'état global et des données utilisateur
 */

import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import { STORAGE_KEYS } from '@/config'

// Types
interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  profilePicture?: string
  language: string
  currency: string
  timezone: string
  onboardingCompleted: boolean
  kycStatus: 'pending' | 'verified' | 'rejected'
  riskProfile?: string
  createdAt: string
  lastLoginAt?: string
}

interface Account {
  id: string
  accountName: string
  accountType: 'checking' | 'savings' | 'credit'
  balance: number
  currency: string
  bankName: string
  isActive: boolean
  lastSyncAt?: string
}

interface DashboardStats {
  totalBalance: number
  weeklySpending: number
  unreadNotifications: number
  activeAccounts: number
  activeBudgets: number
  activeSavingsGoals: number
}

interface AppState {
  // État de l'application
  isInitialized: boolean
  isLoading: boolean
  error: string | null
  
  // Données utilisateur
  user: User | null
  accounts: Account[]
  dashboardStats: DashboardStats | null
  
  // État UI
  sidebarOpen: boolean
  currentPage: string
  
  // Notifications
  notifications: any[]
  unreadCount: number
  
  // Synchronisation
  lastSyncAt: string | null
  isSyncing: boolean
}

interface AppActions {
  // Actions d'initialisation
  initializeApp: () => void
  setInitialized: (initialized: boolean) => void
  
  // Actions utilisateur
  setUser: (user: User | null) => void
  updateUser: (updates: Partial<User>) => void
  
  // Actions comptes
  setAccounts: (accounts: Account[]) => void
  addAccount: (account: Account) => void
  updateAccount: (accountId: string, updates: Partial<Account>) => void
  removeAccount: (accountId: string) => void
  
  // Actions dashboard
  setDashboardStats: (stats: DashboardStats) => void
  
  // Actions UI
  setSidebarOpen: (open: boolean) => void
  toggleSidebar: () => void
  setCurrentPage: (page: string) => void
  
  // Actions notifications
  setNotifications: (notifications: any[]) => void
  addNotification: (notification: any) => void
  markNotificationAsRead: (notificationId: string) => void
  markAllNotificationsAsRead: () => void
  removeNotification: (notificationId: string) => void
  
  // Actions de synchronisation
  setSyncing: (syncing: boolean) => void
  setLastSyncAt: (timestamp: string) => void
  
  // Actions d'erreur
  setError: (error: string | null) => void
  clearError: () => void
  
  // Actions de chargement
  setLoading: (loading: boolean) => void
  
  // Actions de réinitialisation
  resetStore: () => void
}

type AppStore = AppState & AppActions

// État initial
const initialState: AppState = {
  isInitialized: false,
  isLoading: false,
  error: null,
  user: null,
  accounts: [],
  dashboardStats: null,
  sidebarOpen: true,
  currentPage: 'dashboard',
  notifications: [],
  unreadCount: 0,
  lastSyncAt: null,
  isSyncing: false
}

// Store principal
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      immer((set) => ({
        ...initialState,

        // Actions d'initialisation
        initializeApp: () => {
          set((state) => {
            state.isLoading = true
            state.error = null
          })

          // Simuler l'initialisation
          setTimeout(() => {
            set((state) => {
              state.isInitialized = true
              state.isLoading = false
            })
          }, 1000)
        },

        setInitialized: (initialized) => {
          set((state) => {
            state.isInitialized = initialized
          })
        },

        // Actions utilisateur
        setUser: (user) => {
          set((state) => {
            state.user = user
          })
        },

        updateUser: (updates) => {
          set((state) => {
            if (state.user) {
              Object.assign(state.user, updates)
            }
          })
        },

        // Actions comptes
        setAccounts: (accounts) => {
          set((state) => {
            state.accounts = accounts
          })
        },

        addAccount: (account) => {
          set((state) => {
            state.accounts.push(account)
          })
        },

        updateAccount: (accountId, updates) => {
          set((state) => {
            const accountIndex = state.accounts.findIndex((acc: any) => acc.id === accountId)
            if (accountIndex !== -1) {
              Object.assign(state.accounts[accountIndex], updates)
            }
          })
        },

        removeAccount: (accountId) => {
          set((state) => {
            state.accounts = state.accounts.filter((acc: any) => acc.id !== accountId)
          })
        },

        // Actions dashboard
        setDashboardStats: (stats) => {
          set((state) => {
            state.dashboardStats = stats
          })
        },

        // Actions UI
        setSidebarOpen: (open) => {
          set((state) => {
            state.sidebarOpen = open
          })
        },

        toggleSidebar: () => {
          set((state) => {
            state.sidebarOpen = !state.sidebarOpen
          })
        },

        setCurrentPage: (page) => {
          set((state) => {
            state.currentPage = page
          })
        },

        // Actions notifications
        setNotifications: (notifications) => {
          set((state) => {
            state.notifications = notifications
            state.unreadCount = notifications.filter(n => !n.isRead).length
          })
        },

        addNotification: (notification) => {
          set((state) => {
            state.notifications.unshift(notification)
            if (!notification.isRead) {
              state.unreadCount += 1
            }
          })
        },

        markNotificationAsRead: (notificationId) => {
          set((state) => {
            const notification = state.notifications.find((n: any) => n.id === notificationId)
            if (notification && !notification.isRead) {
              notification.isRead = true
              state.unreadCount -= 1
            }
          })
        },

        markAllNotificationsAsRead: () => {
          set((state) => {
            state.notifications.forEach((notification: any) => {
              notification.isRead = true
            })
            state.unreadCount = 0
          })
        },

        removeNotification: (notificationId) => {
          set((state) => {
            const notificationIndex = state.notifications.findIndex(n => n.id === notificationId)
            if (notificationIndex !== -1) {
              const notification = state.notifications[notificationIndex]
              if (!notification.isRead) {
                state.unreadCount -= 1
              }
              state.notifications.splice(notificationIndex, 1)
            }
          })
        },

        // Actions de synchronisation
        setSyncing: (syncing) => {
          set((state) => {
            state.isSyncing = syncing
          })
        },

        setLastSyncAt: (timestamp) => {
          set((state) => {
            state.lastSyncAt = timestamp
          })
        },

        // Actions d'erreur
        setError: (error) => {
          set((state) => {
            state.error = error
          })
        },

        clearError: () => {
          set((state) => {
            state.error = null
          })
        },

        // Actions de chargement
        setLoading: (loading) => {
          set((state) => {
            state.isLoading = loading
          })
        },

        // Actions de réinitialisation
        resetStore: () => {
          set(() => ({ ...initialState }))
        }
      })),
      {
        name: STORAGE_KEYS.USER_PREFERENCES,
        partialize: (state) => ({
          sidebarOpen: state.sidebarOpen,
          currentPage: state.currentPage,
          lastSyncAt: state.lastSyncAt
        })
      }
    ),
    {
      name: 'app-store'
    }
  )
)

// Sélecteurs utiles
export const useUser = () => useAppStore((state) => state.user)
export const useAccounts = () => useAppStore((state) => state.accounts)
export const useDashboardStats = () => useAppStore((state) => state.dashboardStats)
export const useNotifications = () => useAppStore((state) => ({
  notifications: state.notifications,
  unreadCount: state.unreadCount
}))
export const useAppLoading = () => useAppStore((state) => state.isLoading)
export const useAppError = () => useAppStore((state) => state.error)
export const useSidebar = () => useAppStore((state) => ({
  isOpen: state.sidebarOpen,
  toggle: state.toggleSidebar,
  setOpen: state.setSidebarOpen
}))

export default useAppStore
