/**
 * Routes de notifications pour Nouri - Proxy vers le service notification
 */

import { Router, Request, Response } from 'express';
import axios from 'axios';
import { config } from '../config/config';
import { logger } from '../utils/logger';

const router = Router();

const proxyToNotificationService = async (req: Request, res: Response, endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET') => {
  try {
    const url = `${config.services.notificationService}${endpoint}`;
    
    const axiosConfig = {
      method,
      url,
      headers: {
        'Content-Type': 'application/json',
        'X-User-ID': req.user!.id,
        'X-User-Email': req.user!.email,
        'Authorization': req.headers.authorization
      },
      ...(method !== 'GET' && { data: req.body })
    };

    const response = await axios(axiosConfig);
    res.status(response.status).json(response.data);

  } catch (error) {
    if (axios.isAxiosError(error) && error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      logger.error('Erreur lors du proxy vers le service notification:', error);
      res.status(500).json({
        error: 'Erreur de communication avec le service notification',
        code: 'NOTIFICATION_SERVICE_ERROR'
      });
    }
  }
};

// Routes des notifications
router.get('/', async (req: Request, res: Response) => {
  await proxyToNotificationService(req, res, '/notifications');
});

router.put('/:notificationId/read', async (req: Request, res: Response) => {
  await proxyToNotificationService(req, res, `/notifications/${req.params.notificationId}/read`, 'PUT');
});

router.put('/mark-all-read', async (req: Request, res: Response) => {
  await proxyToNotificationService(req, res, '/notifications/mark-all-read', 'PUT');
});

router.delete('/:notificationId', async (req: Request, res: Response) => {
  await proxyToNotificationService(req, res, `/notifications/${req.params.notificationId}`, 'DELETE');
});

export default router;
