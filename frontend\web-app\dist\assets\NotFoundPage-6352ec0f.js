import{j as e,B as t,b as r,f as n,H as i}from"./mui-vendor-b761306f.js";import{u as a}from"./react-vendor-2f216e43.js";const o=()=>{const o=a();return e.jsxs(t,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",p:3},children:[e.jsx(r,{variant:"h1",sx:{fontSize:"6rem",fontWeight:"bold",color:"primary.main",mb:2},children:"🇹🇳 404"}),e.jsx(r,{variant:"h4",gutterBottom:!0,children:"Page non trouvée"}),e.jsx(r,{variant:"body1",color:"text.secondary",sx:{mb:4,maxWidth:500},children:"La page que vous recherchez n'existe pas ou a été déplacée. Retournez à l'accueil pour continuer à utiliser Nouri, votre assistant financier."}),e.jsx(n,{variant:"contained",startIcon:e.jsx(i,{}),onClick:()=>o("/"),children:"Retour à l'accueil"})]})};export{o as NotFoundPage,o as default};
