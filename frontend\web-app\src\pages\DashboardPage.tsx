/**
 * Page du tableau de bord principal de Nouri
 * Vue d'ensemble des finances de l'utilisateur
 */

import React from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Avatar,
  Chip,
  Button,
  LinearProgress,
  IconButton
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalance as AccountBalanceIcon,
  Receipt as ReceiptIcon,
  Savings as SavingsIcon,
  Add as AddIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material'

import { Helmet } from 'react-helmet-async'

// Hooks
import { useUser, useDashboardStats } from '@/store/useAppStore'
import { useThemeStore } from '@/store/useThemeStore'

// Composant de carte de statistique
interface StatCardProps {
  title: string
  value: string | number
  subtitle?: string
  icon: React.ReactNode
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error'
  trend?: 'up' | 'down' | 'neutral'
  trendValue?: string
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color = 'primary',
  trend,
  trendValue
}) => {
  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: `${color}.main`,
              color: `${color}.contrastText`,
              mr: 2
            }}
          >
            {icon}
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h4" component="div" sx={{ fontWeight: 'bold' }}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
        </Box>
        
        {subtitle && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {subtitle}
          </Typography>
        )}
        
        {trend && trendValue && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {trend === 'up' ? (
              <TrendingUpIcon color="success" fontSize="small" />
            ) : trend === 'down' ? (
              <TrendingDownIcon color="error" fontSize="small" />
            ) : null}
            <Typography
              variant="caption"
              color={trend === 'up' ? 'success.main' : trend === 'down' ? 'error.main' : 'text.secondary'}
            >
              {trendValue}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  )
}

// Composant de carte d'objectif d'épargne
interface SavingsGoalCardProps {
  name: string
  current: number
  target: number
  currency: string
  category?: string
}

const SavingsGoalCard: React.FC<SavingsGoalCardProps> = ({
  name,
  current,
  target,
  currency,
  category
}) => {
  const progress = (current / target) * 100

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Typography variant="h6" component="div">
            {name}
          </Typography>
          {category && (
            <Chip label={category} size="small" color="primary" variant="outlined" />
          )}
        </Box>
        
        <Box sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              {current.toLocaleString()} {currency}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {target.toLocaleString()} {currency}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={Math.min(progress, 100)}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'grey.200',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4
              }
            }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
            {progress.toFixed(1)}% atteint
          </Typography>
        </Box>
      </CardContent>
    </Card>
  )
}

export const DashboardPage: React.FC = () => {
  const user = useUser()
  const dashboardStats = useDashboardStats()
  const { currency } = useThemeStore()

  // Données de démonstration
  const mockStats = {
    totalBalance: 15420.50,
    weeklySpending: 1250.30,
    unreadNotifications: 3,
    activeAccounts: 2,
    activeBudgets: 4,
    activeSavingsGoals: 2
  }

  const mockSavingsGoals = [
    {
      name: 'Voyage en Europe',
      current: 2500,
      target: 5000,
      currency: 'TND',
      category: 'Voyage'
    },
    {
      name: 'Fonds d\'urgence',
      current: 8000,
      target: 10000,
      currency: 'TND',
      category: 'Urgence'
    }
  ]

  const stats = dashboardStats || mockStats

  return (
    <>
      <Helmet>
        <title>Tableau de bord - Nouri</title>
        <meta name="description" content="Vue d'ensemble de vos finances avec Nouri" />
      </Helmet>

      <Box>
        {/* En-tête */}
        <Box sx={{ mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 'bold', mb: 1 }}>
                Bonjour {user?.firstName || 'Utilisateur'} 👋
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Voici un aperçu de vos finances aujourd'hui
              </Typography>
            </Box>
            <IconButton color="primary" aria-label="actualiser">
              <RefreshIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Statistiques principales */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Solde total"
              value={`${stats.totalBalance.toLocaleString()} ${currency}`}
              icon={<AccountBalanceIcon />}
              color="primary"
              trend="up"
              trendValue="+2.5% ce mois"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Dépenses cette semaine"
              value={`${stats.weeklySpending.toLocaleString()} ${currency}`}
              icon={<ReceiptIcon />}
              color="warning"
              trend="down"
              trendValue="-15% vs semaine dernière"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Objectifs d'épargne"
              value={stats.activeSavingsGoals}
              subtitle="objectifs actifs"
              icon={<SavingsIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Budgets actifs"
              value={stats.activeBudgets}
              subtitle="budgets en cours"
              icon={<AccountBalanceIcon />}
              color="secondary"
            />
          </Grid>
        </Grid>

        {/* Contenu principal */}
        <Grid container spacing={3}>
          {/* Objectifs d'épargne */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6" component="h2">
                Objectifs d'épargne
              </Typography>
              <Button
                startIcon={<AddIcon />}
                variant="outlined"
                size="small"
              >
                Nouvel objectif
              </Button>
            </Box>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {mockSavingsGoals.map((goal, index) => (
                <SavingsGoalCard key={index} {...goal} />
              ))}
            </Box>
          </Grid>

          {/* Transactions récentes */}
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="h6" component="h2">
                Transactions récentes
              </Typography>
              <Button variant="text" size="small">
                Voir tout
              </Button>
            </Box>
            <Card>
              <CardContent>
                <Typography variant="body2" color="text.secondary" align="center" sx={{ py: 4 }}>
                  Aucune transaction récente
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Actions rapides */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" component="h2" sx={{ mb: 2 }}>
            Actions rapides
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                fullWidth
                sx={{ py: 2 }}
                startIcon={<AddIcon />}
              >
                Nouveau budget
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                fullWidth
                sx={{ py: 2 }}
                startIcon={<SavingsIcon />}
              >
                Objectif d'épargne
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                fullWidth
                sx={{ py: 2 }}
                startIcon={<RefreshIcon />}
              >
                Synchroniser
              </Button>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                variant="outlined"
                fullWidth
                sx={{ py: 2 }}
                startIcon={<ReceiptIcon />}
              >
                Voir rapports
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  )
}

export default DashboardPage
