import{g as t,b as e,r}from"./react-vendor-2f216e43.js";import{r as n}from"./mui-vendor-b761306f.js";var i="undefined"!=typeof Element,o="function"==typeof Map,a="function"==typeof Set,s="function"==typeof ArrayBuffer&&!!ArrayBuffer.isView;function c(t,e){if(t===e)return!0;if(t&&e&&"object"==typeof t&&"object"==typeof e){if(t.constructor!==e.constructor)return!1;var r,n,u,l;if(Array.isArray(t)){if((r=t.length)!=e.length)return!1;for(n=r;0!==n--;)if(!c(t[n],e[n]))return!1;return!0}if(o&&t instanceof Map&&e instanceof Map){if(t.size!==e.size)return!1;for(l=t.entries();!(n=l.next()).done;)if(!e.has(n.value[0]))return!1;for(l=t.entries();!(n=l.next()).done;)if(!c(n.value[1],e.get(n.value[0])))return!1;return!0}if(a&&t instanceof Set&&e instanceof Set){if(t.size!==e.size)return!1;for(l=t.entries();!(n=l.next()).done;)if(!e.has(n.value[0]))return!1;return!0}if(s&&ArrayBuffer.isView(t)&&ArrayBuffer.isView(e)){if((r=t.length)!=e.length)return!1;for(n=r;0!==n--;)if(t[n]!==e[n])return!1;return!0}if(t.constructor===RegExp)return t.source===e.source&&t.flags===e.flags;if(t.valueOf!==Object.prototype.valueOf&&"function"==typeof t.valueOf&&"function"==typeof e.valueOf)return t.valueOf()===e.valueOf();if(t.toString!==Object.prototype.toString&&"function"==typeof t.toString&&"function"==typeof e.toString)return t.toString()===e.toString();if((r=(u=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(n=r;0!==n--;)if(!Object.prototype.hasOwnProperty.call(e,u[n]))return!1;if(i&&t instanceof Element)return!1;for(n=r;0!==n--;)if(("_owner"!==u[n]&&"__v"!==u[n]&&"__o"!==u[n]||!t.$$typeof)&&!c(t[u[n]],e[u[n]]))return!1;return!0}return t!=t&&e!=e}const u=t((function(t,e){try{return c(t,e)}catch(r){if((r.message||"").match(/stack|recursion/i))return!1;throw r}}));const l=t((function(t,e,r,n,i,o,a,s){if(!t){var c;if(void 0===e)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[r,n,i,o,a,s],l=0;(c=new Error(e.replace(/%s/g,(function(){return u[l++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}));const f=t((function(t,e,r,n){var i=r?r.call(n,t,e):void 0;if(void 0!==i)return!!i;if(t===e)return!0;if("object"!=typeof t||!t||"object"!=typeof e||!e)return!1;var o=Object.keys(t),a=Object.keys(e);if(o.length!==a.length)return!1;for(var s=Object.prototype.hasOwnProperty.bind(e),c=0;c<o.length;c++){var u=o[c];if(!s(u))return!1;var l=t[u],f=e[u];if(!1===(i=r?r.call(n,l,f,u):void 0)||void 0===i&&l!==f)return!1}return!0}));function p(){return p=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},p.apply(this,arguments)}function d(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,h(t,e)}function h(t,e){return(h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function y(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)e.indexOf(r=o[n])>=0||(i[r]=t[r]);return i}var m={BASE:"base",BODY:"body",HEAD:"head",HTML:"html",LINK:"link",META:"meta",NOSCRIPT:"noscript",SCRIPT:"script",STYLE:"style",TITLE:"title",FRAGMENT:"Symbol(react.fragment)"},T={rel:["amphtml","canonical","alternate"]},g={type:["application/ld+json"]},v={charset:"",name:["robots","description"],property:["og:type","og:title","og:url","og:image","og:image:alt","og:description","twitter:url","twitter:title","twitter:description","twitter:image","twitter:image:alt","twitter:card","twitter:site"]},b=Object.keys(m).map((function(t){return m[t]})),A={accesskey:"accessKey",charset:"charSet",class:"className",contenteditable:"contentEditable",contextmenu:"contextMenu","http-equiv":"httpEquiv",itemprop:"itemProp",tabindex:"tabIndex"},O=Object.keys(A).reduce((function(t,e){return t[A[e]]=e,t}),{}),C=function(t,e){for(var r=t.length-1;r>=0;r-=1){var n=t[r];if(Object.prototype.hasOwnProperty.call(n,e))return n[e]}return null},S=function(t){var e=C(t,m.TITLE),r=C(t,"titleTemplate");if(Array.isArray(e)&&(e=e.join("")),r&&e)return r.replace(/%s/g,(function(){return e}));var n=C(t,"defaultTitle");return e||n||void 0},E=function(t){return C(t,"onChangeClientState")||function(){}},j=function(t,e){return e.filter((function(e){return void 0!==e[t]})).map((function(e){return e[t]})).reduce((function(t,e){return p({},t,e)}),{})},w=function(t,e){return e.filter((function(t){return void 0!==t[m.BASE]})).map((function(t){return t[m.BASE]})).reverse().reduce((function(e,r){if(!e.length)for(var n=Object.keys(r),i=0;i<n.length;i+=1){var o=n[i].toLowerCase();if(-1!==t.indexOf(o)&&r[o])return e.concat(r)}return e}),[])},x=function(t,e,r){var n={};return r.filter((function(e){return!!Array.isArray(e[t])||(void 0!==e[t]&&console&&console.warn,!1)})).map((function(e){return e[t]})).reverse().reduce((function(t,r){var i={};r.filter((function(t){for(var r,o=Object.keys(t),a=0;a<o.length;a+=1){var s=o[a],c=s.toLowerCase();-1===e.indexOf(c)||"rel"===r&&"canonical"===t[r].toLowerCase()||"rel"===c&&"stylesheet"===t[c].toLowerCase()||(r=c),-1===e.indexOf(s)||"innerHTML"!==s&&"cssText"!==s&&"itemprop"!==s||(r=s)}if(!r||!t[r])return!1;var u=t[r].toLowerCase();return n[r]||(n[r]={}),i[r]||(i[r]={}),!n[r][u]&&(i[r][u]=!0,!0)})).reverse().forEach((function(e){return t.push(e)}));for(var o=Object.keys(i),a=0;a<o.length;a+=1){var s=o[a],c=p({},n[s],i[s]);n[s]=c}return t}),[]).reverse()},P=function(t,e){if(Array.isArray(t)&&t.length)for(var r=0;r<t.length;r+=1)if(t[r][e])return!0;return!1},I=function(t){return Array.isArray(t)?t.join(""):t},L=function(t,e){return Array.isArray(t)?t.reduce((function(t,r){return function(t,e){for(var r=Object.keys(t),n=0;n<r.length;n+=1)if(e[r[n]]&&e[r[n]].includes(t[r[n]]))return!0;return!1}(r,e)?t.priority.push(r):t.default.push(r),t}),{priority:[],default:[]}):{default:t}},M=function(t,e){var r;return p({},t,((r={})[e]=void 0,r))},k=[m.NOSCRIPT,m.SCRIPT,m.STYLE],H=function(t,e){return void 0===e&&(e=!0),!1===e?String(t):String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")},N=function(t){return Object.keys(t).reduce((function(e,r){var n=void 0!==t[r]?r+'="'+t[r]+'"':""+r;return e?e+" "+n:n}),"")},R=function(t,e){return void 0===e&&(e={}),Object.keys(t).reduce((function(e,r){return e[A[r]||r]=t[r],e}),e)},D=function(t,r){return r.map((function(r,n){var i,o=((i={key:n})["data-rh"]=!0,i);return Object.keys(r).forEach((function(t){var e=A[t]||t;"innerHTML"===e||"cssText"===e?o.dangerouslySetInnerHTML={__html:r.innerHTML||r.cssText}:o[e]=r[t]})),e.createElement(t,o)}))},B=function(t,r,n){switch(t){case m.TITLE:return{toComponent:function(){return n=r.titleAttributes,(i={key:t=r.title})["data-rh"]=!0,o=R(n,i),[e.createElement(m.TITLE,o,t)];var t,n,i,o},toString:function(){return e=t,i=r.title,o=r.titleAttributes,a=n,s=N(o),c=I(i),s?"<"+e+' data-rh="true" '+s+">"+H(c,a)+"</"+e+">":"<"+e+' data-rh="true">'+H(c,a)+"</"+e+">";var e,i,o,a,s,c}};case"bodyAttributes":case"htmlAttributes":return{toComponent:function(){return R(r)},toString:function(){return N(r)}};default:return{toComponent:function(){return D(t,r)},toString:function(){return e=t,i=n,r.reduce((function(t,r){var n=Object.keys(r).filter((function(t){return!("innerHTML"===t||"cssText"===t)})).reduce((function(t,e){var n=void 0===r[e]?e:e+'="'+H(r[e],i)+'"';return t?t+" "+n:n}),""),o=r.innerHTML||r.cssText||"",a=-1===k.indexOf(e);return t+"<"+e+' data-rh="true" '+n+(a?"/>":">"+o+"</"+e+">")}),"");var e,i}}}},U=function(t){var e,r,n,i,o,a,s,c=t.baseTag,u=t.bodyAttributes,l=t.encode,f=t.htmlAttributes,p=t.noscriptTags,d=t.styleTags,h=t.title,y=void 0===h?"":h,b=t.titleAttributes,A=t.linkTags,O=t.metaTags,C=t.scriptTags,S={toComponent:function(){},toString:function(){return""}};if(t.prioritizeSeoTags){var E=(r=(e=t).linkTags,n=e.scriptTags,i=e.encode,o=L(e.metaTags,v),a=L(r,T),s=L(n,g),{priorityMethods:{toComponent:function(){return[].concat(D(m.META,o.priority),D(m.LINK,a.priority),D(m.SCRIPT,s.priority))},toString:function(){return B(m.META,o.priority,i)+" "+B(m.LINK,a.priority,i)+" "+B(m.SCRIPT,s.priority,i)}},metaTags:o.default,linkTags:a.default,scriptTags:s.default});S=E.priorityMethods,A=E.linkTags,O=E.metaTags,C=E.scriptTags}return{priority:S,base:B(m.BASE,c,l),bodyAttributes:B("bodyAttributes",u,l),htmlAttributes:B("htmlAttributes",f,l),link:B(m.LINK,A,l),meta:B(m.META,O,l),noscript:B(m.NOSCRIPT,p,l),script:B(m.SCRIPT,C,l),style:B(m.STYLE,d,l),title:B(m.TITLE,{title:y,titleAttributes:b},l)}},Y=[],_=function(t,e){var r=this;void 0===e&&(e="undefined"!=typeof document),this.instances=[],this.value={setHelmet:function(t){r.context.helmet=t},helmetInstances:{get:function(){return r.canUseDOM?Y:r.instances},add:function(t){(r.canUseDOM?Y:r.instances).push(t)},remove:function(t){var e=(r.canUseDOM?Y:r.instances).indexOf(t);(r.canUseDOM?Y:r.instances).splice(e,1)}}},this.context=t,this.canUseDOM=e,e||(t.helmet=U({baseTag:[],bodyAttributes:{},encodeSpecialCharacters:!0,htmlAttributes:{},linkTags:[],metaTags:[],noscriptTags:[],scriptTags:[],styleTags:[],title:"",titleAttributes:{}}))},q=e.createContext({}),z=n.shape({setHelmet:n.func,helmetInstances:n.shape({get:n.func,add:n.func,remove:n.func})}),K="undefined"!=typeof document,F=function(t){function r(e){var n;return(n=t.call(this,e)||this).helmetData=new _(n.props.context,r.canUseDOM),n}return d(r,t),r.prototype.render=function(){return e.createElement(q.Provider,{value:this.helmetData.value},this.props.children)},r}(r.Component);F.canUseDOM=K,F.propTypes={context:n.shape({helmet:n.shape()}),children:n.node.isRequired},F.defaultProps={context:{}},F.displayName="HelmetProvider";var V=function(t,e){var r,n=document.head||document.querySelector(m.HEAD),i=n.querySelectorAll(t+"[data-rh]"),o=[].slice.call(i),a=[];return e&&e.length&&e.forEach((function(e){var n=document.createElement(t);for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&("innerHTML"===i?n.innerHTML=e.innerHTML:"cssText"===i?n.styleSheet?n.styleSheet.cssText=e.cssText:n.appendChild(document.createTextNode(e.cssText)):n.setAttribute(i,void 0===e[i]?"":e[i]));n.setAttribute("data-rh","true"),o.some((function(t,e){return r=e,n.isEqualNode(t)}))?o.splice(r,1):a.push(n)})),o.forEach((function(t){return t.parentNode.removeChild(t)})),a.forEach((function(t){return n.appendChild(t)})),{oldTags:o,newTags:a}},G=function(t,e){var r=document.getElementsByTagName(t)[0];if(r){for(var n=r.getAttribute("data-rh"),i=n?n.split(","):[],o=[].concat(i),a=Object.keys(e),s=0;s<a.length;s+=1){var c=a[s],u=e[c]||"";r.getAttribute(c)!==u&&r.setAttribute(c,u),-1===i.indexOf(c)&&i.push(c);var l=o.indexOf(c);-1!==l&&o.splice(l,1)}for(var f=o.length-1;f>=0;f-=1)r.removeAttribute(o[f]);i.length===o.length?r.removeAttribute("data-rh"):r.getAttribute("data-rh")!==a.join(",")&&r.setAttribute("data-rh",a.join(","))}},$=function(t,e){var r,n,i=t.baseTag,o=t.htmlAttributes,a=t.linkTags,s=t.metaTags,c=t.noscriptTags,u=t.onChangeClientState,l=t.scriptTags,f=t.styleTags,p=t.title,d=t.titleAttributes;G(m.BODY,t.bodyAttributes),G(m.HTML,o),n=d,void 0!==(r=p)&&document.title!==r&&(document.title=I(r)),G(m.TITLE,n);var h={baseTag:V(m.BASE,i),linkTags:V(m.LINK,a),metaTags:V(m.META,s),noscriptTags:V(m.NOSCRIPT,c),scriptTags:V(m.SCRIPT,l),styleTags:V(m.STYLE,f)},y={},T={};Object.keys(h).forEach((function(t){var e=h[t],r=e.newTags,n=e.oldTags;r.length&&(y[t]=r),n.length&&(T[t]=h[t].oldTags)})),e&&e(),u(t,y,T)},W=null,J=function(t){function e(){for(var e,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return(e=t.call.apply(t,[this].concat(n))||this).rendered=!1,e}d(e,t);var r=e.prototype;return r.shouldComponentUpdate=function(t){return!f(t,this.props)},r.componentDidUpdate=function(){this.emitChange()},r.componentWillUnmount=function(){this.props.context.helmetInstances.remove(this),this.emitChange()},r.emitChange=function(){var t,e,r=this.props.context,n=r.setHelmet,i=null,o=(t=r.helmetInstances.get().map((function(t){var e=p({},t.props);return delete e.context,e})),{baseTag:w(["href"],t),bodyAttributes:j("bodyAttributes",t),defer:C(t,"defer"),encode:C(t,"encodeSpecialCharacters"),htmlAttributes:j("htmlAttributes",t),linkTags:x(m.LINK,["rel","href"],t),metaTags:x(m.META,["name","charset","http-equiv","property","itemprop"],t),noscriptTags:x(m.NOSCRIPT,["innerHTML"],t),onChangeClientState:E(t),scriptTags:x(m.SCRIPT,["src","innerHTML"],t),styleTags:x(m.STYLE,["cssText"],t),title:S(t),titleAttributes:j("titleAttributes",t),prioritizeSeoTags:P(t,"prioritizeSeoTags")});F.canUseDOM?(e=o,W&&cancelAnimationFrame(W),e.defer?W=requestAnimationFrame((function(){$(e,(function(){W=null}))})):($(e),W=null)):U&&(i=U(o)),n(i)},r.init=function(){this.rendered||(this.rendered=!0,this.props.context.helmetInstances.add(this),this.emitChange())},r.render=function(){return this.init(),null},e}(r.Component);J.propTypes={context:z.isRequired},J.displayName="HelmetDispatcher";var Q=["children"],X=["children"],Z=function(t){function r(){return t.apply(this,arguments)||this}d(r,t);var n=r.prototype;return n.shouldComponentUpdate=function(t){return!u(M(this.props,"helmetData"),M(t,"helmetData"))},n.mapNestedChildrenToProps=function(t,e){if(!e)return null;switch(t.type){case m.SCRIPT:case m.NOSCRIPT:return{innerHTML:e};case m.STYLE:return{cssText:e};default:throw new Error("<"+t.type+" /> elements are self-closing and can not contain children. Refer to our API for more information.")}},n.flattenArrayTypeChildren=function(t){var e,r=t.child,n=t.arrayTypeChildren;return p({},n,((e={})[r.type]=[].concat(n[r.type]||[],[p({},t.newChildProps,this.mapNestedChildrenToProps(r,t.nestedChildren))]),e))},n.mapObjectTypeChildren=function(t){var e,r,n=t.child,i=t.newProps,o=t.newChildProps,a=t.nestedChildren;switch(n.type){case m.TITLE:return p({},i,((e={})[n.type]=a,e.titleAttributes=p({},o),e));case m.BODY:return p({},i,{bodyAttributes:p({},o)});case m.HTML:return p({},i,{htmlAttributes:p({},o)});default:return p({},i,((r={})[n.type]=p({},o),r))}},n.mapArrayTypeChildrenToProps=function(t,e){var r=p({},e);return Object.keys(t).forEach((function(e){var n;r=p({},r,((n={})[e]=t[e],n))})),r},n.warnOnInvalidChildren=function(t,e){return l(b.some((function(e){return t.type===e})),"function"==typeof t.type?"You may be attempting to nest <Helmet> components within each other, which is not allowed. Refer to our API for more information.":"Only elements types "+b.join(", ")+" are allowed. Helmet does not support rendering <"+t.type+"> elements. Refer to our API for more information."),l(!e||"string"==typeof e||Array.isArray(e)&&!e.some((function(t){return"string"!=typeof t})),"Helmet expects a string as a child of <"+t.type+">. Did you forget to wrap your children in braces? ( <"+t.type+">{``}</"+t.type+"> ) Refer to our API for more information."),!0},n.mapChildrenToProps=function(t,r){var n=this,i={};return e.Children.forEach(t,(function(t){if(t&&t.props){var e=t.props,o=e.children,a=y(e,Q),s=Object.keys(a).reduce((function(t,e){return t[O[e]||e]=a[e],t}),{}),c=t.type;switch("symbol"==typeof c?c=c.toString():n.warnOnInvalidChildren(t,o),c){case m.FRAGMENT:r=n.mapChildrenToProps(o,r);break;case m.LINK:case m.META:case m.NOSCRIPT:case m.SCRIPT:case m.STYLE:i=n.flattenArrayTypeChildren({child:t,arrayTypeChildren:i,newChildProps:s,nestedChildren:o});break;default:r=n.mapObjectTypeChildren({child:t,newProps:r,newChildProps:s,nestedChildren:o})}}})),this.mapArrayTypeChildrenToProps(i,r)},n.render=function(){var t=this.props,r=t.children,n=y(t,X),i=p({},n),o=n.helmetData;return r&&(i=this.mapChildrenToProps(r,i)),!o||o instanceof _||(o=new _(o.context,o.instances)),o?e.createElement(J,p({},i,{context:o.value,helmetData:void 0})):e.createElement(q.Consumer,null,(function(t){return e.createElement(J,p({},i,{context:t}))}))},r}(r.Component);Z.propTypes={base:n.object,bodyAttributes:n.object,children:n.oneOfType([n.arrayOf(n.node),n.node]),defaultTitle:n.string,defer:n.bool,encodeSpecialCharacters:n.bool,htmlAttributes:n.object,link:n.arrayOf(n.object),meta:n.arrayOf(n.object),noscript:n.arrayOf(n.object),onChangeClientState:n.func,script:n.arrayOf(n.object),style:n.arrayOf(n.object),title:n.string,titleAttributes:n.object,titleTemplate:n.string,prioritizeSeoTags:n.bool,helmetData:n.object},Z.defaultProps={defer:!0,encodeSpecialCharacters:!0,prioritizeSeoTags:!1},Z.displayName="Helmet";export{Z as W};
