/**
 * Routes de vérification de santé pour Nouri
 * Monitoring et diagnostics du système
 */

import { Router, Request, Response } from 'express';
import { config } from '../config/config';
import { logger } from '../utils/logger';
import { checkDatabaseHealth, getDatabaseStats } from '../config/database';
import { checkRedisHealth, getRedisStats } from '../config/redis';

const router = Router();

/**
 * GET /api/health
 * Vérification de santé basique
 */
router.get('/', async (req: Request, res: Response) => {
  try {
    const health = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'nouri-api-gateway',
      version: config.app.version,
      environment: config.app.environment,
      uptime: process.uptime(),
      memory: {
        used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
        total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
        external: Math.round(process.memoryUsage().external / 1024 / 1024),
        rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
      },
      cpu: {
        usage: process.cpuUsage()
      }
    };

    res.json(health);
  } catch (error) {
    logger.error('Erreur lors de la vérification de santé:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: 'Erreur interne',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/detailed
 * Vérification de santé détaillée avec dépendances
 */
router.get('/detailed', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();

    // Vérification des dépendances
    const [databaseHealthy, redisHealthy] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealth()
    ]);

    const responseTime = Date.now() - startTime;
    const overallHealthy = databaseHealthy && redisHealthy;

    const health = {
      status: overallHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'nouri-api-gateway',
      version: config.app.version,
      environment: config.app.environment,
      responseTime: `${responseTime}ms`,
      uptime: process.uptime(),
      dependencies: {
        database: {
          status: databaseHealthy ? 'healthy' : 'unhealthy',
          type: 'postgresql'
        },
        cache: {
          status: redisHealthy ? 'healthy' : 'unhealthy',
          type: 'redis'
        }
      },
      system: {
        memory: {
          used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
          total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
          external: Math.round(process.memoryUsage().external / 1024 / 1024),
          rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
        },
        cpu: process.cpuUsage(),
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };

    const statusCode = overallHealthy ? 200 : 503;
    res.status(statusCode).json(health);

  } catch (error) {
    logger.error('Erreur lors de la vérification de santé détaillée:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: 'Erreur lors de la vérification des dépendances',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/database
 * Vérification spécifique de la base de données
 */
router.get('/database', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    const isHealthy = await checkDatabaseHealth();
    const responseTime = Date.now() - startTime;

    let stats = null;
    if (isHealthy) {
      try {
        stats = await getDatabaseStats();
      } catch (error) {
        logger.warn('Impossible de récupérer les statistiques de la base:', error);
      }
    }

    const health = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      type: 'postgresql',
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString(),
      connection: {
        host: config.database.host,
        port: config.database.port,
        database: config.database.name
      },
      stats
    };

    const statusCode = isHealthy ? 200 : 503;
    res.status(statusCode).json(health);

  } catch (error) {
    logger.error('Erreur lors de la vérification de la base de données:', error);
    res.status(500).json({
      status: 'unhealthy',
      type: 'postgresql',
      error: 'Erreur de connexion à la base de données',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/cache
 * Vérification spécifique du cache Redis
 */
router.get('/cache', async (req: Request, res: Response) => {
  try {
    const startTime = Date.now();
    const isHealthy = await checkRedisHealth();
    const responseTime = Date.now() - startTime;

    let stats = null;
    if (isHealthy) {
      try {
        stats = await getRedisStats();
      } catch (error) {
        logger.warn('Impossible de récupérer les statistiques Redis:', error);
      }
    }

    const health = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      type: 'redis',
      responseTime: `${responseTime}ms`,
      timestamp: new Date().toISOString(),
      connection: {
        host: config.redis.host,
        port: config.redis.port
      },
      stats
    };

    const statusCode = isHealthy ? 200 : 503;
    res.status(statusCode).json(health);

  } catch (error) {
    logger.error('Erreur lors de la vérification de Redis:', error);
    res.status(500).json({
      status: 'unhealthy',
      type: 'redis',
      error: 'Erreur de connexion à Redis',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/services
 * Vérification de la santé des microservices
 */
router.get('/services', async (req: Request, res: Response) => {
  try {
    const services = [
      { name: 'user-service', url: `${config.services.userService}/health` },
      { name: 'banking-service', url: `${config.services.bankingService}/health` },
      { name: 'ai-engine', url: `${config.services.aiEngine}/health` },
      { name: 'chatbot-service', url: `${config.services.chatbotService}/health` },
      { name: 'budget-service', url: `${config.services.budgetService}/health` },
      { name: 'notification-service', url: `${config.services.notificationService}/health` },
      { name: 'education-service', url: `${config.services.educationService}/health` },
      { name: 'reporting-service', url: `${config.services.reportingService}/health` }
    ];

    const serviceChecks = await Promise.allSettled(
      services.map(async (service) => {
        const startTime = Date.now();
        try {
          const response = await fetch(service.url, {
            method: 'GET',
            timeout: 5000 // 5 secondes timeout
          });
          const responseTime = Date.now() - startTime;
          
          return {
            name: service.name,
            status: response.ok ? 'healthy' : 'unhealthy',
            responseTime: `${responseTime}ms`,
            statusCode: response.status,
            url: service.url
          };
        } catch (error) {
          const responseTime = Date.now() - startTime;
          return {
            name: service.name,
            status: 'unhealthy',
            responseTime: `${responseTime}ms`,
            error: error instanceof Error ? error.message : 'Erreur inconnue',
            url: service.url
          };
        }
      })
    );

    const results = serviceChecks.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          name: services[index].name,
          status: 'unhealthy',
          error: 'Échec de la vérification',
          url: services[index].url
        };
      }
    });

    const healthyServices = results.filter(service => service.status === 'healthy').length;
    const totalServices = results.length;
    const overallHealthy = healthyServices === totalServices;

    const health = {
      status: overallHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      summary: {
        total: totalServices,
        healthy: healthyServices,
        unhealthy: totalServices - healthyServices
      },
      services: results
    };

    const statusCode = overallHealthy ? 200 : 207; // 207 Multi-Status pour état dégradé
    res.status(statusCode).json(health);

  } catch (error) {
    logger.error('Erreur lors de la vérification des services:', error);
    res.status(500).json({
      status: 'unhealthy',
      error: 'Erreur lors de la vérification des microservices',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /api/health/metrics
 * Métriques système pour monitoring
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const metrics = {
      timestamp: new Date().toISOString(),
      service: 'nouri-api-gateway',
      version: config.app.version,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      eventLoop: {
        delay: process.hrtime.bigint()
      },
      gc: process.memoryUsage(),
      handles: {
        active: (process as any)._getActiveHandles?.()?.length || 0,
        requests: (process as any)._getActiveRequests?.()?.length || 0
      }
    };

    res.json(metrics);
  } catch (error) {
    logger.error('Erreur lors de la récupération des métriques:', error);
    res.status(500).json({
      error: 'Erreur lors de la récupération des métriques',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;
