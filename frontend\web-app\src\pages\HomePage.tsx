/**
 * Page d'accueil de Nouri
 * Landing page pour les utilisateurs non connectés
 */

import React from 'react'
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
  Chip
} from '@mui/material'
import {
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Psychology as AIIcon,
  School as EducationIcon,
  AccountBalance as BankIcon,
  Chat as ChatIcon
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { ROUTES } from '@/config'

// Composant de fonctionnalité
interface FeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
  color?: 'primary' | 'secondary' | 'success' | 'warning'
}

const FeatureCard: React.FC<FeatureCardProps> = ({ 
  icon, 
  title, 
  description, 
  color = 'primary' 
}) => (
  <Card sx={{ height: '100%', textAlign: 'center' }}>
    <CardContent sx={{ p: 3 }}>
      <Avatar
        sx={{
          bgcolor: `${color}.main`,
          color: `${color}.contrastText`,
          width: 64,
          height: 64,
          mx: 'auto',
          mb: 2
        }}
      >
        {icon}
      </Avatar>
      <Typography variant="h6" component="h3" gutterBottom>
        {title}
      </Typography>
      <Typography variant="body2" color="text.secondary">
        {description}
      </Typography>
    </CardContent>
  </Card>
)

export const HomePage: React.FC = () => {
  const navigate = useNavigate()

  const features = [
    {
      icon: <BankIcon />,
      title: 'Gestion bancaire',
      description: 'Connectez tous vos comptes bancaires tunisiens et suivez vos finances en temps réel.',
      color: 'primary' as const
    },
    {
      icon: <AIIcon />,
      title: 'Intelligence artificielle',
      description: 'Notre IA analyse vos habitudes et vous propose des conseils personnalisés.',
      color: 'secondary' as const
    },
    {
      icon: <TrendingUpIcon />,
      title: 'Budgets intelligents',
      description: 'Créez des budgets adaptatifs qui évoluent avec vos besoins.',
      color: 'success' as const
    },
    {
      icon: <ChatIcon />,
      title: 'Assistant virtuel',
      description: 'Posez vos questions financières en français ou en arabe, 24h/24.',
      color: 'warning' as const
    },
    {
      icon: <EducationIcon />,
      title: 'Formation financière',
      description: 'Apprenez les bases de la finance avec nos modules interactifs.',
      color: 'primary' as const
    },
    {
      icon: <SecurityIcon />,
      title: 'Sécurité maximale',
      description: 'Vos données sont protégées par un chiffrement de niveau bancaire.',
      color: 'secondary' as const
    }
  ]

  return (
    <>
      <Helmet>
        <title>Nouri - Coach Financier IA pour la Tunisie</title>
        <meta 
          name="description" 
          content="Gérez vos finances intelligemment avec Nouri, votre coach financier IA. Budgets, épargne, conseils personnalisés pour le marché tunisien." 
        />
      </Helmet>

      <Box sx={{ minHeight: '100vh', backgroundColor: 'background.default' }}>
        {/* Hero Section */}
        <Box
          sx={{
            background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
            color: 'white',
            py: 8
          }}
        >
          <Container maxWidth="lg">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: { xs: 'center', md: 'left' } }}>
                  <Chip
                    label="🇹🇳 Conçu pour la Tunisie"
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      mb: 2
                    }}
                  />
                  <Typography
                    variant="h2"
                    component="h1"
                    sx={{
                      fontWeight: 'bold',
                      mb: 2,
                      fontSize: { xs: '2.5rem', md: '3.5rem' }
                    }}
                  >
                    Votre coach financier intelligent
                  </Typography>
                  <Typography
                    variant="h5"
                    sx={{
                      mb: 4,
                      opacity: 0.9,
                      fontWeight: 300
                    }}
                  >
                    Gérez vos finances, créez des budgets et atteignez vos objectifs 
                    avec l'aide de l'intelligence artificielle.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Button
                      variant="contained"
                      size="large"
                      onClick={() => navigate(ROUTES.REGISTER)}
                      sx={{
                        backgroundColor: 'white',
                        color: 'primary.main',
                        '&:hover': {
                          backgroundColor: 'grey.100'
                        }
                      }}
                    >
                      Commencer gratuitement
                    </Button>
                    <Button
                      variant="outlined"
                      size="large"
                      onClick={() => navigate(ROUTES.LOGIN)}
                      sx={{
                        borderColor: 'white',
                        color: 'white',
                        '&:hover': {
                          borderColor: 'white',
                          backgroundColor: 'rgba(255, 255, 255, 0.1)'
                        }
                      }}
                    >
                      Se connecter
                    </Button>
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    textAlign: 'center',
                    display: { xs: 'none', md: 'block' }
                  }}
                >
                  {/* Ici on pourrait ajouter une illustration ou une capture d'écran */}
                  <Box
                    sx={{
                      width: 400,
                      height: 300,
                      backgroundColor: 'rgba(255, 255, 255, 0.1)',
                      borderRadius: 4,
                      mx: 'auto',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Typography variant="h6" sx={{ opacity: 0.7 }}>
                      Aperçu de l'application
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Box>

        {/* Features Section */}
        <Container maxWidth="lg" sx={{ py: 8 }}>
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" component="h2" gutterBottom>
              Pourquoi choisir Nouri ?
            </Typography>
            <Typography variant="h6" color="text.secondary" sx={{ maxWidth: 600, mx: 'auto' }}>
              Une solution complète pour gérer vos finances personnelles 
              avec l'intelligence artificielle
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <FeatureCard {...feature} />
              </Grid>
            ))}
          </Grid>
        </Container>

        {/* CTA Section */}
        <Box
          sx={{
            backgroundColor: 'grey.50',
            py: 8
          }}
        >
          <Container maxWidth="md">
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h4" component="h2" gutterBottom>
                Prêt à prendre le contrôle de vos finances ?
              </Typography>
              <Typography variant="h6" color="text.secondary" sx={{ mb: 4 }}>
                Rejoignez des milliers de Tunisiens qui font confiance à Nouri 
                pour gérer leur argent intelligemment.
              </Typography>
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate(ROUTES.REGISTER)}
                sx={{ px: 4, py: 1.5 }}
              >
                Créer mon compte gratuitement
              </Button>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Aucune carte de crédit requise • Sécurisé et confidentiel
              </Typography>
            </Box>
          </Container>
        </Box>

        {/* Footer */}
        <Box
          sx={{
            backgroundColor: 'grey.900',
            color: 'white',
            py: 4
          }}
        >
          <Container maxWidth="lg">
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Nouri
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Votre coach financier intelligent pour une meilleure gestion 
                  de vos finances en Tunisie.
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Box sx={{ textAlign: { xs: 'left', md: 'right' } }}>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    © 2024 Nouri. Tous droits réservés.
                  </Typography>
                  <Typography variant="body2" sx={{ opacity: 0.6, mt: 1 }}>
                    Développé avec ❤️ en Tunisie
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Box>
      </Box>
    </>
  )
}

export default HomePage
