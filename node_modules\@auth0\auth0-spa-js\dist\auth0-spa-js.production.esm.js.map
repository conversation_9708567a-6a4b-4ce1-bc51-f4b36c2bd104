{"version": 3, "file": "auth0-spa-js.production.esm.js", "sources": ["../node_modules/tslib/tslib.es6.js", "../node_modules/browser-tabs-lock/processLock.js", "../node_modules/browser-tabs-lock/index.js", "../src/constants.ts", "../src/version.ts", "../src/errors.ts", "../src/utils.ts", "../src/http.ts", "../src/worker/worker.utils.ts", "../src/api.ts", "../src/scope.ts", "../src/cache/shared.ts", "../src/cache/cache-localstorage.ts", "../src/cache/cache-memory.ts", "../src/cache/cache-manager.ts", "../src/transaction-manager.ts", "../src/jwt.ts", "../node_modules/es-cookie/src/es-cookie.js", "../src/storage.ts", "../src/promise-utils.ts", "../src/cache/key-manifest.ts", "../src/Auth0Client.utils.ts", "../src/Auth0Client.ts", "../src/global.ts", "../src/index.ts"], "sourcesContent": ["/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\r\n    function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\r\n    var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\r\n    var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\r\n    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\r\n    var _, done = false;\r\n    for (var i = decorators.length - 1; i >= 0; i--) {\r\n        var context = {};\r\n        for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\r\n        for (var p in contextIn.access) context.access[p] = contextIn.access[p];\r\n        context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\r\n        var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\r\n        if (kind === \"accessor\") {\r\n            if (result === void 0) continue;\r\n            if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\r\n            if (_ = accept(result.get)) descriptor.get = _;\r\n            if (_ = accept(result.set)) descriptor.set = _;\r\n            if (_ = accept(result.init)) initializers.unshift(_);\r\n        }\r\n        else if (_ = accept(result)) {\r\n            if (kind === \"field\") initializers.unshift(_);\r\n            else descriptor[key] = _;\r\n        }\r\n    }\r\n    if (target) Object.defineProperty(target, contextIn.name, descriptor);\r\n    done = true;\r\n};\r\n\r\nexport function __runInitializers(thisArg, initializers, value) {\r\n    var useValue = arguments.length > 2;\r\n    for (var i = 0; i < initializers.length; i++) {\r\n        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\r\n    }\r\n    return useValue ? value : void 0;\r\n};\r\n\r\nexport function __propKey(x) {\r\n    return typeof x === \"symbol\" ? x : \"\".concat(x);\r\n};\r\n\r\nexport function __setFunctionName(f, name, prefix) {\r\n    if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\r\n    return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\r\n};\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n        desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\n/** @deprecated */\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n}\r\n\r\nexport function __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\r\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\r\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\r\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\r\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\r\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\r\n}\r\n\r\nexport function __classPrivateFieldIn(state, receiver) {\r\n    if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\r\n    return typeof state === \"function\" ? receiver === state : state.has(receiver);\r\n}\r\n\r\nexport function __addDisposableResource(env, value, async) {\r\n    if (value !== null && value !== void 0) {\r\n        if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\r\n        var dispose;\r\n        if (async) {\r\n            if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\r\n            dispose = value[Symbol.asyncDispose];\r\n        }\r\n        if (dispose === void 0) {\r\n            if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\r\n            dispose = value[Symbol.dispose];\r\n        }\r\n        if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\r\n        env.stack.push({ value: value, dispose: dispose, async: async });\r\n    }\r\n    else if (async) {\r\n        env.stack.push({ async: true });\r\n    }\r\n    return value;\r\n}\r\n\r\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\r\n\r\nexport function __disposeResources(env) {\r\n    function fail(e) {\r\n        env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\r\n        env.hasError = true;\r\n    }\r\n    function next() {\r\n        while (env.stack.length) {\r\n            var rec = env.stack.pop();\r\n            try {\r\n                var result = rec.dispose && rec.dispose.call(rec.value);\r\n                if (rec.async) return Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\r\n            }\r\n            catch (e) {\r\n                fail(e);\r\n            }\r\n        }\r\n        if (env.hasError) throw env.error;\r\n    }\r\n    return next();\r\n}\r\n\r\nexport default {\r\n    __extends: __extends,\r\n    __assign: __assign,\r\n    __rest: __rest,\r\n    __decorate: __decorate,\r\n    __param: __param,\r\n    __metadata: __metadata,\r\n    __awaiter: __awaiter,\r\n    __generator: __generator,\r\n    __createBinding: __createBinding,\r\n    __exportStar: __exportStar,\r\n    __values: __values,\r\n    __read: __read,\r\n    __spread: __spread,\r\n    __spreadArrays: __spreadArrays,\r\n    __spreadArray: __spreadArray,\r\n    __await: __await,\r\n    __asyncGenerator: __asyncGenerator,\r\n    __asyncDelegator: __asyncDelegator,\r\n    __asyncValues: __asyncValues,\r\n    __makeTemplateObject: __makeTemplateObject,\r\n    __importStar: __importStar,\r\n    __importDefault: __importDefault,\r\n    __classPrivateFieldGet: __classPrivateFieldGet,\r\n    __classPrivateFieldSet: __classPrivateFieldSet,\r\n    __classPrivateFieldIn: __classPrivateFieldIn,\r\n    __addDisposableResource: __addDisposableResource,\r\n    __disposeResources: __disposeResources,\r\n};\r\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar ProcessLocking = /** @class */ (function () {\n    function ProcessLocking() {\n        var _this = this;\n        this.locked = new Map();\n        this.addToLocked = function (key, toAdd) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined) {\n                if (toAdd === undefined) {\n                    _this.locked.set(key, []);\n                }\n                else {\n                    _this.locked.set(key, [toAdd]);\n                }\n            }\n            else {\n                if (toAdd !== undefined) {\n                    callbacks.unshift(toAdd);\n                    _this.locked.set(key, callbacks);\n                }\n            }\n        };\n        this.isLocked = function (key) {\n            return _this.locked.has(key);\n        };\n        this.lock = function (key) {\n            return new Promise(function (resolve, reject) {\n                if (_this.isLocked(key)) {\n                    _this.addToLocked(key, resolve);\n                }\n                else {\n                    _this.addToLocked(key);\n                    resolve();\n                }\n            });\n        };\n        this.unlock = function (key) {\n            var callbacks = _this.locked.get(key);\n            if (callbacks === undefined || callbacks.length === 0) {\n                _this.locked.delete(key);\n                return;\n            }\n            var toCall = callbacks.pop();\n            _this.locked.set(key, callbacks);\n            if (toCall !== undefined) {\n                setTimeout(toCall, 0);\n            }\n        };\n    }\n    ProcessLocking.getInstance = function () {\n        if (ProcessLocking.instance === undefined) {\n            ProcessLocking.instance = new ProcessLocking();\n        }\n        return ProcessLocking.instance;\n    };\n    return ProcessLocking;\n}());\nfunction getLock() {\n    return ProcessLocking.getInstance();\n}\nexports.default = getLock;\n", "\"use strict\";\nvar __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : new P(function (resolve) { resolve(result.value); }).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (_) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nvar _this = this;\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar processLock_1 = require(\"./processLock\");\n/**\n * @author: SuperTokens (https://github.com/supertokens)\n * This library was created as a part of a larger project, SuperTokens(https://supertokens.io) - the best session management solution.\n * You can also check out our other projects on https://github.com/supertokens\n *\n * To contribute to this package visit https://github.com/supertokens/browser-tabs-lock\n * If you face any problems you can file an issue on https://github.com/supertokens/browser-tabs-lock/issues\n *\n * If you have any questions or if you just want to say hi visit https://supertokens.io/discord\n */\n/**\n * @constant\n * @type {string}\n * @default\n * @description All the locks taken by this package will have this as prefix\n*/\nvar LOCK_STORAGE_KEY = 'browser-tabs-lock-key';\nvar DEFAULT_STORAGE_HANDLER = {\n    key: function (index) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    getItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    clear: function () { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            return [2 /*return*/, window.localStorage.clear()];\n        });\n    }); },\n    removeItem: function (key) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    setItem: function (key, value) { return __awaiter(_this, void 0, void 0, function () {\n        return __generator(this, function (_a) {\n            throw new Error(\"Unsupported\");\n        });\n    }); },\n    keySync: function (index) {\n        return window.localStorage.key(index);\n    },\n    getItemSync: function (key) {\n        return window.localStorage.getItem(key);\n    },\n    clearSync: function () {\n        return window.localStorage.clear();\n    },\n    removeItemSync: function (key) {\n        return window.localStorage.removeItem(key);\n    },\n    setItemSync: function (key, value) {\n        return window.localStorage.setItem(key, value);\n    },\n};\n/**\n * @function delay\n * @param {number} milliseconds - How long the delay should be in terms of milliseconds\n * @returns {Promise<void>}\n */\nfunction delay(milliseconds) {\n    return new Promise(function (resolve) { return setTimeout(resolve, milliseconds); });\n}\n/**\n * @function generateRandomString\n * @params {number} length - How long the random string should be\n * @returns {string}\n * @description returns random string whose length is equal to the length passed as parameter\n */\nfunction generateRandomString(length) {\n    var CHARS = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz';\n    var randomstring = '';\n    for (var i = 0; i < length; i++) {\n        var INDEX = Math.floor(Math.random() * CHARS.length);\n        randomstring += CHARS[INDEX];\n    }\n    return randomstring;\n}\n/**\n * @function getLockId\n * @returns {string}\n * @description Generates an id which will be unique for the browser tab\n */\nfunction getLockId() {\n    return Date.now().toString() + generateRandomString(15);\n}\nvar SuperTokensLock = /** @class */ (function () {\n    function SuperTokensLock(storageHandler) {\n        this.acquiredIatSet = new Set();\n        this.storageHandler = undefined;\n        this.id = getLockId();\n        this.acquireLock = this.acquireLock.bind(this);\n        this.releaseLock = this.releaseLock.bind(this);\n        this.releaseLock__private__ = this.releaseLock__private__.bind(this);\n        this.waitForSomethingToChange = this.waitForSomethingToChange.bind(this);\n        this.refreshLockWhileAcquired = this.refreshLockWhileAcquired.bind(this);\n        this.storageHandler = storageHandler;\n        if (SuperTokensLock.waiters === undefined) {\n            SuperTokensLock.waiters = [];\n        }\n    }\n    /**\n     * @async\n     * @memberOf Lock\n     * @function acquireLock\n     * @param {string} lockKey - Key for which the lock is being acquired\n     * @param {number} [timeout=5000] - Maximum time for which the function will wait to acquire the lock\n     * @returns {Promise<boolean>}\n     * @description Will return true if lock is being acquired, else false.\n     *              Also the lock can be acquired for maximum 10 secs\n     */\n    SuperTokensLock.prototype.acquireLock = function (lockKey, timeout) {\n        if (timeout === void 0) { timeout = 5000; }\n        return __awaiter(this, void 0, void 0, function () {\n            var iat, MAX_TIME, STORAGE_KEY, STORAGE, lockObj, TIMEOUT_KEY, lockObjPostDelay, parsedLockObjPostDelay;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        iat = Date.now() + generateRandomString(4);\n                        MAX_TIME = Date.now() + timeout;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        _a.label = 1;\n                    case 1:\n                        if (!(Date.now() < MAX_TIME)) return [3 /*break*/, 8];\n                        return [4 /*yield*/, delay(30)];\n                    case 2:\n                        _a.sent();\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (!(lockObj === null)) return [3 /*break*/, 5];\n                        TIMEOUT_KEY = this.id + \"-\" + lockKey + \"-\" + iat;\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        return [4 /*yield*/, delay(Math.floor(Math.random() * 25))];\n                    case 3:\n                        // there is a problem if setItem happens at the exact same time for 2 different processes.. so we add some random delay here.\n                        _a.sent();\n                        STORAGE.setItemSync(STORAGE_KEY, JSON.stringify({\n                            id: this.id,\n                            iat: iat,\n                            timeoutKey: TIMEOUT_KEY,\n                            timeAcquired: Date.now(),\n                            timeRefreshed: Date.now()\n                        }));\n                        return [4 /*yield*/, delay(30)];\n                    case 4:\n                        _a.sent(); // this is to prevent race conditions. This time must be more than the time it takes for storage.setItem\n                        lockObjPostDelay = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObjPostDelay !== null) {\n                            parsedLockObjPostDelay = JSON.parse(lockObjPostDelay);\n                            if (parsedLockObjPostDelay.id === this.id && parsedLockObjPostDelay.iat === iat) {\n                                this.acquiredIatSet.add(iat);\n                                this.refreshLockWhileAcquired(STORAGE_KEY, iat);\n                                return [2 /*return*/, true];\n                            }\n                        }\n                        return [3 /*break*/, 7];\n                    case 5:\n                        SuperTokensLock.lockCorrector(this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler);\n                        return [4 /*yield*/, this.waitForSomethingToChange(MAX_TIME)];\n                    case 6:\n                        _a.sent();\n                        _a.label = 7;\n                    case 7:\n                        iat = Date.now() + generateRandomString(4);\n                        return [3 /*break*/, 1];\n                    case 8: return [2 /*return*/, false];\n                }\n            });\n        });\n    };\n    SuperTokensLock.prototype.refreshLockWhileAcquired = function (storageKey, iat) {\n        return __awaiter(this, void 0, void 0, function () {\n            var _this = this;\n            return __generator(this, function (_a) {\n                setTimeout(function () { return __awaiter(_this, void 0, void 0, function () {\n                    var STORAGE, lockObj, parsedLockObj;\n                    return __generator(this, function (_a) {\n                        switch (_a.label) {\n                            case 0: return [4 /*yield*/, processLock_1.default().lock(iat)];\n                            case 1:\n                                _a.sent();\n                                if (!this.acquiredIatSet.has(iat)) {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                                lockObj = STORAGE.getItemSync(storageKey);\n                                if (lockObj !== null) {\n                                    parsedLockObj = JSON.parse(lockObj);\n                                    parsedLockObj.timeRefreshed = Date.now();\n                                    STORAGE.setItemSync(storageKey, JSON.stringify(parsedLockObj));\n                                    processLock_1.default().unlock(iat);\n                                }\n                                else {\n                                    processLock_1.default().unlock(iat);\n                                    return [2 /*return*/];\n                                }\n                                this.refreshLockWhileAcquired(storageKey, iat);\n                                return [2 /*return*/];\n                        }\n                    });\n                }); }, 1000);\n                return [2 /*return*/];\n            });\n        });\n    };\n    SuperTokensLock.prototype.waitForSomethingToChange = function (MAX_TIME) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, new Promise(function (resolve) {\n                            var resolvedCalled = false;\n                            var startedAt = Date.now();\n                            var MIN_TIME_TO_WAIT = 50; // ms\n                            var removedListeners = false;\n                            function stopWaiting() {\n                                if (!removedListeners) {\n                                    window.removeEventListener('storage', stopWaiting);\n                                    SuperTokensLock.removeFromWaiting(stopWaiting);\n                                    clearTimeout(timeOutId);\n                                    removedListeners = true;\n                                }\n                                if (!resolvedCalled) {\n                                    resolvedCalled = true;\n                                    var timeToWait = MIN_TIME_TO_WAIT - (Date.now() - startedAt);\n                                    if (timeToWait > 0) {\n                                        setTimeout(resolve, timeToWait);\n                                    }\n                                    else {\n                                        resolve(null);\n                                    }\n                                }\n                            }\n                            window.addEventListener('storage', stopWaiting);\n                            SuperTokensLock.addToWaiting(stopWaiting);\n                            var timeOutId = setTimeout(stopWaiting, Math.max(0, MAX_TIME - Date.now()));\n                        })];\n                    case 1:\n                        _a.sent();\n                        return [2 /*return*/];\n                }\n            });\n        });\n    };\n    SuperTokensLock.addToWaiting = function (func) {\n        this.removeFromWaiting(func);\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters.push(func);\n    };\n    SuperTokensLock.removeFromWaiting = function (func) {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        SuperTokensLock.waiters = SuperTokensLock.waiters.filter(function (i) { return i !== func; });\n    };\n    SuperTokensLock.notifyWaiters = function () {\n        if (SuperTokensLock.waiters === undefined) {\n            return;\n        }\n        var waiters = SuperTokensLock.waiters.slice(); // so that if Lock.waiters is changed it's ok.\n        waiters.forEach(function (i) { return i(); });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0: return [4 /*yield*/, this.releaseLock__private__(lockKey)];\n                    case 1: return [2 /*return*/, _a.sent()];\n                }\n            });\n        });\n    };\n    /**\n     * @function releaseLock\n     * @memberOf Lock\n     * @param {string} lockKey - Key for which lock is being released\n     * @returns {void}\n     * @description Release a lock.\n     */\n    SuperTokensLock.prototype.releaseLock__private__ = function (lockKey) {\n        return __awaiter(this, void 0, void 0, function () {\n            var STORAGE, STORAGE_KEY, lockObj, parsedlockObj;\n            return __generator(this, function (_a) {\n                switch (_a.label) {\n                    case 0:\n                        STORAGE = this.storageHandler === undefined ? DEFAULT_STORAGE_HANDLER : this.storageHandler;\n                        STORAGE_KEY = LOCK_STORAGE_KEY + \"-\" + lockKey;\n                        lockObj = STORAGE.getItemSync(STORAGE_KEY);\n                        if (lockObj === null) {\n                            return [2 /*return*/];\n                        }\n                        parsedlockObj = JSON.parse(lockObj);\n                        if (!(parsedlockObj.id === this.id)) return [3 /*break*/, 2];\n                        return [4 /*yield*/, processLock_1.default().lock(parsedlockObj.iat)];\n                    case 1:\n                        _a.sent();\n                        this.acquiredIatSet.delete(parsedlockObj.iat);\n                        STORAGE.removeItemSync(STORAGE_KEY);\n                        processLock_1.default().unlock(parsedlockObj.iat);\n                        SuperTokensLock.notifyWaiters();\n                        _a.label = 2;\n                    case 2: return [2 /*return*/];\n                }\n            });\n        });\n    };\n    /**\n     * @function lockCorrector\n     * @returns {void}\n     * @description If a lock is acquired by a tab and the tab is closed before the lock is\n     *              released, this function will release those locks\n     */\n    SuperTokensLock.lockCorrector = function (storageHandler) {\n        var MIN_ALLOWED_TIME = Date.now() - 5000;\n        var STORAGE = storageHandler;\n        var KEYS = [];\n        var currIndex = 0;\n        while (true) {\n            var key = STORAGE.keySync(currIndex);\n            if (key === null) {\n                break;\n            }\n            KEYS.push(key);\n            currIndex++;\n        }\n        var notifyWaiters = false;\n        for (var i = 0; i < KEYS.length; i++) {\n            var LOCK_KEY = KEYS[i];\n            if (LOCK_KEY.includes(LOCK_STORAGE_KEY)) {\n                var lockObj = STORAGE.getItemSync(LOCK_KEY);\n                if (lockObj !== null) {\n                    var parsedlockObj = JSON.parse(lockObj);\n                    if ((parsedlockObj.timeRefreshed === undefined && parsedlockObj.timeAcquired < MIN_ALLOWED_TIME) ||\n                        (parsedlockObj.timeRefreshed !== undefined && parsedlockObj.timeRefreshed < MIN_ALLOWED_TIME)) {\n                        STORAGE.removeItemSync(LOCK_KEY);\n                        notifyWaiters = true;\n                    }\n                }\n            }\n        }\n        if (notifyWaiters) {\n            SuperTokensLock.notifyWaiters();\n        }\n    };\n    SuperTokensLock.waiters = undefined;\n    return SuperTokensLock;\n}());\nexports.default = SuperTokensLock;\n", "import { PopupConfigOptions } from './global';\nimport version from './version';\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS = 60;\n\n/**\n * @ignore\n */\nexport const DEFAULT_POPUP_CONFIG_OPTIONS: PopupConfigOptions = {\n  timeoutInSeconds: DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n};\n\n/**\n * @ignore\n */\nexport const DEFAULT_SILENT_TOKEN_RETRY_COUNT = 3;\n\n/**\n * @ignore\n */\nexport const CLEANUP_IFRAME_TIMEOUT_IN_SECONDS = 2;\n\n/**\n * @ignore\n */\nexport const DEFAULT_FETCH_TIMEOUT_MS = 10000;\n\nexport const CACHE_LOCATION_MEMORY = 'memory';\nexport const CACHE_LOCATION_LOCAL_STORAGE = 'localstorage';\n\n/**\n * @ignore\n */\nexport const MISSING_REFRESH_TOKEN_ERROR_MESSAGE = 'Missing Refresh Token';\n\n/**\n * @ignore\n */\nexport const INVALID_REFRESH_TOKEN_ERROR_MESSAGE = 'invalid refresh token';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SCOPE = 'openid profile email';\n\n/**\n * @ignore\n */\nexport const DEFAULT_SESSION_CHECK_EXPIRY_DAYS = 1;\n\n/**\n * @ignore\n */\nexport const DEFAULT_AUTH0_CLIENT = {\n  name: 'auth0-spa-js',\n  version: version\n};\n\nexport const DEFAULT_NOW_PROVIDER = () => Date.now();\n", "export default '2.2.0';\n", "/**\n * Thrown when network requests to the Auth server fail.\n */\nexport class GenericError extends Error {\n  constructor(public error: string, public error_description: string) {\n    super(error_description);\n    Object.setPrototypeOf(this, GenericError.prototype);\n  }\n\n  static fromPayload({\n    error,\n    error_description\n  }: {\n    error: string;\n    error_description: string;\n  }) {\n    return new GenericError(error, error_description);\n  }\n}\n\n/**\n * Thrown when handling the redirect callback fails, will be one of Auth0's\n * Authentication API's Standard Error Responses: https://auth0.com/docs/api/authentication?javascript#standard-error-responses\n */\nexport class AuthenticationError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public state: string,\n    public appState: any = null\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, AuthenticationError.prototype);\n  }\n}\n\n/**\n * Thrown when silent auth times out (usually due to a configuration issue) or\n * when network requests to the Auth server timeout.\n */\nexport class TimeoutError extends GenericError {\n  constructor() {\n    super('timeout', 'Timeout');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, TimeoutError.prototype);\n  }\n}\n\n/**\n * Error thrown when the login popup times out (if the user does not complete auth)\n */\nexport class PopupTimeoutError extends TimeoutError {\n  constructor(public popup: Window) {\n    super();\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupTimeoutError.prototype);\n  }\n}\n\nexport class PopupCancelledError extends GenericError {\n  constructor(public popup: Window) {\n    super('cancelled', 'Popup closed');\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, PopupCancelledError.prototype);\n  }\n}\n\n/**\n * Error thrown when the token exchange results in a `mfa_required` error\n */\nexport class MfaRequiredError extends GenericError {\n  constructor(\n    error: string,\n    error_description: string,\n    public mfa_token: string\n  ) {\n    super(error, error_description);\n    //https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\n    Object.setPrototypeOf(this, MfaRequiredError.prototype);\n  }\n}\n\n/**\n * Error thrown when there is no refresh token to use\n */\nexport class MissingRefreshTokenError extends GenericError {\n  constructor(public audience: string, public scope: string) {\n    super(\n      'missing_refresh_token',\n      `Missing Refresh Token (audience: '${valueOrEmptyString(audience, [\n        'default'\n      ])}', scope: '${valueOrEmptyString(scope)}')`\n    );\n    Object.setPrototypeOf(this, MissingRefreshTokenError.prototype);\n  }\n}\n\n/**\n * Returns an empty string when value is falsy, or when it's value is included in the exclude argument.\n * @param value The value to check\n * @param exclude An array of values that should result in an empty string.\n * @returns The value, or an empty string when falsy or included in the exclude argument.\n */\nfunction valueOrEmptyString(value: string, exclude: string[] = []) {\n  return value && !exclude.includes(value) ? value : '';\n}\n", "import { AuthenticationResult, PopupConfigOptions } from './global';\n\nimport {\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  CLEANUP_IFRAME_TIMEOUT_IN_SECONDS\n} from './constants';\n\nimport {\n  PopupTimeoutError,\n  TimeoutError,\n  GenericError,\n  PopupCancelledError\n} from './errors';\n\nexport const parseAuthenticationResult = (\n  queryString: string\n): AuthenticationResult => {\n  if (queryString.indexOf('#') > -1) {\n    queryString = queryString.substring(0, queryString.indexOf('#'));\n  }\n\n  const searchParams = new URLSearchParams(queryString);\n\n  return {\n    state: searchParams.get('state')!,\n    code: searchParams.get('code') || undefined,\n    error: searchParams.get('error') || undefined,\n    error_description: searchParams.get('error_description') || undefined\n  };\n};\n\nexport const runIframe = (\n  authorizeUrl: string,\n  eventOrigin: string,\n  timeoutInSeconds: number = DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n) => {\n  return new Promise<AuthenticationResult>((res, rej) => {\n    const iframe = window.document.createElement('iframe');\n\n    iframe.setAttribute('width', '0');\n    iframe.setAttribute('height', '0');\n    iframe.style.display = 'none';\n\n    const removeIframe = () => {\n      if (window.document.body.contains(iframe)) {\n        window.document.body.removeChild(iframe);\n        window.removeEventListener('message', iframeEventHandler, false);\n      }\n    };\n\n    let iframeEventHandler: (e: MessageEvent) => void;\n\n    const timeoutSetTimeoutId = setTimeout(() => {\n      rej(new TimeoutError());\n      removeIframe();\n    }, timeoutInSeconds * 1000);\n\n    iframeEventHandler = function (e: MessageEvent) {\n      if (e.origin != eventOrigin) return;\n      if (!e.data || e.data.type !== 'authorization_response') return;\n\n      const eventSource = e.source;\n\n      if (eventSource) {\n        (eventSource as any).close();\n      }\n\n      e.data.response.error\n        ? rej(GenericError.fromPayload(e.data.response))\n        : res(e.data.response);\n\n      clearTimeout(timeoutSetTimeoutId);\n      window.removeEventListener('message', iframeEventHandler, false);\n\n      // Delay the removal of the iframe to prevent hanging loading status\n      // in Chrome: https://github.com/auth0/auth0-spa-js/issues/240\n      setTimeout(removeIframe, CLEANUP_IFRAME_TIMEOUT_IN_SECONDS * 1000);\n    };\n\n    window.addEventListener('message', iframeEventHandler, false);\n    window.document.body.appendChild(iframe);\n    iframe.setAttribute('src', authorizeUrl);\n  });\n};\n\nexport const openPopup = (url: string) => {\n  const width = 400;\n  const height = 600;\n  const left = window.screenX + (window.innerWidth - width) / 2;\n  const top = window.screenY + (window.innerHeight - height) / 2;\n\n  return window.open(\n    url,\n    'auth0:authorize:popup',\n    `left=${left},top=${top},width=${width},height=${height},resizable,scrollbars=yes,status=1`\n  );\n};\n\nexport const runPopup = (config: PopupConfigOptions) => {\n  return new Promise<AuthenticationResult>((resolve, reject) => {\n    let popupEventListener: (e: MessageEvent) => void;\n\n    // Check each second if the popup is closed triggering a PopupCancelledError\n    const popupTimer = setInterval(() => {\n      if (config.popup && config.popup.closed) {\n        clearInterval(popupTimer);\n        clearTimeout(timeoutId);\n        window.removeEventListener('message', popupEventListener, false);\n        reject(new PopupCancelledError(config.popup));\n      }\n    }, 1000);\n\n    const timeoutId = setTimeout(() => {\n      clearInterval(popupTimer);\n      reject(new PopupTimeoutError(config.popup));\n      window.removeEventListener('message', popupEventListener, false);\n    }, (config.timeoutInSeconds || DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS) * 1000);\n\n    popupEventListener = function (e: MessageEvent) {\n      if (!e.data || e.data.type !== 'authorization_response') {\n        return;\n      }\n\n      clearTimeout(timeoutId);\n      clearInterval(popupTimer);\n      window.removeEventListener('message', popupEventListener, false);\n      config.popup.close();\n\n      if (e.data.response.error) {\n        return reject(GenericError.fromPayload(e.data.response));\n      }\n\n      resolve(e.data.response);\n    };\n\n    window.addEventListener('message', popupEventListener);\n  });\n};\n\nexport const getCrypto = () => {\n  return window.crypto;\n};\n\nexport const createRandomString = () => {\n  const charset =\n    '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.';\n  let random = '';\n  const randomValues = Array.from(\n    getCrypto().getRandomValues(new Uint8Array(43))\n  );\n  randomValues.forEach(v => (random += charset[v % charset.length]));\n  return random;\n};\n\nexport const encode = (value: string) => btoa(value);\nexport const decode = (value: string) => atob(value);\n\nconst stripUndefined = (params: any) => {\n  return Object.keys(params)\n    .filter(k => typeof params[k] !== 'undefined')\n    .reduce((acc, key) => ({ ...acc, [key]: params[key] }), {});\n};\n\nexport const createQueryParams = ({ clientId: client_id, ...params }: any) => {\n  return new URLSearchParams(\n    stripUndefined({ client_id, ...params })\n  ).toString();\n};\n\nexport const sha256 = async (s: string) => {\n  const digestOp: any = getCrypto().subtle.digest(\n    { name: 'SHA-256' },\n    new TextEncoder().encode(s)\n  );\n\n  return await digestOp;\n};\n\nconst urlEncodeB64 = (input: string) => {\n  const b64Chars: { [index: string]: string } = { '+': '-', '/': '_', '=': '' };\n  return input.replace(/[+/=]/g, (m: string) => b64Chars[m]);\n};\n\n// https://stackoverflow.com/questions/30106476/\nconst decodeB64 = (input: string) =>\n  decodeURIComponent(\n    atob(input)\n      .split('')\n      .map(c => {\n        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);\n      })\n      .join('')\n  );\n\nexport const urlDecodeB64 = (input: string) =>\n  decodeB64(input.replace(/_/g, '/').replace(/-/g, '+'));\n\nexport const bufferToBase64UrlEncoded = (input: number[] | Uint8Array) => {\n  const ie11SafeInput = new Uint8Array(input);\n  return urlEncodeB64(\n    window.btoa(String.fromCharCode(...Array.from(ie11SafeInput)))\n  );\n};\n\nexport const validateCrypto = () => {\n  if (!getCrypto()) {\n    throw new Error(\n      'For security reasons, `window.crypto` is required to run `auth0-spa-js`.'\n    );\n  }\n  if (typeof getCrypto().subtle === 'undefined') {\n    throw new Error(`\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    `);\n  }\n};\n\n/**\n * @ignore\n */\nexport const getDomain = (domainUrl: string) => {\n  if (!/^https?:\\/\\//.test(domainUrl)) {\n    return `https://${domainUrl}`;\n  }\n\n  return domainUrl;\n};\n\n/**\n * @ignore\n */\nexport const getTokenIssuer = (\n  issuer: string | undefined,\n  domainUrl: string\n) => {\n  if (issuer) {\n    return issuer.startsWith('https://') ? issuer : `https://${issuer}/`;\n  }\n\n  return `${domainUrl}/`;\n};\n\nexport const parseNumber = (value: any): number | undefined => {\n  if (typeof value !== 'string') {\n    return value;\n  }\n  return parseInt(value, 10) || undefined;\n};\n", "import {\n  DEFAULT_FETCH_TIMEOUT_MS,\n  DEFAULT_SILENT_TOKEN_RETRY_COUNT\n} from './constants';\n\nimport { sendMessage } from './worker/worker.utils';\nimport { FetchOptions } from './global';\nimport {\n  GenericError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport const createAbortController = () => new AbortController();\n\nconst dofetch = async (fetchUrl: string, fetchOptions: FetchOptions) => {\n  const response = await fetch(fetchUrl, fetchOptions);\n\n  return {\n    ok: response.ok,\n    json: await response.json()\n  };\n};\n\nconst fetchWithoutWorker = async (\n  fetchUrl: string,\n  fetchOptions: FetchOptions,\n  timeout: number\n) => {\n  const controller = createAbortController();\n  fetchOptions.signal = controller.signal;\n\n  let timeoutId: NodeJS.Timeout;\n\n  // The promise will resolve with one of these two promises (the fetch or the timeout), whichever completes first.\n  return Promise.race([\n    dofetch(fetchUrl, fetchOptions),\n\n    new Promise((_, reject) => {\n      timeoutId = setTimeout(() => {\n        controller.abort();\n        reject(new Error(\"Timeout when executing 'fetch'\"));\n      }, timeout);\n    })\n  ]).finally(() => {\n    clearTimeout(timeoutId);\n  });\n};\n\nconst fetchWithWorker = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  timeout: number,\n  worker: Worker,\n  useFormData?: boolean\n) => {\n  return sendMessage(\n    {\n      auth: {\n        audience,\n        scope\n      },\n      timeout,\n      fetchUrl,\n      fetchOptions,\n      useFormData\n    },\n    worker\n  );\n};\n\nexport const switchFetch = async (\n  fetchUrl: string,\n  audience: string,\n  scope: string,\n  fetchOptions: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean,\n  timeout = DEFAULT_FETCH_TIMEOUT_MS\n): Promise<any> => {\n  if (worker) {\n    return fetchWithWorker(\n      fetchUrl,\n      audience,\n      scope,\n      fetchOptions,\n      timeout,\n      worker,\n      useFormData\n    );\n  } else {\n    return fetchWithoutWorker(fetchUrl, fetchOptions, timeout);\n  }\n};\n\nexport async function getJSON<T>(\n  url: string,\n  timeout: number | undefined,\n  audience: string,\n  scope: string,\n  options: FetchOptions,\n  worker?: Worker,\n  useFormData?: boolean\n): Promise<T> {\n  let fetchError: null | Error = null;\n  let response: any;\n\n  for (let i = 0; i < DEFAULT_SILENT_TOKEN_RETRY_COUNT; i++) {\n    try {\n      response = await switchFetch(\n        url,\n        audience,\n        scope,\n        options,\n        worker,\n        useFormData,\n        timeout\n      );\n      fetchError = null;\n      break;\n    } catch (e) {\n      // Fetch only fails in the case of a network issue, so should be\n      // retried here. Failure status (4xx, 5xx, etc) return a resolved Promise\n      // with the failure in the body.\n      // https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\n      fetchError = e;\n    }\n  }\n\n  if (fetchError) {\n    throw fetchError;\n  }\n\n  const {\n    json: { error, error_description, ...data },\n    ok\n  } = response;\n\n  if (!ok) {\n    const errorMessage =\n      error_description || `HTTP error. Unable to fetch ${url}`;\n\n    if (error === 'mfa_required') {\n      throw new MfaRequiredError(error, errorMessage, data.mfa_token);\n    }\n\n    if (error === 'missing_refresh_token') {\n      throw new MissingRefreshTokenError(audience, scope);\n    }\n\n    throw new GenericError(error || 'request_error', errorMessage);\n  }\n\n  return data;\n}\n", "import { WorkerRefreshTokenMessage } from './worker.types';\n\n/**\n * Sends the specified message to the web worker\n * @param message The message to send\n * @param to The worker to send the message to\n */\nexport const sendMessage = (message: WorkerRefreshTokenMessage, to: Worker) =>\n  new Promise(function (resolve, reject) {\n    const messageChannel = new MessageChannel();\n\n    messageChannel.port1.onmessage = function (event) {\n      // Only for fetch errors, as these get retried\n      if (event.data.error) {\n        reject(new Error(event.data.error));\n      } else {\n        resolve(event.data);\n      }\n      messageChannel.port1.close();\n    };\n\n    to.postMessage(message, [messageChannel.port2]);\n  });\n", "import { TokenEndpointOptions, TokenEndpointResponse } from './global';\nimport { DEFAULT_AUTH0_CLIENT } from './constants';\nimport { getJSON } from './http';\nimport { createQueryParams } from './utils';\n\nexport async function oauthToken(\n  {\n    baseUrl,\n    timeout,\n    audience,\n    scope,\n    auth0Client,\n    useFormData,\n    ...options\n  }: TokenEndpointOptions,\n  worker?: Worker\n) {\n  const body = useFormData\n    ? createQueryParams(options)\n    : JSON.stringify(options);\n\n  return await getJSON<TokenEndpointResponse>(\n    `${baseUrl}/oauth/token`,\n    timeout,\n    audience || 'default',\n    scope,\n    {\n      method: 'POST',\n      body,\n      headers: {\n        'Content-Type': useFormData\n          ? 'application/x-www-form-urlencoded'\n          : 'application/json',\n        'Auth0-Client': btoa(\n          JSON.stringify(auth0Client || DEFAULT_AUTH0_CLIENT)\n        )\n      }\n    },\n    worker,\n    useFormData\n  );\n}\n", "/**\n * @ignore\n */\nconst dedupe = (arr: string[]) => Array.from(new Set(arr));\n\n/**\n * @ignore\n */\n/**\n * Returns a string of unique scopes by removing duplicates and unnecessary whitespace.\n *\n * @param {...(string | undefined)[]} scopes - A list of scope strings or undefined values.\n * @returns {string} A string containing unique scopes separated by a single space.\n */\nexport const getUniqueScopes = (...scopes: (string | undefined)[]) => {\n  return dedupe(scopes.filter(Boolean).join(' ').trim().split(/\\s+/)).join(' ');\n};\n", "import { IdToken, User } from '../global';\n\nexport const CACHE_KEY_PREFIX = '@@auth0spajs@@';\nexport const CACHE_KEY_ID_TOKEN_SUFFIX = '@@user@@';\n\nexport type CacheKeyData = {\n  audience?: string;\n  scope?: string;\n  clientId: string;\n};\n\nexport class C<PERSON><PERSON>ey {\n  public clientId: string;\n  public scope?: string;\n  public audience?: string;\n\n  constructor(\n    data: CacheKeyData,\n    public prefix: string = CACHE_KEY_PREFIX,\n    public suffix?: string\n  ) {\n    this.clientId = data.clientId;\n    this.scope = data.scope;\n    this.audience = data.audience;\n  }\n\n  /**\n   * Converts this `<PERSON><PERSON><PERSON><PERSON>` instance into a string for use in a cache\n   * @returns A string representation of the key\n   */\n  toKey(): string {\n    return [this.prefix, this.clientId, this.audience, this.scope, this.suffix]\n      .filter(Boolean)\n      .join('::');\n  }\n\n  /**\n   * Converts a cache key string into a `CacheKey` instance.\n   * @param key The key to convert\n   * @returns An instance of `<PERSON><PERSON><PERSON><PERSON>`\n   */\n  static from<PERSON><PERSON>(key: string): <PERSON>ache<PERSON><PERSON> {\n    const [prefix, clientId, audience, scope] = key.split('::');\n\n    return new CacheKey({ clientId, scope, audience }, prefix);\n  }\n\n  /**\n   * Utility function to build a `CacheKey` instance from a cache entry\n   * @param entry The entry\n   * @returns An instance of `CacheKey`\n   */\n  static fromCacheEntry(entry: CacheEntry): CacheKey {\n    const { scope, audience, client_id: clientId } = entry;\n\n    return new CacheKey({\n      scope,\n      audience,\n      clientId\n    });\n  }\n}\n\nexport interface DecodedToken {\n  claims: IdToken;\n  user: User;\n}\n\nexport interface IdTokenEntry {\n  id_token: string;\n  decodedToken: DecodedToken;\n}\n\nexport type CacheEntry = {\n  id_token?: string;\n  access_token: string;\n  expires_in: number;\n  decodedToken?: DecodedToken;\n  audience: string;\n  scope: string;\n  client_id: string;\n  refresh_token?: string;\n  oauthTokenScope?: string;\n};\n\nexport type WrappedCacheEntry = {\n  body: Partial<CacheEntry>;\n  expiresAt: number;\n};\n\nexport type KeyManifestEntry = {\n  keys: string[];\n};\n\nexport type Cacheable = WrappedCacheEntry | KeyManifestEntry;\n\nexport type MaybePromise<T> = Promise<T> | T;\n\nexport interface ICache {\n  set<T = Cacheable>(key: string, entry: T): MaybePromise<void>;\n  get<T = Cacheable>(key: string): MaybePromise<T | undefined>;\n  remove(key: string): MaybePromise<void>;\n  allKeys?(): MaybePromise<string[]>;\n}\n", "import { ICache, Cacheable, CACHE_KEY_PREFIX, Maybe<PERSON>rom<PERSON> } from './shared';\n\nexport class LocalStorageCache implements ICache {\n  public set<T = Cacheable>(key: string, entry: T) {\n    localStorage.setItem(key, JSON.stringify(entry));\n  }\n\n  public get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n    const json = window.localStorage.getItem(key);\n\n    if (!json) return;\n\n    try {\n      const payload = JSON.parse(json) as T;\n      return payload;\n      /* c8 ignore next 3 */\n    } catch (e) {\n      return;\n    }\n  }\n\n  public remove(key: string) {\n    localStorage.removeItem(key);\n  }\n\n  public allKeys() {\n    return Object.keys(window.localStorage).filter(key =>\n      key.startsWith(CACHE_KEY_PREFIX)\n    );\n  }\n}\n", "import { Cacheable, ICache, MaybePromise } from './shared';\n\nexport class InMemoryCache {\n  public enclosedCache: ICache = (function () {\n    let cache: Record<string, unknown> = {};\n\n    return {\n      set<T = Cacheable>(key: string, entry: T) {\n        cache[key] = entry;\n      },\n\n      get<T = Cacheable>(key: string): MaybePromise<T | undefined> {\n        const cacheEntry = cache[key] as T;\n\n        if (!cacheEntry) {\n          return;\n        }\n\n        return cacheEntry;\n      },\n\n      remove(key: string) {\n        delete cache[key];\n      },\n\n      allKeys(): string[] {\n        return Object.keys(cache);\n      }\n    };\n  })();\n}\n", "import { DEFAULT_NOW_PROVIDER } from '../constants';\nimport { CacheKeyManifest } from './key-manifest';\n\nimport {\n  CacheEntry,\n  ICache,\n  CacheKey,\n  CACHE_KEY_PREFIX,\n  WrappedCacheEntry,\n  DecodedToken,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  IdTokenEntry\n} from './shared';\n\nconst DEFAULT_EXPIRY_ADJUSTMENT_SECONDS = 0;\n\nexport class CacheManager {\n  private nowProvider: () => number | Promise<number>;\n\n  constructor(\n    private cache: ICache,\n    private keyManifest?: CacheKeyManifest,\n    nowProvider?: () => number | Promise<number>\n  ) {\n    this.nowProvider = nowProvider || DEFAULT_NOW_PROVIDER;\n  }\n\n  async setIdToken(\n    clientId: string,\n    idToken: string,\n    decodedToken: DecodedToken\n  ): Promise<void> {\n    const cacheKey = this.getIdTokenCacheKey(clientId);\n    await this.cache.set(cacheKey, {\n      id_token: idToken,\n      decodedToken\n    });\n    await this.keyManifest?.add(cacheKey);\n  }\n\n  async getIdToken(cacheKey: <PERSON>acheK<PERSON>): Promise<IdTokenEntry | undefined> {\n    const entry = await this.cache.get<IdTokenEntry>(\n      this.getIdTokenCacheKey(cacheKey.clientId)\n    );\n\n    if (!entry && cacheKey.scope && cacheKey.audience) {\n      const entryByScope = await this.get(cacheKey);\n\n      if (!entryByScope) {\n        return;\n      }\n\n      if (!entryByScope.id_token || !entryByScope.decodedToken) {\n        return;\n      }\n\n      return {\n        id_token: entryByScope.id_token,\n        decodedToken: entryByScope.decodedToken\n      };\n    }\n\n    if (!entry) {\n      return;\n    }\n\n    return { id_token: entry.id_token, decodedToken: entry.decodedToken };\n  }\n\n  async get(\n    cacheKey: CacheKey,\n    expiryAdjustmentSeconds = DEFAULT_EXPIRY_ADJUSTMENT_SECONDS\n  ): Promise<Partial<CacheEntry> | undefined> {\n    let wrappedEntry = await this.cache.get<WrappedCacheEntry>(\n      cacheKey.toKey()\n    );\n\n    if (!wrappedEntry) {\n      const keys = await this.getCacheKeys();\n\n      if (!keys) return;\n\n      const matchedKey = this.matchExistingCacheKey(cacheKey, keys);\n\n      if (matchedKey) {\n        wrappedEntry = await this.cache.get<WrappedCacheEntry>(matchedKey);\n      }\n    }\n\n    // If we still don't have an entry, exit.\n    if (!wrappedEntry) {\n      return;\n    }\n\n    const now = await this.nowProvider();\n    const nowSeconds = Math.floor(now / 1000);\n\n    if (wrappedEntry.expiresAt - expiryAdjustmentSeconds < nowSeconds) {\n      if (wrappedEntry.body.refresh_token) {\n        wrappedEntry.body = {\n          refresh_token: wrappedEntry.body.refresh_token\n        };\n\n        await this.cache.set(cacheKey.toKey(), wrappedEntry);\n        return wrappedEntry.body;\n      }\n\n      await this.cache.remove(cacheKey.toKey());\n      await this.keyManifest?.remove(cacheKey.toKey());\n\n      return;\n    }\n\n    return wrappedEntry.body;\n  }\n\n  async set(entry: CacheEntry): Promise<void> {\n    const cacheKey = new CacheKey({\n      clientId: entry.client_id,\n      scope: entry.scope,\n      audience: entry.audience\n    });\n\n    const wrappedEntry = await this.wrapCacheEntry(entry);\n\n    await this.cache.set(cacheKey.toKey(), wrappedEntry);\n    await this.keyManifest?.add(cacheKey.toKey());\n  }\n\n  async clear(clientId?: string): Promise<void> {\n    const keys = await this.getCacheKeys();\n\n    /* c8 ignore next */\n    if (!keys) return;\n\n    await keys\n      .filter(key => (clientId ? key.includes(clientId) : true))\n      .reduce(async (memo, key) => {\n        await memo;\n        await this.cache.remove(key);\n      }, Promise.resolve());\n\n    await this.keyManifest?.clear();\n  }\n\n  private async wrapCacheEntry(entry: CacheEntry): Promise<WrappedCacheEntry> {\n    const now = await this.nowProvider();\n    const expiresInTime = Math.floor(now / 1000) + entry.expires_in;\n\n    return {\n      body: entry,\n      expiresAt: expiresInTime\n    };\n  }\n\n  private async getCacheKeys(): Promise<string[] | undefined> {\n    if (this.keyManifest) {\n      return (await this.keyManifest.get())?.keys;\n    } else if (this.cache.allKeys) {\n      return this.cache.allKeys();\n    }\n  }\n\n  /**\n   * Returns the cache key to be used to store the id token\n   * @param clientId The client id used to link to the id token\n   * @returns The constructed cache key, as a string, to store the id token\n   */\n  private getIdTokenCacheKey(clientId: string) {\n    return new CacheKey(\n      { clientId },\n      CACHE_KEY_PREFIX,\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ).toKey();\n  }\n\n  /**\n   * Finds the corresponding key in the cache based on the provided cache key.\n   * The keys inside the cache are in the format {prefix}::{clientId}::{audience}::{scope}.\n   * The first key in the cache that satisfies the following conditions is returned\n   *  - `prefix` is strict equal to Auth0's internally configured `keyPrefix`\n   *  - `clientId` is strict equal to the `cacheKey.clientId`\n   *  - `audience` is strict equal to the `cacheKey.audience`\n   *  - `scope` contains at least all the `cacheKey.scope` values\n   *  *\n   * @param keyToMatch The provided cache key\n   * @param allKeys A list of existing cache keys\n   */\n  private matchExistingCacheKey(keyToMatch: CacheKey, allKeys: Array<string>) {\n    return allKeys.filter(key => {\n      const cacheKey = CacheKey.fromKey(key);\n      const scopeSet = new Set(cacheKey.scope && cacheKey.scope.split(' '));\n      const scopesToMatch = keyToMatch.scope?.split(' ') || [];\n\n      const hasAllScopes =\n        cacheKey.scope &&\n        scopesToMatch.reduce(\n          (acc, current) => acc && scopeSet.has(current),\n          true\n        );\n\n      return (\n        cacheKey.prefix === CACHE_KEY_PREFIX &&\n        cacheKey.clientId === keyToMatch.clientId &&\n        cacheKey.audience === keyToMatch.audience &&\n        hasAllScopes\n      );\n    })[0];\n  }\n}\n", "import { ClientStorage } from './storage';\n\nconst TRANSACTION_STORAGE_KEY_PREFIX = 'a0.spajs.txs';\n\ninterface Transaction {\n  nonce: string;\n  scope: string;\n  audience: string;\n  appState?: any;\n  code_verifier: string;\n  redirect_uri?: string;\n  organization?: string;\n  state?: string;\n}\n\nexport class TransactionManager {\n  private storageKey: string;\n\n  constructor(\n    private storage: ClientStorage,\n    private clientId: string,\n    private cookieDomain?: string\n  ) {\n    this.storageKey = `${TRANSACTION_STORAGE_KEY_PREFIX}.${this.clientId}`;\n  }\n\n  public create(transaction: Transaction) {\n    this.storage.save(this.storageKey, transaction, {\n      daysUntilExpire: 1,\n      cookieDomain: this.cookieDomain\n    });\n  }\n\n  public get(): Transaction | undefined {\n    return this.storage.get(this.storageKey);\n  }\n\n  public remove() {\n    this.storage.remove(this.storageKey, {\n      cookieDomain: this.cookieDomain\n    });\n  }\n}\n", "import { urlDecodeB64 } from './utils';\nimport { IdToken, JWTVerifyOptions } from './global';\n\nconst isNumber = (n: any) => typeof n === 'number';\n\nconst idTokendecoded = [\n  'iss',\n  'aud',\n  'exp',\n  'nbf',\n  'iat',\n  'jti',\n  'azp',\n  'nonce',\n  'auth_time',\n  'at_hash',\n  'c_hash',\n  'acr',\n  'amr',\n  'sub_jwk',\n  'cnf',\n  'sip_from_tag',\n  'sip_date',\n  'sip_callid',\n  'sip_cseq_num',\n  'sip_via_branch',\n  'orig',\n  'dest',\n  'mky',\n  'events',\n  'toe',\n  'txn',\n  'rph',\n  'sid',\n  'vot',\n  'vtm'\n];\n\nexport const decode = (token: string) => {\n  const parts = token.split('.');\n  const [header, payload, signature] = parts;\n\n  if (parts.length !== 3 || !header || !payload || !signature) {\n    throw new Error('ID token could not be decoded');\n  }\n  const payloadJSON = JSON.parse(urlDecodeB64(payload));\n  const claims: IdToken = { __raw: token };\n  const user: any = {};\n  Object.keys(payloadJSON).forEach(k => {\n    claims[k] = payloadJSON[k];\n    if (!idTokendecoded.includes(k)) {\n      user[k] = payloadJSON[k];\n    }\n  });\n  return {\n    encoded: { header, payload, signature },\n    header: JSON.parse(urlDecodeB64(header)),\n    claims,\n    user\n  };\n};\n\nexport const verify = (options: JWTVerifyOptions) => {\n  if (!options.id_token) {\n    throw new Error('ID token is required but missing');\n  }\n\n  const decoded = decode(options.id_token);\n\n  if (!decoded.claims.iss) {\n    throw new Error(\n      'Issuer (iss) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.claims.iss !== options.iss) {\n    throw new Error(\n      `Issuer (iss) claim mismatch in the ID token; expected \"${options.iss}\", found \"${decoded.claims.iss}\"`\n    );\n  }\n\n  if (!decoded.user.sub) {\n    throw new Error(\n      'Subject (sub) claim must be a string present in the ID token'\n    );\n  }\n\n  if (decoded.header.alg !== 'RS256') {\n    throw new Error(\n      `Signature algorithm of \"${decoded.header.alg}\" is not supported. Expected the ID token to be signed with \"RS256\".`\n    );\n  }\n\n  if (\n    !decoded.claims.aud ||\n    !(\n      typeof decoded.claims.aud === 'string' ||\n      Array.isArray(decoded.claims.aud)\n    )\n  ) {\n    throw new Error(\n      'Audience (aud) claim must be a string or array of strings present in the ID token'\n    );\n  }\n  if (Array.isArray(decoded.claims.aud)) {\n    if (!decoded.claims.aud.includes(options.aud)) {\n      throw new Error(\n        `Audience (aud) claim mismatch in the ID token; expected \"${\n          options.aud\n        }\" but was not one of \"${decoded.claims.aud.join(', ')}\"`\n      );\n    }\n    if (decoded.claims.aud.length > 1) {\n      if (!decoded.claims.azp) {\n        throw new Error(\n          'Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values'\n        );\n      }\n      if (decoded.claims.azp !== options.aud) {\n        throw new Error(\n          `Authorized Party (azp) claim mismatch in the ID token; expected \"${options.aud}\", found \"${decoded.claims.azp}\"`\n        );\n      }\n    }\n  } else if (decoded.claims.aud !== options.aud) {\n    throw new Error(\n      `Audience (aud) claim mismatch in the ID token; expected \"${options.aud}\" but found \"${decoded.claims.aud}\"`\n    );\n  }\n  if (options.nonce) {\n    if (!decoded.claims.nonce) {\n      throw new Error(\n        'Nonce (nonce) claim must be a string present in the ID token'\n      );\n    }\n    if (decoded.claims.nonce !== options.nonce) {\n      throw new Error(\n        `Nonce (nonce) claim mismatch in the ID token; expected \"${options.nonce}\", found \"${decoded.claims.nonce}\"`\n      );\n    }\n  }\n\n  if (options.max_age && !isNumber(decoded.claims.auth_time)) {\n    throw new Error(\n      'Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified'\n    );\n  }\n\n  /* c8 ignore next 5 */\n  if (decoded.claims.exp == null || !isNumber(decoded.claims.exp)) {\n    throw new Error(\n      'Expiration Time (exp) claim must be a number present in the ID token'\n    );\n  }\n  if (!isNumber(decoded.claims.iat)) {\n    throw new Error(\n      'Issued At (iat) claim must be a number present in the ID token'\n    );\n  }\n\n  const leeway = options.leeway || 60;\n  const now = new Date(options.now || Date.now());\n  const expDate = new Date(0);\n\n  expDate.setUTCSeconds(decoded.claims.exp + leeway);\n\n  if (now > expDate) {\n    throw new Error(\n      `Expiration Time (exp) claim error in the ID token; current time (${now}) is after expiration time (${expDate})`\n    );\n  }\n\n  if (decoded.claims.nbf != null && isNumber(decoded.claims.nbf)) {\n    const nbfDate = new Date(0);\n    nbfDate.setUTCSeconds(decoded.claims.nbf - leeway);\n    if (now < nbfDate) {\n      throw new Error(\n        `Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${now}) is before ${nbfDate}`\n      );\n    }\n  }\n\n  if (decoded.claims.auth_time != null && isNumber(decoded.claims.auth_time)) {\n    const authTimeDate = new Date(0);\n    authTimeDate.setUTCSeconds(\n      parseInt(decoded.claims.auth_time) + (options.max_age as number) + leeway\n    );\n\n    if (now > authTimeDate) {\n      throw new Error(\n        `Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${now}) is after last auth at ${authTimeDate}`\n      );\n    }\n  }\n\n  if (options.organization) {\n    const org = options.organization.trim();\n    if (org.startsWith('org_')) {\n      const orgId = org;\n      if (!decoded.claims.org_id) {\n        throw new Error(\n          'Organization ID (org_id) claim must be a string present in the ID token'\n        );\n      } else if (orgId !== decoded.claims.org_id) {\n        throw new Error(\n          `Organization ID (org_id) claim mismatch in the ID token; expected \"${orgId}\", found \"${decoded.claims.org_id}\"`\n        );\n      }\n    } else {\n      const orgName = org.toLowerCase();\n      // TODO should we verify if there is an `org_id` claim?\n      if (!decoded.claims.org_name) {\n        throw new Error(\n          'Organization Name (org_name) claim must be a string present in the ID token'\n        );\n      } else if (orgName !== decoded.claims.org_name) {\n        throw new Error(\n          `Organization Name (org_name) claim mismatch in the ID token; expected \"${orgName}\", found \"${decoded.claims.org_name}\"`\n        );\n      }\n    }\n  }\n\n  return decoded;\n};\n", "\"use strict\";\r\nvar __assign = (this && this.__assign) || function () {\r\n    __assign = Object.assign || function(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\r\n                t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\nexports.__esModule = true;\r\nfunction stringifyAttribute(name, value) {\r\n    if (!value) {\r\n        return '';\r\n    }\r\n    var stringified = '; ' + name;\r\n    if (value === true) {\r\n        return stringified; // boolean attributes shouldn't have a value\r\n    }\r\n    return stringified + '=' + value;\r\n}\r\nfunction stringifyAttributes(attributes) {\r\n    if (typeof attributes.expires === 'number') {\r\n        var expires = new Date();\r\n        expires.setMilliseconds(expires.getMilliseconds() + attributes.expires * 864e+5);\r\n        attributes.expires = expires;\r\n    }\r\n    return stringifyAttribute('Expires', attributes.expires ? attributes.expires.toUTCString() : '')\r\n        + stringifyAttribute('Domain', attributes.domain)\r\n        + stringifyAttribute('Path', attributes.path)\r\n        + stringifyAttribute('Secure', attributes.secure)\r\n        + stringifyAttribute('SameSite', attributes.sameSite);\r\n}\r\nfunction encode(name, value, attributes) {\r\n    return encodeURIComponent(name)\r\n        .replace(/%(23|24|26|2B|5E|60|7C)/g, decodeURIComponent) // allowed special characters\r\n        .replace(/\\(/g, '%28').replace(/\\)/g, '%29') // replace opening and closing parens\r\n        + '=' + encodeURIComponent(value)\r\n        // allowed special characters\r\n        .replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g, decodeURIComponent)\r\n        + stringifyAttributes(attributes);\r\n}\r\nexports.encode = encode;\r\nfunction parse(cookieString) {\r\n    var result = {};\r\n    var cookies = cookieString ? cookieString.split('; ') : [];\r\n    var rdecode = /(%[\\dA-F]{2})+/gi;\r\n    for (var i = 0; i < cookies.length; i++) {\r\n        var parts = cookies[i].split('=');\r\n        var cookie = parts.slice(1).join('=');\r\n        if (cookie.charAt(0) === '\"') {\r\n            cookie = cookie.slice(1, -1);\r\n        }\r\n        try {\r\n            var name_1 = parts[0].replace(rdecode, decodeURIComponent);\r\n            result[name_1] = cookie.replace(rdecode, decodeURIComponent);\r\n        }\r\n        catch (e) {\r\n            // ignore cookies with invalid name/value encoding\r\n        }\r\n    }\r\n    return result;\r\n}\r\nexports.parse = parse;\r\nfunction getAll() {\r\n    return parse(document.cookie);\r\n}\r\nexports.getAll = getAll;\r\nfunction get(name) {\r\n    return getAll()[name];\r\n}\r\nexports.get = get;\r\nfunction set(name, value, attributes) {\r\n    document.cookie = encode(name, value, __assign({ path: '/' }, attributes));\r\n}\r\nexports.set = set;\r\nfunction remove(name, attributes) {\r\n    set(name, '', __assign(__assign({}, attributes), { expires: -1 }));\r\n}\r\nexports.remove = remove;\r\n", "import * as Cookies from 'es-cookie';\n\ninterface ClientStorageOptions {\n  daysUntilExpire?: number;\n  cookieDomain?: string;\n}\n\n/**\n * Defines a type that handles storage to/from a storage location\n */\nexport type ClientStorage = {\n  get<T extends Object>(key: string): T | undefined;\n  save(key: string, value: any, options?: ClientStorageOptions): void;\n  remove(key: string, options?: ClientStorageOptions): void;\n};\n\n/**\n * A storage protocol for marshalling data to/from cookies\n */\nexport const CookieStorage = {\n  get<T extends Object>(key: string) {\n    const value = Cookies.get(key);\n\n    if (typeof value === 'undefined') {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = {\n        secure: true,\n        sameSite: 'none'\n      };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(key, JSON.stringify(value), cookieAttributes);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n  }\n} as ClientStorage;\n\n/**\n * @ignore\n */\nconst LEGACY_PREFIX = '_legacy_';\n\n/**\n * Cookie storage that creates a cookie for modern and legacy browsers.\n * See: https://web.dev/samesite-cookie-recipes/#handling-incompatible-clients\n */\nexport const CookieStorageWithLegacySameSite = {\n  get<T extends Object>(key: string) {\n    const value = CookieStorage.get<T>(key);\n\n    if (value) {\n      return value;\n    }\n\n    return CookieStorage.get<T>(`${LEGACY_PREFIX}${key}`);\n  },\n\n  save(key: string, value: any, options?: ClientStorageOptions): void {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if ('https:' === window.location.protocol) {\n      cookieAttributes = { secure: true };\n    }\n\n    if (options?.daysUntilExpire) {\n      cookieAttributes.expires = options.daysUntilExpire;\n    }\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.set(\n      `${LEGACY_PREFIX}${key}`,\n      JSON.stringify(value),\n      cookieAttributes\n    );\n    CookieStorage.save(key, value, options);\n  },\n\n  remove(key: string, options?: ClientStorageOptions) {\n    let cookieAttributes: Cookies.CookieAttributes = {};\n\n    if (options?.cookieDomain) {\n      cookieAttributes.domain = options.cookieDomain;\n    }\n\n    Cookies.remove(key, cookieAttributes);\n    CookieStorage.remove(key, options);\n    CookieStorage.remove(`${LEGACY_PREFIX}${key}`, options);\n  }\n} as ClientStorage;\n\n/**\n * A storage protocol for marshalling data to/from session storage\n */\nexport const SessionStorage = {\n  get<T extends Object>(key: string) {\n    /* c8 ignore next 3 */\n    if (typeof sessionStorage === 'undefined') {\n      return;\n    }\n\n    const value = sessionStorage.getItem(key);\n\n    if (value == null) {\n      return;\n    }\n\n    return <T>JSON.parse(value);\n  },\n\n  save(key: string, value: any): void {\n    sessionStorage.setItem(key, JSON.stringify(value));\n  },\n\n  remove(key: string) {\n    sessionStorage.removeItem(key);\n  }\n} as ClientStorage;\n", "const singlePromiseMap: Record<string, Promise<any>> = {};\n\nexport const singlePromise = <T>(\n  cb: () => Promise<T>,\n  key: string\n): Promise<T> => {\n  let promise: null | Promise<T> = singlePromiseMap[key];\n  if (!promise) {\n    promise = cb().finally(() => {\n      delete singlePromiseMap[key];\n      promise = null;\n    });\n    singlePromiseMap[key] = promise;\n  }\n  return promise;\n};\n\nexport const retryPromise = async (\n  cb: () => Promise<boolean>,\n  maxNumberOfRetries = 3\n) => {\n  for (let i = 0; i < maxNumberOfRetries; i++) {\n    if (await cb()) {\n      return true;\n    }\n  }\n\n  return false;\n};\n", "import {\n  CACHE_KEY_PREFIX,\n  ICache,\n  KeyManifestEntry,\n  MaybePromise\n} from './shared';\n\nexport class CacheKeyManifest {\n  private readonly manifestKey: string;\n\n  constructor(private cache: ICache, private clientId: string) {\n    this.manifestKey = this.createManifestKeyFrom(this.clientId);\n  }\n\n  async add(key: string): Promise<void> {\n    const keys = new Set(\n      (await this.cache.get<KeyManifestEntry>(this.manifestKey))?.keys || []\n    );\n\n    keys.add(key);\n\n    await this.cache.set<KeyManifestEntry>(this.manifestKey, {\n      keys: [...keys]\n    });\n  }\n\n  async remove(key: string): Promise<void> {\n    const entry = await this.cache.get<KeyManifestEntry>(this.manifestKey);\n\n    if (entry) {\n      const keys = new Set(entry.keys);\n      keys.delete(key);\n\n      if (keys.size > 0) {\n        return await this.cache.set(this.manifestKey, { keys: [...keys] });\n      }\n\n      return await this.cache.remove(this.manifestKey);\n    }\n  }\n\n  get(): MaybePromise<KeyManifestEntry | undefined> {\n    return this.cache.get<KeyManifestEntry>(this.manifestKey);\n  }\n\n  clear(): MaybePromise<void> {\n    return this.cache.remove(this.manifestKey);\n  }\n\n  private createManifestKeyFrom(clientId: string): string {\n    return `${CACHE_KEY_PREFIX}::${clientId}`;\n  }\n}\n", "import { ICache, InMemoryCache, LocalStorageCache } from './cache';\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  LogoutOptions\n} from './global';\nimport { getUniqueScopes } from './scope';\n\n/**\n * @ignore\n */\nexport const GET_TOKEN_SILENTLY_LOCK_KEY = 'auth0.lock.getTokenSilently';\n\n/**\n * @ignore\n */\nexport const buildOrganizationHintCookieName = (clientId: string) =>\n  `auth0.${clientId}.organization_hint`;\n\n/**\n * @ignore\n */\nexport const OLD_IS_AUTHENTICATED_COOKIE_NAME = 'auth0.is.authenticated';\n\n/**\n * @ignore\n */\nexport const buildIsAuthenticatedCookieName = (clientId: string) =>\n  `auth0.${clientId}.is.authenticated`;\n\n/**\n * @ignore\n */\nconst cacheLocationBuilders: Record<string, () => ICache> = {\n  memory: () => new InMemoryCache().enclosedCache,\n  localstorage: () => new LocalStorageCache()\n};\n\n/**\n * @ignore\n */\nexport const cacheFactory = (location: string) => {\n  return cacheLocationBuilders[location];\n};\n\n/**\n * @ignore\n */\nexport const getAuthorizeParams = (\n  clientOptions: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  },\n  scope: string,\n  authorizationParams: AuthorizationParams,\n  state: string,\n  nonce: string,\n  code_challenge: string,\n  redirect_uri: string | undefined,\n  response_mode: string | undefined\n): AuthorizeOptions => {\n  return {\n    client_id: clientOptions.clientId,\n    ...clientOptions.authorizationParams,\n    ...authorizationParams,\n    scope: getUniqueScopes(scope, authorizationParams.scope),\n    response_type: 'code',\n    response_mode: response_mode || 'query',\n    state,\n    nonce,\n    redirect_uri:\n      redirect_uri || clientOptions.authorizationParams.redirect_uri,\n    code_challenge,\n    code_challenge_method: 'S256'\n  };\n};\n\n/**\n * @ignore\n *\n * Function used to provide support for the deprecated onRedirect through openUrl.\n */\nexport const patchOpenUrlWithOnRedirect = <\n  T extends Pick<LogoutOptions, 'openUrl' | 'onRedirect'>\n>(\n  options: T\n) => {\n  const { openUrl, onRedirect, ...originalOptions } = options;\n\n  const result = {\n    ...originalOptions,\n    openUrl: openUrl === false || openUrl ? openUrl : onRedirect\n  };\n\n  return result as T;\n};\n", "import Lock from 'browser-tabs-lock';\n\nimport {\n  createQuery<PERSON>ara<PERSON>,\n  runPopup,\n  parseAuthenticationResult,\n  encode,\n  createRandomString,\n  runIframe,\n  sha256,\n  bufferToBase64UrlEncoded,\n  validateCrypto,\n  openPopup,\n  getDomain,\n  getTokenIssuer,\n  parseNumber\n} from './utils';\n\nimport { oauthToken } from './api';\n\nimport { getUniqueScopes } from './scope';\n\nimport {\n  InMemoryCache,\n  ICache,\n  <PERSON>ache<PERSON>ey,\n  CacheManager,\n  CacheEntry,\n  IdTokenEntry,\n  CACHE_KEY_ID_TOKEN_SUFFIX,\n  DecodedToken\n} from './cache';\n\nimport { TransactionManager } from './transaction-manager';\nimport { verify as verifyIdToken } from './jwt';\nimport {\n  AuthenticationError,\n  GenericError,\n  MissingRefreshTokenError,\n  TimeoutError\n} from './errors';\n\nimport {\n  ClientStorage,\n  CookieStorage,\n  CookieStorageWithLegacySameSite,\n  SessionStorage\n} from './storage';\n\nimport {\n  CACHE_LOCATION_MEMORY,\n  DEFAULT_POPUP_CONFIG_OPTIONS,\n  DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS,\n  MISSING_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_SCOPE,\n  DEFAULT_SESSION_CHECK_EXPIRY_DAYS,\n  DEFAULT_AUTH0_CLIENT,\n  INVALID_REFRESH_TOKEN_ERROR_MESSAGE,\n  DEFAULT_NOW_PROVIDER,\n  DEFAULT_FETCH_TIMEOUT_MS\n} from './constants';\n\nimport {\n  Auth0ClientOptions,\n  AuthorizationParams,\n  AuthorizeOptions,\n  RedirectLoginOptions,\n  PopupLoginOptions,\n  PopupConfigOptions,\n  RedirectLoginResult,\n  GetTokenSilentlyOptions,\n  GetTokenWithPopupOptions,\n  LogoutOptions,\n  CacheLocation,\n  LogoutUrlOptions,\n  User,\n  IdToken,\n  GetTokenSilentlyVerboseResponse,\n  TokenEndpointResponse\n} from './global';\n\n// @ts-ignore\nimport TokenWorker from './worker/token.worker.ts';\nimport { singlePromise, retryPromise } from './promise-utils';\nimport { CacheKeyManifest } from './cache/key-manifest';\nimport {\n  buildIsAuthenticatedCookieName,\n  buildOrganizationHintCookieName,\n  cacheFactory,\n  getAuthorizeParams,\n  GET_TOKEN_SILENTLY_LOCK_KEY,\n  OLD_IS_AUTHENTICATED_COOKIE_NAME,\n  patchOpenUrlWithOnRedirect\n} from './Auth0Client.utils';\nimport { CustomTokenExchangeOptions } from './TokenExchange';\n\n/**\n * @ignore\n */\ntype GetTokenSilentlyResult = TokenEndpointResponse & {\n  decodedToken: ReturnType<typeof verifyIdToken>;\n  scope: string;\n  oauthTokenScope?: string;\n  audience: string;\n};\n\n/**\n * @ignore\n */\nconst lock = new Lock();\n\n/**\n * Auth0 SDK for Single Page Applications using [Authorization Code Grant Flow with PKCE](https://auth0.com/docs/api-auth/tutorials/authorization-code-grant-pkce).\n */\nexport class Auth0Client {\n  private readonly transactionManager: TransactionManager;\n  private readonly cacheManager: CacheManager;\n  private readonly domainUrl: string;\n  private readonly tokenIssuer: string;\n  private readonly scope: string;\n  private readonly cookieStorage: ClientStorage;\n  private readonly sessionCheckExpiryDays: number;\n  private readonly orgHintCookieName: string;\n  private readonly isAuthenticatedCookieName: string;\n  private readonly nowProvider: () => number | Promise<number>;\n  private readonly httpTimeoutMs: number;\n  private readonly options: Auth0ClientOptions & {\n    authorizationParams: AuthorizationParams;\n  };\n  private readonly userCache: ICache = new InMemoryCache().enclosedCache;\n\n  private worker?: Worker;\n\n  private readonly defaultOptions: Partial<Auth0ClientOptions> = {\n    authorizationParams: {\n      scope: DEFAULT_SCOPE\n    },\n    useRefreshTokensFallback: false,\n    useFormData: true\n  };\n\n  constructor(options: Auth0ClientOptions) {\n    this.options = {\n      ...this.defaultOptions,\n      ...options,\n      authorizationParams: {\n        ...this.defaultOptions.authorizationParams,\n        ...options.authorizationParams\n      }\n    };\n\n    typeof window !== 'undefined' && validateCrypto();\n\n    if (options.cache && options.cacheLocation) {\n      console.warn(\n        'Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`.'\n      );\n    }\n\n    let cacheLocation: CacheLocation | undefined;\n    let cache: ICache;\n\n    if (options.cache) {\n      cache = options.cache;\n    } else {\n      cacheLocation = options.cacheLocation || CACHE_LOCATION_MEMORY;\n\n      if (!cacheFactory(cacheLocation)) {\n        throw new Error(`Invalid cache location \"${cacheLocation}\"`);\n      }\n\n      cache = cacheFactory(cacheLocation)();\n    }\n\n    this.httpTimeoutMs = options.httpTimeoutInSeconds\n      ? options.httpTimeoutInSeconds * 1000\n      : DEFAULT_FETCH_TIMEOUT_MS;\n\n    this.cookieStorage =\n      options.legacySameSiteCookie === false\n        ? CookieStorage\n        : CookieStorageWithLegacySameSite;\n\n    this.orgHintCookieName = buildOrganizationHintCookieName(\n      this.options.clientId\n    );\n\n    this.isAuthenticatedCookieName = buildIsAuthenticatedCookieName(\n      this.options.clientId\n    );\n\n    this.sessionCheckExpiryDays =\n      options.sessionCheckExpiryDays || DEFAULT_SESSION_CHECK_EXPIRY_DAYS;\n\n    const transactionStorage = options.useCookiesForTransactions\n      ? this.cookieStorage\n      : SessionStorage;\n\n    // Construct the scopes based on the following:\n    // 1. Always include `openid`\n    // 2. Include the scopes provided in `authorizationParams. This defaults to `profile email`\n    // 3. Add `offline_access` if `useRefreshTokens` is enabled\n    this.scope = getUniqueScopes(\n      'openid',\n      this.options.authorizationParams.scope,\n      this.options.useRefreshTokens ? 'offline_access' : ''\n    );\n\n    this.transactionManager = new TransactionManager(\n      transactionStorage,\n      this.options.clientId,\n      this.options.cookieDomain\n    );\n\n    this.nowProvider = this.options.nowProvider || DEFAULT_NOW_PROVIDER;\n\n    this.cacheManager = new CacheManager(\n      cache,\n      !cache.allKeys\n        ? new CacheKeyManifest(cache, this.options.clientId)\n        : undefined,\n      this.nowProvider\n    );\n\n    this.domainUrl = getDomain(this.options.domain);\n    this.tokenIssuer = getTokenIssuer(this.options.issuer, this.domainUrl);\n\n    // Don't use web workers unless using refresh tokens in memory\n    if (\n      typeof window !== 'undefined' &&\n      window.Worker &&\n      this.options.useRefreshTokens &&\n      cacheLocation === CACHE_LOCATION_MEMORY\n    ) {\n      if (this.options.workerUrl) {\n        this.worker = new Worker(this.options.workerUrl);\n      } else {\n        this.worker = new TokenWorker();\n      }\n    }\n  }\n\n  private _url(path: string) {\n    const auth0Client = encodeURIComponent(\n      btoa(JSON.stringify(this.options.auth0Client || DEFAULT_AUTH0_CLIENT))\n    );\n    return `${this.domainUrl}${path}&auth0Client=${auth0Client}`;\n  }\n\n  private _authorizeUrl(authorizeOptions: AuthorizeOptions) {\n    return this._url(`/authorize?${createQueryParams(authorizeOptions)}`);\n  }\n\n  private async _verifyIdToken(\n    id_token: string,\n    nonce?: string,\n    organization?: string\n  ) {\n    const now = await this.nowProvider();\n\n    return verifyIdToken({\n      iss: this.tokenIssuer,\n      aud: this.options.clientId,\n      id_token,\n      nonce,\n      organization,\n      leeway: this.options.leeway,\n      max_age: parseNumber(this.options.authorizationParams.max_age),\n      now\n    });\n  }\n\n  private _processOrgHint(organization?: string) {\n    if (organization) {\n      this.cookieStorage.save(this.orgHintCookieName, organization, {\n        daysUntilExpire: this.sessionCheckExpiryDays,\n        cookieDomain: this.options.cookieDomain\n      });\n    } else {\n      this.cookieStorage.remove(this.orgHintCookieName, {\n        cookieDomain: this.options.cookieDomain\n      });\n    }\n  }\n\n  private async _prepareAuthorizeUrl(\n    authorizationParams: AuthorizationParams,\n    authorizeOptions?: Partial<AuthorizeOptions>,\n    fallbackRedirectUri?: string\n  ): Promise<{\n    scope: string;\n    audience: string;\n    redirect_uri?: string;\n    nonce: string;\n    code_verifier: string;\n    state: string;\n    url: string;\n  }> {\n    const state = encode(createRandomString());\n    const nonce = encode(createRandomString());\n    const code_verifier = createRandomString();\n    const code_challengeBuffer = await sha256(code_verifier);\n    const code_challenge = bufferToBase64UrlEncoded(code_challengeBuffer);\n\n    const params = getAuthorizeParams(\n      this.options,\n      this.scope,\n      authorizationParams,\n      state,\n      nonce,\n      code_challenge,\n      authorizationParams.redirect_uri ||\n        this.options.authorizationParams.redirect_uri ||\n        fallbackRedirectUri,\n      authorizeOptions?.response_mode\n    );\n\n    const url = this._authorizeUrl(params);\n\n    return {\n      nonce,\n      code_verifier,\n      scope: params.scope,\n      audience: params.audience || 'default',\n      redirect_uri: params.redirect_uri,\n      state,\n      url\n    };\n  }\n\n  /**\n   * ```js\n   * try {\n   *  await auth0.loginWithPopup(options);\n   * } catch(e) {\n   *  if (e instanceof PopupCancelledError) {\n   *    // Popup was closed before login completed\n   *  }\n   * }\n   * ```\n   *\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * IMPORTANT: This method has to be called from an event handler\n   * that was started by the user like a button click, for example,\n   * otherwise the popup will be blocked in most browsers.\n   *\n   * @param options\n   * @param config\n   */\n  public async loginWithPopup(\n    options?: PopupLoginOptions,\n    config?: PopupConfigOptions\n  ) {\n    options = options || {};\n    config = config || {};\n\n    if (!config.popup) {\n      config.popup = openPopup('');\n\n      if (!config.popup) {\n        throw new Error(\n          'Unable to open a popup for loginWithPopup - window.open returned `null`'\n        );\n      }\n    }\n\n    const params = await this._prepareAuthorizeUrl(\n      options.authorizationParams || {},\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    config.popup.location.href = params.url;\n\n    const codeResult = await runPopup({\n      ...config,\n      timeoutInSeconds:\n        config.timeoutInSeconds ||\n        this.options.authorizeTimeoutInSeconds ||\n        DEFAULT_AUTHORIZE_TIMEOUT_IN_SECONDS\n    });\n\n    if (params.state !== codeResult.state) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization =\n      options.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    await this._requestToken(\n      {\n        audience: params.audience,\n        scope: params.scope,\n        code_verifier: params.code_verifier,\n        grant_type: 'authorization_code',\n        code: codeResult.code as string,\n        redirect_uri: params.redirect_uri\n      },\n      {\n        nonceIn: params.nonce,\n        organization\n      }\n    );\n  }\n\n  /**\n   * ```js\n   * const user = await auth0.getUser();\n   * ```\n   *\n   * Returns the user information if available (decoded\n   * from the `id_token`).\n   *\n   * @typeparam TUser The type to return, has to extend {@link User}.\n   */\n  public async getUser<TUser extends User>(): Promise<TUser | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.user as TUser;\n  }\n\n  /**\n   * ```js\n   * const claims = await auth0.getIdTokenClaims();\n   * ```\n   *\n   * Returns all claims from the id_token if available.\n   */\n  public async getIdTokenClaims(): Promise<IdToken | undefined> {\n    const cache = await this._getIdTokenFromCache();\n\n    return cache?.decodedToken?.claims;\n  }\n\n  /**\n   * ```js\n   * await auth0.loginWithRedirect(options);\n   * ```\n   *\n   * Performs a redirect to `/authorize` using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated.\n   *\n   * @param options\n   */\n  public async loginWithRedirect<TAppState = any>(\n    options: RedirectLoginOptions<TAppState> = {}\n  ) {\n    const { openUrl, fragment, appState, ...urlOptions } =\n      patchOpenUrlWithOnRedirect(options);\n\n    const organization =\n      urlOptions.authorizationParams?.organization ||\n      this.options.authorizationParams.organization;\n\n    const { url, ...transaction } = await this._prepareAuthorizeUrl(\n      urlOptions.authorizationParams || {}\n    );\n\n    this.transactionManager.create({\n      ...transaction,\n      appState,\n      ...(organization && { organization })\n    });\n\n    const urlWithFragment = fragment ? `${url}#${fragment}` : url;\n\n    if (openUrl) {\n      await openUrl(urlWithFragment);\n    } else {\n      window.location.assign(urlWithFragment);\n    }\n  }\n\n  /**\n   * After the browser redirects back to the callback page,\n   * call `handleRedirectCallback` to handle success and error\n   * responses from Auth0. If the response is successful, results\n   * will be valid according to their expiration times.\n   */\n  public async handleRedirectCallback<TAppState = any>(\n    url: string = window.location.href\n  ): Promise<RedirectLoginResult<TAppState>> {\n    const queryStringFragments = url.split('?').slice(1);\n\n    if (queryStringFragments.length === 0) {\n      throw new Error('There are no query params available for parsing.');\n    }\n\n    const { state, code, error, error_description } = parseAuthenticationResult(\n      queryStringFragments.join('')\n    );\n\n    const transaction = this.transactionManager.get();\n\n    if (!transaction) {\n      throw new GenericError('missing_transaction', 'Invalid state');\n    }\n\n    this.transactionManager.remove();\n\n    if (error) {\n      throw new AuthenticationError(\n        error,\n        error_description || error,\n        state,\n        transaction.appState\n      );\n    }\n\n    // Transaction should have a `code_verifier` to do PKCE for CSRF protection\n    if (\n      !transaction.code_verifier ||\n      (transaction.state && transaction.state !== state)\n    ) {\n      throw new GenericError('state_mismatch', 'Invalid state');\n    }\n\n    const organization = transaction.organization;\n    const nonceIn = transaction.nonce;\n    const redirect_uri = transaction.redirect_uri;\n\n    await this._requestToken(\n      {\n        audience: transaction.audience,\n        scope: transaction.scope,\n        code_verifier: transaction.code_verifier,\n        grant_type: 'authorization_code',\n        code: code as string,\n        ...(redirect_uri ? { redirect_uri } : {})\n      },\n      { nonceIn, organization }\n    );\n\n    return {\n      appState: transaction.appState\n    };\n  }\n\n  /**\n   * ```js\n   * await auth0.checkSession();\n   * ```\n   *\n   * Check if the user is logged in using `getTokenSilently`. The difference\n   * with `getTokenSilently` is that this doesn't return a token, but it will\n   * pre-fill the token cache.\n   *\n   * This method also heeds the `auth0.{clientId}.is.authenticated` cookie, as an optimization\n   *  to prevent calling Auth0 unnecessarily. If the cookie is not present because\n   * there was no previous login (or it has expired) then tokens will not be refreshed.\n   *\n   * It should be used for silently logging in the user when you instantiate the\n   * `Auth0Client` constructor. You should not need this if you are using the\n   * `createAuth0Client` factory.\n   *\n   * **Note:** the cookie **may not** be present if running an app using a private tab, as some\n   * browsers clear JS cookie data and local storage when the tab or page is closed, or on page reload. This effectively\n   * means that `checkSession` could silently return without authenticating the user on page refresh when\n   * using a private tab, despite having previously logged in. As a workaround, use `getTokenSilently` instead\n   * and handle the possible `login_required` error [as shown in the readme](https://github.com/auth0/auth0-spa-js#creating-the-client).\n   *\n   * @param options\n   */\n  public async checkSession(options?: GetTokenSilentlyOptions) {\n    if (!this.cookieStorage.get(this.isAuthenticatedCookieName)) {\n      if (!this.cookieStorage.get(OLD_IS_AUTHENTICATED_COOKIE_NAME)) {\n        return;\n      } else {\n        // Migrate the existing cookie to the new name scoped by client ID\n        this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n          daysUntilExpire: this.sessionCheckExpiryDays,\n          cookieDomain: this.options.cookieDomain\n        });\n\n        this.cookieStorage.remove(OLD_IS_AUTHENTICATED_COOKIE_NAME);\n      }\n    }\n\n    try {\n      await this.getTokenSilently(options);\n    } catch (_) {}\n  }\n\n  /**\n   * Fetches a new access token and returns the response from the /oauth/token endpoint, omitting the refresh token.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions & { detailedResponse: true }\n  ): Promise<GetTokenSilentlyVerboseResponse>;\n\n  /**\n   * Fetches a new access token and returns it.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options?: GetTokenSilentlyOptions\n  ): Promise<string>;\n\n  /**\n   * Fetches a new access token, and either returns just the access token (the default) or the response from the /oauth/token endpoint, depending on the `detailedResponse` option.\n   *\n   * ```js\n   * const token = await auth0.getTokenSilently(options);\n   * ```\n   *\n   * If there's a valid token stored and it has more than 60 seconds\n   * remaining before expiration, return the token. Otherwise, attempt\n   * to obtain a new token.\n   *\n   * A new token will be obtained either by opening an iframe or a\n   * refresh token (if `useRefreshTokens` is `true`).\n\n   * If iframes are used, opens an iframe with the `/authorize` URL\n   * using the parameters provided as arguments. Random and secure `state`\n   * and `nonce` parameters will be auto-generated. If the response is successful,\n   * results will be validated according to their expiration times.\n   *\n   * If refresh tokens are used, the token endpoint is called directly with the\n   * 'refresh_token' grant. If no refresh token is available to make this call,\n   * the SDK will only fall back to using an iframe to the '/authorize' URL if \n   * the `useRefreshTokensFallback` setting has been set to `true`. By default this\n   * setting is `false`.\n   *\n   * This method may use a web worker to perform the token call if the in-memory\n   * cache is used.\n   *\n   * If an `audience` value is given to this function, the SDK always falls\n   * back to using an iframe to make the token exchange.\n   *\n   * Note that in all cases, falling back to an iframe requires access to\n   * the `auth0` cookie.\n   *\n   * @param options\n   */\n  public async getTokenSilently(\n    options: GetTokenSilentlyOptions = {}\n  ): Promise<undefined | string | GetTokenSilentlyVerboseResponse> {\n    const localOptions: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    } = {\n      cacheMode: 'on',\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    const result = await singlePromise(\n      () => this._getTokenSilently(localOptions),\n      `${this.options.clientId}::${localOptions.authorizationParams.audience}::${localOptions.authorizationParams.scope}`\n    );\n\n    return options.detailedResponse ? result : result?.access_token;\n  }\n\n  private async _getTokenSilently(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const { cacheMode, ...getTokenOptions } = options;\n\n    // Check the cache before acquiring the lock to avoid the latency of\n    // `lock.acquireLock` when the cache is populated.\n    if (cacheMode !== 'off') {\n      const entry = await this._getEntryFromCache({\n        scope: getTokenOptions.authorizationParams.scope,\n        audience: getTokenOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      });\n\n      if (entry) {\n        return entry;\n      }\n    }\n\n    if (cacheMode === 'cache-only') {\n      return;\n    }\n\n    if (\n      await retryPromise(\n        () => lock.acquireLock(GET_TOKEN_SILENTLY_LOCK_KEY, 5000),\n        10\n      )\n    ) {\n      try {\n        window.addEventListener('pagehide', this._releaseLockOnPageHide);\n\n        // Check the cache a second time, because it may have been populated\n        // by a previous call while this call was waiting to acquire the lock.\n        if (cacheMode !== 'off') {\n          const entry = await this._getEntryFromCache({\n            scope: getTokenOptions.authorizationParams.scope,\n            audience: getTokenOptions.authorizationParams.audience || 'default',\n            clientId: this.options.clientId\n          });\n\n          if (entry) {\n            return entry;\n          }\n        }\n\n        const authResult = this.options.useRefreshTokens\n          ? await this._getTokenUsingRefreshToken(getTokenOptions)\n          : await this._getTokenFromIFrame(getTokenOptions);\n\n        const { id_token, access_token, oauthTokenScope, expires_in } =\n          authResult;\n\n        return {\n          id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        };\n      } finally {\n        await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n        window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n      }\n    } else {\n      throw new TimeoutError();\n    }\n  }\n\n  /**\n   * ```js\n   * const token = await auth0.getTokenWithPopup(options);\n   * ```\n   * Opens a popup with the `/authorize` URL using the parameters\n   * provided as arguments. Random and secure `state` and `nonce`\n   * parameters will be auto-generated. If the response is successful,\n   * results will be valid according to their expiration times.\n   *\n   * @param options\n   * @param config\n   */\n  public async getTokenWithPopup(\n    options: GetTokenWithPopupOptions = {},\n    config: PopupConfigOptions = {}\n  ) {\n    const localOptions = {\n      ...options,\n      authorizationParams: {\n        ...this.options.authorizationParams,\n        ...options.authorizationParams,\n        scope: getUniqueScopes(this.scope, options.authorizationParams?.scope)\n      }\n    };\n\n    config = {\n      ...DEFAULT_POPUP_CONFIG_OPTIONS,\n      ...config\n    };\n\n    await this.loginWithPopup(localOptions, config);\n\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: localOptions.authorizationParams.scope,\n        audience: localOptions.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    return cache!.access_token;\n  }\n\n  /**\n   * ```js\n   * const isAuthenticated = await auth0.isAuthenticated();\n   * ```\n   *\n   * Returns `true` if there's valid information stored,\n   * otherwise returns `false`.\n   *\n   */\n  public async isAuthenticated() {\n    const user = await this.getUser();\n    return !!user;\n  }\n\n  /**\n   * ```js\n   * await auth0.buildLogoutUrl(options);\n   * ```\n   *\n   * Builds a URL to the logout endpoint using the parameters provided as arguments.\n   * @param options\n   */\n  private _buildLogoutUrl(options: LogoutUrlOptions): string {\n    if (options.clientId !== null) {\n      options.clientId = options.clientId || this.options.clientId;\n    } else {\n      delete options.clientId;\n    }\n\n    const { federated, ...logoutOptions } = options.logoutParams || {};\n    const federatedQuery = federated ? `&federated` : '';\n    const url = this._url(\n      `/v2/logout?${createQueryParams({\n        clientId: options.clientId,\n        ...logoutOptions\n      })}`\n    );\n\n    return url + federatedQuery;\n  }\n\n  /**\n   * ```js\n   * await auth0.logout(options);\n   * ```\n   *\n   * Clears the application session and performs a redirect to `/v2/logout`, using\n   * the parameters provided as arguments, to clear the Auth0 session.\n   *\n   * If the `federated` option is specified it also clears the Identity Provider session.\n   * [Read more about how Logout works at Auth0](https://auth0.com/docs/logout).\n   *\n   * @param options\n   */\n  public async logout(options: LogoutOptions = {}): Promise<void> {\n    const { openUrl, ...logoutOptions } = patchOpenUrlWithOnRedirect(options);\n\n    if (options.clientId === null) {\n      await this.cacheManager.clear();\n    } else {\n      await this.cacheManager.clear(options.clientId || this.options.clientId);\n    }\n\n    this.cookieStorage.remove(this.orgHintCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.cookieStorage.remove(this.isAuthenticatedCookieName, {\n      cookieDomain: this.options.cookieDomain\n    });\n    this.userCache.remove(CACHE_KEY_ID_TOKEN_SUFFIX);\n\n    const url = this._buildLogoutUrl(logoutOptions);\n\n    if (openUrl) {\n      await openUrl(url);\n    } else if (openUrl !== false) {\n      window.location.assign(url);\n    }\n  }\n\n  private async _getTokenFromIFrame(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const params: AuthorizationParams & { scope: string } = {\n      ...options.authorizationParams,\n      prompt: 'none'\n    };\n\n    const orgHint = this.cookieStorage.get<string>(this.orgHintCookieName);\n\n    if (orgHint && !params.organization) {\n      params.organization = orgHint;\n    }\n\n    const {\n      url,\n      state: stateIn,\n      nonce: nonceIn,\n      code_verifier,\n      redirect_uri,\n      scope,\n      audience\n    } = await this._prepareAuthorizeUrl(\n      params,\n      { response_mode: 'web_message' },\n      window.location.origin\n    );\n\n    try {\n      // When a browser is running in a Cross-Origin Isolated context, using iframes is not possible.\n      // It doesn't throw an error but times out instead, so we should exit early and inform the user about the reason.\n      // https://developer.mozilla.org/en-US/docs/Web/API/crossOriginIsolated\n      if ((window as any).crossOriginIsolated) {\n        throw new GenericError(\n          'login_required',\n          'The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.'\n        );\n      }\n\n      const authorizeTimeout =\n        options.timeoutInSeconds || this.options.authorizeTimeoutInSeconds;\n\n      const codeResult = await runIframe(url, this.domainUrl, authorizeTimeout);\n\n      if (stateIn !== codeResult.state) {\n        throw new GenericError('state_mismatch', 'Invalid state');\n      }\n\n      const tokenResult = await this._requestToken(\n        {\n          ...options.authorizationParams,\n          code_verifier,\n          code: codeResult.code as string,\n          grant_type: 'authorization_code',\n          redirect_uri,\n          timeout: options.authorizationParams.timeout || this.httpTimeoutMs\n        },\n        {\n          nonceIn,\n          organization: params.organization\n        }\n      );\n\n      return {\n        ...tokenResult,\n        scope: scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: audience\n      };\n    } catch (e) {\n      if (e.error === 'login_required') {\n        this.logout({\n          openUrl: false\n        });\n      }\n      throw e;\n    }\n  }\n\n  private async _getTokenUsingRefreshToken(\n    options: GetTokenSilentlyOptions & {\n      authorizationParams: AuthorizationParams & { scope: string };\n    }\n  ): Promise<GetTokenSilentlyResult> {\n    const cache = await this.cacheManager.get(\n      new CacheKey({\n        scope: options.authorizationParams.scope,\n        audience: options.authorizationParams.audience || 'default',\n        clientId: this.options.clientId\n      })\n    );\n\n    // If you don't have a refresh token in memory\n    // and you don't have a refresh token in web worker memory\n    // and useRefreshTokensFallback was explicitly enabled\n    // fallback to an iframe\n    if ((!cache || !cache.refresh_token) && !this.worker) {\n      if (this.options.useRefreshTokensFallback) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw new MissingRefreshTokenError(\n        options.authorizationParams.audience || 'default',\n        options.authorizationParams.scope\n      );\n    }\n\n    const redirect_uri =\n      options.authorizationParams.redirect_uri ||\n      this.options.authorizationParams.redirect_uri ||\n      window.location.origin;\n\n    const timeout =\n      typeof options.timeoutInSeconds === 'number'\n        ? options.timeoutInSeconds * 1000\n        : null;\n\n    try {\n      const tokenResult = await this._requestToken({\n        ...options.authorizationParams,\n        grant_type: 'refresh_token',\n        refresh_token: cache && cache.refresh_token,\n        redirect_uri,\n        ...(timeout && { timeout })\n      });\n\n      return {\n        ...tokenResult,\n        scope: options.authorizationParams.scope,\n        oauthTokenScope: tokenResult.scope,\n        audience: options.authorizationParams.audience || 'default'\n      };\n    } catch (e) {\n      if (\n        // The web worker didn't have a refresh token in memory so\n        // fallback to an iframe.\n        (e.message.indexOf(MISSING_REFRESH_TOKEN_ERROR_MESSAGE) > -1 ||\n          // A refresh token was found, but is it no longer valid\n          // and useRefreshTokensFallback is explicitly enabled. Fallback to an iframe.\n          (e.message &&\n            e.message.indexOf(INVALID_REFRESH_TOKEN_ERROR_MESSAGE) > -1)) &&\n        this.options.useRefreshTokensFallback\n      ) {\n        return await this._getTokenFromIFrame(options);\n      }\n\n      throw e;\n    }\n  }\n\n  private async _saveEntryInCache(\n    entry: CacheEntry & { id_token: string; decodedToken: DecodedToken }\n  ) {\n    const { id_token, decodedToken, ...entryWithoutIdToken } = entry;\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, {\n      id_token,\n      decodedToken\n    });\n\n    await this.cacheManager.setIdToken(\n      this.options.clientId,\n      entry.id_token,\n      entry.decodedToken\n    );\n\n    await this.cacheManager.set(entryWithoutIdToken);\n  }\n\n  private async _getIdTokenFromCache() {\n    const audience = this.options.authorizationParams.audience || 'default';\n\n    const cache = await this.cacheManager.getIdToken(\n      new CacheKey({\n        clientId: this.options.clientId,\n        audience,\n        scope: this.scope\n      })\n    );\n\n    const currentCache = this.userCache.get<IdTokenEntry>(\n      CACHE_KEY_ID_TOKEN_SUFFIX\n    ) as IdTokenEntry;\n\n    // If the id_token in the cache matches the value we previously cached in memory return the in-memory\n    // value so that object comparison will work\n    if (cache && cache.id_token === currentCache?.id_token) {\n      return currentCache;\n    }\n\n    this.userCache.set(CACHE_KEY_ID_TOKEN_SUFFIX, cache);\n    return cache;\n  }\n\n  private async _getEntryFromCache({\n    scope,\n    audience,\n    clientId\n  }: {\n    scope: string;\n    audience: string;\n    clientId: string;\n  }): Promise<undefined | GetTokenSilentlyVerboseResponse> {\n    const entry = await this.cacheManager.get(\n      new CacheKey({\n        scope,\n        audience,\n        clientId\n      }),\n      60 // get a new token if within 60 seconds of expiring\n    );\n\n    if (entry && entry.access_token) {\n      const { access_token, oauthTokenScope, expires_in } = entry as CacheEntry;\n      const cache = await this._getIdTokenFromCache();\n      return (\n        cache && {\n          id_token: cache.id_token,\n          access_token,\n          ...(oauthTokenScope ? { scope: oauthTokenScope } : null),\n          expires_in\n        }\n      );\n    }\n  }\n\n  /**\n   * Releases any lock acquired by the current page that's not released yet\n   *\n   * Get's called on the `pagehide` event.\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/pagehide_event\n   */\n  private _releaseLockOnPageHide = async () => {\n    await lock.releaseLock(GET_TOKEN_SILENTLY_LOCK_KEY);\n\n    window.removeEventListener('pagehide', this._releaseLockOnPageHide);\n  };\n\n  private async _requestToken(\n    options:\n      | PKCERequestTokenOptions\n      | RefreshTokenRequestTokenOptions\n      | TokenExchangeRequestOptions,\n    additionalParameters?: RequestTokenAdditionalParameters\n  ) {\n    const { nonceIn, organization } = additionalParameters || {};\n    const authResult = await oauthToken(\n      {\n        baseUrl: this.domainUrl,\n        client_id: this.options.clientId,\n        auth0Client: this.options.auth0Client,\n        useFormData: this.options.useFormData,\n        timeout: this.httpTimeoutMs,\n        ...options\n      },\n      this.worker\n    );\n\n    const decodedToken = await this._verifyIdToken(\n      authResult.id_token,\n      nonceIn,\n      organization\n    );\n\n    await this._saveEntryInCache({\n      ...authResult,\n      decodedToken,\n      scope: options.scope,\n      audience: options.audience || 'default',\n      ...(authResult.scope ? { oauthTokenScope: authResult.scope } : null),\n      client_id: this.options.clientId\n    });\n\n    this.cookieStorage.save(this.isAuthenticatedCookieName, true, {\n      daysUntilExpire: this.sessionCheckExpiryDays,\n      cookieDomain: this.options.cookieDomain\n    });\n\n    this._processOrgHint(organization || decodedToken.claims.org_id);\n\n    return { ...authResult, decodedToken };\n  }\n\n  /*\n  Custom Token Exchange\n  * **Implementation Notes:**\n  * - Ensure that the `subject_token` provided has been securely obtained and is valid according\n  *   to your external identity provider's policies before invoking this function.\n  * - The function leverages internal helper methods:\n  *   - `validateTokenType` confirms that the `subject_token_type` is supported.\n  *   - `getUniqueScopes` merges and de-duplicates scopes between the provided options and\n  *     the instance's default scopes.\n  *   - `_requestToken` performs the actual HTTP request to the token endpoint.\n  */\n\n  /**\n   * Exchanges an external subject token for an Auth0 token via a token exchange request.\n   *\n   * @param {CustomTokenExchangeOptions} options - The options required to perform the token exchange.\n   *\n   * @returns {Promise<TokenEndpointResponse>} A promise that resolves to the token endpoint response,\n   * which contains the issued Auth0 tokens.\n   *\n   * This method implements the token exchange grant as specified in RFC 8693 by first validating\n   * the provided subject token type and then constructing a token request to the /oauth/token endpoint.\n   * The request includes the following parameters:\n   *\n   * - `grant_type`: Hard-coded to \"urn:ietf:params:oauth:grant-type:token-exchange\".\n   * - `subject_token`: The external token provided via the options.\n   * - `subject_token_type`: The type of the external token (validated by this function).\n   * - `scope`: A unique set of scopes, generated by merging the scopes supplied in the options\n   *            with the SDK’s default scopes.\n   * - `audience`: The target audience, as determined by the SDK's authorization configuration.\n   *\n   * **Example Usage:**\n   *\n   * ```\n   * // Define the token exchange options\n   * const options: CustomTokenExchangeOptions = {\n   *   subject_token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6Ikp...',\n   *   subject_token_type: 'urn:acme:legacy-system-token',\n   *   scope: ['openid', 'profile']\n   * };\n   *\n   * // Exchange the external token for Auth0 tokens\n   * try {\n   *   const tokenResponse = await instance.exchangeToken(options);\n   *   console.log('Token response:', tokenResponse);\n   * } catch (error) {\n   *   console.error('Token exchange failed:', error);\n   * }\n   * ```\n   */\n  async exchangeToken(\n    options: CustomTokenExchangeOptions\n  ): Promise<TokenEndpointResponse> {\n    return this._requestToken({\n      grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange',\n      subject_token: options.subject_token,\n      subject_token_type: options.subject_token_type,\n      scope: getUniqueScopes(options.scope, this.scope),\n      audience: this.options.authorizationParams.audience\n    });\n  }\n}\n\ninterface BaseRequestTokenOptions {\n  audience?: string;\n  scope: string;\n  timeout?: number;\n  redirect_uri?: string;\n}\n\ninterface PKCERequestTokenOptions extends BaseRequestTokenOptions {\n  code: string;\n  grant_type: 'authorization_code';\n  code_verifier: string;\n}\n\ninterface RefreshTokenRequestTokenOptions extends BaseRequestTokenOptions {\n  grant_type: 'refresh_token';\n  refresh_token?: string;\n}\n\ninterface TokenExchangeRequestOptions extends BaseRequestTokenOptions {\n  grant_type: 'urn:ietf:params:oauth:grant-type:token-exchange';\n  subject_token: string;\n  subject_token_type: string;\n  actor_token?: string;\n  actor_token_type?: string;\n}\n\ninterface RequestTokenAdditionalParameters {\n  nonceIn?: string;\n  organization?: string;\n}\n", "import { ICache } from './cache';\n\nexport interface AuthorizationParams {\n  /**\n   * - `'page'`: displays the UI with a full page view\n   * - `'popup'`: displays the UI with a popup window\n   * - `'touch'`: displays the UI in a way that leverages a touch interface\n   * - `'wap'`: displays the UI with a \"feature phone\" type interface\n   */\n  display?: 'page' | 'popup' | 'touch' | 'wap';\n\n  /**\n   * - `'none'`: do not prompt user for login or consent on reauthentication\n   * - `'login'`: prompt user for reauthentication\n   * - `'consent'`: prompt user for consent before processing request\n   * - `'select_account'`: prompt user to select an account\n   */\n  prompt?: 'none' | 'login' | 'consent' | 'select_account';\n\n  /**\n   * Maximum allowable elapsed time (in seconds) since authentication.\n   * If the last time the user authenticated is greater than this value,\n   * the user must be reauthenticated.\n   */\n  max_age?: string | number;\n\n  /**\n   * The space-separated list of language tags, ordered by preference.\n   * For example: `'fr-CA fr en'`.\n   */\n  ui_locales?: string;\n\n  /**\n   * Previously issued ID Token.\n   */\n  id_token_hint?: string;\n\n  /**\n   * Provides a hint to Auth0 as to what flow should be displayed.\n   * The default behavior is to show a login page but you can override\n   * this by passing 'signup' to show the signup page instead.\n   *\n   * This only affects the New Universal Login Experience.\n   */\n  screen_hint?: 'signup' | 'login' | string;\n\n  /**\n   * The user's email address or other identifier. When your app knows\n   * which user is trying to authenticate, you can provide this parameter\n   * to pre-fill the email box or select the right session for sign-in.\n   *\n   * This currently only affects the classic Lock experience.\n   */\n  login_hint?: string;\n\n  acr_values?: string;\n\n  /**\n   * The default scope to be used on authentication requests.\n   *\n   * This defaults to `profile email` if not set. If you are setting extra scopes and require\n   * `profile` and `email` to be included then you must include them in the provided scope.\n   *\n   * Note: The `openid` scope is **always applied** regardless of this setting.\n   */\n  scope?: string;\n\n  /**\n   * The default audience to be used for requesting API access.\n   */\n  audience?: string;\n\n  /**\n   * The name of the connection configured for your application.\n   * If null, it will redirect to the Auth0 Login Page and show\n   * the Login Widget.\n   */\n  connection?: string;\n\n  /**\n   * The organization to log in to.\n   *\n   * This will specify an `organization` parameter in your user's login request.\n   *\n   * - If you provide an Organization ID (a string with the prefix `org_`), it will be validated against the `org_id` claim of your user's ID Token. The validation is case-sensitive.\n   * - If you provide an Organization Name (a string *without* the prefix `org_`), it will be validated against the `org_name` claim of your user's ID Token. The validation is case-insensitive.\n   *\n   */\n  organization?: string;\n\n  /**\n   * The Id of an invitation to accept. This is available from the user invitation URL that is given when participating in a user invitation flow.\n   */\n  invitation?: string;\n\n  /**\n   * The default URL where Auth0 will redirect your browser to with\n   * the authentication result. It must be whitelisted in\n   * the \"Allowed Callback URLs\" field in your Auth0 Application's\n   * settings. If not provided here, it should be provided in the other\n   * methods that provide authentication.\n   */\n  redirect_uri?: string;\n\n  /**\n   * If you need to send custom parameters to the Authorization Server,\n   * make sure to use the original parameter name.\n   */\n  [key: string]: any;\n}\n\ninterface BaseLoginOptions {\n  /**\n   * URL parameters that will be sent back to the Authorization Server. This can be known parameters\n   * defined by Auth0 or custom parameters that you define.\n   */\n  authorizationParams?: AuthorizationParams;\n}\n\nexport interface Auth0ClientOptions extends BaseLoginOptions {\n  /**\n   * Your Auth0 account domain such as `'example.auth0.com'`,\n   * `'example.eu.auth0.com'` or , `'example.mycompany.com'`\n   * (when using [custom domains](https://auth0.com/docs/custom-domains))\n   */\n  domain: string;\n  /**\n   * The issuer to be used for validation of JWTs, optionally defaults to the domain above\n   */\n  issuer?: string;\n  /**\n   * The Client ID found on your Application settings page\n   */\n  clientId: string;\n  /**\n   * The value in seconds used to account for clock skew in JWT expirations.\n   * Typically, this value is no more than a minute or two at maximum.\n   * Defaults to 60s.\n   */\n  leeway?: number;\n\n  /**\n   * The location to use when storing cache data. Valid values are `memory` or `localstorage`.\n   * The default setting is `memory`.\n   *\n   * Read more about [changing storage options in the Auth0 docs](https://auth0.com/docs/libraries/auth0-single-page-app-sdk#change-storage-options)\n   */\n  cacheLocation?: CacheLocation;\n\n  /**\n   * Specify a custom cache implementation to use for token storage and retrieval. This setting takes precedence over `cacheLocation` if they are both specified.\n   */\n  cache?: ICache;\n\n  /**\n   * If true, refresh tokens are used to fetch new access tokens from the Auth0 server. If false, the legacy technique of using a hidden iframe and the `authorization_code` grant with `prompt=none` is used.\n   * The default setting is `false`.\n   *\n   * **Note**: Use of refresh tokens must be enabled by an administrator on your Auth0 client application.\n   */\n  useRefreshTokens?: boolean;\n\n  /**\n   * If true, fallback to the technique of using a hidden iframe and the `authorization_code` grant with `prompt=none` when unable to use refresh tokens. If false, the iframe fallback is not used and\n   * errors relating to a failed `refresh_token` grant should be handled appropriately. The default setting is `false`.\n   *\n   * **Note**: There might be situations where doing silent auth with a Web Message response from an iframe is not possible,\n   * like when you're serving your application from the file system or a custom protocol (like in a Desktop or Native app).\n   * In situations like this you can disable the iframe fallback and handle the failed `refresh_token` grant and prompt the user to login interactively with `loginWithRedirect` or `loginWithPopup`.\"\n   *\n   * E.g. Using the `file:` protocol in an Electron application does not support that legacy technique.\n   *\n   * @example\n   * let token: string;\n   * try {\n   *   token = await auth0.getTokenSilently();\n   * } catch (e) {\n   *   if (e.error === 'missing_refresh_token' || e.error === 'invalid_grant') {\n   *     auth0.loginWithRedirect();\n   *   }\n   * }\n   */\n  useRefreshTokensFallback?: boolean;\n\n  /**\n   * A maximum number of seconds to wait before declaring background calls to /authorize as failed for timeout\n   * Defaults to 60s.\n   */\n  authorizeTimeoutInSeconds?: number;\n\n  /**\n   * Specify the timeout for HTTP calls using `fetch`. The default is 10 seconds.\n   */\n  httpTimeoutInSeconds?: number;\n\n  /**\n   * Internal property to send information about the client to the authorization server.\n   * @internal\n   */\n  auth0Client?: {\n    name: string;\n    version: string;\n    env?: { [key: string]: string };\n  };\n\n  /**\n   * Sets an additional cookie with no SameSite attribute to support legacy browsers\n   * that are not compatible with the latest SameSite changes.\n   * This will log a warning on modern browsers, you can disable the warning by setting\n   * this to false but be aware that some older useragents will not work,\n   * See https://www.chromium.org/updates/same-site/incompatible-clients\n   * Defaults to true\n   */\n  legacySameSiteCookie?: boolean;\n\n  /**\n   * If `true`, the SDK will use a cookie when storing information about the auth transaction while\n   * the user is going through the authentication flow on the authorization server.\n   *\n   * The default is `false`, in which case the SDK will use session storage.\n   *\n   * @notes\n   *\n   * You might want to enable this if you rely on your users being able to authenticate using flows that\n   * may end up spanning across multiple tabs (e.g. magic links) or you cannot otherwise rely on session storage being available.\n   */\n  useCookiesForTransactions?: boolean;\n\n  /**\n   * Number of days until the cookie `auth0.is.authenticated` will expire\n   * Defaults to 1.\n   */\n  sessionCheckExpiryDays?: number;\n\n  /**\n   * The domain the cookie is accessible from. If not set, the cookie is scoped to\n   * the current domain, including the subdomain.\n   *\n   * Note: setting this incorrectly may cause silent authentication to stop working\n   * on page load.\n   *\n   *\n   * To keep a user logged in across multiple subdomains set this to your\n   * top-level domain and prefixed with a `.` (eg: `.example.com`).\n   */\n  cookieDomain?: string;\n\n  /**\n   * If true, data to the token endpoint is transmitted as x-www-form-urlencoded data, if false it will be transmitted as JSON. The default setting is `true`.\n   *\n   * **Note:** Setting this to `false` may affect you if you use Auth0 Rules and are sending custom, non-primitive data. If you disable this,\n   * please verify that your Auth0 Rules continue to work as intended.\n   */\n  useFormData?: boolean;\n\n  /**\n   * Modify the value used as the current time during the token validation.\n   *\n   * **Note**: Using this improperly can potentially compromise the token validation.\n   */\n  nowProvider?: () => Promise<number> | number;\n\n  /**\n   * If provided, the SDK will load the token worker from this URL instead of the integrated `blob`. An example of when this is useful is if you have strict\n   * Content-Security-Policy (CSP) and wish to avoid needing to set `worker-src: blob:`. We recommend either serving the worker, which you can find in the module \n   * at `<module_path>/dist/auth0-spa-js.worker.production.js`, from the same host as your application or using the Auth0 CDN \n   * `https://cdn.auth0.com/js/auth0-spa-js/<version>/auth0-spa-js.worker.production.js`.\n   * \n   * **Note**: The worker is only used when `useRefreshTokens: true`, `cacheLocation: 'memory'`, and the `cache` is not custom.\n   */\n  workerUrl?: string;\n}\n\n/**\n * The possible locations where tokens can be stored\n */\nexport type CacheLocation = 'memory' | 'localstorage';\n\n/**\n * @ignore\n */\nexport interface AuthorizeOptions extends AuthorizationParams {\n  response_type: string;\n  response_mode: string;\n  redirect_uri?: string;\n  nonce: string;\n  state: string;\n  scope: string;\n  code_challenge: string;\n  code_challenge_method: string;\n}\n\nexport interface RedirectLoginOptions<TAppState = any>\n  extends BaseLoginOptions {\n  /**\n   * Used to store state before doing the redirect\n   */\n  appState?: TAppState;\n  /**\n   * Used to add to the URL fragment before redirecting\n   */\n  fragment?: string;\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * const client = new Auth0Client({\n   *   async onRedirect(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   * @deprecated since v2.0.1, use `openUrl` instead.\n   */\n  onRedirect?: (url: string) => Promise<void>;\n\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * const client = new Auth0Client({\n   *   openUrl(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   *\n   * @example\n   * import { Browser } from '@capacitor/browser';\n   *\n   * const client = new Auth0Client({\n   *   async openUrl(url) {\n   *     await Browser.open({ url });\n   *   }\n   * });\n   */\n  openUrl?: (url: string) => Promise<void> | void;\n}\n\nexport interface RedirectLoginResult<TAppState = any> {\n  /**\n   * State stored when the redirect request was made\n   */\n  appState?: TAppState;\n}\n\nexport interface PopupLoginOptions extends BaseLoginOptions {}\n\nexport interface PopupConfigOptions {\n  /**\n   * The number of seconds to wait for a popup response before\n   * throwing a timeout error. Defaults to 60s\n   */\n  timeoutInSeconds?: number;\n\n  /**\n   * Accepts an already-created popup window to use. If not specified, the SDK\n   * will create its own. This may be useful for platforms like iOS that have\n   * security restrictions around when popups can be invoked (e.g. from a user click event)\n   */\n  popup?: any;\n}\n\nexport interface GetTokenSilentlyOptions {\n  /**\n   * When `off`, ignores the cache and always sends a\n   * request to Auth0.\n   * When `cache-only`, only reads from the cache and never sends a request to Auth0.\n   * Defaults to `on`, where it both reads from the cache and sends a request to Auth0 as needed.\n   */\n  cacheMode?: 'on' | 'off' | 'cache-only';\n\n  /**\n   * Parameters that will be sent back to Auth0 as part of a request.\n   */\n  authorizationParams?: {\n    /**\n     * There's no actual redirect when getting a token silently,\n     * but, according to the spec, a `redirect_uri` param is required.\n     * Auth0 uses this parameter to validate that the current `origin`\n     * matches the `redirect_uri` `origin` when sending the response.\n     * It must be whitelisted in the \"Allowed Web Origins\" in your\n     * Auth0 Application's settings.\n     */\n    redirect_uri?: string;\n\n    /**\n     * The scope that was used in the authentication request\n     */\n    scope?: string;\n\n    /**\n     * The audience that was used in the authentication request\n     */\n    audience?: string;\n\n    /**\n     * If you need to send custom parameters to the Authorization Server,\n     * make sure to use the original parameter name.\n     */\n    [key: string]: any;\n  };\n\n  /** A maximum number of seconds to wait before declaring the background /authorize call as failed for timeout\n   * Defaults to 60s.\n   */\n  timeoutInSeconds?: number;\n\n  /**\n   * If true, the full response from the /oauth/token endpoint (or the cache, if the cache was used) is returned\n   * (minus `refresh_token` if one was issued). Otherwise, just the access token is returned.\n   *\n   * The default is `false`.\n   */\n  detailedResponse?: boolean;\n}\n\nexport interface GetTokenWithPopupOptions extends PopupLoginOptions {\n  /**\n   * When `off`, ignores the cache and always sends a request to Auth0.\n   * When `cache-only`, only reads from the cache and never sends a request to Auth0.\n   * Defaults to `on`, where it both reads from the cache and sends a request to Auth0 as needed.\n   */\n  cacheMode?: 'on' | 'off' | 'cache-only';\n}\n\nexport interface LogoutUrlOptions {\n  /**\n   * The `clientId` of your application.\n   *\n   * If this property is not set, then the `clientId` that was used during initialization of the SDK is sent to the logout endpoint.\n   *\n   * If this property is set to `null`, then no client ID value is sent to the logout endpoint.\n   *\n   * [Read more about how redirecting after logout works](https://auth0.com/docs/logout/guides/redirect-users-after-logout)\n   */\n  clientId?: string | null;\n\n  /**\n   * Parameters to pass to the logout endpoint. This can be known parameters defined by Auth0 or custom parameters\n   * you wish to provide.\n   */\n  logoutParams?: {\n    /**\n     * When supported by the upstream identity provider,\n     * forces the user to logout of their identity provider\n     * and from Auth0.\n     * [Read more about how federated logout works at Auth0](https://auth0.com/docs/logout/guides/logout-idps)\n     */\n    federated?: boolean;\n    /**\n     * The URL where Auth0 will redirect your browser to after the logout.\n     *\n     * **Note**: If the `client_id` parameter is included, the\n     * `returnTo` URL that is provided must be listed in the\n     * Application's \"Allowed Logout URLs\" in the Auth0 dashboard.\n     * However, if the `client_id` parameter is not included, the\n     * `returnTo` URL must be listed in the \"Allowed Logout URLs\" at\n     * the account level in the Auth0 dashboard.\n     *\n     * [Read more about how redirecting after logout works](https://auth0.com/docs/logout/guides/redirect-users-after-logout)\n     */\n    returnTo?: string;\n\n    /**\n     * If you need to send custom parameters to the logout endpoint, make sure to use the original parameter name.\n     */\n    [key: string]: any;\n  };\n}\n\nexport interface LogoutOptions extends LogoutUrlOptions {\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * @example\n   * await auth0.logout({\n   *   async onRedirect(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   * @deprecated since v2.0.1, use `openUrl` instead.\n   */\n  onRedirect?: (url: string) => Promise<void>;\n\n  /**\n   * Used to control the redirect and not rely on the SDK to do the actual redirect.\n   *\n   * Set to `false` to disable the redirect, or provide a function to handle the actual redirect yourself.\n   *\n   * @example\n   * await auth0.logout({\n   *   openUrl(url) {\n   *     window.location.replace(url);\n   *   }\n   * });\n   *\n   * @example\n   * import { Browser } from '@capacitor/browser';\n   *\n   * await auth0.logout({\n   *   async openUrl(url) {\n   *     await Browser.open({ url });\n   *   }\n   * });\n   */\n  openUrl?: false | ((url: string) => Promise<void> | void);\n}\n\n/**\n * @ignore\n */\nexport interface AuthenticationResult {\n  state: string;\n  code?: string;\n  error?: string;\n  error_description?: string;\n}\n\n/**\n * @ignore\n */\nexport interface TokenEndpointOptions {\n  baseUrl: string;\n  client_id: string;\n  grant_type: string;\n  timeout?: number;\n  auth0Client: any;\n  useFormData?: boolean;\n  [key: string]: any;\n}\n\nexport type TokenEndpointResponse = {\n  id_token: string;\n  access_token: string;\n  refresh_token?: string;\n  expires_in: number;\n  scope?: string;\n};\n\n/**\n * @ignore\n */\nexport interface OAuthTokenOptions extends TokenEndpointOptions {\n  code_verifier: string;\n  code: string;\n  redirect_uri: string;\n  audience: string;\n  scope: string;\n}\n\n/**\n * @ignore\n */\nexport interface RefreshTokenOptions extends TokenEndpointOptions {\n  refresh_token: string;\n}\n\n/**\n * @ignore\n */\nexport interface JWTVerifyOptions {\n  iss: string;\n  aud: string;\n  id_token: string;\n  nonce?: string;\n  leeway?: number;\n  max_age?: number;\n  organization?: string;\n  now?: number;\n}\n\nexport interface IdToken {\n  __raw: string;\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  middle_name?: string;\n  nickname?: string;\n  preferred_username?: string;\n  profile?: string;\n  picture?: string;\n  website?: string;\n  email?: string;\n  email_verified?: boolean;\n  gender?: string;\n  birthdate?: string;\n  zoneinfo?: string;\n  locale?: string;\n  phone_number?: string;\n  phone_number_verified?: boolean;\n  address?: string;\n  updated_at?: string;\n  iss?: string;\n  aud?: string;\n  exp?: number;\n  nbf?: number;\n  iat?: number;\n  jti?: string;\n  azp?: string;\n  nonce?: string;\n  auth_time?: string;\n  at_hash?: string;\n  c_hash?: string;\n  acr?: string;\n  amr?: string[];\n  sub_jwk?: string;\n  cnf?: string;\n  sid?: string;\n  org_id?: string;\n  org_name?: string;\n  [key: string]: any;\n}\n\nexport class User {\n  name?: string;\n  given_name?: string;\n  family_name?: string;\n  middle_name?: string;\n  nickname?: string;\n  preferred_username?: string;\n  profile?: string;\n  picture?: string;\n  website?: string;\n  email?: string;\n  email_verified?: boolean;\n  gender?: string;\n  birthdate?: string;\n  zoneinfo?: string;\n  locale?: string;\n  phone_number?: string;\n  phone_number_verified?: boolean;\n  address?: string;\n  updated_at?: string;\n  sub?: string;\n  [key: string]: any;\n}\n\n/**\n * @ignore\n */\nexport type FetchOptions = {\n  method?: string;\n  headers?: Record<string, string>;\n  credentials?: 'include' | 'omit';\n  body?: string;\n  signal?: AbortSignal;\n};\n\nexport type GetTokenSilentlyVerboseResponse = Omit<\n  TokenEndpointResponse,\n  'refresh_token'\n>;\n", "import { Auth0Client } from './Auth0Client';\nimport { Auth0ClientOptions } from './global';\n\nimport './global';\n\nexport * from './global';\n\n/**\n * Asynchronously creates the Auth0Client instance and calls `checkSession`.\n *\n * **Note:** There are caveats to using this in a private browser tab, which may not silently authenticae\n * a user on page refresh. Please see [the checkSession docs](https://auth0.github.io/auth0-spa-js/classes/Auth0Client.html#checksession) for more info.\n *\n * @param options The client options\n * @returns An instance of Auth0Client\n */\nexport async function createAuth0Client(options: Auth0ClientOptions) {\n  const auth0 = new Auth0Client(options);\n  await auth0.checkSession();\n  return auth0;\n}\n\nexport { Auth0Client };\n\nexport {\n  GenericError,\n  AuthenticationError,\n  TimeoutError,\n  PopupTimeoutError,\n  PopupCancelledError,\n  MfaRequiredError,\n  MissingRefreshTokenError\n} from './errors';\n\nexport {\n  ICache,\n  LocalStorageCache,\n  InMemoryCache,\n  Cacheable,\n  DecodedToken,\n  CacheEntry,\n  WrappedCacheEntry,\n  KeyManifestEntry,\n  MaybePromise,\n  CacheKey,\n  CacheKeyData\n} from './cache';\n"], "names": ["__rest", "s", "e", "t", "p", "Object", "prototype", "hasOwnProperty", "call", "indexOf", "getOwnPropertySymbols", "i", "length", "propertyIsEnumerable", "SuppressedError", "defineProperty", "exports", "value", "ProcessLocking", "_this", "this", "locked", "Map", "addToLocked", "key", "toAdd", "callbacks", "get", "undefined", "set", "unshift", "isLocked", "has", "lock", "Promise", "resolve", "reject", "unlock", "toCall", "pop", "setTimeout", "delete", "getInstance", "instance", "default", "__awaiter", "thisArg", "_arguments", "P", "generator", "fulfilled", "step", "next", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "TypeError", "push", "LOCK_STORAGE_KEY", "DEFAULT_STORAGE_HANDLER", "index", "_a", "Error", "getItem", "clear", "window", "localStorage", "removeItem", "setItem", "keySync", "getItemSync", "clearSync", "removeItemSync", "setItemSync", "delay", "milliseconds", "generateRandomString", "CHARS", "randomstring", "Math", "floor", "random", "SuperTokensLock", "storageHandler", "acquiredIatSet", "Set", "id", "Date", "now", "toString", "acquireLock", "bind", "releaseLock", "releaseLock__private__", "waitForSomethingToChange", "refreshLockWhileAcquired", "waiters", "<PERSON><PERSON><PERSON>", "timeout", "iat", "MAX_TIME", "STORAGE_KEY", "STORAGE", "TIMEOUT_KEY", "lockObjPostDelay", "parsedLockObjPostDelay", "JSON", "stringify", "timeout<PERSON><PERSON>", "timeAcquired", "timeRefreshed", "parse", "add", "lock<PERSON><PERSON><PERSON><PERSON>", "storageKey", "lock<PERSON>bj", "parsedLockObj", "processLock_1", "resolvedCalled", "startedAt", "removedListeners", "stopWaiting", "removeEventListener", "removeFromWaiting", "clearTimeout", "timeOutId", "timeToWait", "addEventListener", "addToWaiting", "max", "func", "filter", "notify<PERSON><PERSON><PERSON>", "slice", "for<PERSON>ach", "parsedlockObj", "MIN_ALLOWED_TIME", "KEYS", "currIndex", "LOCK_KEY", "includes", "DEFAULT_POPUP_CONFIG_OPTIONS", "timeoutInSeconds", "DEFAULT_AUTH0_CLIENT", "name", "version", "DEFAULT_NOW_PROVIDER", "GenericError", "constructor", "error", "error_description", "super", "setPrototypeOf", "static", "AuthenticationError", "state", "appState", "TimeoutError", "PopupTimeoutError", "popup", "PopupCancelledError", "MfaRequiredError", "mfa_token", "MissingRefreshTokenError", "audience", "scope", "valueOrEmptyString", "exclude", "getCrypto", "crypto", "createRandomString", "charset", "Array", "from", "getRandomValues", "Uint8Array", "encode", "btoa", "createQueryParams", "clientId", "client_id", "params", "URLSearchParams", "keys", "k", "reduce", "acc", "assign", "stripUndefined", "urlDecodeB64", "input", "decodeURIComponent", "atob", "split", "map", "c", "charCodeAt", "join", "decodeB64", "replace", "dofetch", "async", "fetchUrl", "fetchOptions", "response", "fetch", "ok", "json", "fetchWithoutWorker", "controller", "AbortController", "timeoutId", "signal", "race", "abort", "finally", "fetchWithWorker", "worker", "useFormData", "message", "auth", "to", "messageChannel", "MessageChannel", "port1", "onmessage", "event", "data", "close", "postMessage", "port2", "switchFetch", "oauthToken", "baseUrl", "auth0Client", "options", "url", "fetchError", "errorMessage", "getJSON", "method", "headers", "getUniqueScopes", "scopes", "arr", "Boolean", "trim", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "suffix", "to<PERSON><PERSON>", "entry", "LocalStorageCache", "remove", "allKeys", "startsWith", "InMemoryCache", "enclosedCache", "cache", "cacheEntry", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "keyManifest", "nowProvider", "idToken", "decodedToken", "cache<PERSON>ey", "getIdTokenCache<PERSON>ey", "id_token", "entryByScope", "expiryAdjustmentSeconds", "wrappedEntry", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "matchExisting<PERSON>ache<PERSON>ey", "nowSeconds", "expiresAt", "refresh_token", "wrapCacheEntry", "memo", "expires_in", "keyToMatch", "fromKey", "scopeSet", "scopesToMatch", "hasAllScopes", "current", "TransactionManager", "storage", "cookieDomain", "create", "transaction", "save", "daysUntilExpire", "isNumber", "idTokendecoded", "verify", "decoded", "token", "parts", "header", "payload", "signature", "payloadJSON", "claims", "__raw", "user", "encoded", "decode", "iss", "sub", "alg", "aud", "isArray", "azp", "nonce", "max_age", "auth_time", "exp", "leeway", "expDate", "setUTCSeconds", "nbf", "nbfDate", "authTimeDate", "parseInt", "organization", "org", "orgId", "org_id", "orgName", "toLowerCase", "org_name", "__assign", "arguments", "stringifyAttribute", "stringified", "attributes", "encodeURIComponent", "expires", "setMilliseconds", "getMilliseconds", "toUTCString", "domain", "path", "secure", "sameSite", "stringifyAttributes", "cookieString", "cookies", "rdecode", "cookie", "char<PERSON>t", "getAll", "document", "__esModule", "Cookie<PERSON>torage", "Cookies.get", "cookieAttributes", "location", "protocol", "Cookies.set", "Cookies.remove", "CookieStorageWithLegacySameSite", "SessionStorage", "sessionStorage", "singlePromiseMap", "CacheKeyManifest", "manifest<PERSON>ey", "createManifestKeyFrom", "size", "cacheLocationBuilders", "memory", "localstorage", "cacheFactory", "patchOpenUrlWithOnRedirect", "openUrl", "onRedirect", "originalOptions", "Lock", "Auth0Client", "cacheLocation", "userCache", "defaultOptions", "authorizationParams", "useRefreshTokensFallback", "_releaseLockOnPageHide", "subtle", "validateCrypto", "console", "warn", "httpTimeoutMs", "httpTimeoutInSeconds", "cookieStorage", "legacySameSiteCookie", "orgHintCookieName", "isAuthenticatedCookieName", "buildIsAuthenticatedCookieName", "sessionCheckExpiryDays", "transactionStorage", "useCookiesForTransactions", "domainUrl", "useRefreshTokens", "transactionManager", "cacheManager", "test", "token<PERSON>ssuer", "issuer", "getT<PERSON><PERSON><PERSON><PERSON>", "Worker", "workerUrl", "TokenWorker", "_url", "_authorizeUrl", "authorizeOptions", "verifyIdToken", "_processOrgHint", "fallbackRedirectUri", "code_verifier", "code_challenge", "ie11SafeInput", "b64Chars", "m", "urlEncodeB64", "String", "fromCharCode", "bufferToBase64UrlEncoded", "digestOp", "digest", "TextEncoder", "sha256", "clientOptions", "redirect_uri", "response_mode", "response_type", "code_challenge_method", "getAuthorizeParams", "config", "left", "screenX", "innerWidth", "top", "screenY", "innerHeight", "open", "openPopup", "_prepareAuthorizeUrl", "origin", "href", "codeResult", "popupEventListener", "popupTimer", "setInterval", "closed", "clearInterval", "type", "fromPayload", "runPopup", "authorizeTimeoutInSeconds", "_requestToken", "grant_type", "code", "nonceIn", "_getIdTokenFromCache", "_b", "fragment", "urlOptions", "_c", "urlWithFragment", "queryStringFragments", "queryString", "substring", "searchParams", "parseAuthenticationResult", "getTokenSilently", "localOptions", "cacheMode", "cb", "promise", "singlePromise", "_getTokenSilently", "detailedResponse", "access_token", "getTokenOptions", "_getEntryFromCache", "maxNumberOfRetries", "retryPromise", "authResult", "_getTokenUsingRefreshToken", "_getTokenFromIFrame", "oauthTokenScope", "loginWithPopup", "getUser", "_buildLogoutUrl", "logoutParams", "federated", "logoutOptions", "federatedQuery", "prompt", "orgHint", "stateIn", "crossOriginIsolated", "authorizeTimeout", "authorizeUrl", "<PERSON><PERSON><PERSON><PERSON>", "res", "rej", "iframe", "createElement", "setAttribute", "style", "display", "removeIframe", "contains", "<PERSON><PERSON><PERSON><PERSON>", "iframeEventHandler", "timeoutSetTimeoutId", "eventSource", "source", "CLEANUP_IFRAME_TIMEOUT_IN_SECONDS", "append<PERSON><PERSON><PERSON>", "runIframe", "tokenResult", "logout", "entryWithoutIdToken", "setIdToken", "getIdToken", "currentCache", "additionalParameters", "_verifyIdToken", "_saveEntryInCache", "subject_token", "subject_token_type", "User", "createAuth0Client", "auth0", "checkSession"], "mappings": "AA0CO,SAASA,EAAOC,EAAGC,GACtB,IAAIC,EAAI,CAAA,EACR,IAAK,IAAIC,KAAKH,EAAOI,OAAOC,UAAUC,eAAeC,KAAKP,EAAGG,IAAMF,EAAEO,QAAQL,GAAK,IAC9ED,EAAEC,GAAKH,EAAEG,IACb,GAAS,MAALH,GAAqD,mBAAjCI,OAAOK,sBACtB,KAAIC,EAAI,EAAb,IAAgBP,EAAIC,OAAOK,sBAAsBT,GAAIU,EAAIP,EAAEQ,OAAQD,IAC3DT,EAAEO,QAAQL,EAAEO,IAAM,GAAKN,OAAOC,UAAUO,qBAAqBL,KAAKP,EAAGG,EAAEO,MACvER,EAAEC,EAAEO,IAAMV,EAAEG,EAAEO,IAF4B,CAItD,OAAOR,CACX,CAuQkD,mBAApBW,iBAAiCA,6VC1T/DT,OAAOU,eAAeC,EAAS,aAAc,CAAEC,OAAO,IACtD,IAAIC,EAAgC,WAChC,SAASA,IACL,IAAIC,EAAQC,KACZA,KAAKC,OAAS,IAAIC,IAClBF,KAAKG,YAAc,SAAUC,EAAKC,GAC9B,IAAIC,EAAYP,EAAME,OAAOM,IAAIH,QACfI,IAAdF,OACcE,IAAVH,EACAN,EAAME,OAAOQ,IAAIL,EAAK,IAGtBL,EAAME,OAAOQ,IAAIL,EAAK,CAACC,SAIbG,IAAVH,IACAC,EAAUI,QAAQL,GAClBN,EAAME,OAAOQ,IAAIL,EAAKE,GAG1C,EACQN,KAAKW,SAAW,SAAUP,GACtB,OAAOL,EAAME,OAAOW,IAAIR,EACpC,EACQJ,KAAKa,KAAO,SAAUT,GAClB,OAAO,IAAIU,SAAQ,SAAUC,EAASC,GAC9BjB,EAAMY,SAASP,GACfL,EAAMI,YAAYC,EAAKW,IAGvBhB,EAAMI,YAAYC,GAClBW,IAEpB,GACA,EACQf,KAAKiB,OAAS,SAAUb,GACpB,IAAIE,EAAYP,EAAME,OAAOM,IAAIH,GACjC,QAAkBI,IAAdF,GAAgD,IAArBA,EAAUd,OAAzC,CAIA,IAAI0B,EAASZ,EAAUa,MACvBpB,EAAME,OAAOQ,IAAIL,EAAKE,QACPE,IAAXU,GACAE,WAAWF,EAAQ,EAJtB,MAFGnB,EAAME,OAAOoB,OAAOjB,EAQpC,CACK,CAOD,OANAN,EAAewB,YAAc,WAIzB,YAHgCd,IAA5BV,EAAeyB,WACfzB,EAAeyB,SAAW,IAAIzB,GAE3BA,EAAeyB,QAC9B,EACWzB,CACX,IAIAF,EAAA4B,QAHA,WACI,OAAO1B,EAAewB,aAC1B,mCC3DA,IAAIG,EAAazB,GAAQA,EAAKyB,WAAc,SAAUC,EAASC,EAAYC,EAAGC,GAC1E,OAAO,IAAKD,IAAMA,EAAId,WAAU,SAAUC,EAASC,GAC/C,SAASc,EAAUjC,GAAS,IAAMkC,EAAKF,EAAUG,KAAKnC,IAAW,MAAOf,GAAKkC,EAAOlC,GAAO,CAC3F,SAASmD,EAASpC,GAAS,IAAMkC,EAAKF,EAAiB,MAAEhC,IAAW,MAAOf,GAAKkC,EAAOlC,GAAO,CAC9F,SAASiD,EAAKG,GAAUA,EAAOC,KAAOpB,EAAQmB,EAAOrC,OAAS,IAAI+B,GAAE,SAAUb,GAAWA,EAAQmB,EAAOrC,UAAWuC,KAAKN,EAAWG,EAAY,CAC/IF,GAAMF,EAAYA,EAAUQ,MAAMX,EAASC,GAAc,KAAKK,OACtE,GACA,EACIM,EAAetC,GAAQA,EAAKsC,aAAgB,SAAUZ,EAASa,GAC/D,IAAsGC,EAAGC,EAAG1D,EAAG2D,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAP9D,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAK,EAAE+D,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEV,KAAMgB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOpD,IAAO,GAAG0C,EACvJ,SAASM,EAAKK,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIf,EAAG,MAAM,IAAIgB,UAAU,mCAC3B,KAAOb,OACH,GAAIH,EAAI,EAAGC,IAAM1D,EAAY,EAARwE,EAAG,GAASd,EAAU,OAAIc,EAAG,GAAKd,EAAS,SAAO1D,EAAI0D,EAAU,SAAM1D,EAAEK,KAAKqD,GAAI,GAAKA,EAAET,SAAWjD,EAAIA,EAAEK,KAAKqD,EAAGc,EAAG,KAAKpB,KAAM,OAAOpD,EAE3J,OADI0D,EAAI,EAAG1D,IAAGwE,EAAK,CAAS,EAARA,EAAG,GAAQxE,EAAEc,QACzB0D,EAAG,IACP,KAAK,EAAG,KAAK,EAAGxE,EAAIwE,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAE/C,MAAO0D,EAAG,GAAIpB,MAAM,GAChD,KAAK,EAAGQ,EAAEC,QAASH,EAAIc,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAI5B,MAAOwB,EAAEG,KAAK3B,MAAO,SACxC,QACI,KAAMpC,EAAI4D,EAAEG,MAAM/D,EAAIA,EAAES,OAAS,GAAKT,EAAEA,EAAES,OAAS,KAAkB,IAAV+D,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,QAAW,CAC5G,GAAc,IAAVY,EAAG,MAAcxE,GAAMwE,EAAG,GAAKxE,EAAE,IAAMwE,EAAG,GAAKxE,EAAE,IAAM,CAAE4D,EAAEC,MAAQW,EAAG,GAAI,KAAQ,CACtF,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQ7D,EAAE,GAAI,CAAE4D,EAAEC,MAAQ7D,EAAE,GAAIA,EAAIwE,EAAI,KAAQ,CACrE,GAAIxE,GAAK4D,EAAEC,MAAQ7D,EAAE,GAAI,CAAE4D,EAAEC,MAAQ7D,EAAE,GAAI4D,EAAEI,IAAIU,KAAKF,GAAK,KAAQ,CAC/DxE,EAAE,IAAI4D,EAAEI,IAAI5B,MAChBwB,EAAEG,KAAK3B,MAAO,SAEtBoC,EAAKhB,EAAKnD,KAAKsC,EAASiB,GAC1B,MAAO7D,GAAKyE,EAAK,CAAC,EAAGzE,GAAI2D,EAAI,CAAE,CAAW,QAAED,EAAIzD,EAAI,CAAI,CAC1D,GAAY,EAARwE,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE1D,MAAO0D,EAAG,GAAKA,EAAG,QAAK,EAAQpB,MAAM,EAC7E,CAtB+CJ,CAAK,CAACsB,EAAGC,GAAM,CAAG,CAuBtE,EACIvD,EAAQC,EACZf,OAAOU,eAAeC,EAAS,aAAc,CAAEC,OAAO,IAkBtD,IAAI6D,EAAmB,wBACnBC,EAA0B,CAC1BvD,IAAK,SAAUwD,GAAS,OAAOnC,EAAU1B,OAAO,OAAQ,GAAQ,WAC5D,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLC,QAAS,SAAU3D,GAAO,OAAOqB,EAAU1B,OAAO,OAAQ,GAAQ,WAC9D,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLE,MAAO,WAAc,OAAOvC,EAAU1B,OAAO,OAAQ,GAAQ,WACzD,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAO,CAAC,EAAcI,OAAOC,aAAaF,QACtD,GACK,GAAI,EACLG,WAAY,SAAU/D,GAAO,OAAOqB,EAAU1B,OAAO,OAAQ,GAAQ,WACjE,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLM,QAAS,SAAUhE,EAAKP,GAAS,OAAO4B,EAAU1B,OAAO,OAAQ,GAAQ,WACrE,OAAOuC,EAAYtC,MAAM,SAAU6D,GAC/B,MAAM,IAAIC,MAAM,cAC5B,GACK,GAAI,EACLO,QAAS,SAAUT,GACf,OAAOK,OAAOC,aAAa9D,IAAIwD,EAClC,EACDU,YAAa,SAAUlE,GACnB,OAAO6D,OAAOC,aAAaH,QAAQ3D,EACtC,EACDmE,UAAW,WACP,OAAON,OAAOC,aAAaF,OAC9B,EACDQ,eAAgB,SAAUpE,GACtB,OAAO6D,OAAOC,aAAaC,WAAW/D,EACzC,EACDqE,YAAa,SAAUrE,EAAKP,GACxB,OAAOoE,OAAOC,aAAaE,QAAQhE,EAAKP,EAC3C,GAOL,SAAS6E,EAAMC,GACX,OAAO,IAAI7D,SAAQ,SAAUC,GAAW,OAAOK,WAAWL,EAAS4D,EAAc,GACrF,CAOA,SAASC,EAAqBpF,GAG1B,IAFA,IAAIqF,EAAQ,gEACRC,EAAe,GACVvF,EAAI,EAAGA,EAAIC,EAAQD,IAAK,CAE7BuF,GAAgBD,EADJE,KAAKC,MAAMD,KAAKE,SAAWJ,EAAMrF,QAEhD,CACD,OAAOsF,CACX,CASA,IAAII,EAAiC,WACjC,SAASA,EAAgBC,GACrBnF,KAAKoF,eAAiB,IAAIC,IAC1BrF,KAAKmF,oBAAiB3E,EACtBR,KAAKsF,GANFC,KAAKC,MAAMC,WAAab,EAAqB,IAOhD5E,KAAK0F,YAAc1F,KAAK0F,YAAYC,KAAK3F,MACzCA,KAAK4F,YAAc5F,KAAK4F,YAAYD,KAAK3F,MACzCA,KAAK6F,uBAAyB7F,KAAK6F,uBAAuBF,KAAK3F,MAC/DA,KAAK8F,yBAA2B9F,KAAK8F,yBAAyBH,KAAK3F,MACnEA,KAAK+F,yBAA2B/F,KAAK+F,yBAAyBJ,KAAK3F,MACnEA,KAAKmF,eAAiBA,OACU3E,IAA5B0E,EAAgBc,UAChBd,EAAgBc,QAAU,GAEjC,CA8PD,OAnPAd,EAAgBhG,UAAUwG,YAAc,SAAUO,EAASC,GAEvD,YADgB,IAAZA,IAAsBA,EAAU,KAC7BzE,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,IAAImG,EAAKC,EAAUC,EAAaC,EAAkBC,EAAaC,EAAkBC,EACjF,OAAOnE,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EACDuD,EAAMZ,KAAKC,MAAQZ,EAAqB,GACxCwB,EAAWb,KAAKC,MAAQU,EACxBG,EAAc3C,EAAmB,IAAMuC,EACvCK,OAAkC9F,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,eAC7EtB,EAAGjB,MAAQ,EACf,KAAK,EACD,OAAM2C,KAAKC,MAAQY,EACZ,CAAC,EAAa1B,EAAM,KADU,CAAC,EAAa,GAEvD,KAAK,EAGD,OAFAb,EAAGhB,OAEe,OADRyD,EAAQhC,YAAY+B,GACE,CAAC,EAAa,IAC9CE,EAAcvG,KAAKsF,GAAK,IAAMW,EAAU,IAAME,EAEvC,CAAC,EAAazB,EAAMK,KAAKC,MAAsB,GAAhBD,KAAKE,aAC/C,KAAK,EAUD,OARApB,EAAGhB,OACHyD,EAAQ7B,YAAY4B,EAAaK,KAAKC,UAAU,CAC5CrB,GAAItF,KAAKsF,GACTa,IAAKA,EACLS,WAAYL,EACZM,aAActB,KAAKC,MACnBsB,cAAevB,KAAKC,SAEjB,CAAC,EAAad,EAAM,KAC/B,KAAK,EAGD,OAFAb,EAAGhB,OAEsB,QADzB2D,EAAmBF,EAAQhC,YAAY+B,MAEnCI,EAAyBC,KAAKK,MAAMP,IACTlB,KAAOtF,KAAKsF,IAAMmB,EAAuBN,MAAQA,GACxEnG,KAAKoF,eAAe4B,IAAIb,GACxBnG,KAAK+F,yBAAyBM,EAAaF,GACpC,CAAC,GAAc,IAGvB,CAAC,EAAa,GACzB,KAAK,EAED,OADAjB,EAAgB+B,mBAAsCzG,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,gBAC1F,CAAC,EAAanF,KAAK8F,yBAAyBM,IACvD,KAAK,EACDvC,EAAGhB,OACHgB,EAAGjB,MAAQ,EACf,KAAK,EAED,OADAuD,EAAMZ,KAAKC,MAAQZ,EAAqB,GACjC,CAAC,EAAa,GACzB,KAAK,EAAG,MAAO,CAAC,GAAc,GAElD,GACA,GACA,EACIM,EAAgBhG,UAAU6G,yBAA2B,SAAUmB,EAAYf,GACvE,OAAO1E,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,IAAID,EAAQC,KACZ,OAAOsC,EAAYtC,MAAM,SAAU6D,GA6B/B,OA5BAzC,YAAW,WAAc,OAAOK,EAAU1B,OAAO,OAAQ,GAAQ,WAC7D,IAAIuG,EAASa,EAASC,EACtB,OAAO9E,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAAG,MAAO,CAAC,EAAayE,EAAc7F,UAAUX,KAAKsF,IAC1D,KAAK,EAED,OADAtC,EAAGhB,OACE7C,KAAKoF,eAAexE,IAAIuF,IAI7BG,OAAkC9F,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,eAE7D,QADhBgC,EAAUb,EAAQhC,YAAY4C,KAQ1BG,EAAc7F,UAAUP,OAAOkF,GACxB,CAAC,MAPRiB,EAAgBV,KAAKK,MAAMI,IACbL,cAAgBvB,KAAKC,MACnCc,EAAQ7B,YAAYyC,EAAYR,KAAKC,UAAUS,IAC/CC,EAAc7F,UAAUP,OAAOkF,GAMnCnG,KAAK+F,yBAAyBmB,EAAYf,GACnC,CAAC,MAhBJkB,EAAc7F,UAAUP,OAAOkF,GACxB,CAAC,IAiB5C,GACA,GAAmB,GAAI,KACA,CAAC,EACxB,GACA,GACA,EACIjB,EAAgBhG,UAAU4G,yBAA2B,SAAUM,GAC3D,OAAO3E,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,OAAOsC,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAAG,MAAO,CAAC,EAAa,IAAI9B,SAAQ,SAAUC,GAC3C,IAAIuG,GAAiB,EACjBC,EAAYhC,KAAKC,MAEjBgC,GAAmB,EACvB,SAASC,IAOL,GANKD,IACDvD,OAAOyD,oBAAoB,UAAWD,GACtCvC,EAAgByC,kBAAkBF,GAClCG,aAAaC,GACbL,GAAmB,IAElBF,EAAgB,CACjBA,GAAiB,EACjB,IAAIQ,EAXW,IAWsBvC,KAAKC,MAAQ+B,GAC9CO,EAAa,EACb1G,WAAWL,EAAS+G,GAGpB/G,EAAQ,KAEf,CACJ,CACDkD,OAAO8D,iBAAiB,UAAWN,GACnCvC,EAAgB8C,aAAaP,GAC7B,IAAII,EAAYzG,WAAWqG,EAAa1C,KAAKkD,IAAI,EAAG7B,EAAWb,KAAKC,OACvE,KACL,KAAK,EAED,OADA3B,EAAGhB,OACI,CAAC,GAEhC,GACA,GACA,EACIqC,EAAgB8C,aAAe,SAAUE,GACrClI,KAAK2H,kBAAkBO,QACS1H,IAA5B0E,EAAgBc,SAGpBd,EAAgBc,QAAQvC,KAAKyE,EACrC,EACIhD,EAAgByC,kBAAoB,SAAUO,QACV1H,IAA5B0E,EAAgBc,UAGpBd,EAAgBc,QAAUd,EAAgBc,QAAQmC,QAAO,SAAU5I,GAAK,OAAOA,IAAM2I,CAAO,IACpG,EACIhD,EAAgBkD,cAAgB,gBACI5H,IAA5B0E,EAAgBc,SAGNd,EAAgBc,QAAQqC,QAC9BC,SAAQ,SAAU/I,GAAK,OAAOA,GAAI,GAClD,EAQI2F,EAAgBhG,UAAU0G,YAAc,SAAUK,GAC9C,OAAOxE,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,OAAOsC,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAAG,MAAO,CAAC,EAAa5C,KAAK6F,uBAAuBI,IACzD,KAAK,EAAG,MAAO,CAAC,EAAcpC,EAAGhB,QAErD,GACA,GACA,EAQIqC,EAAgBhG,UAAU2G,uBAAyB,SAAUI,GACzD,OAAOxE,EAAUzB,UAAM,OAAQ,GAAQ,WACnC,IAAIsG,EAASD,EAAac,EAASoB,EACnC,OAAOjG,EAAYtC,MAAM,SAAU6D,GAC/B,OAAQA,EAAGjB,OACP,KAAK,EAID,OAHA0D,OAAkC9F,IAAxBR,KAAKmF,eAA+BxB,EAA0B3D,KAAKmF,eAC7EkB,EAAc3C,EAAmB,IAAMuC,EAEvB,QADhBkB,EAAUb,EAAQhC,YAAY+B,IAEnB,CAAC,IAEZkC,EAAgB7B,KAAKK,MAAMI,IACP7B,KAAOtF,KAAKsF,GAAY,CAAC,EAAa,GACnD,CAAC,EAAa+B,EAAc7F,UAAUX,KAAK0H,EAAcpC,MACpE,KAAK,EACDtC,EAAGhB,OACH7C,KAAKoF,eAAe/D,OAAOkH,EAAcpC,KACzCG,EAAQ9B,eAAe6B,GACvBgB,EAAc7F,UAAUP,OAAOsH,EAAcpC,KAC7CjB,EAAgBkD,gBAChBvE,EAAGjB,MAAQ,EACf,KAAK,EAAG,MAAO,CAAC,GAEpC,GACA,GACA,EAOIsC,EAAgB+B,cAAgB,SAAU9B,GAKtC,IAJA,IAAIqD,EAAmBjD,KAAKC,MAAQ,IAChCc,EAAUnB,EACVsD,EAAO,GACPC,EAAY,IACH,CACT,IAAItI,EAAMkG,EAAQjC,QAAQqE,GAC1B,GAAY,OAARtI,EACA,MAEJqI,EAAKhF,KAAKrD,GACVsI,GACH,CAED,IADA,IAAIN,GAAgB,EACX7I,EAAI,EAAGA,EAAIkJ,EAAKjJ,OAAQD,IAAK,CAClC,IAAIoJ,EAAWF,EAAKlJ,GACpB,GAAIoJ,EAASC,SAASlF,GAAmB,CACrC,IAAIyD,EAAUb,EAAQhC,YAAYqE,GAClC,GAAgB,OAAZxB,EAAkB,CAClB,IAAIoB,EAAgB7B,KAAKK,MAAMI,SACM3G,IAAhC+H,EAAczB,eAA+ByB,EAAc1B,aAAe2B,QAC1ChI,IAAhC+H,EAAczB,eAA+ByB,EAAczB,cAAgB0B,KAC5ElC,EAAQ9B,eAAemE,GACvBP,GAAgB,EAEvB,CACJ,CACJ,CACGA,GACAlD,EAAgBkD,eAE5B,EACIlD,EAAgBc,aAAUxF,EACnB0E,CACX,IACAtF,EAAA4B,QAAkB0D,MCzYX,MAKM2D,EAAmD,CAC9DC,iBANkD,IAkDvCC,EAAuB,CAClCC,KAAM,eACNC,QC1Da,SD6DFC,EAAuB,IAAM3D,KAAKC,ME1DzC,MAAO2D,UAAqBrF,MAChCsF,YAAmBC,EAAsBC,GACvCC,MAAMD,GADWtJ,KAAKqJ,MAALA,EAAsBrJ,KAAiBsJ,kBAAjBA,EAEvCrK,OAAOuK,eAAexJ,KAAMmJ,EAAajK,UAC1C,CAEDuK,oBAAmBJ,MACjBA,EAAKC,kBACLA,IAKA,OAAO,IAAIH,EAAaE,EAAOC,EAChC,EAOG,MAAOI,UAA4BP,EACvCC,YACEC,EACAC,EACOK,EACAC,EAAgB,MAEvBL,MAAMF,EAAOC,GAHNtJ,KAAK2J,MAALA,EACA3J,KAAQ4J,SAARA,EAIP3K,OAAOuK,eAAexJ,KAAM0J,EAAoBxK,UACjD,EAOG,MAAO2K,UAAqBV,EAChCC,cACEG,MAAM,UAAW,WAEjBtK,OAAOuK,eAAexJ,KAAM6J,EAAa3K,UAC1C,EAMG,MAAO4K,UAA0BD,EACrCT,YAAmBW,GACjBR,QADiBvJ,KAAK+J,MAALA,EAGjB9K,OAAOuK,eAAexJ,KAAM8J,EAAkB5K,UAC/C,EAGG,MAAO8K,UAA4Bb,EACvCC,YAAmBW,GACjBR,MAAM,YAAa,gBADFvJ,KAAK+J,MAALA,EAGjB9K,OAAOuK,eAAexJ,KAAMgK,EAAoB9K,UACjD,EAMG,MAAO+K,UAAyBd,EACpCC,YACEC,EACAC,EACOY,GAEPX,MAAMF,EAAOC,GAFNtJ,KAASkK,UAATA,EAIPjL,OAAOuK,eAAexJ,KAAMiK,EAAiB/K,UAC9C,EAMG,MAAOiL,UAAiChB,EAC5CC,YAAmBgB,EAAyBC,GAC1Cd,MACE,wBACA,qCAAqCe,EAAmBF,EAAU,CAChE,yBACcE,EAAmBD,QALpBrK,KAAQoK,SAARA,EAAyBpK,KAAKqK,MAALA,EAO1CpL,OAAOuK,eAAexJ,KAAMmK,EAAyBjL,UACtD,EASH,SAASoL,EAAmBzK,EAAe0K,EAAoB,IAC7D,OAAO1K,IAAU0K,EAAQ3B,SAAS/I,GAASA,EAAQ,EACrD,CC5FO,MA6HM2K,EAAY,IAChBvG,OAAOwG,OAGHC,EAAqB,KAChC,MAAMC,EACJ,qEACF,IAAI1F,EAAS,GAKb,OAJqB2F,MAAMC,KACzBL,IAAYM,gBAAgB,IAAIC,WAAW,MAEhCzC,SAAQhF,GAAM2B,GAAU0F,EAAQrH,EAAIqH,EAAQnL,UAClDyF,CAAM,EAGF+F,EAAUnL,GAAkBoL,KAAKpL,GASjCqL,EAAqBrH,QAAEsH,SAAUC,GAASvH,EAAKwH,EAAMzM,EAAAiF,EAAhC,cAChC,OAAO,IAAIyH,gBAPU,CAACD,GACfpM,OAAOsM,KAAKF,GAChBlD,QAAOqD,QAA0B,IAAdH,EAAOG,KAC1BC,QAAO,CAACC,EAAKtL,IAAQnB,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAMD,GAAG,CAAEtL,CAACA,GAAMiL,EAAOjL,MAAS,CAAA,GAKxDwL,CAAiB3M,OAAA0M,OAAA,CAAAP,aAAcC,KAC/B5F,UAAU,EA4BDoG,EAAgBC,GAVX,CAACA,GACjBC,mBACEC,KAAKF,GACFG,MAAM,IACNC,KAAIC,GACI,KAAO,KAAOA,EAAEC,WAAW,GAAG3G,SAAS,KAAK4C,OAAO,KAE3DgE,KAAK,KAIVC,CAAUR,EAAMS,QAAQ,KAAM,KAAKA,QAAQ,KAAM,MCpL7CC,EAAUC,MAAOC,EAAkBC,KACvC,MAAMC,QAAiBC,MAAMH,EAAUC,GAEvC,MAAO,CACLG,GAAIF,EAASE,GACbC,WAAYH,EAASG,OACtB,EAGGC,EAAqBP,MACzBC,EACAC,EACAzG,KAEA,MAAM+G,EAhBmC,IAAIC,gBAmB7C,IAAIC,EAGJ,OALAR,EAAaS,OAASH,EAAWG,OAK1BtM,QAAQuM,KAAK,CAClBb,EAAQE,EAAUC,GAElB,IAAI7L,SAAQ,CAAC6B,EAAG3B,KACdmM,EAAY/L,YAAW,KACrB6L,EAAWK,QACXtM,EAAO,IAAI8C,MAAM,kCAAkC,GAClDoC,EAAQ,MAEZqH,SAAQ,KACT3F,aAAauF,EAAU,GACvB,EAGEK,EAAkBf,MACtBC,EACAtC,EACAC,EACAsC,EACAzG,EACAuH,EACAC,KAEA,OCnD0BC,EDoDxB,CACEC,KAAM,CACJxD,WACAC,SAEFnE,UACAwG,WACAC,eACAe,eC5D0DG,ED8D5DJ,EC7DF,IAAI3M,SAAQ,SAAUC,EAASC,GAC7B,MAAM8M,EAAiB,IAAIC,eAE3BD,EAAeE,MAAMC,UAAY,SAAUC,GAErCA,EAAMC,KAAK9E,MACbrI,EAAO,IAAI8C,MAAMoK,EAAMC,KAAK9E,QAE5BtI,EAAQmN,EAAMC,MAEhBL,EAAeE,MAAMI,OACvB,EAEAP,EAAGQ,YAAYV,EAAS,CAACG,EAAeQ,OAC1C,IAfyB,IAACX,EAAoCE,CD+D7D,EAGUU,EAAc9B,MACzBC,EACAtC,EACAC,EACAsC,EACAc,EACAC,EACAxH,EJpDsC,MIsDlCuH,EACKD,EACLd,EACAtC,EACAC,EACAsC,EACAzG,EACAuH,EACAC,GAGKV,EAAmBN,EAAUC,EAAczG,GExF/CuG,eAAe+B,EACpB3K,EASA4J,GATA,IAAAgB,QACEA,EAAOvI,QACPA,EAAOkE,SACPA,EAAQC,MACRA,EAAKqE,YACLA,EAAWhB,YACXA,GAEqB7J,EADlB8K,EAAO/P,EAAAiF,EAPZ,sEAWA,MAAMtB,EAAOmL,EACTxC,EAAkByD,GAClBjI,KAAKC,UAAUgI,GAEnB,aF4EKlC,eACLmC,EACA1I,EACAkE,EACAC,EACAsE,EACAlB,EACAC,GAEA,IACId,EADAiC,EAA2B,KAG/B,IAAK,IAAItP,EAAI,EAAGA,EJ3F8B,EI2FQA,IACpD,IACEqN,QAAiB2B,EACfK,EACAxE,EACAC,EACAsE,EACAlB,EACAC,EACAxH,GAEF2I,EAAa,KACb,KAOD,CANC,MAAO/P,GAKP+P,EAAa/P,CACd,CAGH,GAAI+P,EACF,MAAMA,EAGR,MACEhL,EAEE+I,EAAQG,MAFV1D,MAAQA,EAAKC,kBAAEA,GAAiBzF,EAAKsK,EAAIvP,EAAAiF,EAAnC,gCADFiJ,GAEJA,GACEF,EAEJ,IAAKE,EAAI,CACP,MAAMgC,EACJxF,GAAqB,+BAA+BsF,IAEtD,GAAc,iBAAVvF,EACF,MAAM,IAAIY,EAAiBZ,EAAOyF,EAAcX,EAAKjE,WAGvD,GAAc,0BAAVb,EACF,MAAM,IAAIc,EAAyBC,EAAUC,GAG/C,MAAM,IAAIlB,EAAaE,GAAS,gBAAiByF,EAClD,CAED,OAAOX,CACT,CEvIeY,CACX,GAAGN,gBACHvI,EACAkE,GAAY,UACZC,EACA,CACE2E,OAAQ,OACRzM,OACA0M,QAAS,CACP,eAAgBvB,EACZ,oCACA,mBACJ,eAAgBzC,KACdvE,KAAKC,UAAU+H,GAAe3F,MAIpC0E,EACAC,EAEJ,CCtCA,MAWawB,EAAkB,IAAIC,KACjC,OAZcC,EAYAD,EAAOhH,OAAOkH,SAAShD,KAAK,KAAKiD,OAAOrD,MAAM,OAZ5BrB,MAAMC,KAAK,IAAIxF,IAAI+J,KAYiB/C,KAAK,KAZ5D,IAAC+C,CAY+D,QCJlEG,EAKXnG,YACE+E,EACOqB,EAhBqB,iBAiBrBC,GADAzP,KAAMwP,OAANA,EACAxP,KAAMyP,OAANA,EAEPzP,KAAKmL,SAAWgD,EAAKhD,SACrBnL,KAAKqK,MAAQ8D,EAAK9D,MAClBrK,KAAKoK,SAAW+D,EAAK/D,QACtB,CAMDsF,QACE,MAAO,CAAC1P,KAAKwP,OAAQxP,KAAKmL,SAAUnL,KAAKoK,SAAUpK,KAAKqK,MAAOrK,KAAKyP,QACjEtH,OAAOkH,SACPhD,KAAK,KACT,CAOD5C,eAAerJ,GACb,MAAOoP,EAAQrE,EAAUf,EAAUC,GAASjK,EAAI6L,MAAM,MAEtD,OAAO,IAAIsD,EAAS,CAAEpE,WAAUd,QAAOD,YAAYoF,EACpD,CAOD/F,sBAAsBkG,GACpB,MAAMtF,MAAEA,EAAKD,SAAEA,EAAUgB,UAAWD,GAAawE,EAEjD,OAAO,IAAIJ,EAAS,CAClBlF,QACAD,WACAe,YAEH,QC1DUyE,EACJnP,IAAmBL,EAAauP,GACrCzL,aAAaE,QAAQhE,EAAKsG,KAAKC,UAAUgJ,GAC1C,CAEMpP,IAAmBH,GACxB,MAAM2M,EAAO9I,OAAOC,aAAaH,QAAQ3D,GAEzC,GAAK2M,EAEL,IAEE,OADgBrG,KAAKK,MAAMgG,EAK5B,CAFC,MAAOjO,GACP,MACD,CACF,CAEM+Q,OAAOzP,GACZ8D,aAAaC,WAAW/D,EACzB,CAEM0P,UACL,OAAO7Q,OAAOsM,KAAKtH,OAAOC,cAAciE,QAAO/H,GAC7CA,EAAI2P,WDzBsB,mBC2B7B,QC3BUC,EAAb5G,cACSpJ,KAAAiQ,cAAwB,WAC7B,IAAIC,EAAiC,CAAA,EAErC,MAAO,CACLzP,IAAmBL,EAAauP,GAC9BO,EAAM9P,GAAOuP,CACd,EAEDpP,IAAmBH,GACjB,MAAM+P,EAAaD,EAAM9P,GAEzB,GAAK+P,EAIL,OAAOA,CACR,EAEDN,OAAOzP,UACE8P,EAAM9P,EACd,EAED0P,QAAO,IACE7Q,OAAOsM,KAAK2E,GAGxB,CA1B8B,EA2BhC,QCdYE,EAGXhH,YACU8G,EACAG,EACRC,GAFQtQ,KAAKkQ,MAALA,EACAlQ,KAAWqQ,YAAXA,EAGRrQ,KAAKsQ,YAAcA,GAAepH,CACnC,CAEDuD,iBACEtB,EACAoF,EACAC,SAEA,MAAMC,EAAWzQ,KAAK0Q,mBAAmBvF,SACnCnL,KAAKkQ,MAAMzP,IAAIgQ,EAAU,CAC7BE,SAAUJ,EACVC,uBAEsB,QAAlB3M,EAAA7D,KAAKqQ,mBAAa,IAAAxM,OAAA,EAAAA,EAAAmD,IAAIyJ,GAC7B,CAEDhE,iBAAiBgE,GACf,MAAMd,QAAc3P,KAAKkQ,MAAM3P,IAC7BP,KAAK0Q,mBAAmBD,EAAStF,WAGnC,IAAKwE,GAASc,EAASpG,OAASoG,EAASrG,SAAU,CACjD,MAAMwG,QAAqB5Q,KAAKO,IAAIkQ,GAEpC,IAAKG,EACH,OAGF,IAAKA,EAAaD,WAAaC,EAAaJ,aAC1C,OAGF,MAAO,CACLG,SAAUC,EAAaD,SACvBH,aAAcI,EAAaJ,aAE9B,CAED,GAAKb,EAIL,MAAO,CAAEgB,SAAUhB,EAAMgB,SAAUH,aAAcb,EAAMa,aACxD,CAED/D,UACEgE,EACAI,EAzDsC,SA2DtC,IAAIC,QAAqB9Q,KAAKkQ,MAAM3P,IAClCkQ,EAASf,SAGX,IAAKoB,EAAc,CACjB,MAAMvF,QAAavL,KAAK+Q,eAExB,IAAKxF,EAAM,OAEX,MAAMyF,EAAahR,KAAKiR,sBAAsBR,EAAUlF,GAEpDyF,IACFF,QAAqB9Q,KAAKkQ,MAAM3P,IAAuByQ,GAE1D,CAGD,IAAKF,EACH,OAGF,MAAMtL,QAAYxF,KAAKsQ,cACjBY,EAAanM,KAAKC,MAAMQ,EAAM,KAEpC,OAAIsL,EAAaK,UAAYN,EAA0BK,EACjDJ,EAAavO,KAAK6O,eACpBN,EAAavO,KAAO,CAClB6O,cAAeN,EAAavO,KAAK6O,qBAG7BpR,KAAKkQ,MAAMzP,IAAIgQ,EAASf,QAASoB,GAChCA,EAAavO,aAGhBvC,KAAKkQ,MAAML,OAAOY,EAASf,oBACT,QAAlB7L,EAAA7D,KAAKqQ,mBAAa,IAAAxM,OAAA,EAAAA,EAAAgM,OAAOY,EAASf,WAKnCoB,EAAavO,IACrB,CAEDkK,UAAUkD,SACR,MAAMc,EAAW,IAAIlB,EAAS,CAC5BpE,SAAUwE,EAAMvE,UAChBf,MAAOsF,EAAMtF,MACbD,SAAUuF,EAAMvF,WAGZ0G,QAAqB9Q,KAAKqR,eAAe1B,SAEzC3P,KAAKkQ,MAAMzP,IAAIgQ,EAASf,QAASoB,SACf,QAAlBjN,EAAA7D,KAAKqQ,mBAAa,IAAAxM,OAAA,EAAAA,EAAAmD,IAAIyJ,EAASf,SACtC,CAEDjD,YAAYtB,SACV,MAAMI,QAAavL,KAAK+Q,eAGnBxF,UAECA,EACHpD,QAAO/H,IAAQ+K,GAAW/K,EAAIwI,SAASuC,KACvCM,QAAOgB,MAAO6E,EAAMlR,WACbkR,QACAtR,KAAKkQ,MAAML,OAAOzP,EAAI,GAC3BU,QAAQC,iBAEW,UAAlBf,KAAKqQ,mBAAa,IAAAxM,OAAA,EAAAA,EAAAG,SACzB,CAEOyI,qBAAqBkD,GAC3B,MAAMnK,QAAYxF,KAAKsQ,cAGvB,MAAO,CACL/N,KAAMoN,EACNwB,UAJoBpM,KAAKC,MAAMQ,EAAM,KAAQmK,EAAM4B,WAMtD,CAEO9E,2BACN,OAAIzM,KAAKqQ,YACgC,QAAhCxM,QAAO7D,KAAKqQ,YAAY9P,aAAQ,IAAAsD,OAAA,EAAAA,EAAA0H,KAC9BvL,KAAKkQ,MAAMJ,QACb9P,KAAKkQ,MAAMJ,eADb,CAGR,CAOOY,mBAAmBvF,GACzB,OAAO,IAAIoE,EACT,CAAEpE,YHxKwB,iBACS,YG0KnCuE,OACH,CAcOuB,sBAAsBO,EAAsB1B,GAClD,OAAOA,EAAQ3H,QAAO/H,UACpB,MAAMqQ,EAAWlB,EAASkC,QAAQrR,GAC5BsR,EAAW,IAAIrM,IAAIoL,EAASpG,OAASoG,EAASpG,MAAM4B,MAAM,MAC1D0F,GAAkC,QAAlB9N,EAAA2N,EAAWnH,aAAO,IAAAxG,OAAA,EAAAA,EAAAoI,MAAM,OAAQ,GAEhD2F,EACJnB,EAASpG,OACTsH,EAAclG,QACZ,CAACC,EAAKmG,IAAYnG,GAAOgG,EAAS9Q,IAAIiR,KACtC,GAGJ,MHvM0B,mBGwMxBpB,EAASjB,QACTiB,EAAStF,WAAaqG,EAAWrG,UACjCsF,EAASrG,WAAaoH,EAAWpH,UACjCwH,CACA,IACD,EACJ,QCjMUE,EAGX1I,YACU2I,EACA5G,EACA6G,GAFAhS,KAAO+R,QAAPA,EACA/R,KAAQmL,SAARA,EACAnL,KAAYgS,aAAZA,EAERhS,KAAKkH,WAAa,gBAAqClH,KAAKmL,UAC7D,CAEM8G,OAAOC,GACZlS,KAAK+R,QAAQI,KAAKnS,KAAKkH,WAAYgL,EAAa,CAC9CE,gBAAiB,EACjBJ,aAAchS,KAAKgS,cAEtB,CAEMzR,MACL,OAAOP,KAAK+R,QAAQxR,IAAIP,KAAKkH,WAC9B,CAEM2I,SACL7P,KAAK+R,QAAQlC,OAAO7P,KAAKkH,WAAY,CACnC8K,aAAchS,KAAKgS,cAEtB,ECtCH,MAAMK,EAAYhP,GAAwB,iBAANA,EAE9BiP,EAAiB,CACrB,MACA,MACA,MACA,MACA,MACA,MACA,MACA,QACA,YACA,UACA,SACA,MACA,MACA,UACA,MACA,eACA,WACA,aACA,eACA,iBACA,OACA,OACA,MACA,SACA,MACA,MACA,MACA,MACA,MACA,OA2BWC,EAAU5D,IACrB,IAAKA,EAAQgC,SACX,MAAM,IAAI7M,MAAM,oCAGlB,MAAM0O,EA7Bc,CAACC,IACrB,MAAMC,EAAQD,EAAMxG,MAAM,MACnB0G,EAAQC,EAASC,GAAaH,EAErC,GAAqB,IAAjBA,EAAMlT,SAAiBmT,IAAWC,IAAYC,EAChD,MAAM,IAAI/O,MAAM,iCAElB,MAAMgP,EAAcpM,KAAKK,MAAM8E,EAAa+G,IACtCG,EAAkB,CAAEC,MAAOP,GAC3BQ,EAAY,CAAA,EAOlB,OANAhU,OAAOsM,KAAKuH,GAAaxK,SAAQkD,IAC/BuH,EAAOvH,GAAKsH,EAAYtH,GACnB8G,EAAe1J,SAAS4C,KAC3ByH,EAAKzH,GAAKsH,EAAYtH,GACvB,IAEI,CACL0H,QAAS,CAAEP,SAAQC,UAASC,aAC5BF,OAAQjM,KAAKK,MAAM8E,EAAa8G,IAChCI,SACAE,OACD,EAQeE,CAAOxE,EAAQgC,UAE/B,IAAK6B,EAAQO,OAAOK,IAClB,MAAM,IAAItP,MACR,+DAIJ,GAAI0O,EAAQO,OAAOK,MAAQzE,EAAQyE,IACjC,MAAM,IAAItP,MACR,0DAA0D6K,EAAQyE,gBAAgBZ,EAAQO,OAAOK,QAIrG,IAAKZ,EAAQS,KAAKI,IAChB,MAAM,IAAIvP,MACR,gEAIJ,GAA2B,UAAvB0O,EAAQG,OAAOW,IACjB,MAAM,IAAIxP,MACR,2BAA2B0O,EAAQG,OAAOW,2EAI9C,IACGd,EAAQO,OAAOQ,KAEgB,iBAAvBf,EAAQO,OAAOQ,MACtB3I,MAAM4I,QAAQhB,EAAQO,OAAOQ,KAG/B,MAAM,IAAIzP,MACR,qFAGJ,GAAI8G,MAAM4I,QAAQhB,EAAQO,OAAOQ,KAAM,CACrC,IAAKf,EAAQO,OAAOQ,IAAI3K,SAAS+F,EAAQ4E,KACvC,MAAM,IAAIzP,MACR,4DACE6K,EAAQ4E,4BACef,EAAQO,OAAOQ,IAAIlH,KAAK,UAGrD,GAAImG,EAAQO,OAAOQ,IAAI/T,OAAS,EAAG,CACjC,IAAKgT,EAAQO,OAAOU,IAClB,MAAM,IAAI3P,MACR,uHAGJ,GAAI0O,EAAQO,OAAOU,MAAQ9E,EAAQ4E,IACjC,MAAM,IAAIzP,MACR,oEAAoE6K,EAAQ4E,gBAAgBf,EAAQO,OAAOU,OAGhH,CACF,MAAM,GAAIjB,EAAQO,OAAOQ,MAAQ5E,EAAQ4E,IACxC,MAAM,IAAIzP,MACR,4DAA4D6K,EAAQ4E,mBAAmBf,EAAQO,OAAOQ,QAG1G,GAAI5E,EAAQ+E,MAAO,CACjB,IAAKlB,EAAQO,OAAOW,MAClB,MAAM,IAAI5P,MACR,gEAGJ,GAAI0O,EAAQO,OAAOW,QAAU/E,EAAQ+E,MACnC,MAAM,IAAI5P,MACR,2DAA2D6K,EAAQ+E,kBAAkBlB,EAAQO,OAAOW,SAGzG,CAED,GAAI/E,EAAQgF,UAAYtB,EAASG,EAAQO,OAAOa,WAC9C,MAAM,IAAI9P,MACR,sHAKJ,GAA0B,MAAtB0O,EAAQO,OAAOc,MAAgBxB,EAASG,EAAQO,OAAOc,KACzD,MAAM,IAAI/P,MACR,wEAGJ,IAAKuO,EAASG,EAAQO,OAAO5M,KAC3B,MAAM,IAAIrC,MACR,kEAIJ,MAAMgQ,EAASnF,EAAQmF,QAAU,GAC3BtO,EAAM,IAAID,KAAKoJ,EAAQnJ,KAAOD,KAAKC,OACnCuO,EAAU,IAAIxO,KAAK,GAIzB,GAFAwO,EAAQC,cAAcxB,EAAQO,OAAOc,IAAMC,GAEvCtO,EAAMuO,EACR,MAAM,IAAIjQ,MACR,oEAAoE0B,gCAAkCuO,MAI1G,GAA0B,MAAtBvB,EAAQO,OAAOkB,KAAe5B,EAASG,EAAQO,OAAOkB,KAAM,CAC9D,MAAMC,EAAU,IAAI3O,KAAK,GAEzB,GADA2O,EAAQF,cAAcxB,EAAQO,OAAOkB,IAAMH,GACvCtO,EAAM0O,EACR,MAAM,IAAIpQ,MACR,+GAA+G0B,gBAAkB0O,IAGtI,CAED,GAAgC,MAA5B1B,EAAQO,OAAOa,WAAqBvB,EAASG,EAAQO,OAAOa,WAAY,CAC1E,MAAMO,EAAe,IAAI5O,KAAK,GAK9B,GAJA4O,EAAaH,cACXI,SAAS5B,EAAQO,OAAOa,WAAcjF,EAAQgF,QAAqBG,GAGjEtO,EAAM2O,EACR,MAAM,IAAIrQ,MACR,uJAAuJ0B,4BAA8B2O,IAG1L,CAED,GAAIxF,EAAQ0F,aAAc,CACxB,MAAMC,EAAM3F,EAAQ0F,aAAa/E,OACjC,GAAIgF,EAAIvE,WAAW,QAAS,CAC1B,MAAMwE,EAAQD,EACd,IAAK9B,EAAQO,OAAOyB,OAClB,MAAM,IAAI1Q,MACR,2EAEG,GAAIyQ,IAAU/B,EAAQO,OAAOyB,OAClC,MAAM,IAAI1Q,MACR,sEAAsEyQ,cAAkB/B,EAAQO,OAAOyB,UAG5G,KAAM,CACL,MAAMC,EAAUH,EAAII,cAEpB,IAAKlC,EAAQO,OAAO4B,SAClB,MAAM,IAAI7Q,MACR,+EAEG,GAAI2Q,IAAYjC,EAAQO,OAAO4B,SACpC,MAAM,IAAI7Q,MACR,0EAA0E2Q,cAAoBjC,EAAQO,OAAO4B,YAGlH,CACF,CAED,OAAOnC,CAAO,yBC9NhB,IAAIoC,EAAY5U,GAAQA,EAAK4U,UAAa,WAStC,OARAA,EAAW3V,OAAO0M,QAAU,SAAS5M,GACjC,IAAK,IAAIF,EAAGU,EAAI,EAAG8D,EAAIwR,UAAUrV,OAAQD,EAAI8D,EAAG9D,IAE5C,IAAK,IAAIP,KADTH,EAAIgW,UAAUtV,GACON,OAAOC,UAAUC,eAAeC,KAAKP,EAAGG,KACzDD,EAAEC,GAAKH,EAAEG,IAEjB,OAAOD,CACf,EACW6V,EAASvS,MAAMrC,KAAM6U,UAChC,EAEA,SAASC,EAAmB9L,EAAMnJ,GAC9B,IAAKA,EACD,MAAO,GAEX,IAAIkV,EAAc,KAAO/L,EACzB,OAAc,IAAVnJ,EACOkV,EAEJA,EAAc,IAAMlV,CAC/B,CAaA,SAASmL,EAAOhC,EAAMnJ,EAAOmV,GACzB,OAAOC,mBAAmBjM,GACrBuD,QAAQ,2BAA4BR,oBACpCQ,QAAQ,MAAO,OAAOA,QAAQ,MAAO,OACpC,IAAM0I,mBAAmBpV,GAE1B0M,QAAQ,4DAA6DR,oBAlB9E,SAA6BiJ,GACzB,GAAkC,iBAAvBA,EAAWE,QAAsB,CACxC,IAAIA,EAAU,IAAI3P,KAClB2P,EAAQC,gBAAgBD,EAAQE,kBAAyC,MAArBJ,EAAWE,SAC/DF,EAAWE,QAAUA,CACxB,CACD,OAAOJ,EAAmB,UAAWE,EAAWE,QAAUF,EAAWE,QAAQG,cAAgB,IACvFP,EAAmB,SAAUE,EAAWM,QACxCR,EAAmB,OAAQE,EAAWO,MACtCT,EAAmB,SAAUE,EAAWQ,QACxCV,EAAmB,WAAYE,EAAWS,SACpD,CAQUC,CAAoBV,EAC9B,CAEA,SAASjO,EAAM4O,GAIX,IAHA,IAAIzT,EAAS,CAAA,EACT0T,EAAUD,EAAeA,EAAa1J,MAAM,MAAQ,GACpD4J,EAAU,mBACLtW,EAAI,EAAGA,EAAIqW,EAAQpW,OAAQD,IAAK,CACrC,IAAImT,EAAQkD,EAAQrW,GAAG0M,MAAM,KACzB6J,EAASpD,EAAMrK,MAAM,GAAGgE,KAAK,KACR,MAArByJ,EAAOC,OAAO,KACdD,EAASA,EAAOzN,MAAM,GAAI,IAE9B,IAEInG,EADawQ,EAAM,GAAGnG,QAAQsJ,EAAS9J,qBACtB+J,EAAOvJ,QAAQsJ,EAAS9J,mBAI5C,CAFD,MAAOjN,GAEN,CACJ,CACD,OAAOoD,CACX,CAEA,SAAS8T,IACL,OAAOjP,EAAMkP,SAASH,OAC1B,CAMA,SAASrV,EAAIuI,EAAMnJ,EAAOmV,GACtBiB,SAASH,OAAS9K,EAAOhC,EAAMnJ,EAAO+U,EAAS,CAAEW,KAAM,KAAOP,GAClE,CAhEApV,EAAkBsW,YAAG,EAgCrBtW,EAAcoL,OAAGA,EAqBjBpL,EAAamH,MAAGA,EAIhBnH,EAAcoW,OAAGA,EAIjBpW,EAAWW,IAHX,SAAayI,GACT,OAAOgN,IAAShN,EACpB,EAKApJ,EAAWa,IAAGA,EAIdb,EAAAiQ,OAHA,SAAgB7G,EAAMgM,GAClBvU,EAAIuI,EAAM,GAAI4L,EAASA,EAAS,CAAA,EAAII,GAAa,CAAEE,SAAU,IACjE,mEC7DO,MAAMiB,EAAgB,CAC3B5V,IAAsBH,GACpB,MAAMP,EAAQuW,EAAYhW,GAE1B,QAAqB,IAAVP,EAIX,OAAU6G,KAAKK,MAAMlH,EACtB,EAEDsS,KAAK/R,EAAaP,EAAY8O,GAC5B,IAAI0H,EAA6C,CAAA,EAE7C,WAAapS,OAAOqS,SAASC,WAC/BF,EAAmB,CACjBb,QAAQ,EACRC,SAAU,UAIV9G,eAAAA,EAASyD,mBACXiE,EAAiBnB,QAAUvG,EAAQyD,kBAGjCzD,eAAAA,EAASqD,gBACXqE,EAAiBf,OAAS3G,EAAQqD,cAGpCwE,EAAYpW,EAAKsG,KAAKC,UAAU9G,GAAQwW,EACzC,EAEDxG,OAAOzP,EAAauO,GAClB,IAAI0H,EAA6C,CAAA,GAE7C1H,eAAAA,EAASqD,gBACXqE,EAAiBf,OAAS3G,EAAQqD,cAGpCyE,EAAerW,EAAKiW,EACrB,GAYUK,EAAkC,CAC7CnW,IAAsBH,GACpB,MAAMP,EAAQsW,EAAc5V,IAAOH,GAEnC,OAAIP,GAIGsW,EAAc5V,IAAO,WAAmBH,IAChD,EAED+R,KAAK/R,EAAaP,EAAY8O,GAC5B,IAAI0H,EAA6C,CAAA,EAE7C,WAAapS,OAAOqS,SAASC,WAC/BF,EAAmB,CAAEb,QAAQ,KAG3B7G,eAAAA,EAASyD,mBACXiE,EAAiBnB,QAAUvG,EAAQyD,kBAGjCzD,eAAAA,EAASqD,gBACXqE,EAAiBf,OAAS3G,EAAQqD,cAGpCwE,EACE,WAAmBpW,IACnBsG,KAAKC,UAAU9G,GACfwW,GAEFF,EAAchE,KAAK/R,EAAKP,EAAO8O,EAChC,EAEDkB,OAAOzP,EAAauO,GAClB,IAAI0H,EAA6C,CAAA,GAE7C1H,eAAAA,EAASqD,gBACXqE,EAAiBf,OAAS3G,EAAQqD,cAGpCyE,EAAerW,EAAKiW,GACpBF,EAActG,OAAOzP,EAAKuO,GAC1BwH,EAActG,OAAO,WAAmBzP,IAAOuO,EAChD,GAMUgI,EAAiB,CAC5BpW,IAAsBH,GAEpB,GAA8B,oBAAnBwW,eACT,OAGF,MAAM/W,EAAQ+W,eAAe7S,QAAQ3D,GAErC,OAAa,MAATP,EAIM6G,KAAKK,MAAMlH,QAJrB,CAKD,EAEDsS,KAAK/R,EAAaP,GAChB+W,eAAexS,QAAQhE,EAAKsG,KAAKC,UAAU9G,GAC5C,EAEDgQ,OAAOzP,GACLwW,eAAezS,WAAW/D,EAC3B,87GC/IH,MAAMyW,EAAiD,CAAA,QCO1CC,EAGX1N,YAAoB8G,EAAuB/E,GAAvBnL,KAAKkQ,MAALA,EAAuBlQ,KAAQmL,SAARA,EACzCnL,KAAK+W,YAAc/W,KAAKgX,sBAAsBhX,KAAKmL,SACpD,CAEDsB,UAAUrM,SACR,MAAMmL,EAAO,IAAIlG,KAC2C,QAA1DxB,QAAO7D,KAAKkQ,MAAM3P,IAAsBP,KAAK+W,oBAAa,IAAAlT,OAAA,EAAAA,EAAE0H,OAAQ,IAGtEA,EAAKvE,IAAI5G,SAEHJ,KAAKkQ,MAAMzP,IAAsBT,KAAK+W,YAAa,CACvDxL,KAAM,IAAIA,IAEb,CAEDkB,aAAarM,GACX,MAAMuP,QAAc3P,KAAKkQ,MAAM3P,IAAsBP,KAAK+W,aAE1D,GAAIpH,EAAO,CACT,MAAMpE,EAAO,IAAIlG,IAAIsK,EAAMpE,MAG3B,OAFAA,EAAKlK,OAAOjB,GAERmL,EAAK0L,KAAO,QACDjX,KAAKkQ,MAAMzP,IAAIT,KAAK+W,YAAa,CAAExL,KAAM,IAAIA,WAG/CvL,KAAKkQ,MAAML,OAAO7P,KAAK+W,YACrC,CACF,CAEDxW,MACE,OAAOP,KAAKkQ,MAAM3P,IAAsBP,KAAK+W,YAC9C,CAED/S,QACE,OAAOhE,KAAKkQ,MAAML,OAAO7P,KAAK+W,YAC/B,CAEOC,sBAAsB7L,GAC5B,MAAO,mBAAwBA,GAChC,ECvCI,MAsBD+L,EAAsD,CAC1DC,OAAQ,KAAM,IAAInH,GAAgBC,cAClCmH,aAAc,IAAM,IAAIxH,GAMbyH,EAAgBf,GACpBY,EAAsBZ,GAuClBgB,EAGX3I,IAEA,MAAM4I,QAAEA,EAAOC,WAAEA,GAAmC7I,EAApB8I,EAAoB7Y,EAAA+P,EAA9C,CAAA,UAAA,eAON,sCAJK8I,GAAe,CAClBF,SAAqB,IAAZA,GAAqBA,EAAUA,EAAUC,GAGlC,ECed3W,GAAO,IAAI6W,QAKJC,GA2BXvO,YAAYuF,GAkBV,IAAIiJ,EACA1H,EAEJ,GAjCelQ,KAAA6X,WAAoB,IAAI7H,GAAgBC,cAIxCjQ,KAAA8X,eAA8C,CAC7DC,oBAAqB,CACnB1N,MnBzFuB,wBmB2FzB2N,0BAA0B,EAC1BtK,aAAa,GA27BP1N,KAAsBiY,uBAAGxL,gBACzB5L,GAAK+E,YD1jC4B,+BC4jCvC3B,OAAOyD,oBAAoB,WAAY1H,KAAKiY,uBAAuB,EA17BnEjY,KAAK2O,QACA1P,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAA3L,KAAK8X,gBACLnJ,GACH,CAAAoJ,mDACK/X,KAAK8X,eAAeC,qBACpBpJ,EAAQoJ,uBAIG,oBAAX9T,QhBqDmB,MAC5B,IAAKuG,IACH,MAAM,IAAI1G,MACR,4EAGJ,QAAkC,IAAvB0G,IAAY0N,OACrB,MAAM,IAAIpU,MAAM,iMAGjB,EgB/DkCqU,GAE7BxJ,EAAQuB,OAASvB,EAAQiJ,eAC3BQ,QAAQC,KACN,8IAOA1J,EAAQuB,MACVA,EAAQvB,EAAQuB,UACX,CAGL,GAFA0H,EAAgBjJ,EAAQiJ,enBvIO,UmByI1BP,EAAaO,GAChB,MAAM,IAAI9T,MAAM,2BAA2B8T,MAG7C1H,EAAQmH,EAAaO,EAAbP,EACT,CAEDrX,KAAKsY,cAAgB3J,EAAQ4J,qBACM,IAA/B5J,EAAQ4J,qBnBnJwB,ImBsJpCvY,KAAKwY,eAC8B,IAAjC7J,EAAQ8J,qBACJtC,EACAO,EAEN1W,KAAK0Y,kBDrKP,SCsKI1Y,KAAK2O,QAAQxD,6BAGfnL,KAAK2Y,0BD/JqC,CAACxN,GAC7C,SAASA,qBC8J0ByN,CAC/B5Y,KAAK2O,QAAQxD,UAGfnL,KAAK6Y,uBACHlK,EAAQkK,wBnB7ImC,EmB+I7C,MAAMC,EAAqBnK,EAAQoK,0BAC/B/Y,KAAKwY,cACL7B,EhBwBiB,IAACqC,EgBlBtBhZ,KAAKqK,MAAQ6E,EACX,SACAlP,KAAK2O,QAAQoJ,oBAAoB1N,MACjCrK,KAAK2O,QAAQsK,iBAAmB,iBAAmB,IAGrDjZ,KAAKkZ,mBAAqB,IAAIpH,EAC5BgH,EACA9Y,KAAK2O,QAAQxD,SACbnL,KAAK2O,QAAQqD,cAGfhS,KAAKsQ,YAActQ,KAAK2O,QAAQ2B,aAAepH,EAE/ClJ,KAAKmZ,aAAe,IAAI/I,EACtBF,EACCA,EAAMJ,aAEHtP,EADA,IAAIsW,EAAiB5G,EAAOlQ,KAAK2O,QAAQxD,UAE7CnL,KAAKsQ,aAGPtQ,KAAKgZ,WhBJiBA,EgBIKhZ,KAAK2O,QAAQ2G,OhBHrC,eAAe8D,KAAKJ,GAIlBA,EAHE,WAAWA,KgBGlBhZ,KAAKqZ,YhBMqB,EAC5BC,EACAN,IAEIM,EACKA,EAAOvJ,WAAW,YAAcuJ,EAAS,WAAWA,KAGtD,GAAGN,KgBdWO,CAAevZ,KAAK2O,QAAQ2K,OAAQtZ,KAAKgZ,WAIxC,oBAAX/U,QACPA,OAAOuV,QACPxZ,KAAK2O,QAAQsK,kBnBzMkB,WmB0M/BrB,IAEI5X,KAAK2O,QAAQ8K,UACfzZ,KAAKyN,OAAS,IAAI+L,OAAOxZ,KAAK2O,QAAQ8K,WAEtCzZ,KAAKyN,OAAS,IAAIiM,EAGvB,CAEOC,KAAKpE,GACX,MAAM7G,EAAcuG,mBAClBhK,KAAKvE,KAAKC,UAAU3G,KAAK2O,QAAQD,aAAe3F,KAElD,MAAO,GAAG/I,KAAKgZ,YAAYzD,iBAAoB7G,GAChD,CAEOkL,cAAcC,GACpB,OAAO7Z,KAAK2Z,KAAK,cAAczO,EAAkB2O,KAClD,CAEOpN,qBACNkE,EACA+C,EACAW,GAEA,MAAM7O,QAAYxF,KAAKsQ,cAEvB,OAAOwJ,EAAc,CACnB1G,IAAKpT,KAAKqZ,YACV9F,IAAKvT,KAAK2O,QAAQxD,SAClBwF,WACA+C,QACAW,eACAP,OAAQ9T,KAAK2O,QAAQmF,OACrBH,ShBzBsB9T,EgByBDG,KAAK2O,QAAQoJ,oBAAoBpE,QhBxBrC,iBAAV9T,EACFA,EAEFuU,SAASvU,EAAO,UAAOW,GgBsB1BgF,QhB1BqB,IAAC3F,CgB4BzB,CAEOka,gBAAgB1F,GAClBA,EACFrU,KAAKwY,cAAcrG,KAAKnS,KAAK0Y,kBAAmBrE,EAAc,CAC5DjC,gBAAiBpS,KAAK6Y,uBACtB7G,aAAchS,KAAK2O,QAAQqD,eAG7BhS,KAAKwY,cAAc3I,OAAO7P,KAAK0Y,kBAAmB,CAChD1G,aAAchS,KAAK2O,QAAQqD,cAGhC,CAEOvF,2BACNsL,EACA8B,EACAG,GAUA,MAAMrQ,EAAQqB,EAAON,KACfgJ,EAAQ1I,EAAON,KACfuP,EAAgBvP,IAEhBwP,EhBzG8B,CAACpO,IACvC,MAAMqO,EAAgB,IAAIpP,WAAWe,GACrC,MArBmB,CAACA,IACpB,MAAMsO,EAAwC,CAAE,IAAK,IAAK,IAAK,IAAK,IAAK,IACzE,OAAOtO,EAAMS,QAAQ,UAAW8N,GAAcD,EAASC,IAAG,EAmBnDC,CACLrW,OAAOgH,KAAKsP,OAAOC,gBAAgB5P,MAAMC,KAAKsP,KAC/C,EgBqGwBM,MhBrILhO,OAAO5N,IAC3B,MAAM6b,EAAgBlQ,IAAY0N,OAAOyC,OACvC,CAAE3R,KAAM,YACR,IAAI4R,aAAc5P,OAAOnM,IAG3B,aAAa6b,CAAQ,EgB8HgBG,CAAOZ,IAGpC5O,ED/PwB,EAChCyP,EAGAzQ,EACA0N,EACApO,EACA+J,EACAwG,EACAa,EACAC,IAEA/b,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,CACEP,UAAW0P,EAAc3P,UACtB2P,EAAc/C,qBACdA,GACH,CAAA1N,MAAO6E,EAAgB7E,EAAO0N,EAAoB1N,OAClD4Q,cAAe,OACfD,cAAeA,GAAiB,QAChCrR,QACA+J,QACAqH,aACEA,GAAgBD,EAAc/C,oBAAoBgD,aACpDb,iBACAgB,sBAAuB,SCuORC,CACbnb,KAAK2O,QACL3O,KAAKqK,MACL0N,EACApO,EACA+J,EACAwG,EACAnC,EAAoBgD,cAClB/a,KAAK2O,QAAQoJ,oBAAoBgD,cACjCf,EACFH,eAAAA,EAAkBmB,eAGdpM,EAAM5O,KAAK4Z,cAAcvO,GAE/B,MAAO,CACLqI,QACAuG,gBACA5P,MAAOgB,EAAOhB,MACdD,SAAUiB,EAAOjB,UAAY,UAC7B2Q,aAAc1P,EAAO0P,aACrBpR,QACAiF,MAEH,CAyBMnC,qBACLkC,EACAyM,SAKA,GAHAzM,EAAUA,GAAW,KACrByM,EAASA,GAAU,IAEPrR,QACVqR,EAAOrR,MhBpRY,CAAC6E,IACxB,MAEMyM,EAAOpX,OAAOqX,SAAWrX,OAAOsX,WAFxB,KAE8C,EACtDC,EAAMvX,OAAOwX,SAAWxX,OAAOyX,YAFtB,KAE8C,EAE7D,OAAOzX,OAAO0X,KACZ/M,EACA,wBACA,QAAQyM,SAAYG,2DACrB,EgB0QkBI,CAAU,KAEpBR,EAAOrR,OACV,MAAM,IAAIjG,MACR,2EAKN,MAAMuH,QAAerL,KAAK6b,qBACxBlN,EAAQoJ,qBAAuB,CAAA,EAC/B,CAAEiD,cAAe,eACjB/W,OAAOqS,SAASwF,QAGlBV,EAAOrR,MAAMuM,SAASyF,KAAO1Q,EAAOuD,IAEpC,MAAMoN,OhBxRc,CAACZ,GAChB,IAAIta,SAA8B,CAACC,EAASC,KACjD,IAAIib,EAGJ,MAAMC,EAAaC,aAAY,KACzBf,EAAOrR,OAASqR,EAAOrR,MAAMqS,SAC/BC,cAAcH,GACdtU,aAAauF,GACblJ,OAAOyD,oBAAoB,UAAWuU,GAAoB,GAC1Djb,EAAO,IAAIgJ,EAAoBoR,EAAOrR,QACvC,GACA,KAEGoD,EAAY/L,YAAW,KAC3Bib,cAAcH,GACdlb,EAAO,IAAI8I,EAAkBsR,EAAOrR,QACpC9F,OAAOyD,oBAAoB,UAAWuU,GAAoB,EAAM,GACK,KAAnEb,EAAOtS,kBH9GqC,KGgHhDmT,EAAqB,SAAUnd,GAC7B,GAAKA,EAAEqP,MAAwB,2BAAhBrP,EAAEqP,KAAKmO,KAAtB,CASA,GALA1U,aAAauF,GACbkP,cAAcH,GACdjY,OAAOyD,oBAAoB,UAAWuU,GAAoB,GAC1Db,EAAOrR,MAAMqE,QAETtP,EAAEqP,KAAKvB,SAASvD,MAClB,OAAOrI,EAAOmI,EAAaoT,YAAYzd,EAAEqP,KAAKvB,WAGhD7L,EAAQjC,EAAEqP,KAAKvB,SAXd,CAYH,EAEA3I,OAAO8D,iBAAiB,UAAWkU,EAAmB,IgBmP7BO,CAAQvd,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAC5ByP,GAAM,CACTtS,iBACEsS,EAAOtS,kBACP9I,KAAK2O,QAAQ8N,2BnBxX+B,MmB4XhD,GAAIpR,EAAO1B,QAAUqS,EAAWrS,MAC9B,MAAM,IAAIR,EAAa,iBAAkB,iBAG3C,MAAMkL,WACJxQ,EAAA8K,EAAQoJ,0CAAqB1D,eAC7BrU,KAAK2O,QAAQoJ,oBAAoB1D,mBAE7BrU,KAAK0c,cACT,CACEtS,SAAUiB,EAAOjB,SACjBC,MAAOgB,EAAOhB,MACd4P,cAAe5O,EAAO4O,cACtB0C,WAAY,qBACZC,KAAMZ,EAAWY,KACjB7B,aAAc1P,EAAO0P,cAEvB,CACE8B,QAASxR,EAAOqI,MAChBW,gBAGL,CAYM5H,sBACL,MAAMyD,QAAclQ,KAAK8c,uBAEzB,OAA4B,QAArBjZ,EAAAqM,aAAK,EAALA,EAAOM,oBAAc,IAAA3M,OAAA,EAAAA,EAAAoP,IAC7B,CASMxG,+BACL,MAAMyD,QAAclQ,KAAK8c,uBAEzB,OAA4B,QAArBjZ,EAAAqM,aAAK,EAALA,EAAOM,oBAAc,IAAA3M,OAAA,EAAAA,EAAAkP,MAC7B,CAaMtG,wBACLkC,EAA2C,UAE3C,MAAMoO,EACJzF,EAA2B3I,IADvB4I,QAAEA,EAAOyF,SAAEA,EAAQpT,SAAEA,GACUmT,EADGE,EAAlCre,EAAAme,EAAA,CAAA,UAAA,WAAA,aAGA1I,WACJxQ,EAAAoZ,EAAWlF,0CAAqB1D,eAChCrU,KAAK2O,QAAQoJ,oBAAoB1D,aAE7B6I,QAAgCld,KAAK6b,qBACzCoB,EAAWlF,qBAAuB,KAD9BnJ,IAAEA,GAAGsO,EAAKhL,EAAWtT,EAAAse,EAArB,CAAuB,QAI7Bld,KAAKkZ,mBAAmBjH,OAAMhT,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EACzBuG,GACH,CAAAtI,aACIyK,GAAgB,CAAEA,kBAGxB,MAAM8I,EAAkBH,EAAW,GAAGpO,KAAOoO,IAAapO,EAEtD2I,QACIA,EAAQ4F,GAEdlZ,OAAOqS,SAAS3K,OAAOwR,EAE1B,CAQM1Q,6BACLmC,EAAc3K,OAAOqS,SAASyF,MAE9B,MAAMqB,EAAuBxO,EAAI3C,MAAM,KAAK5D,MAAM,GAElD,GAAoC,IAAhC+U,EAAqB5d,OACvB,MAAM,IAAIsE,MAAM,oDAGlB,MAAM6F,MAAEA,EAAKiT,KAAEA,EAAIvT,MAAEA,EAAKC,kBAAEA,GhBheS,CACvC+T,IAEIA,EAAYhe,QAAQ,MAAQ,IAC9Bge,EAAcA,EAAYC,UAAU,EAAGD,EAAYhe,QAAQ,OAG7D,MAAMke,EAAe,IAAIjS,gBAAgB+R,GAEzC,MAAO,CACL1T,MAAO4T,EAAahd,IAAI,SACxBqc,KAAMW,EAAahd,IAAI,cAAWC,EAClC6I,MAAOkU,EAAahd,IAAI,eAAYC,EACpC8I,kBAAmBiU,EAAahd,IAAI,2BAAwBC,EAC7D,EgBkdmDgd,CAChDJ,EAAqB/Q,KAAK,KAGtB6F,EAAclS,KAAKkZ,mBAAmB3Y,MAE5C,IAAK2R,EACH,MAAM,IAAI/I,EAAa,sBAAuB,iBAKhD,GAFAnJ,KAAKkZ,mBAAmBrJ,SAEpBxG,EACF,MAAM,IAAIK,EACRL,EACAC,GAAqBD,EACrBM,EACAuI,EAAYtI,UAKhB,IACGsI,EAAY+H,eACZ/H,EAAYvI,OAASuI,EAAYvI,QAAUA,EAE5C,MAAM,IAAIR,EAAa,iBAAkB,iBAG3C,MAAMkL,EAAenC,EAAYmC,aAC3BwI,EAAU3K,EAAYwB,MACtBqH,EAAe7I,EAAY6I,aAcjC,aAZM/a,KAAK0c,cAAazd,OAAA0M,OAAA,CAEpBvB,SAAU8H,EAAY9H,SACtBC,MAAO6H,EAAY7H,MACnB4P,cAAe/H,EAAY+H,cAC3B0C,WAAY,qBACZC,KAAMA,GACF7B,EAAe,CAAEA,gBAAiB,CAAE,GAE1C,CAAE8B,UAASxI,iBAGN,CACLzK,SAAUsI,EAAYtI,SAEzB,CA2BM6C,mBAAmBkC,GACxB,IAAK3O,KAAKwY,cAAcjY,IAAIP,KAAK2Y,2BAA4B,CAC3D,IAAK3Y,KAAKwY,cAAcjY,IDpiBkB,0BCqiBxC,OAGAP,KAAKwY,cAAcrG,KAAKnS,KAAK2Y,2BAA2B,EAAM,CAC5DvG,gBAAiBpS,KAAK6Y,uBACtB7G,aAAchS,KAAK2O,QAAQqD,eAG7BhS,KAAKwY,cAAc3I,OD7iBqB,yBC+iB3C,CAED,UACQ7P,KAAKyd,iBAAiB9O,EAChB,CAAZ,MAAOhM,GAAK,CACf,CAwDM8J,uBACLkC,EAAmC,UAEnC,MAAM+O,EAGJze,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAAgS,UAAW,MACRhP,GAAO,CACVoJ,oBAAmB9Y,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,GACd3L,KAAK2O,QAAQoJ,qBACbpJ,EAAQoJ,qBAAmB,CAC9B1N,MAAO6E,EAAgBlP,KAAKqK,MAAkC,QAA3BxG,EAAA8K,EAAQoJ,2BAAmB,IAAAlU,OAAA,EAAAA,EAAEwG,WAI9DnI,OHhpBmB,EAC3B0b,EACAxd,KAEA,IAAIyd,EAA6BhH,EAAiBzW,GAQlD,OAPKyd,IACHA,EAAUD,IAAKrQ,SAAQ,YACdsJ,EAAiBzW,GACxByd,EAAU,IAAI,IAEhBhH,EAAiBzW,GAAOyd,GAEnBA,CAAO,EGooBSC,EACnB,IAAM9d,KAAK+d,kBAAkBL,IAC7B,GAAG1d,KAAK2O,QAAQxD,aAAauS,EAAa3F,oBAAoB3N,aAAasT,EAAa3F,oBAAoB1N,SAG9G,OAAOsE,EAAQqP,iBAAmB9b,EAASA,eAAAA,EAAQ+b,YACpD,CAEOxR,wBACNkC,GAIA,MAAMgP,UAAEA,GAAkChP,EAApBuP,EAAetf,EAAK+P,EAApC,CAAiC,cAIvC,GAAkB,QAAdgP,EAAqB,CACvB,MAAMhO,QAAc3P,KAAKme,mBAAmB,CAC1C9T,MAAO6T,EAAgBnG,oBAAoB1N,MAC3CD,SAAU8T,EAAgBnG,oBAAoB3N,UAAY,UAC1De,SAAUnL,KAAK2O,QAAQxD,WAGzB,GAAIwE,EACF,OAAOA,CAEV,CAED,GAAkB,eAAdgO,EAAJ,CAIA,SHlqBwBlR,OAC1BmR,EACAQ,EAAqB,KAErB,IAAK,IAAI7e,EAAI,EAAGA,EAAI6e,EAAoB7e,IACtC,SAAUqe,IACR,OAAO,EAIX,OAAO,CAAK,EGypBFS,EACJ,IAAMxd,GAAK6E,YDzqBwB,8BCyqBiB,MACpD,IAsCF,MAAM,IAAImE,EAnCV,IAKE,GAJA5F,OAAO8D,iBAAiB,WAAY/H,KAAKiY,wBAIvB,QAAd0F,EAAqB,CACvB,MAAMhO,QAAc3P,KAAKme,mBAAmB,CAC1C9T,MAAO6T,EAAgBnG,oBAAoB1N,MAC3CD,SAAU8T,EAAgBnG,oBAAoB3N,UAAY,UAC1De,SAAUnL,KAAK2O,QAAQxD,WAGzB,GAAIwE,EACF,OAAOA,CAEV,CAED,MAAM2O,EAAate,KAAK2O,QAAQsK,uBACtBjZ,KAAKue,2BAA2BL,SAChCle,KAAKwe,oBAAoBN,IAE7BvN,SAAEA,EAAQsN,aAAEA,EAAYQ,gBAAEA,EAAelN,WAAEA,GAC/C+M,EAEF,OAAArf,OAAA0M,OAAA1M,OAAA0M,OAAA,CACEgF,WACAsN,gBACIQ,EAAkB,CAAEpU,MAAOoU,GAAoB,MAAK,CACxDlN,cAKH,CAHS,cACF1Q,GAAK+E,YD5sBwB,+BC6sBnC3B,OAAOyD,oBAAoB,WAAY1H,KAAKiY,uBAC7C,CAzCF,CA6CF,CAcMxL,wBACLkC,EAAoC,GACpCyM,EAA6B,CAAA,SAE7B,MAAMsC,EAAYze,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EACbgD,GAAO,CACVoJ,oBACK9Y,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAA3L,KAAK2O,QAAQoJ,qBACbpJ,EAAQoJ,qBAAmB,CAC9B1N,MAAO6E,EAAgBlP,KAAKqK,MAAkC,QAA3BxG,EAAA8K,EAAQoJ,2BAAmB,IAAAlU,OAAA,EAAAA,EAAEwG,WAIpE+Q,EACKnc,OAAA0M,OAAA1M,OAAA0M,OAAA,GAAA9C,GACAuS,SAGCpb,KAAK0e,eAAehB,EAActC,GAUxC,aARoBpb,KAAKmZ,aAAa5Y,IACpC,IAAIgP,EAAS,CACXlF,MAAOqT,EAAa3F,oBAAoB1N,MACxCD,SAAUsT,EAAa3F,oBAAoB3N,UAAY,UACvDe,SAAUnL,KAAK2O,QAAQxD,aAIb8S,YACf,CAWMxR,wBAEL,cADmBzM,KAAK2e,SAEzB,CAUOC,gBAAgBjQ,GACG,OAArBA,EAAQxD,SACVwD,EAAQxD,SAAWwD,EAAQxD,UAAYnL,KAAK2O,QAAQxD,gBAE7CwD,EAAQxD,SAGjB,MAAMtH,EAAkC8K,EAAQkQ,cAAgB,CAAA,GAA1DC,UAAEA,KAAcC,EAAhBngB,EAAAiF,EAAA,CAAA,cACAmb,EAAiBF,EAAY,aAAe,GAQlD,OAPY9e,KAAK2Z,KACf,cAAczO,EAAiBjM,OAAA0M,OAAA,CAC7BR,SAAUwD,EAAQxD,UACf4T,OAIMC,CACd,CAeMvS,aAAakC,EAAyB,IAC3C,MAAM9K,EAAgCyT,EAA2B3I,IAA3D4I,QAAEA,GAAO1T,EAAKkb,EAAdngB,EAAAiF,EAAA,CAAA,YAEmB,OAArB8K,EAAQxD,eACJnL,KAAKmZ,aAAanV,cAElBhE,KAAKmZ,aAAanV,MAAM2K,EAAQxD,UAAYnL,KAAK2O,QAAQxD,UAGjEnL,KAAKwY,cAAc3I,OAAO7P,KAAK0Y,kBAAmB,CAChD1G,aAAchS,KAAK2O,QAAQqD,eAE7BhS,KAAKwY,cAAc3I,OAAO7P,KAAK2Y,0BAA2B,CACxD3G,aAAchS,KAAK2O,QAAQqD,eAE7BhS,KAAK6X,UAAUhI,OX70BsB,YW+0BrC,MAAMjB,EAAM5O,KAAK4e,gBAAgBG,GAE7BxH,QACIA,EAAQ3I,IACO,IAAZ2I,GACTtT,OAAOqS,SAAS3K,OAAOiD,EAE1B,CAEOnC,0BACNkC,GAIA,MAAMtD,EACDpM,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAAgD,EAAQoJ,sBACXkH,OAAQ,SAGJC,EAAUlf,KAAKwY,cAAcjY,IAAYP,KAAK0Y,mBAEhDwG,IAAY7T,EAAOgJ,eACrBhJ,EAAOgJ,aAAe6K,GAGxB,MAAMtQ,IACJA,EACAjF,MAAOwV,EACPzL,MAAOmJ,EAAO5C,cACdA,EAAac,aACbA,EAAY1Q,MACZA,EAAKD,SACLA,SACQpK,KAAK6b,qBACbxQ,EACA,CAAE2P,cAAe,eACjB/W,OAAOqS,SAASwF,QAGlB,IAIE,GAAK7X,OAAemb,oBAClB,MAAM,IAAIjW,EACR,iBACA,qIAIJ,MAAMkW,EACJ1Q,EAAQ7F,kBAAoB9I,KAAK2O,QAAQ8N,0BAErCT,OhBx2Ba,EACvBsD,EACAC,EACAzW,EH5BkD,KG8B3C,IAAIhI,SAA8B,CAAC0e,EAAKC,KAC7C,MAAMC,EAASzb,OAAOgS,SAAS0J,cAAc,UAE7CD,EAAOE,aAAa,QAAS,KAC7BF,EAAOE,aAAa,SAAU,KAC9BF,EAAOG,MAAMC,QAAU,OAEvB,MAAMC,EAAe,KACf9b,OAAOgS,SAAS1T,KAAKyd,SAASN,KAChCzb,OAAOgS,SAAS1T,KAAK0d,YAAYP,GACjCzb,OAAOyD,oBAAoB,UAAWwY,GAAoB,GAC3D,EAGH,IAAIA,EAEJ,MAAMC,EAAsB/e,YAAW,KACrCqe,EAAI,IAAI5V,GACRkW,GAAc,GACM,IAAnBjX,GAEHoX,EAAqB,SAAUphB,GAC7B,GAAIA,EAAEgd,QAAUyD,EAAa,OAC7B,IAAKzgB,EAAEqP,MAAwB,2BAAhBrP,EAAEqP,KAAKmO,KAAmC,OAEzD,MAAM8D,EAActhB,EAAEuhB,OAElBD,GACDA,EAAoBhS,QAGvBtP,EAAEqP,KAAKvB,SAASvD,MACZoW,EAAItW,EAAaoT,YAAYzd,EAAEqP,KAAKvB,WACpC4S,EAAI1gB,EAAEqP,KAAKvB,UAEfhF,aAAauY,GACblc,OAAOyD,oBAAoB,UAAWwY,GAAoB,GAI1D9e,WAAW2e,EAAcO,IAC3B,EAEArc,OAAO8D,iBAAiB,UAAWmY,GAAoB,GACvDjc,OAAOgS,SAAS1T,KAAKge,YAAYb,GACjCA,EAAOE,aAAa,MAAON,EAAa,IgBszBbkB,CAAU5R,EAAK5O,KAAKgZ,UAAWqG,GAExD,GAAIF,IAAYnD,EAAWrS,MACzB,MAAM,IAAIR,EAAa,iBAAkB,iBAG3C,MAAMsX,QAAoBzgB,KAAK0c,cAExBzd,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAAgD,EAAQoJ,sBACXkC,gBACA2C,KAAMZ,EAAWY,KACjBD,WAAY,qBACZ5B,eACA7U,QAASyI,EAAQoJ,oBAAoB7R,SAAWlG,KAAKsY,gBAEvD,CACEuE,UACAxI,aAAchJ,EAAOgJ,eAIzB,OAAApV,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EACK8U,GAAW,CACdpW,MAAOA,EACPoU,gBAAiBgC,EAAYpW,MAC7BD,SAAUA,GASb,CAPC,MAAOtL,GAMP,KALgB,mBAAZA,EAAEuK,OACJrJ,KAAK0gB,OAAO,CACVnJ,SAAS,IAGPzY,CACP,CACF,CAEO2N,iCACNkC,GAIA,MAAMuB,QAAclQ,KAAKmZ,aAAa5Y,IACpC,IAAIgP,EAAS,CACXlF,MAAOsE,EAAQoJ,oBAAoB1N,MACnCD,SAAUuE,EAAQoJ,oBAAoB3N,UAAY,UAClDe,SAAUnL,KAAK2O,QAAQxD,YAQ3B,KAAM+E,GAAUA,EAAMkB,eAAmBpR,KAAKyN,QAAQ,CACpD,GAAIzN,KAAK2O,QAAQqJ,yBACf,aAAahY,KAAKwe,oBAAoB7P,GAGxC,MAAM,IAAIxE,EACRwE,EAAQoJ,oBAAoB3N,UAAY,UACxCuE,EAAQoJ,oBAAoB1N,MAE/B,CAED,MAAM0Q,EACJpM,EAAQoJ,oBAAoBgD,cAC5B/a,KAAK2O,QAAQoJ,oBAAoBgD,cACjC9W,OAAOqS,SAASwF,OAEZ5V,EACgC,iBAA7ByI,EAAQ7F,iBACgB,IAA3B6F,EAAQ7F,iBACR,KAEN,IACE,MAAM2X,QAAoBzgB,KAAK0c,cAAazd,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EACvCgD,EAAQoJ,qBAAmB,CAC9B4E,WAAY,gBACZvL,cAAelB,GAASA,EAAMkB,cAC9B2J,iBACI7U,GAAW,CAAEA,aAGnB,OACKjH,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAA8U,GACH,CAAApW,MAAOsE,EAAQoJ,oBAAoB1N,MACnCoU,gBAAiBgC,EAAYpW,MAC7BD,SAAUuE,EAAQoJ,oBAAoB3N,UAAY,WAiBrD,CAfC,MAAOtL,GACP,IAGGA,EAAE6O,QAAQtO,QnBj8BgC,0BmBi8BgB,GAGxDP,EAAE6O,SACD7O,EAAE6O,QAAQtO,QnBh8B6B,0BmBg8BmB,IAC9DW,KAAK2O,QAAQqJ,yBAEb,aAAahY,KAAKwe,oBAAoB7P,GAGxC,MAAM7P,CACP,CACF,CAEO2N,wBACNkD,GAEA,MAAMgB,SAAEA,EAAQH,aAAEA,GAAyCb,EAAxBgR,EAAwB/hB,EAAA+Q,EAArD,CAAA,WAAA,iBAEN3P,KAAK6X,UAAUpX,IXr/BsB,WWq/BS,CAC5CkQ,WACAH,uBAGIxQ,KAAKmZ,aAAayH,WACtB5gB,KAAK2O,QAAQxD,SACbwE,EAAMgB,SACNhB,EAAMa,oBAGFxQ,KAAKmZ,aAAa1Y,IAAIkgB,EAC7B,CAEOlU,6BACN,MAAMrC,EAAWpK,KAAK2O,QAAQoJ,oBAAoB3N,UAAY,UAExD8F,QAAclQ,KAAKmZ,aAAa0H,WACpC,IAAItR,EAAS,CACXpE,SAAUnL,KAAK2O,QAAQxD,SACvBf,WACAC,MAAOrK,KAAKqK,SAIVyW,EAAe9gB,KAAK6X,UAAUtX,IX9gCC,YWohCrC,OAAI2P,GAASA,EAAMS,YAAamQ,aAAA,EAAAA,EAAcnQ,UACrCmQ,GAGT9gB,KAAK6X,UAAUpX,IXxhCsB,WWwhCSyP,GACvCA,EACR,CAEOzD,0BAAyBpC,MAC/BA,EAAKD,SACLA,EAAQe,SACRA,IAMA,MAAMwE,QAAc3P,KAAKmZ,aAAa5Y,IACpC,IAAIgP,EAAS,CACXlF,QACAD,WACAe,aAEF,IAGF,GAAIwE,GAASA,EAAMsO,aAAc,CAC/B,MAAMA,aAAEA,EAAYQ,gBAAEA,EAAelN,WAAEA,GAAe5B,EAChDO,QAAclQ,KAAK8c,uBACzB,OACE5M,GACEjR,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAAgF,SAAUT,EAAMS,SAChBsN,gBACIQ,EAAkB,CAAEpU,MAAOoU,GAAoB,MAAK,CACxDlN,cAGL,CACF,CAcO9E,oBACNkC,EAIAoS,GAEA,MAAMlE,QAAEA,EAAOxI,aAAEA,GAAiB0M,GAAwB,CAAA,EACpDzC,QAAmB9P,iBAErBC,QAASzO,KAAKgZ,UACd5N,UAAWpL,KAAK2O,QAAQxD,SACxBuD,YAAa1O,KAAK2O,QAAQD,YAC1BhB,YAAa1N,KAAK2O,QAAQjB,YAC1BxH,QAASlG,KAAKsY,eACX3J,GAEL3O,KAAKyN,QAGD+C,QAAqBxQ,KAAKghB,eAC9B1C,EAAW3N,SACXkM,EACAxI,GAmBF,aAhBMrU,KAAKihB,kBAAiBhiB,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EACvB2S,GACH,CAAA9N,eACAnG,MAAOsE,EAAQtE,MACfD,SAAUuE,EAAQvE,UAAY,YAC1BkU,EAAWjU,MAAQ,CAAEoU,gBAAiBH,EAAWjU,OAAU,MAC/D,CAAAe,UAAWpL,KAAK2O,QAAQxD,YAG1BnL,KAAKwY,cAAcrG,KAAKnS,KAAK2Y,2BAA2B,EAAM,CAC5DvG,gBAAiBpS,KAAK6Y,uBACtB7G,aAAchS,KAAK2O,QAAQqD,eAG7BhS,KAAK+Z,gBAAgB1F,GAAgB7D,EAAauC,OAAOyB,QAE7CvV,OAAA0M,OAAA1M,OAAA0M,OAAA,CAAA,EAAA2S,GAAY,CAAA9N,gBACzB,CAoDD/D,oBACEkC,GAEA,OAAO3O,KAAK0c,cAAc,CACxBC,WAAY,kDACZuE,cAAevS,EAAQuS,cACvBC,mBAAoBxS,EAAQwS,mBAC5B9W,MAAO6E,EAAgBP,EAAQtE,MAAOrK,KAAKqK,OAC3CD,SAAUpK,KAAK2O,QAAQoJ,oBAAoB3N,UAE9C,QChlBUgX,ICplBN3U,eAAe4U,GAAkB1S,GACtC,MAAM2S,EAAQ,IAAI3J,GAAYhJ,GAE9B,aADM2S,EAAMC,eACLD,CACT"}