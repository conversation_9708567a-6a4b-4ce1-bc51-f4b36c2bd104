import{j as e,B as s,o as t,p as n,b as i,I as a,H as o,q as r,d as l,f as c,k as d,G as x,g as h,h as j,L as u,U as m,V as g,W as p,X as y,F as b,v,w as f,M as C,s as D,Y as T,Z as A,_ as B,e as F,m as N,n as S}from"./mui-vendor-b761306f.js";import{u as W,r as w}from"./react-vendor-2f216e43.js";import{A as M,R as k}from"./index-bd55ea72.js";const z=[{id:"1",category:"Alimentation",allocated:400,spent:285,period:"monthly",color:"#4caf50",icon:"🍽️"},{id:"2",category:"Transport",allocated:200,spent:150,period:"monthly",color:"#2196f3",icon:"🚗"},{id:"3",category:"Loisirs",allocated:150,spent:180,period:"monthly",color:"#ff9800",icon:"🎮"},{id:"4",category:"Santé",allocated:100,spent:45,period:"monthly",color:"#e91e63",icon:"🏥"},{id:"5",category:"Éducation",allocated:300,spent:120,period:"monthly",color:"#9c27b0",icon:"📚"},{id:"6",category:"Vêtements",allocated:120,spent:95,period:"monthly",color:"#607d8b",icon:"👕"}],I=["Alimentation","Transport","Loisirs","Santé","Éducation","Vêtements","Utilities","Restaurants","Shopping","Voyage","Assurance","Autre"],R=({budget:t,onEdit:n,onDelete:o})=>{const r=t.spent/t.allocated*100,l=t.allocated-t.spent,c=r>100;return e.jsx(h,{sx:{height:"100%"},children:e.jsxs(j,{children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",mb:2},children:[e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[e.jsx(i,{variant:"h4",children:t.icon}),e.jsxs(s,{children:[e.jsx(i,{variant:"h6",fontWeight:"bold",children:t.category}),e.jsx(i,{variant:"caption",color:"text.secondary",children:"Budget mensuel"})]})]}),e.jsxs(s,{children:[e.jsx(a,{size:"small",onClick:()=>n(t),children:e.jsx(A,{})}),e.jsx(a,{size:"small",onClick:()=>o(t.id),color:"error",children:e.jsx(B,{})})]})]}),e.jsxs(s,{sx:{mb:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsxs(i,{variant:"body2",color:"text.secondary",children:["Dépensé: ",t.spent.toFixed(2)," TND"]}),e.jsxs(i,{variant:"body2",color:"text.secondary",children:["Budget: ",t.allocated.toFixed(2)," TND"]})]}),e.jsx(u,{variant:"determinate",value:Math.min(r,100),sx:{height:8,borderRadius:4,backgroundColor:"grey.200","& .MuiLinearProgress-bar":{borderRadius:4,backgroundColor:c?"error.main":t.color}}}),e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mt:1},children:[e.jsxs(i,{variant:"caption",color:"text.secondary",children:[r.toFixed(1),"% utilisé"]}),e.jsx(F,{size:"small",label:l>=0?`${l.toFixed(2)} TND restants`:`Dépassé de ${Math.abs(l).toFixed(2)} TND`,color:l>=0?"success":"error",variant:"outlined"})]})]}),e.jsxs(s,{sx:{display:"flex",alignItems:"center",gap:1},children:[c?e.jsx(N,{color:"error",fontSize:"small"}):e.jsx(S,{color:"success",fontSize:"small"}),e.jsx(i,{variant:"caption",color:c?"error.main":"success.main",children:c?"Budget dépassé":"Dans les limites"})]})]})})},V=()=>{const A=W(),{logout:B}=w.useContext(M),[F,N]=w.useState(z),[S,V]=w.useState(!1),[G,L]=w.useState(null),[E,H]=w.useState({category:"",allocated:"",icon:"💰"}),O=e=>{L(e),H({category:e.category,allocated:e.allocated.toString(),icon:e.icon}),V(!0)},U=e=>{N(F.filter((s=>s.id!==e)))},$=F.reduce(((e,s)=>e+s.allocated),0),q=F.reduce(((e,s)=>e+s.spent),0),P=F.filter((e=>e.spent>e.allocated)).length;return e.jsxs(s,{sx:{flexGrow:1},children:[e.jsx(t,{position:"static",elevation:1,children:e.jsxs(n,{children:[e.jsx(i,{variant:"h6",component:"div",sx:{flexGrow:1,display:"flex",alignItems:"center"},children:"🇹🇳 Nouri - Budgets"}),e.jsx(a,{color:"inherit",onClick:()=>A(k.DASHBOARD),children:e.jsx(o,{})}),e.jsx(a,{color:"inherit",onClick:()=>{B(),A(k.HOME)},children:e.jsx(r,{})})]})}),e.jsxs(l,{maxWidth:"xl",sx:{py:4},children:[e.jsx(s,{sx:{mb:4},children:e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[e.jsxs(s,{children:[e.jsx(i,{variant:"h4",component:"h1",gutterBottom:!0,fontWeight:"bold",children:"Mes Budgets"}),e.jsx(i,{variant:"body1",color:"text.secondary",children:"Gérez vos budgets mensuels et suivez vos dépenses par catégorie"})]}),e.jsx(c,{variant:"contained",startIcon:e.jsx(d,{}),onClick:()=>{L(null),H({category:"",allocated:"",icon:"💰"}),V(!0)},size:"large",children:"Nouveau Budget"})]})}),e.jsxs(x,{container:!0,spacing:3,sx:{mb:4},children:[e.jsx(x,{item:!0,xs:12,md:4,children:e.jsx(h,{children:e.jsxs(j,{sx:{textAlign:"center"},children:[e.jsxs(i,{variant:"h4",color:"primary.main",fontWeight:"bold",children:[$.toFixed(2)," TND"]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Budget Total Mensuel"})]})})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsx(h,{children:e.jsxs(j,{sx:{textAlign:"center"},children:[e.jsxs(i,{variant:"h4",color:"warning.main",fontWeight:"bold",children:[q.toFixed(2)," TND"]}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Total Dépensé"})]})})}),e.jsx(x,{item:!0,xs:12,md:4,children:e.jsx(h,{children:e.jsxs(j,{sx:{textAlign:"center"},children:[e.jsx(i,{variant:"h4",color:P>0?"error.main":"success.main",fontWeight:"bold",children:P}),e.jsx(i,{variant:"body2",color:"text.secondary",children:"Budgets Dépassés"})]})})})]}),e.jsx(h,{sx:{mb:4},children:e.jsxs(j,{children:[e.jsx(i,{variant:"h6",gutterBottom:!0,children:"Vue d'ensemble du budget mensuel"}),e.jsxs(s,{sx:{mb:2},children:[e.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",mb:1},children:[e.jsxs(i,{variant:"body2",children:[q.toFixed(2)," TND / ",$.toFixed(2)," TND"]}),e.jsxs(i,{variant:"body2",children:[(q/$*100).toFixed(1),"%"]})]}),e.jsx(u,{variant:"determinate",value:Math.min(q/$*100,100),sx:{height:10,borderRadius:5},color:q>$?"error":"primary"})]}),P>0&&e.jsxs(m,{severity:"warning",sx:{mt:2},children:["Attention: ",P," budget(s) dépassé(s) ce mois-ci"]})]})}),e.jsx(x,{container:!0,spacing:3,children:F.map((s=>e.jsx(x,{item:!0,xs:12,sm:6,md:4,children:e.jsx(R,{budget:s,onEdit:O,onDelete:U})},s.id)))}),e.jsxs(g,{open:S,onClose:()=>V(!1),maxWidth:"sm",fullWidth:!0,children:[e.jsx(p,{children:G?"Modifier le budget":"Nouveau budget"}),e.jsx(y,{children:e.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:2,mt:1},children:[e.jsxs(b,{fullWidth:!0,children:[e.jsx(v,{children:"Catégorie"}),e.jsx(f,{value:E.category,label:"Catégorie",onChange:e=>H({...E,category:e.target.value}),children:I.map((s=>e.jsx(C,{value:s,children:s},s)))})]}),e.jsx(D,{label:"Montant alloué (TND)",type:"number",value:E.allocated,onChange:e=>H({...E,allocated:e.target.value}),fullWidth:!0}),e.jsx(D,{label:"Icône",value:E.icon,onChange:e=>H({...E,icon:e.target.value}),fullWidth:!0,helperText:"Choisissez un emoji pour représenter cette catégorie"})]})}),e.jsxs(T,{children:[e.jsx(c,{onClick:()=>V(!1),children:"Annuler"}),e.jsx(c,{onClick:()=>{if(!E.category||!E.allocated)return;const e={id:(null==G?void 0:G.id)||Date.now().toString(),category:E.category,allocated:parseFloat(E.allocated),spent:(null==G?void 0:G.spent)||0,period:"monthly",color:(null==G?void 0:G.color)||"#4caf50",icon:E.icon};N(G?F.map((s=>s.id===G.id?e:s)):[...F,e]),V(!1)},variant:"contained",children:G?"Modifier":"Créer"})]})]})]})]})};export{V as BudgetsPage,V as default};
