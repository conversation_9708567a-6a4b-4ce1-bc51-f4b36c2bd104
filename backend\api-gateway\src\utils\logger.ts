/**
 * Système de logging centralisé pour Nouri
 * Utilise Winston pour un logging structuré et performant
 */

import winston from 'winston';
import path from 'path';
import { config } from '../config/config';

// Formats personnalisés
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, service = 'api-gateway', ...meta }) => {
    const logEntry = {
      timestamp,
      level,
      service,
      message,
      ...meta
    };
    return JSON.stringify(logEntry);
  })
);

// Configuration des transports
const transports: winston.transport[] = [
  // Console pour le développement
  new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple(),
      winston.format.printf(({ timestamp, level, message, service = 'api-gateway' }) => {
        return `${timestamp} [${service}] ${level}: ${message}`;
      })
    )
  })
];

// Fichiers de logs pour la production
if (config.app.environment === 'production') {
  const logDir = path.join(__dirname, '../../logs');
  
  // Log général
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'app.log'),
      format: customFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );

  // Log des erreurs
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      format: customFormat,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    })
  );

  // Log des accès (pour audit)
  transports.push(
    new winston.transports.File({
      filename: path.join(logDir, 'access.log'),
      format: customFormat,
      maxsize: 10485760, // 10MB
      maxFiles: 10,
    })
  );
}

// Création du logger principal
const logger = winston.createLogger({
  level: config.monitoring.logLevel,
  format: customFormat,
  defaultMeta: { 
    service: 'nouri-api-gateway',
    version: config.app.version,
    environment: config.app.environment
  },
  transports,
  // Gestion des exceptions non capturées
  exceptionHandlers: [
    new winston.transports.File({ 
      filename: path.join(__dirname, '../../logs/exceptions.log') 
    })
  ],
  // Gestion des rejections non gérées
  rejectionHandlers: [
    new winston.transports.File({ 
      filename: path.join(__dirname, '../../logs/rejections.log') 
    })
  ]
});

// Logger spécialisés
export const auditLogger = winston.createLogger({
  level: 'info',
  format: customFormat,
  defaultMeta: { 
    service: 'nouri-audit',
    type: 'audit'
  },
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/audit.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 20,
    })
  ]
});

export const securityLogger = winston.createLogger({
  level: 'warn',
  format: customFormat,
  defaultMeta: { 
    service: 'nouri-security',
    type: 'security'
  },
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/security.log'),
      maxsize: 10485760, // 10MB
      maxFiles: 20,
    })
  ]
});

export const performanceLogger = winston.createLogger({
  level: 'info',
  format: customFormat,
  defaultMeta: { 
    service: 'nouri-performance',
    type: 'performance'
  },
  transports: [
    new winston.transports.File({
      filename: path.join(__dirname, '../../logs/performance.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 10,
    })
  ]
});

// Fonctions utilitaires pour le logging
export const logRequest = (req: any, res: any, responseTime: number) => {
  auditLogger.info('HTTP Request', {
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    userId: req.user?.id,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    contentLength: res.get('Content-Length')
  });
};

export const logSecurityEvent = (event: string, details: any, req?: any) => {
  securityLogger.warn('Security Event', {
    event,
    details,
    ip: req?.ip,
    userAgent: req?.get('User-Agent'),
    userId: req?.user?.id,
    timestamp: new Date().toISOString()
  });
};

export const logPerformance = (operation: string, duration: number, metadata?: any) => {
  performanceLogger.info('Performance Metric', {
    operation,
    duration: `${duration}ms`,
    ...metadata,
    timestamp: new Date().toISOString()
  });
};

export const logError = (error: Error, context?: any) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  });
};

export const logBusinessEvent = (event: string, userId: string, details: any) => {
  auditLogger.info('Business Event', {
    event,
    userId,
    details,
    timestamp: new Date().toISOString()
  });
};

// Middleware pour logger automatiquement les requêtes
export const requestLoggerMiddleware = (req: any, res: any, next: any) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    logRequest(req, res, duration);
    
    // Log des requêtes lentes (> 1 seconde)
    if (duration > 1000) {
      logPerformance('slow_request', duration, {
        method: req.method,
        url: req.originalUrl,
        userId: req.user?.id
      });
    }
  });
  
  next();
};

export { logger };
