/**
 * Nouri - Coach Financier IA
 * API Gateway Principal
 * 
 * Point d'entrée unique pour toutes les requêtes clients
 * Gestion de l'authentification, autorisation, et routage
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import 'express-async-errors';

import { config } from './config/config';
import { logger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { authMiddleware } from './middleware/auth';
import { metricsMiddleware } from './middleware/metrics';
import { setupSwagger } from './config/swagger';
import { connectDatabase } from './config/database';
import { connectRedis } from './config/redis';

// Import des routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import bankingRoutes from './routes/banking';
import budgetRoutes from './routes/budgets';
import chatbotRoutes from './routes/chatbot';
import notificationRoutes from './routes/notifications';
import educationRoutes from './routes/education';
import reportingRoutes from './routes/reporting';
import healthRoutes from './routes/health';

class ApiGateway {
  private app: express.Application;

  constructor() {
    this.app = express();
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // Sécurité
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        error: 'Trop de requêtes, veuillez réessayer plus tard.',
        code: 'RATE_LIMIT_EXCEEDED'
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use('/api/', limiter);

    // Logging
    this.app.use(morgan('combined', {
      stream: { write: (message) => logger.info(message.trim()) }
    }));

    // Métriques Prometheus
    this.app.use(metricsMiddleware);

    // Parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Headers personnalisés
    this.app.use((req, res, next) => {
      res.setHeader('X-Powered-By', 'Nouri Financial Coach');
      res.setHeader('X-API-Version', config.app.version);
      next();
    });
  }

  private initializeRoutes(): void {
    // Route de base
    this.app.get('/', (req, res) => {
      res.json({
        message: 'Nouri - Coach Financier IA API Gateway',
        version: config.app.version,
        status: 'active',
        timestamp: new Date().toISOString(),
        documentation: '/api-docs'
      });
    });

    // Routes publiques
    this.app.use('/api/health', healthRoutes);
    this.app.use('/api/auth', authRoutes);

    // Routes protégées (nécessitent authentification)
    this.app.use('/api/users', authMiddleware, userRoutes);
    this.app.use('/api/banking', authMiddleware, bankingRoutes);
    this.app.use('/api/budgets', authMiddleware, budgetRoutes);
    this.app.use('/api/chatbot', authMiddleware, chatbotRoutes);
    this.app.use('/api/notifications', authMiddleware, notificationRoutes);
    this.app.use('/api/education', authMiddleware, educationRoutes);
    this.app.use('/api/reporting', authMiddleware, reportingRoutes);

    // Documentation Swagger
    setupSwagger(this.app);

    // Route 404
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Route non trouvée',
        message: `La route ${req.originalUrl} n'existe pas`,
        code: 'ROUTE_NOT_FOUND'
      });
    });
  }

  private initializeErrorHandling(): void {
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Connexion à la base de données
      await connectDatabase();
      logger.info('✅ Connexion à PostgreSQL établie');

      // Connexion à Redis
      await connectRedis();
      logger.info('✅ Connexion à Redis établie');

      // Démarrage du serveur
      const port = config.server.port;
      this.app.listen(port, () => {
        logger.info(`🚀 API Gateway Nouri démarré sur le port ${port}`);
        logger.info(`📚 Documentation disponible sur http://localhost:${port}/api-docs`);
        logger.info(`🏥 Health check disponible sur http://localhost:${port}/api/health`);
      });

    } catch (error) {
      logger.error('❌ Erreur lors du démarrage de l\'API Gateway:', error);
      process.exit(1);
    }
  }

  public getApp(): express.Application {
    return this.app;
  }
}

// Gestion des signaux de fermeture
process.on('SIGTERM', () => {
  logger.info('🛑 Signal SIGTERM reçu, arrêt en cours...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('🛑 Signal SIGINT reçu, arrêt en cours...');
  process.exit(0);
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  logger.error('❌ Exception non capturée:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('❌ Promesse rejetée non gérée:', reason);
  process.exit(1);
});

// Démarrage de l'application
if (require.main === module) {
  const gateway = new ApiGateway();
  gateway.start();
}

export default ApiGateway;
