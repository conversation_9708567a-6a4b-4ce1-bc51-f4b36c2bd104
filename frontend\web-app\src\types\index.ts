/**
 * Types TypeScript pour Nouri
 */

// Types utilisateur
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phoneNumber?: string
  profilePicture?: string
  language: string
  currency: string
  timezone: string
  onboardingCompleted: boolean
  kycStatus: 'pending' | 'verified' | 'rejected'
  riskProfile?: string
  createdAt: string
  lastLoginAt?: string
}

// Types de compte
export interface Account {
  id: string
  accountName: string
  accountType: 'checking' | 'savings' | 'credit'
  balance: number
  currency: string
  bankName: string
  isActive: boolean
  lastSyncAt?: string
}

// Types de transaction
export interface Transaction {
  id: string
  amount: number
  currency: string
  description: string
  category?: string
  transactionDate: string
  transactionType: 'debit' | 'credit'
}

// Types de budget
export interface Budget {
  id: string
  name: string
  totalAmount: number
  spentAmount: number
  currency: string
  startDate: string
  endDate: string
}

// Types d'objectif d'épargne
export interface SavingsGoal {
  id: string
  name: string
  targetAmount: number
  currentAmount: number
  currency: string
  targetDate?: string
  category?: string
}

export default {}
