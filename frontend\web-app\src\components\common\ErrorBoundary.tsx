/**
 * Composant Error Boundary pour capturer les erreurs React
 */

import { Component, ErrorInfo, ReactNode } from 'react'
import {
  Box,
  Typography,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Alert,
  AlertTitle
} from '@mui/material'
import {
  Refresh as RefreshIcon,
  Home as HomeIcon,
  BugReport as BugIcon
} from '@mui/icons-material'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    })

    // Log l'erreur pour le monitoring
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    // En production, envoyer l'erreur à un service de monitoring
    if (import.meta.env.PROD) {
      // Exemple: Sentry.captureException(error, { extra: errorInfo })
    }
  }

  handleRefresh = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleReportBug = () => {
    const subject = encodeURIComponent('Erreur dans l\'application Nouri')
    const body = encodeURIComponent(`
Bonjour,

J'ai rencontré une erreur dans l'application Nouri :

Erreur: ${this.state.error?.message}
Stack: ${this.state.error?.stack}

Informations supplémentaires:
- URL: ${window.location.href}
- User Agent: ${navigator.userAgent}
- Timestamp: ${new Date().toISOString()}

Merci de votre aide.
    `)
    
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
  }

  override render() {
    if (this.state.hasError) {
      // Utiliser le fallback personnalisé si fourni
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Interface d'erreur par défaut
      return (
        <Box
          sx={{
            minHeight: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 3,
            backgroundColor: 'background.default'
          }}
        >
          <Card sx={{ maxWidth: 600, width: '100%' }}>
            <CardContent sx={{ p: 4, textAlign: 'center' }}>
              {/* Icône d'erreur */}
              <BugIcon
                sx={{
                  fontSize: 64,
                  color: 'error.main',
                  mb: 2
                }}
              />

              {/* Titre */}
              <Typography variant="h4" component="h1" gutterBottom>
                Oups ! Une erreur s'est produite
              </Typography>

              {/* Description */}
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                Nous sommes désolés, mais quelque chose s'est mal passé. 
                Notre équipe a été notifiée et travaille sur une solution.
              </Typography>

              {/* Détails de l'erreur en mode développement */}
              {import.meta.env.DEV && this.state.error && (
                <Alert severity="error" sx={{ mb: 3, textAlign: 'left' }}>
                  <AlertTitle>Détails de l'erreur (mode développement)</AlertTitle>
                  <Typography variant="body2" component="pre" sx={{ 
                    fontSize: '0.75rem',
                    overflow: 'auto',
                    maxHeight: 200
                  }}>
                    {this.state.error.message}
                    {'\n\n'}
                    {this.state.error.stack}
                  </Typography>
                </Alert>
              )}

              {/* Actions */}
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  startIcon={<RefreshIcon />}
                  onClick={this.handleRefresh}
                >
                  Actualiser la page
                </Button>
                
                <Button
                  variant="outlined"
                  startIcon={<HomeIcon />}
                  onClick={this.handleGoHome}
                >
                  Retour à l'accueil
                </Button>
                
                <Button
                  variant="text"
                  startIcon={<BugIcon />}
                  onClick={this.handleReportBug}
                  color="error"
                >
                  Signaler le problème
                </Button>
              </Box>

              {/* Informations de contact */}
              <Typography variant="caption" color="text.disabled" sx={{ mt: 3, display: 'block' }}>
                Si le problème persiste, contactez-nous à <EMAIL>
              </Typography>
            </CardContent>
          </Card>
        </Box>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
